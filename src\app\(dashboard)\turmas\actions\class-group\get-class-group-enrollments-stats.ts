"use server";

import { createAdminClient } from "@/services/supabase/server";
import { validateUserAuthentication, validateClassGroup } from "./shared/validation-helpers";

/**
 * Obtém apenas as estatísticas de matrículas de uma turma (sem filtros de busca)
 * Esta função sempre retorna os números reais da turma, independentemente de filtros aplicados
 */
export async function getClassGroupEnrollmentsStats(groupId: string) {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;

    // Verificar se a turma existe
    const classGroupValidation = await validateClassGroup(groupId, tenantId);
    if (!classGroupValidation.success) {
      return classGroupValidation;
    }

    const supabase = await createAdminClient();

    // Buscar as matrículas com status
    const { data: enrollments, error } = await supabase
      .from("class_group_enrollments")
      .select("id, status")
      .eq("class_group_id", groupId)
      .eq("tenant_id", tenantId);

    if (error) {
      console.error("Erro ao buscar estatísticas de matrículas:", error);
      return { success: false, errors: { _form: "Erro ao buscar estatísticas" } };
    }

    if (!enrollments || enrollments.length === 0) {
      return { 
        success: true, 
        data: {
          total: 0,
          active: 0,
          inactive: 0,
          suspended: 0,
          completed: 0,
          paused: 0,
          active_including_paused: 0
        }
      };
    }

    // Buscar pausas ativas para as matrículas
    const enrollmentIds = enrollments.map(e => e.id);
    
    const { data: pausesData, error: pausesError } = await supabase
      .from("enrollment_pauses")
      .select("enrollment_id")
      .in("enrollment_id", enrollmentIds)
      .eq("tenant_id", tenantId)
      .is("resumed_at", null); // Apenas pausas ativas

    if (pausesError) {
      console.error("Erro ao buscar pausas:", pausesError);
      // Continuar sem informações de pausa se houver erro
    }

    // Mapear IDs de matrículas pausadas
    const pausedEnrollmentIds = new Set(pausesData?.map(p => p.enrollment_id) || []);

    // Calcular estatísticas considerando pausas
    let activeCount = 0;
    let pausedCount = 0;
    let inactiveCount = 0;
    let suspendedCount = 0;
    let completedCount = 0;

    enrollments.forEach(enrollment => {
      const isPaused = pausedEnrollmentIds.has(enrollment.id);
      
      if (isPaused) {
        pausedCount++;
      } else {
        switch (enrollment.status) {
          case 'active':
            activeCount++;
            break;
          case 'inactive':
            inactiveCount++;
            break;
          case 'suspended':
            suspendedCount++;
            break;
          case 'completed':
            completedCount++;
            break;
        }
      }
    });

    const stats = {
      total: enrollments.length,
      active: activeCount, // Apenas ativos não pausados
      inactive: inactiveCount,
      suspended: suspendedCount,
      completed: completedCount,
      paused: pausedCount,
      active_including_paused: activeCount + pausedCount // Total de matrículas ativas (incluindo pausadas)
    };

    return { success: true, data: stats };
  } catch (error) {
    console.error("Erro ao buscar estatísticas de matrículas:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 