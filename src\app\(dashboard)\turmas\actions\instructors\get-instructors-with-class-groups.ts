"use server";

import { createClient } from "@/services/supabase/server";
import { validateUserAuthentication } from "../class-group/shared/validation-helpers";

export interface InstructorWithBelt {
  id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  avatar_url: string | null;
  belt_color: string;
  degree: number;
  belt_label: string;
  stripe_color: string;
  show_center_line: boolean;
  center_line_color: string;
  sort_order: number;
}

/**
 * Busca instrutores únicos que possuem turmas vinculadas
 * Inclui informações da faixa atual de cada instrutor
 */
export async function getInstructorsWithClassGroups(): Promise<{ 
  success: boolean; 
  data?: InstructorWithBelt[]; 
  errors?: any 
}> {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;
    const supabase = await createClient();

    // Buscar instrutores únicos que têm turmas
    const { data: classGroups, error: classGroupsError } = await supabase
      .from("class_groups")
      .select(`
        instructor_id,
        instructor:users!class_groups_instructor_id_fkey(
          id,
          first_name,
          last_name,
          full_name,
          avatar_url
        )
      `)
      .eq("tenant_id", tenantId);

    if (classGroupsError) {
      console.error("Erro ao buscar turmas:", classGroupsError);
      return { success: false, errors: { _form: "Erro ao buscar instrutores" } };
    }

    // Obter IDs únicos dos instrutores
    const instructorIds = Array.from(new Set(classGroups?.map(cg => cg.instructor_id).filter(Boolean) || []));

    if (instructorIds.length === 0) {
      return { success: true, data: [] };
    }

    // Buscar dados completos dos instrutores
    const { data: instructorsData, error } = await supabase
      .from("users")
      .select(`
        id,
        first_name,
        last_name,
        full_name,
        avatar_url
      `)
      .in("id", instructorIds)
      .order("first_name", { ascending: true });

    if (error) {
      console.error("Erro ao buscar dados dos instrutores:", error);
      return { success: false, errors: { _form: "Erro ao buscar dados dos instrutores" } };
    }

    // Buscar dados dos instrutores (tabela instructors)
    const { data: instructorsTable, error: instructorsError } = await supabase
      .from("instructors")
      .select(`
        id,
        user_id
      `)
      .in("user_id", instructorIds);

    if (instructorsError) {
      console.error("Erro ao buscar tabela instructors:", instructorsError);
      return { success: false, errors: { _form: "Erro ao buscar dados de instrutores" } };
    }

    // Buscar faixas atuais dos instrutores usando a função RPC
    const instructorTableIds = instructorsTable?.map(i => i.id) || [];
    
    // Buscar dados de faixas atuais usando RPC function para cada instrutor
    const beltPromises = instructorTableIds.map(async (instructorId) => {
      const { data, error } = await supabase.rpc('get_instructor_current_belt_details', {
        instructor_id_param: instructorId
      });
      
      if (error) {
        console.warn(`Erro ao buscar faixa do instrutor ${instructorId}:`, error);
        return null;
      }
      
      return data && data.length > 0 ? { instructor_id: instructorId, belt: data[0] } : null;
    });

    const beltsResults = await Promise.all(beltPromises);
    const beltsData = beltsResults.filter(Boolean);

    // Criar mapa de instrutores por user_id
    const instructorMapByUserId = new Map<string, string>();
    instructorsTable?.forEach(instructor => {
      instructorMapByUserId.set(instructor.user_id, instructor.id);
    });

    // Processar dados para extrair informações dos instrutores
    const uniqueInstructors: InstructorWithBelt[] = instructorsData?.map((user: any) => {
      // Buscar instructor_id correspondente ao user_id
      const instructorId = instructorMapByUserId.get(user.id);
      
      // Buscar a faixa mais recente
      let currentBelt = { 
        belt_color: "#FFFFFF", 
        degree: 1,
        belt_label: "Faixa Branca - 1º Grau",
        stripe_color: "#000000",
        show_center_line: false,
        center_line_color: "",
        sort_order: 1
      };
      
      if (beltsData && beltsData.length > 0 && instructorId) {
        const userBeltData = beltsData.find(item => item && item.instructor_id === instructorId);
        
        if (userBeltData && userBeltData.belt) {
          currentBelt = {
            belt_color: userBeltData.belt.belt_color || "#FFFFFF",
            degree: userBeltData.belt.degree || 1,
            belt_label: userBeltData.belt.label || "Faixa Branca - 1º Grau",
            stripe_color: userBeltData.belt.stripe_color || "#000000",
            show_center_line: userBeltData.belt.show_center_line || false,
            center_line_color: userBeltData.belt.center_line_color || "",
            sort_order: userBeltData.belt.sort_order || 1
          };
        }
      }

      return {
        id: user.id,
        first_name: user.first_name || "",
        last_name: user.last_name || "",
        full_name: user.full_name || `${user.first_name || ""} ${user.last_name || ""}`.trim(),
        avatar_url: user.avatar_url,
        belt_color: currentBelt.belt_color,
        degree: currentBelt.degree,
        belt_label: currentBelt.belt_label,
        stripe_color: currentBelt.stripe_color,
        show_center_line: currentBelt.show_center_line,
        center_line_color: currentBelt.center_line_color,
        sort_order: currentBelt.sort_order,
      };
    }) || [];

    return {
      success: true,
      data: uniqueInstructors,
    };
  } catch (error) {
    console.error("Erro ao buscar instrutores:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 