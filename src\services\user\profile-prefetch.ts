'use client';

import { QueryClient } from '@tanstack/react-query';
import { CACHE_KEYS } from '@/constants/cache-keys';
import { getPermissionClientService } from '../permissions/client-service';
import { prefetchPermissionContextOnLogin } from '../auth/actions/permission-prefetch';
import { loadUserProfileClient } from './profile-access-client';
import { cacheService } from '@/services/cache';

/**
 * Realiza o prefetch do perfil do usuário e permissões logo após o login
 * Esta função deve ser chamada após uma autenticação bem-sucedida
 * 
 * @param userId ID do usuário autenticado
 * @param queryClient Instância do QueryClient compartilhada
 * @returns true se o prefetch foi bem-sucedido
 */
export async function prefetchUserProfileData(
  userId: string,
  queryClient: QueryClient
): Promise<boolean> {
  if (!userId || !queryClient) {
    console.error('Prefetch não foi possível: ID de usuário ou queryClient não fornecidos');
    return false;
  }
  
  try {
    console.log('[Prefetch] Iniciando prefetch de dados do usuário:', userId);
    
    // Inicializar o serviço de cache centralizado
    cacheService.initialize(queryClient);
    
    // 1. Garantir que o serviço de permissões tenha acesso ao queryClient
    const permissionService = getPermissionClientService();
    permissionService.setQueryClient(queryClient);
    
    // 2. Prefetch do contexto de permissões via server action
    const permissionContextFetched = await prefetchPermissionContextOnLogin(userId);
    console.log('[Prefetch] Contexto de permissões via server action:', permissionContextFetched ? 'Sucesso' : 'Falha');
    
    // 3. Aguardar um curto período para garantir que a resposta do servidor seja processada
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 4. Prefetch do contexto de permissões para cache do cliente usando o queryKey correto
    const permissionContext = await permissionService.prefetchPermissionContext(userId);
    
    // 5. Verificar se o context foi armazenado em cache corretamente
    const basicPermissionKey = CACHE_KEYS.BASIC_PERMISSION_CONTEXT(userId);
    const cachedContext = queryClient.getQueryData(basicPermissionKey);
    console.log('[Prefetch] Cache de permissões do cliente:', cachedContext ? 'Presente no cache' : 'Ausente no cache');
    
    if (!cachedContext && permissionContext) {
      // Se não estiver em cache, forçar o armazenamento
      cacheService.setData(basicPermissionKey, permissionContext);
      console.log('[Prefetch] Cache de permissões forçado manualmente');
    }
    
    // 6. Prefetch do perfil completo do usuário
    await queryClient.prefetchQuery({
      queryKey: CACHE_KEYS.USER_PROFILE,
      queryFn: async () => {
        const result = await loadUserProfileClient(userId);
        console.log('[Prefetch] Dados do perfil carregados:', result ? 'Sucesso' : 'Falha');
        return result;
      },
      staleTime: 5 * 60 * 1000, // 5 minutos
    });
    
    console.log('[Prefetch] Prefetch de dados do usuário concluído com sucesso');
    return true;
  } catch (error) {
    console.error('[Prefetch] Erro durante prefetch de dados do usuário:', error);
    return false;
  }
} 