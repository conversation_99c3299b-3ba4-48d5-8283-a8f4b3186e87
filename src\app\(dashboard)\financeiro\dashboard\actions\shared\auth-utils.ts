/**
 * Utilitários de autenticação compartilhados para actions do dashboard
 */

import { createTenantServerClient } from '@/services/supabase/server';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';

/**
 * Obtém cliente Supabase autenticado
 */
export const getAuthenticatedClient = async () => {

  const user = await getCurrentUser();
  if (!user) {
    throw new Error('Usuário não autenticado');
  }

  const supabase = await createTenantServerClient();
  const tenantId = user.app_metadata?.tenant_id;

  if (!tenantId) {
    throw new Error('Tenant não encontrado');
  }

  return { supabase, tenantId, user };
};

/**
 * Formata datas para queries SQL
 */
export const formatDateForSQL = (date: Date): string => {
  return date.toISOString().split('T')[0];
};
