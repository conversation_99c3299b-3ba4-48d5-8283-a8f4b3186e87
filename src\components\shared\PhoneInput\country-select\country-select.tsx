'use client';

import { useState } from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useTenantTheme } from '@/hooks/tenant/use-tenant-theme';
import { countries } from '../countries';

export interface CountrySelectProps {
  value: string;
  onValueChange: (value: string) => void;
  disabled?: boolean;
  showFlag?: boolean;
  triggerClassName?: string;
}

export function CountrySelect({
  value,
  onValueChange,
  disabled = false,
  showFlag = true,
  triggerClassName = '',
}: CountrySelectProps) {
  const [open, setOpen] = useState(false);
  const selectedCountry = countries.find(country => country.phoneCode === value) || countries.find(c => c.code === 'BR') || countries[0];
  const { primaryColor } = useTenantTheme();

  const handleSelectCountry = (country: typeof countries[0]) => {
    onValueChange(country.phoneCode);
    setOpen(false);
  };

  // Estilos condicionais para cores do tenant
  const activeItemStyle = primaryColor ? { 
    backgroundColor: `${primaryColor}15`,
    color: primaryColor 
  } : undefined;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={`justify-between ${
            disabled ? 'opacity-50 cursor-not-allowed' : ''
          } ${triggerClassName}`}
          disabled={disabled}
        >
          <div className="flex items-center gap-2">
            {showFlag && selectedCountry?.flag && (
              <span className="text-base flex items-center">{selectedCountry.flag}</span>
            )}
            <span className="text-sm font-medium">{selectedCountry.phoneCode}</span>
          </div>
          <ChevronsUpDown className="ml-1 h-4 w-4 shrink-0 text-slate-400 dark:text-slate-500" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[220px] p-0 border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 shadow-md">
        <Command className="bg-transparent">
          <CommandInput 
            placeholder="Buscar país..." 
            className="border-none focus:ring-0 text-slate-900 dark:text-slate-100 placeholder:text-slate-400 dark:placeholder:text-slate-500"
          />
          <CommandEmpty className="text-slate-500 dark:text-slate-400 py-2">
            Nenhum país encontrado.
          </CommandEmpty>
          <CommandList className="max-h-[300px] overflow-auto scrollbar-thin scrollbar-thumb-slate-200 dark:scrollbar-thumb-slate-700">
            <CommandGroup className="text-slate-700 dark:text-slate-300">
              {countries.map((country) => (
                <CommandItem
                  key={country.code}
                  value={`${country.name} ${country.phoneCode}`}
                  onSelect={() => handleSelectCountry(country)}
                  className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 aria-selected:bg-primary/10"
                  style={selectedCountry.code === country.code ? activeItemStyle : undefined}
                >
                  <div className="flex items-center">
                    <span className="mr-2 text-xl flex items-center">{country.flag}</span>
                    <span className="mr-2 font-medium text-slate-900 dark:text-slate-100">{country.phoneCode}</span>
                    <span className="text-sm text-slate-500 dark:text-slate-400">{country.name}</span>
                  </div>
                  <Check
                    className={cn(
                      "ml-auto h-4 w-4",
                      selectedCountry.code === country.code 
                        ? "opacity-100" 
                        : "opacity-0"
                    )}
                    style={primaryColor ? { color: primaryColor } : undefined}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
} 