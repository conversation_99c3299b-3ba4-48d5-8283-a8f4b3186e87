'use client';

import { useState, useEffect } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/ui/use-toast';
import { Loader2, Save, X } from 'lucide-react';
import { despesaFormSchema, type DespesaFormValues } from '../schemas/despesa-schema';
import { BasicInfoSection } from './form-sections/BasicInfoSection';
import { PaymentInfoSection } from './form-sections/PaymentInfoSection';
import { AdditionalInfoSection } from './form-sections/AdditionalInfoSection';
import { RecurrenceSection } from './form-sections/RecurrenceSection';
import { getPaymentMethods, getExpenseCategories, addExpense, type ExpenseData } from '../../actions';
import { PaymentMethodOption, ExpenseCategory } from '../../components/types';

export function AdicionarDespesaForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethodOption[]>([]);
  const [expenseCategories, setExpenseCategories] = useState<ExpenseCategory[]>([]);
  const router = useRouter();
  const { toast } = useToast();

  const form = useForm<DespesaFormValues>({
    resolver: zodResolver(despesaFormSchema),
    defaultValues: {
      supplierName: '',
      categoryId: '',
      type: '',
      amount: '',
      paymentMethod: '',
      date: new Date().toISOString().split('T')[0], // Data atual como padrão
      dueDate: '',
      description: '',
      status: 'Pendente',
      isRecurring: false,
      recurrenceFrequency: undefined,
      recurrenceEndDate: '',
      recurrenceCount: '',
    },
  });

  // Carregar métodos de pagamento e categorias
  useEffect(() => {
    const loadData = async () => {
      try {
        const [methods, categories] = await Promise.all([
          getPaymentMethods(),
          getExpenseCategories()
        ]);

        setPaymentMethods(methods);
        setExpenseCategories(categories);
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        toast({
          title: 'Erro ao carregar dados',
          description: 'Não foi possível carregar os dados necessários.',
          variant: 'destructive',
        });
      }
    };

    loadData();
  }, [toast]);

  // Função para mapear dados do formulário para o formato da action
  const mapFormDataToExpenseData = (data: DespesaFormValues): ExpenseData => {
    // Converter valor de string para número
    const numericAmount = parseFloat(data.amount.replace(/[^\d,]/g, '').replace(',', '.'));

    // Mapear status do formulário para status da API
    const statusMap: Record<string, 'pending' | 'paid' | 'overdue' | 'canceled'> = {
      'Pendente': 'pending',
      'Pago': 'paid',
      'Vencido': 'overdue'
    };

    // Configuração de recorrência se aplicável
    const recurrenceConfig = data.isRecurring ? {
      frequency: data.recurrenceFrequency,
      endDate: data.recurrenceEndDate || null,
      count: data.recurrenceCount ? parseInt(data.recurrenceCount) : null
    } : null;

    return {
      category_id: data.categoryId || undefined,
      supplier_name: data.supplierName,
      amount: numericAmount,
      currency: 'BRL',
      description: data.description ? `${data.type} - ${data.description}` : data.type,
      due_date: data.dueDate || undefined,
      payment_method_id: data.paymentMethod || undefined,
      status: statusMap[data.status] || 'pending',
      is_recurring: data.isRecurring,
      recurrence_config: recurrenceConfig,
      metadata: {
        type: data.type,
        expense_date: data.date, // Data da despesa
        originalDescription: data.description,
        originalFormData: data
      }
    };
  };

  const handleSubmit = async (data: DespesaFormValues) => {
    setIsSubmitting(true);

    try {
      const expenseData = mapFormDataToExpenseData(data);
      const result = await addExpense(expenseData);

      if (result.success) {
        toast({
          title: 'Despesa adicionada com sucesso!',
          description: 'A despesa foi registrada no sistema.',
        });

        router.push('/financeiro/pagamentos');
      } else {
        throw new Error(result.error || 'Erro desconhecido');
      }
    } catch (error) {
      console.error('Erro ao adicionar despesa:', error);
      toast({
        title: 'Erro ao adicionar despesa',
        description: error instanceof Error ? error.message : 'Ocorreu um erro ao registrar a despesa. Tente novamente.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.push('/financeiro/pagamentos');
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Informações Básicas */}
          <Card className="border-gray-200 dark:border-gray-700 shadow-sm">
            <CardContent className="p-6">
              <BasicInfoSection categories={expenseCategories} />
            </CardContent>
          </Card>

          {/* Informações de Pagamento */}
          <Card className="border-gray-200 dark:border-gray-700 shadow-sm">
            <CardContent className="p-6">
              <PaymentInfoSection paymentMethods={paymentMethods} />
            </CardContent>
          </Card>

          {/* Recorrência */}
          <Card className="border-gray-200 dark:border-gray-700 shadow-sm">
            <CardContent className="p-6">
              <RecurrenceSection />
            </CardContent>
          </Card>

          {/* Informações Adicionais */}
          <Card className="border-gray-200 dark:border-gray-700 shadow-sm">
            <CardContent className="p-6">
              <AdditionalInfoSection />
            </CardContent>
          </Card>

          {/* Botões de Ação */}
          <div className="flex items-center justify-end space-x-4 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              <X className="h-4 w-4 mr-2" />
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px] bg-blue-600 hover:bg-blue-700 text-white shadow-md"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Salvando...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Salvar Despesa
                </>
              )}
            </Button>
          </div>
        </form>
      </FormProvider>
    </div>
  );
}
