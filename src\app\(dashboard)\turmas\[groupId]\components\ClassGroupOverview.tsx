'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ClassGroupWithDetails } from '@/app/(dashboard)/aulas/types';
import { formatDateTimeBrazil } from '@/utils/format';
import { 
  User, 
  MapPin, 
  Users, 
  Calendar,
  Clock,
  TrendingUp,
  AlertCircle
} from 'lucide-react';

interface ClassGroupOverviewProps {
  classGroup: ClassGroupWithDetails;
}

export function ClassGroupOverview({ classGroup }: ClassGroupOverviewProps) {
  // Função para gerar as iniciais do nome (seguindo padrão dos instrutores)
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const getCategoryDisplay = (category: string | null) => {
    const categories = {
      'kids': 'Infantil',
      'teens': 'Juvenil', 
      'adults': 'Adulto',
      'seniors': 'Sênior'
    };
    return category ? categories[category as keyof typeof categories] || category : 'Não especificada';
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <Badge variant={isActive ? 'default' : 'secondary'}>
        {isActive ? 'Ativa' : 'Inativa'}
      </Badge>
    );
  };

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {/* Informações Básicas */}
      <Card className="bg-white dark:bg-gray-900 border-slate-200 dark:border-gray-700">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2 text-slate-900 dark:text-gray-100">
            <User className="h-5 w-5 text-slate-600 dark:text-gray-400" />
            Informações Básicas
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-slate-600 dark:text-gray-400">Status:</span>
            {getStatusBadge(classGroup.is_active ?? false)}
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-slate-500 dark:text-gray-500" />
              <span className="text-sm text-slate-600 dark:text-gray-400">Instrutor:</span>
            </div>
            <div className="flex items-center gap-2">
              <Avatar className="h-8 w-8 border-2 border-muted">
                <AvatarImage 
                  src={classGroup.instructor.avatar_url || ""} 
                  alt={classGroup.instructor.full_name || `${classGroup.instructor.first_name} ${classGroup.instructor.last_name || ''}`} 
                />
                <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold">
                  {getInitials(classGroup.instructor.full_name || `${classGroup.instructor.first_name} ${classGroup.instructor.last_name || ''}`)}
                </AvatarFallback>
              </Avatar>
              <span className="text-sm font-medium text-slate-900 dark:text-gray-100">
                {classGroup.instructor.full_name || `${classGroup.instructor.first_name} ${classGroup.instructor.last_name || ''}`}
              </span>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-slate-500 dark:text-gray-500" />
              <span className="text-sm text-slate-600 dark:text-gray-400">Filial:</span>
            </div>
            <p className="text-sm font-medium pl-6 text-slate-900 dark:text-gray-100">
              {classGroup.branch.name}
            </p>
          </div>

          <div className="pt-2">
            <Badge variant="outline" className="text-xs">
              {getCategoryDisplay(classGroup.category)}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Capacidade e Matrículas */}
      <Card className="bg-white dark:bg-gray-900 border-slate-200 dark:border-gray-700">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2 text-slate-900 dark:text-gray-100">
            <Users className="h-5 w-5 text-slate-600 dark:text-gray-400" />
            Capacidade
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-slate-600 dark:text-gray-400">Matriculados:</span>
            <span className="font-semibold text-slate-900 dark:text-gray-100">
              {classGroup._count.enrollments}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-slate-600 dark:text-gray-400">Capacidade máxima:</span>
            <span className="font-semibold text-slate-900 dark:text-gray-100">
              {classGroup.max_capacity || 'Ilimitada'}
            </span>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-600 dark:text-gray-400">Lista de espera:</span>
              <div className="flex items-center gap-2">
                {classGroup.allow_waitlist && (
                  <span className="font-semibold text-slate-900 dark:text-gray-100">
                    {classGroup._count.waitlist || 0}
                  </span>
                )}
                {!classGroup.allow_waitlist && (
                  <Badge variant="secondary" className="text-xs flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    Desativada
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {classGroup.capacity_usage_percentage && classGroup.max_capacity && (
            <div className="pt-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-slate-600 dark:text-gray-400">Taxa de ocupação</span>
                <span className="text-sm font-medium text-slate-900 dark:text-gray-100">
                  {classGroup.capacity_usage_percentage.toFixed(1)}%
                </span>
              </div>
              <div className="w-full bg-slate-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-blue-600 dark:bg-blue-500 h-2 rounded-full transition-all duration-300" 
                  style={{ width: `${Math.min(classGroup.capacity_usage_percentage, 100)}%` }}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Estatísticas Rápidas */}
      <Card className="bg-white dark:bg-gray-900 border-slate-200 dark:border-gray-700">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2 text-slate-900 dark:text-gray-100">
            <TrendingUp className="h-5 w-5 text-slate-600 dark:text-gray-400" />
            Estatísticas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4">
            <div className="text-center p-4 bg-slate-50 dark:bg-gray-800 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {classGroup._count.classes}
              </div>
              <div className="text-sm text-slate-600 dark:text-gray-400">
                Total de aulas
              </div>
            </div>
            
            <div className="text-center p-4 bg-slate-50 dark:bg-gray-800 rounded-lg">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {classGroup._count.enrollments}
              </div>
              <div className="text-sm text-slate-600 dark:text-gray-400">
                Alunos ativos
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Período de Funcionamento */}
      {(classGroup.start_date || classGroup.end_date) && (
        <Card className="bg-white dark:bg-gray-900 border-slate-200 dark:border-gray-700 md:col-span-2 lg:col-span-3">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2 text-slate-900 dark:text-gray-100">
              <Calendar className="h-5 w-5 text-slate-600 dark:text-gray-400" />
              Período de Funcionamento
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              {classGroup.start_date && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-slate-500 dark:text-gray-500" />
                    <span className="text-sm text-slate-600 dark:text-gray-400">Data de início:</span>
                  </div>
                  <p className="text-sm font-medium pl-6 text-slate-900 dark:text-gray-100">
                    {formatDateTimeBrazil(classGroup.start_date)}
                  </p>
                </div>
              )}
              
              {classGroup.end_date && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-slate-500 dark:text-gray-500" />
                    <span className="text-sm text-slate-600 dark:text-gray-400">Data de término:</span>
                  </div>
                  <p className="text-sm font-medium pl-6 text-slate-900 dark:text-gray-100">
                    {formatDateTimeBrazil(classGroup.end_date)}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 