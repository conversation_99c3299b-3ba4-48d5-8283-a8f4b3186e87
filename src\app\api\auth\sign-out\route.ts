import { NextResponse } from 'next/server';
import { createClient } from '@/services/supabase/server';
import { addClearAuthCookiesToResponse } from '@/services/auth/utils/server-utils';

export async function POST() {
  try {
    const supabase = await createClient();
    
    await supabase.auth.signOut();
    
    const response = NextResponse.json({ success: true });
    
    const responseWithClearedCookies = await addClearAuthCookiesToResponse(response);
    
    return responseWithClearedCookies;
  } catch (error) {
    console.error('Erro no processo de logout:', error);
    return NextResponse.json(
      { error: 'Falha ao realizar logout', success: false },
      { status: 500 }
    );
  }
} 