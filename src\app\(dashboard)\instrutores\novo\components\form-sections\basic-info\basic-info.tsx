'use client';

import { useF<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { NovoInstrutorFormValues } from "../../../actions/schemas/instrutor-schema";
import { useEffect, useMemo, useState } from "react";
import { useTenantTheme } from "@/hooks/tenant/use-tenant-theme";
import { cn } from "@/lib/utils";
import { AvatarCropModal } from "../../avatar-crop-modal";
import { Mail, User, Phone, AtSign, GraduationCap, Calendar, Building, AlertCircle } from "lucide-react";
import { PhoneInput } from "@/components/shared/PhoneInput";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { getBranchesByTenant } from "../../../actions/branch-actions";
import { toast } from "sonner";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useEmailAvailability } from "@/hooks/instrutores/use-email-availability";
import { getModalitiesForGraduation, getBeltLevelsForModality } from "@/app/(dashboard)/perfil/actions/graduation-actions";
import { BeltWithDetails } from "@/components/belt/BeltWithDetails";

// Remover beltOptions hardcoded e adicionar interfaces
interface Modality {
  id: string;
  slug: string;
  name: string;
  enabled: boolean;
}

interface BeltLevel {
  id: string;
  belt_color: string;
  degree: number;
  label: string;
  sort_order: number;
  stripe_color?: string | null;
  show_center_line?: boolean | null;
  center_line_color?: string | null;
}

const genderOptions = [
  { value: "masculino", label: "Masculino" },
  { value: "feminino", label: "Feminino" },
  { value: "outro", label: "Outro" },
  { value: "prefiro_nao_informar", label: "Prefiro não informar" },
];

// Tipo para representar as filiais
type Branch = {
  id: string;
  name: string;
};

export default function BasicInfoSection() {
  const { control, watch, setValue, getValues } = useFormContext<NovoInstrutorFormValues>();
  const fullName = watch('full_name') || '';
  const avatarUrl = watch('avatar_url') || '';
  const selectedModalityId = watch('current_belt_level_id');
  
  // Verificação de disponibilidade de e-mail
  const emailValue = watch('email') || '';
  const { data: emailAvailability, isLoading: emailChecking } = useEmailAvailability(emailValue);
  
  // Estados para modalidades e belt levels
  const [modalities, setModalities] = useState<Modality[]>([]);
  const [beltLevels, setBeltLevels] = useState<BeltLevel[]>([]);
  const [selectedModalityId_local, setSelectedModalityId_local] = useState<string>('');
  const [selectedBeltLevelId, setSelectedBeltLevelId] = useState<string>('');
  const [loadingModalities, setLoadingModalities] = useState(false);
  const [loadingBeltLevels, setLoadingBeltLevels] = useState(false);
  
  // Extrai os dois primeiros nomes para exibição no cabeçalho
  const displayName = useMemo(() => {
    const nameParts = fullName.trim().split(' ');
    return nameParts.length > 1 
      ? `${nameParts[0]} ${nameParts[1]}` 
      : fullName;
  }, [fullName]);

  // Derivar first_name e last_name do nome completo
  // IMPORTANTE: Esta lógica deve estar alinhada com extractNameParts() do backend
  useEffect(() => {
    if (fullName) {
      const nameParts = fullName.trim().split(' ').filter(part => part.length > 0);
      
      if (nameParts.length === 0) {
        setValue('first_name', '', { shouldValidate: true });
        setValue('last_name', '', { shouldValidate: true });
      } else if (nameParts.length === 1) {
        // Se só tem um nome, vai todo para first_name
        setValue('first_name', nameParts[0], { shouldValidate: true });
        setValue('last_name', '', { shouldValidate: true });
      } else {
        // Primeiro nome para first_name, segundo nome para last_name
        // Esta lógica está alinhada com utils/name-utils.ts
        setValue('first_name', nameParts[0], { shouldValidate: true });
        setValue('last_name', nameParts[1], { shouldValidate: true });
      }
    }
  }, [fullName, setValue]);

  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const { primaryColor } = useTenantTheme();
  
  // Estados para o modal de crop
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [tempPreviewUrl, setTempPreviewUrl] = useState<string | null>(null);

  // Estados para carregamento de filiais
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoadingBranches, setIsLoadingBranches] = useState(true);
  const [branchError, setBranchError] = useState<string | null>(null);

  // Inicializa o preview do avatar com o valor do formulário
  useEffect(() => {
    if (avatarUrl && !avatarPreview) {
      setAvatarPreview(avatarUrl);
    }
  }, [avatarUrl, avatarPreview]);

  // Carregar modalidades disponíveis
  useEffect(() => {
    const loadModalities = async () => {
      setLoadingModalities(true);
      try {
        const result = await getModalitiesForGraduation();
        if (result.success && result.data) {
          setModalities(result.data);
          
          // Se só existe uma modalidade, selecionar automaticamente
          if (result.data.length === 1) {
            setSelectedModalityId_local(result.data[0].id);
          }
        } else {
          toast.error('Erro ao carregar modalidades');
        }
      } catch (error) {
        console.error('Erro ao carregar modalidades:', error);
        toast.error('Erro ao carregar modalidades');
      } finally {
        setLoadingModalities(false);
      }
    };

    loadModalities();
  }, []);

  // Carregar níveis de faixa quando modalidade for selecionada
  useEffect(() => {
    const loadBeltLevels = async () => {
      if (!selectedModalityId_local) {
        setBeltLevels([]);
        setSelectedBeltLevelId('');
        setValue('current_belt_level_id', undefined);
        return;
      }

      const selectedModalityData = modalities.find(m => m.id === selectedModalityId_local);
      if (!selectedModalityData) return;

      setLoadingBeltLevels(true);
      try {
        const result = await getBeltLevelsForModality(selectedModalityData.slug);
        if (result.success && result.data) {
          setBeltLevels(result.data);
        } else {
          toast.error('Erro ao buscar níveis de faixa');
        }
      } catch (error) {
        console.error('Erro ao carregar níveis de faixa:', error);
        toast.error('Erro ao carregar níveis de faixa');
      } finally {
        setLoadingBeltLevels(false);
      }
    };

    loadBeltLevels();
  }, [selectedModalityId_local, modalities, setValue]);

  // Carregar filiais disponíveis usando o server action
  useEffect(() => {
    const fetchBranches = async () => {
      setIsLoadingBranches(true);
      setBranchError(null);
      
      try {
        const result = await getBranchesByTenant();

        if (!result.success) {
          setBranchError(result.error || 'Erro desconhecido ao carregar filiais');
          toast.error(`Erro ao carregar filiais: ${result.error}`);
          return;
        }
        
        if (!result.data || result.data.length === 0) {
          setBranchError('Nenhuma filial encontrada para este tenant');
          return;
        }
        
        setBranches(result.data);
        
        // Se só existe uma filial, selecionar automaticamente
        if (result.data.length === 1) {
          setValue('branch_id', result.data[0].id, { shouldValidate: true });
        }
      } catch (error) {
        setBranchError(error instanceof Error ? error.message : 'Erro desconhecido ao carregar filiais');
        toast.error('Erro ao carregar filiais');
      } finally {
        setIsLoadingBranches(false);
      }
    };
    
    fetchBranches();
  }, [setValue]);

  // Função para gerar iniciais do nome para exibir no avatar quando não há imagem
  const getInitials = (name: string): string => {
    if (!name) return "";
    return name
      .split(" ")
      .map((n: string) => n?.[0])
      .slice(0, 2)
      .join("")
      .toUpperCase();
  };

  // Obter dados da faixa selecionada
  const selectedBeltLevelData = beltLevels.find(bl => bl.id === selectedBeltLevelId);

  // Manipulador para quando o usuário seleciona um arquivo
  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Criar URL temporária para o preview no modal
      const reader = new FileReader();
      reader.onloadend = () => {
        setTempPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
      
      // Atualizar o arquivo selecionado
      setSelectedFile(file);
      
      // Abrir o modal de crop
      setIsModalOpen(true);
    }
  };

  // Manipulador para quando o usuário confirma o crop
  const handleCroppedImageChange = (dataUrl: string) => {
    // Atualizar o preview
    setAvatarPreview(dataUrl);
    
    // Atualizar o valor no formulário
    setValue('avatar_url', dataUrl, { 
      shouldValidate: true,
      shouldDirty: true
    });
  };

  // Manipulador para seleção de belt level
  const handleBeltLevelChange = (beltLevelId: string) => {
    setSelectedBeltLevelId(beltLevelId);
    setValue('current_belt_level_id', beltLevelId, { shouldValidate: true });
  };

  return (
    <div className="grid grid-cols-1 gap-6">
      {/* Avatar e cabeçalho do perfil */}
      <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <User className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Foto e Perfil
          </h2>
        </div>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4 md:gap-6 items-center md:items-start">
            {/* Avatar Upload */}
            <div className="flex flex-col items-center flex-shrink-0">
              <div className="flex justify-center">
                <div className="relative">
                  <div 
                    className={cn(
                      "h-24 w-24 rounded-full flex items-center justify-center overflow-hidden bg-slate-200 dark:bg-slate-700 text-2xl font-semibold text-slate-600 dark:text-slate-300",
                      avatarPreview ? "border-2 border-primary" : ""
                    )}
                    style={primaryColor && !avatarPreview ? { backgroundColor: `${primaryColor}20` } : undefined}
                  >
                    {avatarPreview ? (
                      <img src={avatarPreview} alt={displayName} className="w-full h-full object-cover" />
                    ) : (
                      <span style={primaryColor ? { color: primaryColor } : undefined}>
                        {getInitials(displayName)}
                      </span>
                    )}
                  </div>
                  
                  <label 
                    htmlFor="avatar-upload" 
                    className="absolute bottom-0 right-0 w-7 h-7 flex items-center justify-center rounded-full bg-primary text-white cursor-pointer shadow-md hover:brightness-110 transition-all"
                    style={primaryColor ? { backgroundColor: primaryColor } : undefined}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </label>
                  
                  <input 
                    id="avatar-upload" 
                    type="file" 
                    accept="image/*" 
                    className="hidden" 
                    onChange={handleAvatarChange}
                  />
                </div>
              </div>
              <p className="mt-2 text-xs text-center text-slate-500 dark:text-slate-400">
                Clique para adicionar foto
              </p>
            </div>
            
            {/* Informações de perfil */}
            <div className="flex-grow text-center md:text-left">
              <div className="mb-4">
                <div className="flex flex-wrap items-center justify-center md:justify-start gap-2 mb-1.5">
                  <span className={cn(
                    "px-2.5 py-0.5 rounded-full text-xs font-medium border",
                    "bg-primary/10 text-primary border-primary/20"
                  )}
                  style={primaryColor ? { 
                    backgroundColor: `${primaryColor}20`, 
                    color: primaryColor,
                    borderColor: `${primaryColor}30` 
                  } : undefined}
                  >
                    Novo Instrutor
                  </span>
                  
                  {selectedBeltLevelData && (
                    <div className="flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium border bg-slate-50 dark:bg-slate-800 border-slate-200 dark:border-slate-700">
                      <BeltWithDetails
                        color={selectedBeltLevelData.belt_color}
                        degree={selectedBeltLevelData.degree}
                        stripeColor={selectedBeltLevelData.stripe_color}
                        showCenterLine={selectedBeltLevelData.show_center_line}
                        centerLineColor={selectedBeltLevelData.center_line_color}
                        size="sm"
                        className="w-4 h-2"
                      />
                      <span>{selectedBeltLevelData.label}</span>
                    </div>
                  )}
                </div>
                
                <h2 className="text-2xl sm:text-3xl font-bold text-slate-900 dark:text-slate-100">
                  {displayName || 'Nome do Instrutor'}
                </h2>
                
                <div className="flex flex-wrap items-center justify-center md:justify-start gap-3 mt-2">
                  <div className="flex items-center text-sm text-slate-500 dark:text-slate-400">
                    <GraduationCap className="w-3.5 h-3.5 mr-1" />
                    <span>Instrutor</span>
                  </div>
                  <div className="flex items-center text-sm text-slate-500 dark:text-slate-400">
                    <Calendar className="w-3.5 h-3.5 mr-1" />
                    <span>Novo cadastro</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Informações básicas */}
      <Card className="border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <User className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Informações Pessoais
          </h2>
        </div>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Nome completo */}
            <div className="md:col-span-2">
              <FormField
                control={control}
                name="full_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-700 dark:text-slate-300">Nome completo *</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Digite o nome completo" 
                        {...field}
                        className="border-slate-300 dark:border-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Email */}
            <FormField
              control={control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-700 dark:text-slate-300 flex items-center">
                    <Mail className="w-4 h-4 mr-1" />
                    Email *
                  </FormLabel>
                  <FormControl>
                    <Input 
                      type="email" 
                      placeholder="<EMAIL>" 
                      {...field}
                      className="border-slate-300 dark:border-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                  {emailValue && !emailChecking && emailAvailability?.available && (
                    <p className="text-xs text-green-600 mt-1">E-mail disponível</p>
                  )}
                  {emailValue && !emailChecking && emailAvailability && !emailAvailability.available && (
                    <p className="text-xs text-red-600 mt-1">E-mail já está em uso</p>
                  )}
                  {emailValue && emailChecking && (
                    <p className="text-xs text-muted-foreground mt-1">Verificando...</p>
                  )}
                </FormItem>
              )}
            />

            {/* Telefone */}
            <FormField
              control={control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-700 dark:text-slate-300 flex items-center">
                    <Phone className="w-4 h-4 mr-1" />
                    Telefone
                  </FormLabel>
                  <FormControl>
                    <PhoneInput 
                      field={field}
                      className="border-slate-300 dark:border-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Data de nascimento */}
            <FormField
              control={control}
              name="birth_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-700 dark:text-slate-300">Data de nascimento</FormLabel>
                  <FormControl>
                    <Input 
                      type="date" 
                      {...field}
                      className="border-slate-300 dark:border-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Gênero */}
            <FormField
              control={control}
              name="gender"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-700 dark:text-slate-300">Gênero</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className="border-slate-300 dark:border-slate-600">
                        <SelectValue placeholder="Selecione o gênero" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {genderOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Graduação e Experiência */}
      <Card className="border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <GraduationCap className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Graduação e Experiência
          </h2>
        </div>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Seleção de Modalidade */}
            <div className="space-y-3">
              <FormLabel className="text-slate-700 dark:text-slate-300">Modalidade</FormLabel>
              <Select 
                value={selectedModalityId_local} 
                onValueChange={setSelectedModalityId_local}
                disabled={loadingModalities}
              >
                <SelectTrigger className="border-slate-300 dark:border-slate-600">
                  <SelectValue placeholder={loadingModalities ? "Carregando..." : "Selecione a modalidade"} />
                </SelectTrigger>
                <SelectContent>
                  {modalities.map((modality) => (
                    <SelectItem key={modality.id} value={modality.id}>
                      {modality.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                {loadingModalities ? "Carregando modalidades..." : `${modalities.length} modalidade(s) disponível(is)`}
              </p>
            </div>

            {/* Seleção de Nível de Faixa */}
            <div className="space-y-3">
              <FormLabel className="text-slate-700 dark:text-slate-300">Graduação atual</FormLabel>
              <Select 
                value={selectedBeltLevelId} 
                onValueChange={handleBeltLevelChange}
                disabled={!selectedModalityId_local || loadingBeltLevels || beltLevels.length === 0}
              >
                <SelectTrigger className="border-slate-300 dark:border-slate-600">
                  <SelectValue placeholder={
                    !selectedModalityId_local 
                      ? "Selecione uma modalidade primeiro" 
                      : loadingBeltLevels 
                        ? "Carregando níveis..."
                        : beltLevels.length === 0
                          ? "Nenhum nível disponível"
                          : "Selecione a graduação"
                  } />
                </SelectTrigger>
                <SelectContent>
                  {beltLevels.map((level) => (
                    <SelectItem key={level.id} value={level.id}>
                      <div className="flex items-center gap-3">
                        <BeltWithDetails
                          color={level.belt_color}
                          degree={level.degree}
                          stripeColor={level.stripe_color}
                          showCenterLine={level.show_center_line}
                          centerLineColor={level.center_line_color}
                          size="sm"
                          className="w-8 h-2"
                        />
                        <span>{level.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                {loadingBeltLevels ? "Carregando níveis..." : `${beltLevels.length} nível(is) disponível(is)`}
              </p>
            </div>

            {/* Anos de experiência */}
            <FormField
              control={control}
              name="experience_years"
              render={({ field }) => (
                <FormItem className="md:col-span-2">
                  <FormLabel className="text-slate-700 dark:text-slate-300">Anos de experiência</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      min="0" 
                      placeholder="0"
                      value={field.value ?? ''}
                      onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                      className="border-slate-300 dark:border-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Preview da Graduação Selecionada */}
          {selectedBeltLevelData && (
            <div className="mt-6 p-4 bg-primary/5 border border-primary/20 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <BeltWithDetails
                    color={selectedBeltLevelData.belt_color}
                    degree={selectedBeltLevelData.degree}
                    stripeColor={selectedBeltLevelData.stripe_color}
                    showCenterLine={selectedBeltLevelData.show_center_line}
                    centerLineColor={selectedBeltLevelData.center_line_color}
                    size="md"
                    className="w-16 h-4"
                  />
                  <span className="text-sm font-medium text-primary">
                    Graduação: {selectedBeltLevelData.label}
                  </span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Seleção de Filial */}
      <Card className="border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <Building className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Filial
          </h2>
        </div>
        <CardContent className="p-6">
          {branchError ? (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {branchError}
              </AlertDescription>
            </Alert>
          ) : null}
          
          <FormField
            control={control}
            name="branch_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-slate-700 dark:text-slate-300 flex items-center">
                  <Building className="w-4 h-4 mr-1" />
                  Filial *
                </FormLabel>
                <FormControl>
                  <Select 
                    onValueChange={field.onChange} 
                    value={field.value}
                    disabled={isLoadingBranches || branches.length === 0}
                  >
                    <SelectTrigger className="border-slate-300 dark:border-slate-600">
                      <SelectValue 
                        placeholder={
                          isLoadingBranches 
                            ? "Carregando filiais..." 
                            : branches.length === 0 
                              ? "Nenhuma filial disponível"
                              : "Selecione uma filial"
                        } 
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {branches.map((branch) => (
                        <SelectItem key={branch.id} value={branch.id}>
                          {branch.name || `Filial ${branch.id}`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <p className="text-xs text-muted-foreground mt-1">
                  {isLoadingBranches ? "Carregando filiais..." : `${branches.length} filial(is) disponível(is)`}
                </p>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Modal para crop de imagem */}
      <AvatarCropModal
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        imageUrl={tempPreviewUrl || ''}
        onCroppedImageChange={handleCroppedImageChange}
      />
    </div>
  );
} 