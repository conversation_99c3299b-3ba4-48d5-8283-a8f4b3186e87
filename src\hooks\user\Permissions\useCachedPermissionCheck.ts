'use client';

import { useQuery } from '@tanstack/react-query';
import { useUserMetadata } from '@/hooks/user/Auth';
import { ActionType, PermissionCheckResult, ResourceType } from '@/services/permissions/types/permission-types';
import { useBasicPermissionContext } from './usePermissionContext';
import { CACHE_KEYS } from '@/constants/cache-keys';

export function useCachedPermissionCheck(
  resource: ResourceType,
  action: ActionType,
  targetUserId?: string
) {
  const { metadata } = useUserMetadata();
  const currentUserId = metadata?.id;
  const { permissionContext, isLoading: isLoadingContext } = useBasicPermissionContext();

  const {
    data: permissionCheck,
    isLoading,
    error
  } = useQuery({
    queryKey: CACHE_KEYS.PERMISSION_CHECK(resource, action, currentUserId, targetUserId),
    queryFn: async (): Promise<PermissionCheckResult> => {
      if (!currentUserId) {
        return { granted: false, reason: 'Usuário não autenticado' };
      }
      
      if (!permissionContext) {
        return { granted: false, reason: 'Contexto de permissão não disponível' };
      }
      
      try {
        // Importar dinamicamente para evitar erros com Server Components
        const { getPermissionClientService } = await import('@/services/permissions/client-service');
        const permissionService = getPermissionClientService();
        
        return await permissionService.hasPermission(
          currentUserId,
          resource,
          action,
          targetUserId
        );
      } catch (err) {
        console.error('Erro ao verificar permissão:', err);
        return { granted: false, reason: 'Erro ao verificar permissão' };
      }
    },
    enabled: !!currentUserId && !!permissionContext && !isLoadingContext,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
  });

  return {
    permissionCheck: permissionCheck || { granted: false },
    isAllowed: permissionCheck?.granted || false,
    isLoading: isLoading || isLoadingContext,
    error
  };
} 