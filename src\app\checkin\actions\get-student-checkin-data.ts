'use server';

import { z } from 'zod';

import { createTenantServerClient } from '@/services/supabase/server';
import { getTenantSlug } from '@/services/tenant/server';
import type { StudentCheckInData } from './search-student-by-code';

interface Payload {
  studentId: string;
}

const schema = z.object({
  studentId: z.string().uuid('ID inválido'),
});

export async function getStudentCheckInData(
  payload: unknown,
): Promise<
  | { success: true; data: StudentCheckInData }
  | { success: false; error: string }
> {
  const parsed = schema.safeParse(payload);
  if (!parsed.success) {
    return { success: false, error: 'Parâmetros inválidos' };
  }

  const { studentId } = parsed.data;

  try {
    console.log('Iniciando busca de dados de check-in do aluno:', studentId);
    
    const supabase = await createTenantServerClient();
    console.log('Cliente Supabase criado com sucesso');
    
    const tenantSlug = await getTenantSlug();
    console.log('Tenant slug obtido:', tenantSlug);

    if (!tenantSlug) {
      console.error('Tenant slug não encontrado');
      return { success: false, error: 'Tenant não encontrado' };
    }

    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .select('id')
      .eq('slug', tenantSlug)
      .single();

    if (tenantError) {
      console.error('Erro ao buscar tenant:', tenantError);
      return { success: false, error: 'Erro ao buscar configurações do tenant' };
    }

    if (!tenant) {
      console.error('Tenant não encontrado para slug:', tenantSlug);
      return { success: false, error: 'Tenant inválido' };
    }

    console.log('Tenant encontrado:', tenant.id);

    // Buscar aluno pelo ID
    const { data: student, error: studentError } = await supabase
      .from('students')
      .select(
        `id,
         check_in_code,
         users:users!students_user_id_fkey!inner(full_name, email, avatar_url),
         subscriptions(status, current_period_end)`
      )
      .eq('tenant_id', tenant.id)
      .eq('id', studentId)
      .single();

    if (studentError || !student) {
      console.error('Erro ao buscar aluno ou aluno não encontrado:', studentError);
      return { success: false, error: 'Aluno não encontrado' };
    }

    console.log('Aluno encontrado:', student.id);

    const user = student.users || {};
    const subs = (student.subscriptions || []) as Array<{
      status: string;
      current_period_end: string;
    }>;
    const activeSub = subs.find((s) => s.status === 'active');

    // ----------------------------------------------------------------
    // Buscar aulas disponíveis HOJE para o aluno (turmas dele ou livres)
    // ----------------------------------------------------------------
    const now = new Date();
    const dayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
    const dayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);

    // Buscar turmas em que o aluno está matriculado
    const { data: enrollments, error: enrollErr } = await supabase
      .from('class_group_enrollments')
      .select('class_group_id')
      .eq('tenant_id', tenant.id)
      .eq('student_id', student.id)
      .eq('status', 'active');

    if (enrollErr) {
      console.error('Erro ao buscar matrículas do aluno:', enrollErr);
    }

    const enrolledGroupIds = (enrollments ?? []).map((e: any) => e.class_group_id);

    const { data: classesToday, error: classesError } = await supabase
      .from('classes')
      .select(
        `id,
         name,
         start_time,
         end_time,
         status,
         max_capacity,
         class_group_id,
         instructor:users!instructor_id(full_name),
         branch:branches!inner(name)`
      )
      .eq('tenant_id', tenant.id)
      .gte('start_time', dayStart.toISOString())
      .lte('start_time', dayEnd.toISOString())
      .in('status', ['scheduled', 'ongoing'])
      .order('start_time', { ascending: true });

    if (classesError) {
      console.error('Erro ao buscar aulas de hoje:', classesError);
    }

    // Filtrar pelas turmas do aluno ou classes livres
    const filteredClasses = (classesToday || []).filter((cls: any) => {
      return cls.class_group_id === null || enrolledGroupIds.includes(cls.class_group_id);
    });

    // Contagem de presença por aula
    const attendanceCountsMap: Record<string, number> = {};
    if (filteredClasses && filteredClasses.length > 0) {
      await Promise.all(
        filteredClasses.map(async (cls: any) => {
          const { count } = await supabase
            .from('attendance')
            .select('*', { count: 'exact', head: true })
            .eq('tenant_id', tenant.id)
            .eq('class_id', cls.id)
            .gte('checked_in_at', dayStart.toISOString())
            .lte('checked_in_at', dayEnd.toISOString());
          attendanceCountsMap[cls.id] = count || 0;
        }),
      );
    }

    const formatTime = (dateString: string) => {
      const date = new Date(dateString);
      return date.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit',
      });
    };

    const availableClasses = filteredClasses.map((cls: any) => {
      let status: 'upcoming' | 'in-progress' | 'ended' = 'upcoming';
      if (cls.status === 'ongoing') status = 'in-progress';
      if (cls.status === 'completed' || cls.status === 'cancelled') status = 'ended';

      return {
        id: cls.id,
        name: cls.name,
        instructor: cls.instructor?.full_name ?? 'Instrutor',
        time: `${formatTime(cls.start_time)} - ${formatTime(cls.end_time)}`,
        location: cls.branch?.name ?? 'Academia',
        capacity: cls.max_capacity ?? 0,
        enrolled: attendanceCountsMap[cls.id] || 0,
        status,
      };
    });

    const formatted: StudentCheckInData = {
      student: {
        id: student.id,
        name: user.full_name ?? 'Aluno',
        email: user.email ?? undefined,
        avatar: user.avatar_url ?? undefined,
        code: student.check_in_code ?? undefined,
        membership: activeSub
          ? {
              type: 'Plano ativo',
              status: 'active',
              expiresAt: activeSub.current_period_end,
            }
          : {
              type: 'Sem plano',
              status: 'inactive',
              expiresAt: undefined,
            },
      },
      availableClasses,
    };

    console.log('Busca de dados de check-in concluída com sucesso');
    return { success: true, data: formatted };
  } catch (err) {
    console.error('Erro inesperado ao buscar dados do aluno:', err);
    
    // Verificar se é um erro de configuração do Supabase
    if (err instanceof Error && err.message.includes('Configuração do Supabase não encontrada')) {
      return { 
        success: false, 
        error: 'Erro de configuração do sistema. Entre em contato com o suporte.' 
      };
    }
    
    // Verificar se é um erro de rede ou conectividade
    if (err instanceof Error && (err.message.includes('fetch') || err.message.includes('network'))) {
      return { 
        success: false, 
        error: 'Erro de conectividade. Verifique sua conexão e tente novamente.' 
      };
    }
    
    return { success: false, error: 'Erro inesperado' };
  }
} 