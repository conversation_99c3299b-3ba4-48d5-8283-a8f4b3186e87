import { redirect } from 'next/navigation';
import { getRedirectPathAfterLogin } from '../services/user/role-service';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';

export default async function RootPage() {
  // Usar server action para verificar autenticação sem expor API key
  const user = await getCurrentUser();
  
  if (user) {
    const redirectPath = await getRedirectPathAfterLogin(user.id);
    redirect(redirectPath);
  }
  
  redirect('/login');
}
