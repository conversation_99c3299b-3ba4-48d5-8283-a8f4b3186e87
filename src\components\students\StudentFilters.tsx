'use client';

import { useState, useTransition, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  Search, 
  X, 
  Filter, 
  SortAsc, 
  SortDesc, 
  Settings2,
  Calendar,
  Award,
  Keyboard
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface StudentFiltersProps {
  stats: {
    total: number;
    active: number;
    inactive: number;
    suspended: number;
    paused?: number;
  };
}

type StatusFilter = 'all' | 'active' | 'inactive' | 'suspended' | 'paused';
type SortField = 'name' | 'enrollment_date' | 'belt' | 'status';
type SortOrder = 'asc' | 'desc';

// Hook for debounced search
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

export function StudentFilters({ stats }: StudentFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();

  const currentStatus = (searchParams.get('status') as StatusFilter) || 'all';
  const currentSearch = searchParams.get('search') || '';
  const currentSort = (searchParams.get('sort') as SortField) || 'name';
  const currentOrder = (searchParams.get('order') as SortOrder) || 'asc';

  const [searchValue, setSearchValue] = useState(currentSearch);
  const [showAdvanced, setShowAdvanced] = useState(false);
  
  // Debounced search with 500ms delay
  const debouncedSearch = useDebounce(searchValue, 500);

  const statusOptions = [
    { 
      value: 'all', 
      label: 'Todos', 
      count: stats.total,
      color: 'bg-muted text-muted-foreground',
      activeColor: 'bg-blue-600 text-white border-blue-600 dark:bg-blue-500 dark:border-blue-500'
    },
    { 
      value: 'active', 
      label: 'Ativos', 
      count: stats.active,
      color: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/40 dark:text-green-200 dark:border-green-700',
      activeColor: 'bg-green-600 text-white border-green-600 dark:bg-green-500 dark:border-green-500'
    },
    ...(stats.paused && stats.paused > 0 ? [{ 
      value: 'paused', 
      label: 'Pausados', 
      count: stats.paused,
      color: 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/40 dark:text-amber-200 dark:border-amber-700',
      activeColor: 'bg-amber-600 text-white border-amber-600 dark:bg-amber-500 dark:border-amber-500'
    }] : []),
    ...(stats.inactive > 0 ? [{ 
      value: 'inactive', 
      label: 'Inativos', 
      count: stats.inactive,
      color: 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-800/60 dark:text-gray-200 dark:border-gray-600',
      activeColor: 'bg-gray-600 text-white border-gray-600 dark:bg-gray-500 dark:border-gray-500'
    }] : []),
    ...(stats.suspended > 0 ? [{ 
      value: 'suspended', 
      label: 'Suspensos', 
      count: stats.suspended,
      color: 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/40 dark:text-red-200 dark:border-red-700',
      activeColor: 'bg-red-600 text-white border-red-600 dark:bg-red-500 dark:border-red-500'
    }] : []),
  ];

  const sortOptions = [
    { value: 'name', label: 'Nome', icon: null },
    { value: 'enrollment_date', label: 'Data de Matrícula', icon: Calendar },
    { value: 'belt', label: 'Graduação', icon: Award },
    { value: 'status', label: 'Status', icon: null },
  ];

  // Auto-apply debounced search
  useEffect(() => {
    if (debouncedSearch !== currentSearch) {
      updateFilters({ search: debouncedSearch || null });
    }
  }, [debouncedSearch]);

  const updateFilters = useCallback((updates: Record<string, string | null>) => {
    const params = new URLSearchParams(searchParams);
    
    Object.entries(updates).forEach(([key, value]) => {
      if (value && value !== 'all') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    
    startTransition(() => {
      router.push(`?${params.toString()}`, { scroll: false });
    });
  }, [searchParams, router]);

  const handleStatusChange = (status: StatusFilter) => {
    updateFilters({ status: status === 'all' ? null : status });
  };

  const handleSortChange = (sort: SortField) => {
    updateFilters({ sort });
  };

  const handleOrderToggle = () => {
    updateFilters({ order: currentOrder === 'asc' ? 'desc' : 'asc' });
  };

  const clearFilters = () => {
    setSearchValue('');
    startTransition(() => {
      router.push(window.location.pathname, { scroll: false });
    });
  };

  const clearSearch = () => {
    setSearchValue('');
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && (searchValue || currentStatus !== 'all')) {
        clearFilters();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [searchValue, currentStatus]);

  const hasActiveFilters = currentStatus !== 'all' || currentSearch || currentSort !== 'name' || currentOrder !== 'asc';
  const isSearching = isPending && debouncedSearch !== currentSearch;

  return (
    <div className="bg-card border rounded-lg overflow-hidden" role="search" aria-label="Filtros e busca de alunos">
      {/* Search Bar - Always Visible */}
      <div className="p-3 sm:p-4 pb-0">
        <div className="relative">
          <label htmlFor="student-search" className="sr-only">
            Buscar alunos por nome, email ou telefone
          </label>
          <Search className={cn(
            "absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors",
            isSearching ? "text-primary animate-pulse" : "text-muted-foreground"
          )} aria-hidden="true" />
          <Input
            id="student-search"
            type="search"
            placeholder="Buscar por nome, email ou telefone... (Esc para limpar)"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            className="pl-10 pr-12 h-10 sm:h-11 bg-background/50 border-muted-foreground/20 focus:border-primary/50 transition-all duration-200"
            aria-describedby="search-help"
            autoComplete="off"
          />
          <div id="search-help" className="sr-only">
            A busca é realizada automaticamente após 500ms. Use Escape para limpar todos os filtros.
          </div>
          {searchValue && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 hover:bg-muted/80 transition-colors"
              onClick={clearSearch}
              aria-label="Limpar busca"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
          {isSearching && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
            </div>
          )}
        </div>
      </div>

      {/* Filters Section */}
      <div className="p-4 space-y-4">
        {/* Status Filter Pills */}
        <div className="flex items-center gap-2 flex-wrap" role="group" aria-label="Filtros por status">
          <span className="text-sm font-medium text-muted-foreground mr-1 hidden sm:inline" id="status-filter-label">
            Status:
          </span>
          {statusOptions.map((option) => {
            const isActive = currentStatus === option.value;
            return (
              <Button
                key={option.value}
                variant="outline"
                size="sm"
                onClick={() => handleStatusChange(option.value as StatusFilter)}
                disabled={isPending}
                className={cn(
                  "h-8 border transition-all duration-200 hover:scale-105 focus:ring-2 focus:ring-ring focus:ring-offset-2",
                  isActive 
                    ? option.activeColor 
                    : `${option.color} hover:bg-opacity-80`
                )}
                aria-pressed={isActive}
                aria-label={`Filtrar por status ${option.label.toLowerCase()}, ${option.count} ${option.count === 1 ? 'aluno' : 'alunos'}`}
                aria-describedby="status-filter-label"
              >
                {option.label}
                <Badge 
                  variant="secondary" 
                  className={cn(
                    "ml-2 h-4 text-xs font-medium min-w-[20px] justify-center",
                    isActive ? "bg-black/20 text-white" : "bg-black/10"
                  )}
                  aria-hidden="true"
                >
                  {option.count}
                </Badge>
              </Button>
            );
          })}
        </div>

        {/* Sort Controls & Advanced Filters */}
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
          {/* Sort Controls */}
          <div className="flex items-center gap-2" role="group" aria-label="Controles de ordenação">
            <span className="text-sm font-medium text-muted-foreground" id="sort-controls-label">
              Ordenar:
            </span>
            <Select
              value={currentSort}
              onValueChange={handleSortChange}
              disabled={isPending}
              aria-label="Campo de ordenação"
              aria-describedby="sort-controls-label"
            >
              <SelectTrigger className="w-32 sm:w-40 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {sortOptions.map((option) => {
                  const Icon = option.icon;
                  return (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        {Icon && <Icon className="h-3 w-3" aria-hidden="true" />}
                        {option.label}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="sm"
              onClick={handleOrderToggle}
              disabled={isPending}
              className="h-8 w-8 p-0 transition-all duration-200 hover:scale-105 focus:ring-2 focus:ring-ring focus:ring-offset-2"
              aria-label={`Alterar para ordenação ${currentOrder === 'asc' ? 'decrescente' : 'crescente'}`}
              title={`Ordenar ${currentOrder === 'asc' ? 'decrescente' : 'crescente'}`}
            >
              {currentOrder === 'asc' ? (
                <SortAsc className="h-3 w-3" aria-hidden="true" />
              ) : (
                <SortDesc className="h-3 w-3" aria-hidden="true" />
              )}
            </Button>
          </div>

          {/* Advanced Filters & Actions */}
          <div className="flex items-center gap-2">
            {/* Advanced Filters Popover */}
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline" 
                  size="sm"
                  className="h-8 text-muted-foreground hover:text-foreground transition-colors"
                >
                  <Settings2 className="h-3 w-3 mr-1" />
                  Avançado
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80" align="end">
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Settings2 className="h-4 w-4" />
                    <h4 className="font-medium">Filtros Avançados</h4>
                  </div>
                  <Separator />
                  
                  {/* Belt Level Filter - Future implementation */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium flex items-center gap-2">
                      <Award className="h-3 w-3" />
                      Graduação
                    </Label>
                    <Select disabled>
                      <SelectTrigger className="h-8">
                        <SelectValue placeholder="Todas as graduações" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todas as graduações</SelectItem>
                        {/* Future: Dynamic belt options */}
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">
                      Em breve: filtrar por graduação específica
                    </p>
                  </div>

                  {/* Enrollment Date Filter - Future implementation */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium flex items-center gap-2">
                      <Calendar className="h-3 w-3" />
                      Data de Matrícula
                    </Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input 
                        type="date" 
                        placeholder="De"
                        className="h-8"
                        disabled
                      />
                      <Input 
                        type="date" 
                        placeholder="Até"
                        className="h-8"
                        disabled
                      />
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Em breve: filtrar por período de matrícula
                    </p>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            {/* Keyboard Shortcuts Info */}
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground transition-colors"
                  title="Atalhos do teclado"
                >
                  <Keyboard className="h-3 w-3" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-64" align="end">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Keyboard className="h-4 w-4" />
                    <h4 className="font-medium">Atalhos</h4>
                  </div>
                  <Separator />
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Limpar filtros</span>
                      <Badge variant="outline" className="text-xs">Esc</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Buscar automática</span>
                      <Badge variant="outline" className="text-xs">500ms</Badge>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div className="pt-3 border-t bg-muted/30 -mx-4 px-4 pb-4 mt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 flex-wrap">
                <span className="text-sm text-muted-foreground font-medium">
                  Filtros ativos:
                </span>
                {currentStatus !== 'all' && (
                  <Badge 
                    variant="secondary" 
                    className="capitalize bg-primary/10 text-primary border-primary/20"
                  >
                    {statusOptions.find(s => s.value === currentStatus)?.label}
                  </Badge>
                )}
                {currentSearch && (
                  <Badge 
                    variant="secondary"
                    className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/40 dark:text-blue-200 dark:border-blue-600"
                  >
                    <Search className="h-3 w-3 mr-1" />
                    "{currentSearch}"
                  </Badge>
                )}
                {(currentSort !== 'name' || currentOrder !== 'asc') && (
                  <Badge 
                    variant="secondary"
                    className="bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/40 dark:text-purple-200 dark:border-purple-600"
                  >
                    {sortOptions.find(s => s.value === currentSort)?.label} 
                    {currentOrder === 'desc' ? ' ↓' : ' ↑'}
                  </Badge>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                disabled={isPending}
                className="text-muted-foreground hover:text-foreground h-7 transition-colors"
              >
                <X className="h-3 w-3 mr-1" />
                Limpar
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 