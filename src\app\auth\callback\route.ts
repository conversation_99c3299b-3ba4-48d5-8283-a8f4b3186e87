import { createClient } from "@/services/supabase/server";
import { NextResponse } from "next/server";
import { getCurrentUser } from "@/services/auth/actions/auth-actions";

/**
 * Rota de callback para autenticação com Supabase
 * Processa o código de autenticação e troca por uma sessão válida
 * @param request Objeto de requisição
 * @returns Redirecionamento para a página apropriada
 */
export async function GET(request: Request) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get("code");
  const origin = requestUrl.origin;
  const redirectTo = requestUrl.searchParams.get("redirect_to")?.toString();

  if (!code) {
    return NextResponse.redirect(`${origin}/login?error=invalid_code`);
  }

  const supabase = await createClient();
  const { error } = await supabase.auth.exchangeCodeForSession(code);
  
  if (error) {
    return NextResponse.redirect(`${origin}/login?error=${encodeURIComponent(error.message)}`);
  }

  const user = await getCurrentUser();
  
  if (!user) {
    return NextResponse.redirect(`${origin}/login?error=authentication_failed`);
  }

  if (redirectTo) {
    return NextResponse.redirect(`${origin}${redirectTo}`);
  }

  return NextResponse.redirect(`${origin}/home`);
}
