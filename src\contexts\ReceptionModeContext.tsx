'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User } from '@supabase/supabase-js';

interface ReceptionModeState {
  isActive: boolean;
  activatedBy: {
    id: string;
    email: string;
    name?: string;
  } | null;
  activatedAt: string | null;
}

interface ReceptionModeContextType {
  state: ReceptionModeState;
  activateMode: (user: User) => void;
  deactivateMode: () => void;
  isReceptionModeActive: boolean;
}

const ReceptionModeContext = createContext<ReceptionModeContextType | null>(null);

const STORAGE_KEY = 'apexsaas_reception_mode';
const COOKIE_KEY = 'apexsaas_reception_mode';

// Funções utilitárias para gerenciar cookies
function setCookie(name: string, value: string, days: number = 7) {
  const expires = new Date(Date.now() + days * 24 * 60 * 60 * 1000).toUTCString();
  document.cookie = `${name}=${value}; expires=${expires}; path=/; SameSite=Strict`;
}

function deleteCookie(name: string) {
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
}

export function ReceptionModeProvider({ children }: { children: ReactNode }) {
  const [state, setState] = useState<ReceptionModeState>({
    isActive: false,
    activatedBy: null,
    activatedAt: null,
  });

  // Carregar estado do localStorage na inicialização
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsedState = JSON.parse(stored);
        if (parsedState.isActive) {
          // Garante que o cookie seja definido se o localStorage existir, mas o cookie não
          setCookie(COOKIE_KEY, stored);
        }
        setState(parsedState);
      }
    } catch (error) {
      console.error('Erro ao carregar estado do modo de recepção:', error);
      // Se houver erro, limpar o localStorage e o cookie
      localStorage.removeItem(STORAGE_KEY);
      deleteCookie(COOKIE_KEY);
    }
  }, []);

  const activateMode = (user: User) => {
    const newState: ReceptionModeState = {
      isActive: true,
      activatedBy: {
        id: user.id,
        email: user.email || '',
        name: user.user_metadata?.name || user.user_metadata?.full_name || user.email,
      },
      activatedAt: new Date().toISOString(),
    };
    
    try {
      const stateJson = JSON.stringify(newState);
      // Definir o estado de forma síncrona para evitar race conditions
      localStorage.setItem(STORAGE_KEY, stateJson);
      setCookie(COOKIE_KEY, stateJson);
      setState(newState);
    } catch (error) {
      console.error('Erro ao ativar modo de recepção:', error);
    }
  };

  const deactivateMode = () => {
    try {
      // Limpar o estado de forma síncrona
      localStorage.removeItem(STORAGE_KEY);
      deleteCookie(COOKIE_KEY);
      setState({
        isActive: false,
        activatedBy: null,
        activatedAt: null,
      });
    } catch (error) {
      console.error('Erro ao desativar modo de recepção:', error);
    }
  };

  const contextValue: ReceptionModeContextType = {
    state,
    activateMode,
    deactivateMode,
    isReceptionModeActive: state.isActive,
  };

  return (
    <ReceptionModeContext.Provider value={contextValue}>
      {children}
    </ReceptionModeContext.Provider>
  );
}

export function useReceptionMode() {
  const context = useContext(ReceptionModeContext);
  if (!context) {
    throw new Error('useReceptionMode deve ser usado dentro de um ReceptionModeProvider');
  }
  return context;
} 