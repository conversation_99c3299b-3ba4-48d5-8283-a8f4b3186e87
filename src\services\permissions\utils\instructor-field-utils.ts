import { ProfileField, EditCondition } from '../types/field-permission-types';
import { UserRole } from '../types/permission-types';

// Campos que instrutores podem editar em seus próprios perfis
export const INSTRUCTOR_SELF_EDITABLE_FIELDS: ProfileField[] = [
  // Informações pessoais básicas
  'fullName',
  'phone',
  'birthDate',
  'gender',
  'avatar',
  
  // Informações de localização
  'address',
  
  // Contatos de emergência
  'emergency_contact',
  'emergency_phone',
  'emergency_contact_relationship',
  
  // Informações profissionais
  'experience_years',
  'bio',
  
  // Certificações
  'cbjj_certified',
  'ibjjf_certified',
  'first_aid_certified',
  'cpr_certified',
  
  // Informações de saúde
  'healthNotes',
  'allergies',
  'medicalConditions',
  'medications',
  
  // Observações
  'notes'
];

// Campos que apenas admin pode editar
export const ADMIN_ONLY_FIELDS: ProfileField[] = [
  'email',
  'specialties',
  'currentBelt',
  'contract_type',
  'payment_model',
  'hourly_rate',
  'monthly_salary',
  'commission_percentage',
  'branch_id'
];

// Campos relacionados a certificações
export const CERTIFICATION_FIELDS: ProfileField[] = [
  'cbjj_certified',
  'ibjjf_certified',
  'first_aid_certified',
  'cpr_certified'
];

// Campos relacionados a informações financeiras
export const FINANCIAL_FIELDS: ProfileField[] = [
  'contract_type',
  'payment_model',
  'hourly_rate',
  'monthly_salary',
  'commission_percentage'
];

// Campos relacionados a informações de saúde
export const HEALTH_FIELDS: ProfileField[] = [
  'healthNotes',
  'allergies',
  'medicalConditions',
  'medications'
];

// Campos relacionados a informações profissionais de Jiu-Jitsu
export const PROFESSIONAL_JJB_FIELDS: ProfileField[] = [
  'currentBelt',
  'experience_years',
  'bio'
];

/**
 * Verifica se um instrutor pode editar um campo específico
 */
export const canInstructorEditField = (
  field: ProfileField,
  targetUserId: string,
  currentUserId: string
): boolean => {
  // Instrutor só pode editar seus próprios campos (exceto admin-only)
  if (targetUserId !== currentUserId) {
    // Exceção: campos de saúde podem ser editados em outros perfis para emergências
    return HEALTH_FIELDS.includes(field) || field === 'notes';
  }
  
  // Não pode editar campos restritos ao admin
  if (ADMIN_ONLY_FIELDS.includes(field)) {
    return false;
  }
  
  // Pode editar seus próprios campos permitidos
  return INSTRUCTOR_SELF_EDITABLE_FIELDS.includes(field);
};

/**
 * Retorna os campos editáveis por categoria para um instrutor
 */
export const getInstructorEditableFieldsByCategory = () => {
  return {
    personalInfo: [
      'fullName',
      'phone',
      'birthDate',
      'gender',
      'avatar'
    ] as ProfileField[],
    
    locationInfo: [
      'address'
    ] as ProfileField[],
    
    emergencyContact: [
      'emergency_contact',
      'emergency_phone',
      'emergency_contact_relationship'
    ] as ProfileField[],
    
    professionalInfo: [
      'experience_years',
      'bio'
    ] as ProfileField[],
    
    certifications: CERTIFICATION_FIELDS,
    
    healthInfo: HEALTH_FIELDS,
    
    notes: [
      'notes'
    ] as ProfileField[]
  };
};

/**
 * Retorna as estatísticas de permissões para instrutores
 */
export const getInstructorPermissionStats = () => {
  const totalFields = [
    ...INSTRUCTOR_SELF_EDITABLE_FIELDS,
    ...ADMIN_ONLY_FIELDS
  ].length;
  
  const editableFields = INSTRUCTOR_SELF_EDITABLE_FIELDS.length;
  const restrictedFields = ADMIN_ONLY_FIELDS.length;
  
  return {
    total: totalFields,
    editable: editableFields,
    restricted: restrictedFields,
    editablePercentage: Math.round((editableFields / totalFields) * 100),
    restrictedPercentage: Math.round((restrictedFields / totalFields) * 100)
  };
};

/**
 * Verifica se um campo é relacionado a informações financeiras
 */
export const isFinancialField = (field: ProfileField): boolean => {
  return FINANCIAL_FIELDS.includes(field);
};

/**
 * Verifica se um campo é uma certificação
 */
export const isCertificationField = (field: ProfileField): boolean => {
  return CERTIFICATION_FIELDS.includes(field);
};

/**
 * Verifica se um campo é relacionado à saúde
 */
export const isHealthField = (field: ProfileField): boolean => {
  return HEALTH_FIELDS.includes(field);
};

/**
 * Verifica se um campo é profissional do Jiu-Jitsu
 */
export const isProfessionalJJBField = (field: ProfileField): boolean => {
  return PROFESSIONAL_JJB_FIELDS.includes(field);
};

/**
 * Retorna a categoria de um campo específico
 */
export const getFieldCategory = (field: ProfileField): string => {
  if (['fullName', 'phone', 'birthDate', 'gender', 'avatar'].includes(field)) {
    return 'Informações Pessoais';
  }
  
  if (field === 'address') {
    return 'Localização';
  }
  
  if (['emergency_contact', 'emergency_phone', 'emergency_contact_relationship'].includes(field)) {
    return 'Contato de Emergência';
  }
  
  if (isProfessionalJJBField(field)) {
    return 'Profissional Jiu-Jitsu';
  }
  
  if (isCertificationField(field)) {
    return 'Certificações';
  }
  
  if (isFinancialField(field)) {
    return 'Financeiro';
  }
  
  if (isHealthField(field)) {
    return 'Saúde';
  }
  
  if (field === 'notes') {
    return 'Observações';
  }
  
  if (field === 'email') {
    return 'Acesso/Login';
  }
  
  if (field === 'branch_id') {
    return 'Organizacional';
  }
  
  return 'Outros';
}; 