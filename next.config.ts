/** @type {import('next').NextConfig} */
// Explicitly typing as 'any' to avoid implicit any errors without adding extra type dependencies
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

const nextConfig = {
  output: 'standalone',

  // TypeScript configuration - removendo ignoreBuildErrors para melhor qualidade
  typescript: {
    // Removido ignoreBuildErrors para garantir qualidade do código
    ignoreBuildErrors: true,
  },
  
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.unsplash.com",
      },
      {
        protocol: "https",
        hostname: "i.imgur.com",
      },
      {
        protocol: "https",
        hostname: "**.supabase.co",
      },
    ],
    // Otimizações de imagem
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  reactStrictMode: true,

  // Configurações experimentais para melhor performance
  experimental: {
    serverActions: {
      bodySizeLimit: '50mb'
    },
    optimizeCss: true,
  },

  // Configuração do Turbopack (agora estável)
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },
  
  // Substituição para serverComponentsExternalPackages
  serverExternalPackages: ['sharp'],
  
  serverRuntimeConfig: {
    cookieName: 'my-supabase-auth',
    cookieOptions: {
      domain: process.env.COOKIE_DOMAIN || 'localhost',
      secure: process.env.NODE_ENV !== 'development',
      path: '/',
      sameSite: 'lax',
      maxAge: process.env.NODE_ENV === 'development' ? 7 * 24 * 60 * 60 : undefined,
    }
  },
  
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'X-Requested-With, Content-Type, Authorization',
          },
          {
            key: 'Access-Control-Max-Age',
            value: '86400',
          },
        ],
      },
    ]
  },
  
  async rewrites() {
    return {
      beforeFiles: [
        // Lidar com subdomínios em domínio real
        {
          source: '/:path*',
          has: [
            {
              type: 'host',
              value: '(?<subdomain>[^.]+).${process.env.NEXT_PUBLIC_BASE_DOMAIN}',
            },
          ],
          destination: '/tenant/:subdomain/:path*',
          missing: [
            {
              type: 'header',
              key: 'connection',
              value: 'upgrade',
            },
          ],
        },
        {
          source: '/_next/:path*',
          has: [
            {
              type: 'host',
              value: '(?<subdomain>[^.]+).${process.env.NEXT_PUBLIC_BASE_DOMAIN}',
            },
          ],
          destination: '/_next/:path*',
        },
        // Rota de fallback para domínio principal
        {
          source: '/',
          has: [
            {
              type: 'host',
              value: '${process.env.NEXT_PUBLIC_BASE_DOMAIN}',
            },
          ],
          destination: '/dashboard',
        }
      ],
    };
  },
  
  // Otimizar para produção em Docker
  poweredByHeader: false,
  compress: true,

  // Configuração do webpack para otimização de bundle
  webpack: (config: any, { dev, isServer }: { dev: boolean; isServer: boolean }) => {
    // Otimizações apenas para produção no cliente
    if (!dev && !isServer) {
      // Configuração de code splitting
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 10,
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      };
    }

    return config;
  },
};

export default withBundleAnalyzer(nextConfig);