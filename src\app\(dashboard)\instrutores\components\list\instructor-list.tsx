'use client';

import { useState } from "react";
import { EmptyState } from "../errors/empty-state";
import { InstructorTable } from "./instructor-table";
import { InstructorCard } from "./instructor-card";
import { Pagination } from "../pagination";
import { useInstructorsQuery } from "../../hooks/use-instructors-query";
import { useInstructorsFilter } from "../../hooks/use-instructors-filter";
import { Instructor } from "../../types/types";
import { AlertCircle, MousePointerClick, Settings2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { deleteMultipleInstructors, updateInstructorsStatus, deleteMultipleInstructorsPermanently } from "../../actions/bulk-actions";

interface InstructorListProps {
  isSelecting: boolean;
  selectedUsers: Instructor[];
  setSelectedUsers: React.Dispatch<React.SetStateAction<Instructor[]>>;
  onCloseSelectionMode: () => void;
  onActivateSelectionMode: () => void;
}

export function InstructorList({ 
  isSelecting = false, 
  selectedUsers = [], 
  setSelectedUsers, 
  onCloseSelectionMode,
  onActivateSelectionMode
}: InstructorListProps) {
  // Usar os hooks personalizados
  const { data, isError, error, refetch } = useInstructorsQuery();
  const { filters, updatePage } = useInstructorsFilter();
  const [showActionsDialog, setShowActionsDialog] = useState(false);
  
  const handleBulkAction = async (action: string, ids: string[]) => {
    try {
      let result;
      
      // Mapeamento de tipos de ação para as animações
      const actionTypeMap: Record<string, 'delete' | 'delete-permanently' | 'inactivate' | 'activate' | 'generic'> = {
        'delete': 'delete',
        'delete-permanently': 'delete-permanently',
        'inactivate': 'inactivate',
        'activate': 'activate'
      };
      
      switch (action) {
        case 'delete':
          result = await deleteMultipleInstructors(ids);
          break;
        case 'delete-permanently':
          // Para deletar permanentemente, precisamos dos user_ids
          const userIds = selectedUsers
            .filter(instructor => ids.includes(instructor.id))
            .map(instructor => instructor.user_id);
          
          if (userIds.length === 0) {
            throw new Error('Nenhum usuário válido encontrado para exclusão');
          }
          
          return await deleteMultipleInstructorsPermanently(userIds);
        case 'inactivate':
          result = await updateInstructorsStatus(ids, false);
          break;
        case 'activate':
          result = await updateInstructorsStatus(ids, true);
          break;
        default:
          throw new Error('Ação não suportada');
      }
      
      if (result.success) {
        toast.success(result.message);
        refetch();
        onCloseSelectionMode();
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      toast.error(err instanceof Error ? err.message : "Ocorreu um erro ao processar a ação");
    }
  };
  
  // Mostrar mensagem de erro
  if (isError) {
    return (
      <div className="rounded-md bg-destructive/15 p-4 flex items-center gap-3">
        <AlertCircle className="h-5 w-5 text-destructive" />
        <div className="text-sm text-destructive">
          Erro ao carregar dados: {error instanceof Error ? error.message : 'Erro desconhecido'}
        </div>
      </div>
    );
  }
  
  if (!data || !data.data) {
    return (
      <div className="rounded-md bg-yellow-100 p-4 flex items-center gap-3">
        <AlertCircle className="h-5 w-5 text-yellow-600" />
        <div className="text-sm text-yellow-700">Nenhum dado encontrado</div>
      </div>
    );
  }
  
  const { data: instructors, pagination } = data;

  // Se não houver resultados, mostrar estado vazio
  if (instructors.length === 0) {
    return <EmptyState />;
  }
  
  return (
    <div className="space-y-4">
      {/* View para desktop */}
      <div className="hidden md:block">
        <InstructorTable 
          instructors={instructors} 
          isSelecting={isSelecting} 
          selectedUsers={selectedUsers as any}
          onSelectUser={(user) => {
            if (isSelecting) {
              setSelectedUsers(prev => {
                const isSelected = prev.find(u => u.id === user.id);
                if (isSelected) {
                  return prev.filter(u => u.id !== user.id);
                }
                return [...prev, user as Instructor];
              });
            }
          }}
          onRefresh={() => refetch()}
        />
      </div>
      
      {/* View para mobile */}
      <div className="md:hidden space-y-4">
        {instructors.map((instructor: Instructor) => (
          <InstructorCard 
            key={instructor.id} 
            instructor={instructor}
            isSelecting={isSelecting}
            isSelected={selectedUsers.some(u => u.id === instructor.id)}
            onSelect={() => {
              if (isSelecting) {
                setSelectedUsers(prev => {
                  const isSelected = prev.find(u => u.id === instructor.id);
                  if (isSelected) {
                    return prev.filter(u => u.id !== instructor.id);
                  }
                  return [...prev, instructor];
                });
              }
            }}
            onRefresh={() => refetch()}
          />
        ))}
      </div>
      
      <div className="flex items-center justify-between">
        {/* Área de botões na parte inferior esquerda */}
        <div className="flex items-center gap-2">
          <Button 
            variant={isSelecting ? "default" : "secondary"}
            size="sm"
            className="flex items-center gap-2"
            onClick={isSelecting ? onCloseSelectionMode : onActivateSelectionMode}
          >
            <MousePointerClick className="h-4 w-4" />
            {isSelecting ? "Cancelar seleção" : "Modo de seleção"}
          </Button>

          {/* Botão de Ações - Exibido apenas quando há instrutores selecionados */}
          {isSelecting && selectedUsers.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowActionsDialog(true)}
              className="flex items-center gap-2"
            >
              <Settings2 className="h-4 w-4" />
              Ações ({selectedUsers.length})
            </Button>
          )}
        </div>

        {/* Paginação */}
        {pagination && pagination.total_pages > 1 && (
          <Pagination
            currentPage={pagination.current_page}
            totalPages={pagination.total_pages}
            totalItems={pagination.total}
            itemsPerPage={(pagination as any).per_page || 10}
            onPageChange={updatePage}
          />
        )}
      </div>
    </div>
  );
} 