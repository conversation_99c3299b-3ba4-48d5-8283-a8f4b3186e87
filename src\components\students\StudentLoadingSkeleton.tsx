'use client';

import { cn } from '@/lib/utils';

interface StudentLoadingSkeletonProps {
  count?: number;
  showFilters?: boolean;
  showHeader?: boolean;
  className?: string;
}

export function StudentLoadingSkeleton({ 
  count = 6, 
  showFilters = true, 
  showHeader = true,
  className 
}: StudentLoadingSkeletonProps) {
  return (
    <div className={cn("min-h-screen bg-background", className)}>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="space-y-8">
          {/* Header Skeleton */}
          {showHeader && (
            <div className="space-y-6">
              {/* Breadcrumb skeleton */}
              <div className="flex items-center space-x-2">
                <SkeletonBox className="h-4 w-12" />
                <span className="text-muted-foreground">/</span>
                <SkeletonBox className="h-4 w-24" />
                <span className="text-muted-foreground">/</span>
                <SkeletonBox className="h-4 w-16" />
              </div>
              
              {/* Title and stats skeleton */}
              <div className="flex flex-col space-y-6 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
                <div className="space-y-4">
                  <SkeletonBox className="h-8 w-64" />
                  <div className="flex items-center gap-4">
                    {Array.from({ length: 4 }).map((_, i) => (
                      <div key={i} className="flex items-center gap-2">
                        <SkeletonBox className="w-8 h-8 rounded-full" />
                        <div className="space-y-1">
                          <SkeletonBox className="h-4 w-8" />
                          <SkeletonBox className="h-3 w-12" />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Action buttons skeleton */}
                <div className="flex items-center gap-3">
                  <SkeletonBox className="h-10 w-20" />
                  <SkeletonBox className="h-10 w-32" />
                </div>
              </div>
            </div>
          )}

          {/* Filters Skeleton */}
          {showFilters && (
            <div className="bg-card border rounded-lg overflow-hidden">
              {/* Search bar skeleton */}
              <div className="p-4 pb-0">
                <SkeletonBox className="h-11 w-full" />
              </div>
              
              {/* Filter controls skeleton */}
              <div className="p-4 space-y-4">
                {/* Status filters */}
                <div className="flex items-center gap-2 flex-wrap">
                  <SkeletonBox className="h-4 w-12" />
                  {Array.from({ length: 3 }).map((_, i) => (
                    <SkeletonBox key={i} className="h-8 w-20" />
                  ))}
                </div>
                
                {/* Sort controls */}
                <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
                  <div className="flex items-center gap-2">
                    <SkeletonBox className="h-4 w-16" />
                    <SkeletonBox className="h-8 w-40" />
                    <SkeletonBox className="h-8 w-8" />
                  </div>
                  <div className="flex items-center gap-2">
                    <SkeletonBox className="h-8 w-24" />
                    <SkeletonBox className="h-8 w-8" />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Content Skeleton */}
          <div className="bg-card border rounded-lg p-6">
            <div className="space-y-4">
              {Array.from({ length: count }).map((_, i) => (
                <StudentCardSkeleton key={i} delay={i * 100} />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Individual student card skeleton
function StudentCardSkeleton({ delay = 0 }: { delay?: number }) {
  return (
    <div 
      className="border rounded-xl p-6 bg-card"
      style={{ animationDelay: `${delay}ms` }}
    >
      <div className="flex items-center justify-between">
        {/* Left side - Avatar and info */}
        <div className="flex items-center space-x-4">
          <SkeletonBox className="w-12 h-12 rounded-full" />
          <div className="space-y-2">
            <SkeletonBox className="h-4 w-32" />
            <SkeletonBox className="h-3 w-48" />
            <div className="flex items-center gap-2">
              <SkeletonBox className="h-3 w-16" />
              <SkeletonBox className="h-5 w-12 rounded-full" />
            </div>
          </div>
        </div>

        {/* Right side - Status and actions */}
        <div className="flex items-center gap-3">
          <SkeletonBox className="h-6 w-16 rounded-full" />
          <SkeletonBox className="h-8 w-8 rounded" />
        </div>
      </div>
    </div>
  );
}

// Reusable skeleton box with enhanced animation
function SkeletonBox({ className, ...props }: { className?: string } & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        "bg-gradient-to-r from-muted via-muted/50 to-muted",
        "animate-pulse bg-[length:200%_100%] bg-[position:-200%_0%]",
        "rounded",
        className
      )}
      style={{
        animation: 'shimmer 2s ease-in-out infinite',
      }}
      {...props}
    >
      <style jsx>{`
        @keyframes shimmer {
          0% {
            background-position: -200% 0%;
          }
          100% {
            background-position: 200% 0%;
          }
        }
      `}</style>
    </div>
  );
}

// Compact version for smaller areas
export function StudentListSkeleton({ count = 3 }: { count?: number }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, i) => (
        <StudentCardSkeleton key={i} delay={i * 50} />
      ))}
    </div>
  );
}

// Minimal skeleton for quick loading states
export function StudentMiniSkeleton() {
  return (
    <div className="flex items-center space-x-3 p-3">
      <SkeletonBox className="w-8 h-8 rounded-full" />
      <div className="space-y-1 flex-1">
        <SkeletonBox className="h-3 w-24" />
        <SkeletonBox className="h-2 w-16" />
      </div>
      <SkeletonBox className="h-6 w-12 rounded-full" />
    </div>
  );
} 