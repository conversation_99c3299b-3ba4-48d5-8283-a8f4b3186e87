export * from './types/permission-types';

export { getPermissionService } from './service';
export { getPermissionClientService } from './client-service';

export { PermissionGate, PermissionButton } from './components/permission-gate';

export { usePermission } from './hooks/use-permission';
export { useFieldPermission } from './hooks/useFieldPermission';

export { policyMap } from './policies/default-policies';
export { profileFieldPermissions, fieldPermissionMap } from './policies/field-permissions';

export { getPermissionContext, getBasicPermissionContext } from './contexts/permission-context';
export { PermissionContextClientResolver } from './contexts/permission-context-client';
export { PermissionRepository } from './repository/permission-repository';
export { PermissionEvaluator } from './actions/permission-evaluator';

export type { 
  UserRole, 
  ResourceType, 
  ActionType, 
  PermissionContext 
} from './types/permission-types';

export type {
  ProfileField,
  EditCondition,
  FieldPermission,
  FieldPermissionContext,
  RoleBasedFieldPermission,
  IFieldPermissionStrategy
} from './types/field-permission-types';

export { DefaultFieldPermissionFactory } from './factories/field-permission-factory';

export { FieldPermissionChecker } from './strategies/field-permission-checker';