import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { 
  syncUserMetadata, 
  forceFullUserMetadataSync,
  UserMetadataSync,
  SyncResult 
} from '@/services/auth/utils/server-utils';
import { getPermissionService } from '@/services/permissions';
import { createTenantServerClient } from '@/services/supabase/server';

// Schema para validação do body da requisição
const syncRequestSchema = z.object({
  action: z.enum(['force_sync', 'sync_fields'], {
    errorMap: () => ({ message: 'Ação deve ser force_sync ou sync_fields' })
  }),
  fields: z.array(z.enum([
    'status', 'role', 'tenant_id', 'branch_id', 
    'first_name', 'last_name', 'avatar_url'
  ] as const)).optional(),
  direction: z.enum(['to-auth', 'from-auth']).default('to-auth').optional()
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;
    
    if (!userId) {
      return NextResponse.json(
        { error: 'ID do usuário é obrigatório' }, 
        { status: 400 }
      );
    }

    // Verificar autenticação e permissões
    const supabase = await createTenantServerClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Não autenticado' }, 
        { status: 401 }
      );
    }

    const permissionService = getPermissionService();
    const permissionResult = await permissionService.hasPermission(
      user.id,
      'user',
      'edit',
      userId
    );

    if (!permissionResult.granted) {
      return NextResponse.json(
        { error: 'Acesso negado' }, 
        { status: 403 }
      );
    }

    // Validar o body da requisição
    const body = await request.json();
    const validationResult = syncRequestSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Dados de entrada inválidos',
          details: validationResult.error.format()
        }, 
        { status: 400 }
      );
    }

    const { action, fields, direction } = validationResult.data;

    let syncResult: SyncResult | undefined;

    if (action === 'force_sync') {
      // Sincronização completa de todos os campos
      console.log(`Executando sincronização completa para usuário ${userId}`);
      syncResult = await forceFullUserMetadataSync(userId);
    } else if (action === 'sync_fields') {
      // Sincronização de campos específicos
      if (!fields || fields.length === 0) {
        return NextResponse.json(
          { error: 'Campos devem ser especificados para sync_fields' }, 
          { status: 400 }
        );
      }
      
      console.log(`Sincronizando campos específicos para usuário ${userId}:`, fields);
      syncResult = await syncUserMetadata(
        userId, 
        fields as (keyof UserMetadataSync)[], 
        direction
      );
    }

    if (!syncResult) {
      return NextResponse.json(
        { error: 'Ação não processada' }, 
        { status: 400 }
      );
    }

    // Log do resultado
    console.log(`Sincronização concluída para usuário ${userId}:`, {
      success: syncResult.success,
      syncedCount: syncResult.synced.length,
      errorsCount: syncResult.errors.length
    });

    return NextResponse.json({
      ...syncResult,
      userId,
      action,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erro ao sincronizar metadados:', error);
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor na sincronização',
        details: error instanceof Error ? error.message : String(error)
      }, 
      { status: 500 }
    );
  }
} 