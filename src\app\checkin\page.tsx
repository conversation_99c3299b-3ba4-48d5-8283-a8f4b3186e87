'use client';

import { useState } from 'react';
import { CheckInHeader } from './components/checkin-header';
import { TodayClassesSidebar } from './components/today-classes-sidebar';
import { CheckInMainCard } from './components/checkin-main-card';
import { RecentCheckInsSidebar } from './components/recent-checkins-sidebar';
import { ExitModal } from './components/exit-modal';
import { StudentsModal } from './components/students-modal';

export default function CheckInReceptionPage() {
  const [showExitModal, setShowExitModal] = useState(false);
  const [showStudentsModal, setShowStudentsModal] = useState(false);
  const [selectedClass, setSelectedClass] = useState<{ id: string; name: string } | null>(null);

  const handleShowStudents = (classId: string, className: string) => {
    setSelectedClass({ id: classId, name: className });
    setShowStudentsModal(true);
  };

  const handleCloseStudentsModal = () => {
    setShowStudentsModal(false);
    setSelectedClass(null);
  };

  return (
    <div className="bg-slate-50 dark:bg-gray-900 min-h-screen">
      <CheckInHeader onExitClick={() => setShowExitModal(true)} />
      
      <div className="py-8 px-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Esquerda - Aulas Hoje */}
          <div className="lg:col-span-1">
            <TodayClassesSidebar 
              onShowStudents={handleShowStudents}
            />
          </div>

          {/* Área Principal - Check-in */}
          <div className="lg:col-span-2">
            <CheckInMainCard />
          </div>

          {/* Sidebar Direita - Check-ins Recentes */}
          <div className="lg:col-span-1">
            <RecentCheckInsSidebar />
          </div>
        </div>
      </div>

      {/* Modais */}
      <ExitModal 
        isOpen={showExitModal}
        onClose={() => setShowExitModal(false)}
      />
      
      <StudentsModal
        isOpen={showStudentsModal}
        onClose={handleCloseStudentsModal}
        classId={selectedClass?.id}
        className={selectedClass?.name}
      />
    </div>
  );
} 