/**
 * Tipos TypeScript para o Dashboard Financeiro
 * Fase 1: Estrutura base com KPIs, filtros e layout responsivo
 */

// ============================================================================
// FILTROS E PERÍODO
// ============================================================================

export type PeriodType = 'month' | 'quarter' | 'semester' | 'year' | 'custom';

export interface DateRange {
  startDate: Date;
  endDate: Date;
  period: PeriodType;
  label: string;
}

export interface DashboardFilters {
  dateRange: DateRange;
  refreshInterval?: number;
}

// ============================================================================
// MÉTRICAS COM CRESCIMENTO
// ============================================================================

export type TrendDirection = 'up' | 'down' | 'stable';

export interface MetricWithGrowth {
  current: number;
  previous: number;
  growth: number;
  trend: TrendDirection;
  formatted: {
    current: string;
    previous: string;
    growth: string;
  };
}

// ============================================================================
// KPIs PRINCIPAIS
// ============================================================================

export interface FinancialKPIs {
  totalRevenue: MetricWithGrowth;
  totalExpenses: MetricWithGrowth;
  netProfit: MetricWithGrowth;
  activeStudents: MetricWithGrowth;
  averageRevenuePerUser: MetricWithGrowth;
  profitMargin: MetricWithGrowth;
}

// ============================================================================
// MÉTRICAS DE RECEITAS
// ============================================================================

export interface FinancialRevenueMetrics {
  totalRevenue: number;
  paidPayments: number;
  pendingPayments: number;
  overduePayments: number;
  averagePayment: number;
  monthlyRecurringRevenue: number;
  revenueGrowth: number;
}

// ============================================================================
// MÉTRICAS DE DESPESAS
// ============================================================================

export interface ExpenseMetrics {
  totalExpenses: number;
  paidExpenses: number;
  pendingExpenses: number;
  overdueExpenses: number;
  averageExpense: number;
  expenseGrowth: number;
  expensesByCategory: ExpenseCategoryData[];
}

export interface ExpenseCategoryData {
  categoryId: string;
  categoryName: string;
  categoryColor: string;
  amount: number;
  count: number;
  percentage: number;
}

// ============================================================================
// MÉTRICAS DE FLUXO DE CAIXA
// ============================================================================

export interface CashFlowMetrics {
  currentBalance: number;
  totalInflows: number;
  totalOutflows: number;
  netCashFlow: number;
  projectedBalance: number;
  cashFlowTrend: TrendDirection;
}

// ============================================================================
// MÉTRICAS DE ALUNOS
// ============================================================================

export interface StudentMetrics {
  totalStudents: number;
  activeStudents: number;
  newStudents: number;
  churnedStudents: number;
  retentionRate: number;
  churnRate: number;
  averageLifetimeValue: number;
}

// ============================================================================
// DADOS CONSOLIDADOS DO DASHBOARD
// ============================================================================

export interface DashboardData {
  kpis: FinancialKPIs;
  revenueMetrics: FinancialRevenueMetrics;
  expenseMetrics: ExpenseMetrics;
  cashFlowMetrics: CashFlowMetrics;
  studentMetrics: StudentMetrics;
  lastUpdated: Date;
}

// ============================================================================
// PROPS DOS COMPONENTES
// ============================================================================

export interface FinancialDashboardProps {
  initialFilters?: DashboardFilters;
  refreshInterval?: number;
  className?: string;
}

export interface DashboardFiltersProps {
  filters: DashboardFilters;
  onFiltersChange: (filters: DashboardFilters) => void;
  loading?: boolean;
  className?: string;
}

export interface DashboardLayoutProps {
  kpis: FinancialKPIs;
  data: DashboardData;
  loading?: boolean;
  error?: string | null;
  className?: string;
}

// ============================================================================
// ESTADOS DE LOADING
// ============================================================================

export interface LoadingStates {
  kpis: boolean;
  revenue: boolean;
  expenses: boolean;
  cashFlow: boolean;
  students: boolean;
  overall: boolean;
}

// ============================================================================
// RESPOSTA DAS ACTIONS
// ============================================================================

export interface DashboardActionResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// ============================================================================
// UTILITÁRIOS DE FORMATAÇÃO
// ============================================================================

export interface FormatOptions {
  currency?: string;
  locale?: string;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
}

// ============================================================================
// FASE 3: TIPOS PARA GRÁFICOS DE RECEITA
// ============================================================================

export interface MonthlyRevenueData {
  month: string;
  revenue: number;
  formattedRevenue: string;
}

export interface ModalityRevenueData {
  modality: string;
  revenue: number;
  count: number;
  percentage: number;
  formattedRevenue: string;
  color: string;
}

export interface PaymentStatusData {
  status: string;
  label: string;
  amount: number;
  count: number;
  percentage: number;
  countPercentage: number;
  formattedAmount: string;
  color: string;
}

export interface PaymentMethodData {
  method: string;
  amount: number;
  count: number;
  percentage: number;
  countPercentage: number;
  formattedAmount: string;
  color: string;
}

// ============================================================================
// FASE 4: TIPOS PARA GRÁFICOS DE DESPESAS
// ============================================================================

export interface MonthlyExpenseData {
  month: string;
  expense: number;
  formattedExpense: string;
}

export interface ExpenseCategoryChartData {
  categoryId: string;
  categoryName: string;
  categoryColor: string;
  amount: number;
  count: number;
  percentage: number;
  formattedAmount: string;
}

export interface ExpenseTypeData {
  type: 'fixed' | 'variable';
  label: string;
  amount: number;
  count: number;
  percentage: number;
  formattedAmount: string;
  color: string;
}

export interface PendingExpenseData {
  id: string;
  supplierName: string;
  amount: number;
  formattedAmount: string;
  dueDate: string;
  formattedDueDate: string;
  daysPastDue: number;
  categoryName: string;
  categoryColor: string;
  description?: string;
  status: 'pending' | 'overdue';
}

// ============================================================================
// FASE 5: TIPOS PARA GRÁFICOS DE FLUXO DE CAIXA
// ============================================================================

export interface MonthlyCashFlowData {
  month: string;
  inflows: number;
  outflows: number;
  netFlow: number;
  formattedInflows: string;
  formattedOutflows: string;
  formattedNetFlow: string;
}

export interface CashFlowProjectionData {
  month: string;
  projectedInflows: number;
  projectedOutflows: number;
  projectedNetFlow: number;
  confidence: number; // 0-100
  formattedProjectedInflows: string;
  formattedProjectedOutflows: string;
  formattedProjectedNetFlow: string;
}

export interface CumulativeBalanceData {
  month: string;
  cumulativeBalance: number;
  formattedBalance: string;
}

export interface SeasonalityData {
  month: string;
  monthName: string;
  averageInflows: number;
  averageOutflows: number;
  averageNetFlow: number;
  formattedAverageInflows: string;
  formattedAverageOutflows: string;
  formattedAverageNetFlow: string;
}

export interface CashFlowAnalysisData {
  monthlyCashFlow: MonthlyCashFlowData[];
  projections: CashFlowProjectionData[];
  cumulativeBalance: CumulativeBalanceData[];
  seasonality: SeasonalityData[];
  currentBalance: number;
  projectedBalance: number;
  cashFlowTrend: TrendDirection;
}

// ============================================================================
// CONFIGURAÇÕES DO DASHBOARD
// ============================================================================

export interface DashboardConfig {
  defaultPeriod: PeriodType;
  refreshInterval: number;
  enableRealTime: boolean;
  enableNotifications: boolean;
  layout: {
    showKPIs: boolean;
    showCharts: boolean;
    showTables: boolean;
  };
}
