import { TenantConfig } from './types';

/**
 * Configuração centralizada para o sistema de tenants
 * Esta configuração é compartilhada entre client e server
 */
export const TENANT_CONFIG: TenantConfig = {
  baseDomain: process.env.NEXT_PUBLIC_BASE_DOMAIN || 'localhost:3000',
  reservedPaths: ['_next', 'api', 'login', 'home', 'dashboard', 'static', 'images', 'favicon.ico'],
  defaultTenant: '',
  mainDomains: [
    '*************.sslip.io',
    'www.*************.sslip.io',
    'sondtheanime.site',
    'www.sondtheanime.site',
    process.env.NEXT_PUBLIC_BASE_DOMAIN || 'localhost:3000',
    'localhost',
    '127.0.0.1'
  ],
  cacheEnabled: true,
  cacheTTL: 5 * 60 * 1000 // 5 minutos em ms
};

/**
 * Prioridade das estratégias de extração de tenant
 * Ordem determina qual método é tentado primeiro
 */
export const EXTRACTION_PRIORITY: Array<'header' | 'subdomain' | 'cookie' | 'path'> = [
  'header',
  'subdomain', 
  'cookie',
  'path'
];

/**
 * Regex patterns para validação
 */
export const PATTERNS = {
  IP_ADDRESS: /^\d+(\.\d+)*$/,
  VALID_SLUG: /^[a-z0-9][a-z0-9-]*[a-z0-9]$/,
  LOCALHOST: /^(localhost|127\.0\.0\.1)/
};

/**
 * Headers utilizados para detecção de tenant
 */
export const TENANT_HEADERS = {
  SLUG: 'x-tenant-slug',
  ID: 'x-tenant-id',
  HOST: 'host',
  FORWARDED_HOST: 'x-forwarded-host',
  ORIGINAL_HOST: 'x-original-host'
} as const; 