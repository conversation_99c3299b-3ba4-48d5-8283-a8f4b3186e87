import { TeacherDashboardData } from '../types'

interface TeacherDashboardProps {
  data: TeacherDashboardData
}

export function TeacherDashboard({ data }: TeacherDashboardProps) {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      <div className="rounded-xl border bg-card text-card-foreground shadow">
        <div className="p-6 flex flex-col gap-2">
          <h3 className="font-semibold">Meus Alunos</h3>
          <div className="text-3xl font-bold">{data.meusAlunos}</div>
          <p className="text-muted-foreground text-sm">
            Alunos sob sua responsabilidade
          </p>
        </div>
      </div>
      
      <div className="rounded-xl border bg-card text-card-foreground shadow">
        <div className="p-6 flex flex-col gap-2">
          <h3 className="font-semibold">Aulas Programadas</h3>
          <div className="text-3xl font-bold">{data.aulasProgramadas}</div>
          <p className="text-muted-foreground text-sm">
            Próximas aulas agendadas
          </p>
        </div>
      </div>
      
      <div className="rounded-xl border bg-card text-card-foreground shadow">
        <div className="p-6 flex flex-col gap-2">
          <h3 className="font-semibold">Avaliações Pendentes</h3>
          <div className="text-3xl font-bold">{data.avaliacoesPendentes}</div>
          <p className="text-muted-foreground text-sm">
            Avaliações a serem realizadas
          </p>
        </div>
      </div>
    </div>
  )
} 