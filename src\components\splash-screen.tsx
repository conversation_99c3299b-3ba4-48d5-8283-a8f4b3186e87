'use client'

import { useEffect, useState } from 'react'
import { motion } from "framer-motion"
import Image from 'next/image'
import { useTenantTheme } from '@/hooks/tenant/use-tenant-theme'

interface SplashScreenProps {
  children: React.ReactNode
  logoUrl?: string
}

export function SplashScreen({ children, logoUrl: propLogoUrl }: SplashScreenProps) {
  const [isLoading, setIsLoading] = useState(true)
  const { logoUrl: themeLogoUrl, tenantName } = useTenantTheme()
  const [mounted, setMounted] = useState(false)

  // Efeito para controlar a montagem e o carregamento
  useEffect(() => {
    // Garante que estamos no ambiente do cliente
    setMounted(true)
    
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  // Nada é renderizado inicialmente no servidor para evitar discrepâncias de hidratação
  if (!mounted) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-[#1a1a1a]">
        <div className="text-center">
          <div className="w-36 h-36 bg-gray-800 mx-auto rounded-md animate-pulse" />
          <div className="mt-4 h-8 w-40 bg-gray-800 mx-auto rounded-md animate-pulse" />
        </div>
      </div>
    )
  }

  // Quando estiver carregando, mostra a tela de splash
  if (isLoading) {
    // Elementos e lógica renderizados apenas no cliente
    const ClientSideLoadingScreen = () => {
      const displayLogoUrl = propLogoUrl || themeLogoUrl || '/logo.svg'
      const altText = tenantName || 'Academia'
      const displayName = tenantName || 'Apex BJJ'
      
      return (
        <div className="fixed inset-0 flex items-center justify-center bg-[#1a1a1a] z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{
              duration: 0.5,
              repeat: Infinity,
              repeatType: 'reverse',
            }}
            className="text-center"
          >
            <Image
              src={displayLogoUrl}
              alt={altText}
              width={144}
              height={144}
              priority
              className="mx-auto object-contain"
            />
            <h1 className="mt-4 text-2xl font-bold text-white">{displayName}</h1>
          </motion.div>
        </div>
      )
    }
    
    return <ClientSideLoadingScreen />
  }

  // Quando o carregamento terminar, mostra o conteúdo filho
  return <>{children}</>
} 