'use client'

import { <PERSON>Header } from "@/components/layout/page-header";
import ProfileTabs from "./tabs";
import { ProfileEditWrapper } from '../components/profile-edit-wrapper';
import { useEffect, useState, useCallback, useRef } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { loadUserProfileClient } from '@/services/user/profile-access-client';
import { useFormatProfileData } from '@/hooks/user/Profile';
import { 
  ProfileHeaderActions,
  ProfileHeaderAvatar,
  ProfileHeaderInfo,
  ProfileHeaderGuardianInfo,
  ProfileContactCards, 
  ProfileLoadingState, 
  ProfileErrorState 
} from './components';

type UserDataType = Record<string, any>;

export default function PerfilPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const userId = params.userId as string;
  const [userData, setUserData] = useState<UserDataType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const updateCounterRef = useRef(0);
  
  const fromAlunos = searchParams.get('from') === 'alunos';
  const fromInstrutores = searchParams.get('from') === 'instrutores';

  const { data: formattedUserData, isFormatting, refreshFormatting } = useFormatProfileData(userData);

  // Função otimizada que atualiza dados localmente sem forçar refresh da página
  const refreshUserData = useCallback(async (fieldsToUpdate?: Record<string, any>) => {
    console.log('[PERFIL] Atualizando dados do perfil', fieldsToUpdate ? 'com atualizações parciais' : 'completamente');
    
    // Se recebemos campos específicos para atualizar, aplicamos diretamente ao estado sem refetch
    if (fieldsToUpdate && userData) {
      setUserData(prevData => {
        // Aplicar apenas os campos que realmente mudaram
        const updatedData = { ...prevData as Record<string, any> };
        
        // Tratar metadados especialmente
        if (fieldsToUpdate.metadata) {
          updatedData.metadata = {
            ...(updatedData.metadata || {}),
            ...fieldsToUpdate.metadata
          };
        }
        
        // Aplicar outros campos diretamente
        Object.entries(fieldsToUpdate).forEach(([key, value]) => {
          if (key !== 'metadata') {
            updatedData[key] = value;
          }
        });
        
        return updatedData;
      });
      
      // Atualizar o formato também
      refreshFormatting();
      return;
    }
    
    // Se não temos atualizações parciais ou não temos dados ainda, fazemos uma busca completa
    try {
      setIsLoading(true);
      updateCounterRef.current += 1;
      
      const { data, error: loadError } = await loadUserProfileClient(userId);
      
      if (loadError || !data) {
        setError(loadError || 'Erro ao carregar perfil');
        return;
      }

      setUserData(data);
      console.log('[PERFIL] Dados do usuário atualizados:', data);
    } catch (err) {
      console.error('Erro ao carregar perfil:', err);
      setError('Ocorreu um erro ao carregar o perfil');
    } finally {
      setIsLoading(false);
    }
  }, [userId, userData, refreshFormatting]);

  // Função otimizada para receber atualizações de campos específicos
  const handleProfileUpdated = useCallback((updatedFields?: Record<string, any>) => {
    console.log('[PERFIL] Callback de atualização de perfil chamado', updatedFields ? 'com campos específicos' : '');
    refreshUserData(updatedFields);
  }, [refreshUserData]);
  
  // Ouvir eventos de atualização de perfil
  useEffect(() => {
    const handleProfileUpdateEvent = (event: Event) => {
      const customEvent = event as CustomEvent;
      const updatedUserId = customEvent.detail?.userId;
      const updatedFields = customEvent.detail?.fields;
      
      // Apenas processar se for para este usuário
      if (updatedUserId === userId) {
        console.log('[PERFIL] Recebido evento de atualização para campos específicos:', updatedFields);
        handleProfileUpdated(updatedFields);
      }
    };
    
    const handleProfileChangeEvent = () => {
      console.log('[PERFIL] Recebido evento global de mudança de perfil');
      refreshUserData();
    };
    
    // Ouvir ambos os tipos de eventos
    window.addEventListener('profile:updated', handleProfileUpdateEvent);
    window.addEventListener('app:profile-change', handleProfileChangeEvent);
    
    return () => {
      window.removeEventListener('profile:updated', handleProfileUpdateEvent);
      window.removeEventListener('app:profile-change', handleProfileChangeEvent);
    };
  }, [userId, handleProfileUpdated, refreshUserData]);

  // Carregar dados iniciais
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        
        const { data, error: loadError } = await loadUserProfileClient(userId);
        
        if (loadError || !data) {
          setError(loadError || 'Erro ao carregar perfil');
          return;
        }

        setUserData(data);
        console.log('[PERFIL] Dados do usuário carregados:', data);
      } catch (err) {
        console.error('Erro ao carregar perfil:', err);
        setError('Ocorreu um erro ao carregar o perfil');
      } finally {
        setIsLoading(false);
      }
    };

    if (userId) {
      loadData();
    }
  }, [userId]);

  if (isLoading || isFormatting || !formattedUserData) {
    return <ProfileLoadingState />;
  }

  if (error) {
    return <ProfileErrorState error={error} />;
  }

  const isAdmin = formattedUserData.role === 'admin';
  
  let backHref = '';
  let backLabel = '';
  
  if (fromAlunos) {
    backHref = '/alunos';
    backLabel = 'Voltar para alunos';
  } else if (fromInstrutores) {
    backHref = '/instrutores';
    backLabel = 'Voltar para instrutores';
  } else {
    backHref = isAdmin ? "/dashboard" : "/home";
    backLabel = isAdmin ? "Voltar para dashboard" : "Voltar para página inicial";
  }

  return (
    <ProfileEditWrapper 
      userId={userId} 
      userData={formattedUserData}
      onProfileUpdated={handleProfileUpdated}
    >
      <div className="min-h-screen pb-12">
        <PageHeader
          backHref={backHref}
          backLabel={backLabel}
          className="shadow-sm border-b border-slate-200 dark:border-slate-700"
        >
          <ProfileHeaderActions userId={userId} />
        </PageHeader>

        <main>
          {/* Cabeçalho do Perfil */}
          <div className="shadow-sm mb-6">
            <div className="px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
              <div className="flex flex-col md:flex-row gap-8">
                <ProfileHeaderAvatar 
                  avatar={formattedUserData.avatar_url} 
                  name={formattedUserData.fullName}
                  userId={userId} 
                />
                <div className="flex-grow">
                  <ProfileHeaderInfo 
                    userId={userId}
                    userData={formattedUserData}
                  />
                  <ProfileHeaderGuardianInfo userId={userId} />
                </div>
              </div>
              
              <ProfileContactCards 
                userId={userId} 
                userData={formattedUserData}
                refreshUserData={refreshUserData}
              />
            </div>
          </div>
          
          <div className="px-4 sm:px-6 lg:px-8">
            <ProfileTabs 
              userId={userId} 
              role={formattedUserData.role} 
              userData={formattedUserData} 
            />
          </div>
        </main>
      </div>
    </ProfileEditWrapper>
  );
}
declare global {
  interface Window {
    __skipNextProfileUpdate?: boolean;
  }
} 