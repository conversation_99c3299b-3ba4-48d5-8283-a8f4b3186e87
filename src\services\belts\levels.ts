import { createAdminClient } from '@/services/supabase/server'
import { BeltColor } from '@/components/belt'

export interface GraduationLevel {
  id: string
  belt_color: BeltColor
  degree: number
  label: string
  sort_order: number
  members_count: number
  stripe_color?: string | null
  show_center_line?: boolean | null
  center_line_color?: string | null
}

/**
 * Helper function: Busca uma modalidade por slug garantindo isolamento por tenant
 * @param tenantId ID do tenant
 * @param modalitySlug Slug da modalidade
 * @returns modalidade encontrada ou null se não existir
 */
async function getModalityBySlugForTenant(tenantId: string, modalitySlug: string): Promise<{ id: string } | null> {
  const supabase = await createAdminClient()
  
  const { data: modality, error: modalityError } = await supabase
    .from('modalities')
    .select('id')
    .eq('slug', modalitySlug)
    .eq('tenant_id', tenantId)
    .single()

  if (modalityError) {
    if (modalityError.code === 'PGRST116') {
      // Não encontrado
      return null
    }
    console.error('Erro ao buscar modalidade por slug para tenant:', modalityError)
    throw new Error('Erro ao buscar modalidade')
  }

  return modality
}

/**
 * Lista todos os níveis (faixa + grau) configurados para uma modalidade, acrescentando
 * a contagem de alunos que possuem aquele nível como faixa atual.
 *
 * @param tenantId   ID do tenant corrente
 * @param modalitySlug Slug da modalidade (ex.: "jiujitsu_adulto")
 */
export async function listGraduationLevels(tenantId: string, modalitySlug: string): Promise<GraduationLevel[]> {
  console.log('🔍 Carregando graduation levels para:', { tenantId, modalitySlug })
  const supabase = await createAdminClient()

  // 1. Descobrir modality_id a partir do slug e tenant_id (isolamento por tenant)
  const modality = await getModalityBySlugForTenant(tenantId, modalitySlug)

  if (!modality) {
    console.error('Modalidade não encontrada para o tenant:', { tenantId, modalitySlug })
    throw new Error('Modalidade não encontrada')
  }

  // 2. Buscar todos os níveis configurados
  console.log('🔍 Buscando belt_levels para modality_id:', modality.id)
  const { data: levels, error: levelsError } = await supabase.from('belt_levels').select('id, belt_color, degree, label, sort_order, stripe_color, show_center_line, center_line_color').eq('tenant_id', tenantId).eq('modality_id', modality.id).order('sort_order', { ascending: true })

  if (levelsError) {
    console.error('❌ Erro ao buscar belt_levels:', levelsError)
    throw new Error('Erro ao listar níveis')
  }

  console.log('✅ Belt levels encontrados no carregamento:', levels?.length || 0)
  console.log(
    '📋 Levels carregados:',
    levels?.map((l) => `${l.label} (${l.belt_color}, grau ${l.degree})`)
  )

  // 3. Buscar contagem de membros por belt_level_id
  // Primeiro buscar os student_belts que são current_belt dos estudantes
  const { data: currentBelts, error: currentBeltsError } = await supabase.from('students').select('current_belt_id').eq('tenant_id', tenantId).not('current_belt_id', 'is', null)

  if (currentBeltsError) {
    console.error('Erro ao buscar current_belt dos alunos:', currentBeltsError)
    throw new Error('Erro ao listar níveis')
  }

  // Extrair IDs dos student_belts
  const currentBeltIds = currentBelts?.map((s: any) => s.current_belt_id).filter(Boolean) || []

  // Buscar os belt_level_id correspondentes
  const { data: studentBeltsData, error: studentBeltsError } = await supabase.from('student_belts').select('belt_level_id').in('id', currentBeltIds)

  if (studentBeltsError) {
    console.error('Erro ao buscar belt_level_id dos student_belts:', studentBeltsError)
    throw new Error('Erro ao listar níveis')
  }

  // 4. Criar mapa de contagem por belt_level_id
  const counts = new Map<string, number>()
  studentBeltsData?.forEach((sb: any) => {
    if (sb.belt_level_id) {
      counts.set(sb.belt_level_id, (counts.get(sb.belt_level_id) || 0) + 1)
    }
  })

  console.log('📊 Contagem de membros por faixa:', Object.fromEntries(counts))

  // 5. Montar resposta, usando o id do nível como chave
  return (levels || []).map((lvl) => {
    return {
      ...lvl,
      members_count: counts.get(lvl.id) || 0
    } as GraduationLevel
  })
}

// ------------------------------
// Upsert / criar níveis de graduação
// ------------------------------
export interface BeltLevelUpsertInput {
  id?: string
  belt_color: BeltColor | string
  degree: number
  label: string
  stripe_color?: string | null
  show_center_line?: boolean | null
  center_line_color?: string | null
  sort_order: number
}

/**
 * Cria ou atualiza níveis de graduação (belt_levels) para uma modalidade.
 * Utiliza privilégios de admin para ignorar RLS.
 */
export async function upsertBeltLevels(tenantId: string, modalityId: string, levels: BeltLevelUpsertInput[]): Promise<{ success: boolean; error?: any }> {
  if (!levels.length) return { success: true }

  const supabase = await createAdminClient()

  // Transformar para payload de upsert
  const payload = levels.map((lvl) => {
    const base = {
      tenant_id: tenantId,
      modality_id: modalityId,
      belt_color: lvl.belt_color,
      degree: lvl.degree,
      label: lvl.label,
      stripe_color: lvl.stripe_color ?? null,
      show_center_line: lvl.show_center_line ?? null,
      center_line_color: lvl.center_line_color ?? null,
      sort_order: lvl.sort_order
    } as any

    if (lvl.id) {
      base.id = lvl.id
    }
    return base
  })

  // Usar função RPC que contorna problemas de RLS
  console.log('🏢 Tenant ID sendo usado:', tenantId)
  console.log('🥋 Modality ID sendo usado:', modalityId)
  console.log('📋 Payload completo:', payload)

  const { data, error } = await supabase.rpc('upsert_belt_levels_admin', {
    p_tenant_id: tenantId,
    p_modality_id: modalityId,
    p_levels: payload
  })

  if (error) {
    console.error('❌ Erro ao upsert belt_levels:', error)
    return { success: false, error }
  }

  console.log('📊 Resposta da função RPC:', data)

  if (data && !data.success) {
    console.error('❌ Erro na função RPC upsert_belt_levels:', data.error)
    return { success: false, error: data.error }
  }

  // 🔍 VERIFICAÇÃO IMEDIATA: Confirmar se os dados foram realmente persistidos
  try {
    console.log('🔍 Verificando persistência dos dados...')
    const { data: verificationData, error: verificationError } = await supabase.from('belt_levels').select('id, label, belt_color, degree').eq('tenant_id', tenantId).eq('modality_id', modalityId).order('sort_order')

    if (verificationError) {
      console.error('❌ Erro ao verificar persistência:', verificationError)
    } else {
      console.log('✅ Dados verificados na tabela:', verificationData?.length || 0, 'registros encontrados')
      console.log(
        '📋 Registros encontrados:',
        verificationData?.map((d) => `${d.label} (${d.belt_color}, grau ${d.degree})`)
      )
    }
  } catch (verificationErr) {
    console.error('❌ Erro durante verificação de persistência:', verificationErr)
  }

  return { success: true }
}

/**
 * Deleta um nível de graduação específico
 */
export async function deleteBeltLevel(tenantId: string, beltLevelId: string): Promise<{ success: boolean; error?: any }> {
  const supabase = await createAdminClient()

  // Primeiro verificar se existem alunos usando esta faixa
  const { data: studentsCount, error: countError } = await supabase.from('students').select('id', { count: 'exact', head: true }).eq('current_belt_id', beltLevelId)

  if (countError) {
    console.error('Erro ao verificar uso da faixa:', countError)
    return { success: false, error: countError }
  }

  const count = studentsCount?.length ?? 0

  if (count > 0) {
    return {
      success: false,
      error: { message: 'Não é possível excluir esta faixa porque existem alunos que a utilizam.' }
    }
  }

  // Excluir requisitos associados primeiro
  const { error: reqError } = await supabase.from('tenant_belt_level_requirements').delete().eq('tenant_id', tenantId).eq('belt_level_id', beltLevelId)

  if (reqError) {
    console.error('Erro ao deletar requisitos do nível:', reqError)
    return { success: false, error: reqError }
  }

  // Então excluir o nível
  const { error } = await supabase.from('belt_levels').delete().eq('tenant_id', tenantId).eq('id', beltLevelId)

  if (error) {
    console.error('Erro ao deletar nível:', error)
    return { success: false, error }
  }

  return { success: true }
}
