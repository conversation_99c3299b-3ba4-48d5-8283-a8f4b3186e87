'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp, Clock, Calendar, Users, Plus } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { ClassCard } from './ClassCard';
import Link from 'next/link';

interface Class {
  id: string;
  title: string;
  instructor: {
    name: string;
    avatar?: string;
  };
  time: string;
  duration: number;
  enrolledCount: number;
  maxCapacity: number | null;
  location: string;
  status: 'ongoing' | 'upcoming' | 'completed' | 'cancelled';
  startDate: string;
  description?: string;
  isOpenClass?: boolean;
}

interface Section {
  id: string;
  title: string;
  subtitle: string;
  icon: React.ReactNode;
  classes: Class[];
  color: string;
  emptyMessage: string;
  actionButton?: {
    label: string;
    href: string;
  };
}

interface ClassesSectionProps {
  ongoingClasses: Class[];
  upcomingClasses: Class[];
  completedClasses: Class[];
}

export function ClassesSection({ 
  ongoingClasses, 
  upcomingClasses, 
  completedClasses 
}: ClassesSectionProps) {
  const [expandedSections, setExpandedSections] = useState<string[]>(['ongoing', 'upcoming']);

  const sections: Section[] = [
    {
      id: 'ongoing',
      title: 'Aulas em Andamento',
      subtitle: `${ongoingClasses.length} aula${ongoingClasses.length !== 1 ? 's' : ''}`,
      icon: <Clock className="h-5 w-5" />,
      classes: ongoingClasses,
      color: '#10b981',
      emptyMessage: 'Nenhuma aula em andamento no momento.',
      actionButton: {
        label: 'Iniciar Nova Aula Livre',
        href: '/aulas/livres/nova'
      }
    },
    {
      id: 'upcoming',
      title: 'Próximas Aulas',
      subtitle: `${upcomingClasses.length} aula${upcomingClasses.length !== 1 ? 's' : ''} hoje`,
      icon: <Calendar className="h-5 w-5" />,
      classes: upcomingClasses,
      color: '#3b82f6',
      emptyMessage: 'Nenhuma aula agendada para hoje.',
      actionButton: {
        label: 'Agendar Aula',
        href: '/aulas/calendario'
      }
    },
    {
      id: 'completed',
      title: 'Aulas Concluídas',
      subtitle: `${completedClasses.length} aula${completedClasses.length !== 1 ? 's' : ''} hoje`,
      icon: <Users className="h-5 w-5" />,
      classes: completedClasses,
      color: '#8b5cf6',
      emptyMessage: 'Nenhuma aula foi concluída hoje.',
    }
  ];

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const EmptyState = ({ section }: { section: Section }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="text-center py-12"
    >
      <div 
        className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
        style={{ backgroundColor: `${section.color}15` }}
      >
        <div style={{ color: section.color }}>
          {section.icon}
        </div>
      </div>
      <h3 className="text-lg font-medium text-slate-900 dark:text-gray-100 mb-2">
        Nenhuma aula encontrada
      </h3>
      <p className="text-slate-500 dark:text-gray-400 mb-6 max-w-sm mx-auto">
        {section.emptyMessage}
      </p>
      {section.actionButton && (
        <Link href={section.actionButton.href}>
          <Button 
            className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700 text-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            {section.actionButton.label}
          </Button>
        </Link>
      )}
    </motion.div>
  );

  return (
    <div className="space-y-6">
      {sections.map((section) => (
        <motion.div
          key={section.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 shadow-sm">
            <CardContent className="p-0">
              
              {/* Section Header */}
              <button
                onClick={() => toggleSection(section.id)}
                className="w-full p-6 flex items-center justify-between hover:bg-slate-50 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-center gap-4">
                  <div 
                    className="p-3 rounded-lg"
                    style={{ backgroundColor: `${section.color}15` }}
                  >
                    <div style={{ color: section.color }}>
                      {section.icon}
                    </div>
                  </div>
                  <div className="text-left">
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-gray-100">
                      {section.title}
                    </h3>
                    <p className="text-sm text-slate-500 dark:text-gray-400">
                      {section.subtitle}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  {section.classes.length > 0 && (
                    <span 
                      className="text-sm font-medium px-2 py-1 rounded-full"
                      style={{ 
                        backgroundColor: `${section.color}15`,
                        color: section.color
                      }}
                    >
                      {section.classes.length}
                    </span>
                  )}
                  <motion.div
                    animate={{ rotate: expandedSections.includes(section.id) ? 180 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ChevronDown className="h-5 w-5 text-slate-400 dark:text-gray-500" />
                  </motion.div>
                </div>
              </button>

              {/* Section Content */}
              <AnimatePresence>
                {expandedSections.includes(section.id) && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3, ease: 'easeInOut' }}
                    className="overflow-hidden"
                  >
                    <div className="px-6 pb-6 border-t border-slate-100 dark:border-gray-700">
                      {section.classes.length === 0 ? (
                        <EmptyState section={section} />
                      ) : (
                        <motion.div
                          initial="hidden"
                          animate="visible"
                          variants={{
                            hidden: { opacity: 0 },
                            visible: {
                              opacity: 1,
                              transition: {
                                staggerChildren: 0.1
                              }
                            }
                          }}
                          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6"
                        >
                          {section.classes.map((classItem) => (
                            <motion.div
                              key={classItem.id}
                              variants={{
                                hidden: { opacity: 0, y: 20 },
                                visible: { opacity: 1, y: 0 }
                              }}
                            >
                              <ClassCard {...classItem} />
                            </motion.div>
                          ))}
                        </motion.div>
                      )}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );
} 