'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { usePermissionPrefetch } from '@/hooks/user/Auth';
import { logoutUser } from '@/services/auth/actions/auth-actions';
import { createClient } from '@/services/supabase/client';

interface AuthLogoutProps {
  variant?: 'default' | 'ghost' | 'link' | 'destructive' | 'outline' | 'secondary';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
}

export function AuthLogout({ variant = 'default', size = 'default', className }: AuthLogoutProps) {
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const { invalidatePermissions } = usePermissionPrefetch();
  const router = useRouter();

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      
      // Invalidar cache de permissões
      invalidatePermissions();
      
      // Garantir que tokens locais sejam removidos antes do logout no servidor
      try {
        const supabase = createClient();
        await supabase.auth.signOut();
      } catch (clientError) {
        console.warn('Falha ao limpar sessão no cliente:', clientError);
      }
      
      // Disparar evento de logout para outros componentes
      window.dispatchEvent(new CustomEvent('app:logout'));
      
      // Chamar server action de logout
      await logoutUser();
      
      // Se por algum motivo o redirect no server action não funcionar
      router.push('/login?signout=true');
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <Button 
      onClick={handleLogout} 
      disabled={isLoggingOut}
      variant={variant}
      size={size}
      className={className}
    >
      {isLoggingOut ? 'Saindo...' : 'Sair'}
    </Button>
  );
} 