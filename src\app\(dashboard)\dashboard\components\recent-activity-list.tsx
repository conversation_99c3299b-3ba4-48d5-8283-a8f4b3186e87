import Link from "next/link";
import { ActivityItem } from "../types";
import { ActivityItemIcon } from "./activity-item-icon";
import { BeltColor, beltColorTranslation } from "@/components/belt";

interface RecentActivityListProps {
  activities: ActivityItem[];
}

const beltColorRegex = new RegExp(
  `\\b(${Object.keys(beltColorTranslation).join("|")})\\b`,
  "gi"
);

function translateDescription(description: string) {
  if (!description) return ""

  return description.replace(beltColorRegex, (match) => {
    const color = match.toLowerCase() as BeltColor;
    return beltColorTranslation[color] || match;
  });
}

export function RecentActivityList({ activities }: RecentActivityListProps) {
  if (!activities.length) {
    return (
      <div className="dashboard-stats-card h-[180px] flex items-center justify-center">
        <p className="text-center text-gray-500 dark:text-gray-400">
          Nenhuma atividade recente encontrada
        </p>
      </div>
    );
  }

  return (
    <div className="dashboard-stats-card  flex flex-col">
      <h3 className="text-base font-semibold leading-6 text-gray-900 dark:text-white mb-3">
        Atividade Recente
      </h3>
      <div className="flex-1 overflow-y-auto">
        <div className="-my-2 divide-y divide-gray-200 dark:divide-gray-800">
          {activities.slice(0, 6).map((item) => (
            <div key={item.id} className="flex gap-2 py-2">
              <ActivityItemIcon type={item.type} />
              <div className="flex-1 min-w-0">
                {item.person.href ? (
                  <Link
                    href={item.person.href}
                    className="text-sm font-medium text-gray-900 dark:text-white truncate hover:underline"
                  >
                    {item.person.name}
                  </Link>
                ) : (
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {item.person.name}
                  </p>
                )}
                <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                  {translateDescription(item.description)}
                </p>
                <p className="text-xs text-gray-400 dark:text-gray-500">
                  {item.date}
                </p>
              </div>
            </div>
          ))}
        </div>
        {activities.length > 6 && (
          <div className="mt-2 text-center">
            <p className="text-xs text-gray-500 dark:text-gray-400">
              +{activities.length - 6} atividades mais
            </p>
          </div>
        )}
      </div>
    </div>
  );
} 