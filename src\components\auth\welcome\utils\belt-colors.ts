/**
 * Utilitário para trabalhar com cores de faixas vindas do banco de dados
 */

export interface BeltData {
  belt_color: string // Cor em HEX (ex: "#FFFFFF")
  stripe_color?: string // Cor da faixa em HEX (ex: "#FF0000")
  label: string // Nome da faixa (ex: "Faixa Branca - 1º Grau")
  show_center_line?: boolean
  center_line_color?: string
}

/**
 * Converte uma cor HEX para determinar se é clara ou escura
 * @param hex - Cor em formato HEX (ex: "#FFFFFF")
 * @returns true se a cor for clara, false se for escura
 */
export function isLightColor(hex: string): boolean {
  // Remove o # se presente
  const cleanHex = hex.replace('#', '')

  // Converte para RGB
  const r = parseInt(cleanHex.substring(0, 2), 16)
  const g = parseInt(cleanHex.substring(2, 4), 16)
  const b = parseInt(cleanHex.substring(4, 6), 16)

  // Calcula a luminância usando a fórmula padrão
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255

  // Considera clara se a luminância for maior que 0.5
  return luminance > 0.5
}

/**
 * Gera classes CSS para o indicador de faixa baseado nos dados do banco
 * @param beltData - Dados da faixa vindos do banco de dados
 * @returns String com classes CSS para o indicador
 */
export function getBeltIndicatorClasses(beltData: BeltData | null): string {
  if (!beltData || !beltData.belt_color) {
    return 'h-3 w-3 rounded-full bg-gray-300 ring-1 ring-gray-400'
  }

  const baseClasses = 'h-3 w-3 rounded-full'
  const isLight = isLightColor(beltData.belt_color)

  // Para cores claras, adiciona uma borda para melhor visibilidade
  const ringClass = isLight ? 'ring-1 ring-gray-400' : ''

  return `${baseClasses} ${ringClass}`.trim()
}

/**
 * Gera o estilo inline para o indicador de faixa
 * @param beltData - Dados da faixa vindos do banco de dados
 * @returns Objeto com estilos CSS inline
 */
export function getBeltIndicatorStyle(beltData: BeltData | null): React.CSSProperties {
  if (!beltData || !beltData.belt_color) {
    return {
      backgroundColor: '#d1d5db' // gray-300
    }
  }

  return {
    backgroundColor: beltData.belt_color
  }
}

/**
 * Obtém a cor do texto baseada na cor de fundo da faixa
 * @param beltData - Dados da faixa vindos do banco de dados
 * @returns Classe CSS para a cor do texto
 */
export function getBeltTextColor(beltData: BeltData | null): string {
  if (!beltData || !beltData.belt_color) {
    return 'text-gray-700'
  }

  const isLight = isLightColor(beltData.belt_color)
  return isLight ? 'text-gray-900' : 'text-white'
}

/**
 * Obtém o nome da faixa do banco de dados ou fallback
 * @param beltData - Dados da faixa vindos do banco de dados
 * @param fallbackColor - Cor para fallback caso não haja dados
 * @returns Nome da faixa para exibição
 */
export function getBeltDisplayName(beltData: BeltData | null, fallbackColor?: string): string {
  if (beltData?.label) {
    return beltData.label
  }

  // Fallback básico baseado na cor
  if (fallbackColor) {
    const fallbackNames: Record<string, string> = {
      '#FFFFFF': 'Faixa Branca',
      '#FAFF00': 'Faixa Amarela',
      '#FFA500': 'Faixa Laranja',
      '#22C55E': 'Faixa Verde',
      '#2563EB': 'Faixa Azul',
      '#7C3AED': 'Faixa Roxa',
      '#92400E': 'Faixa Marrom',
      '#000000': 'Faixa Preta',
      '#EF4444': 'Faixa Vermelha',
      '#909090': 'Faixa Cinza'
    }
    return fallbackNames[fallbackColor] || 'Faixa'
  }

  return 'Faixa'
}

/**
 * Gera estilos para exibir faixas com listras (stripe_color)
 * @param beltData - Dados da faixa vindos do banco de dados
 * @returns Objeto com estilos CSS para faixas com listras
 */
export function getBeltStripeStyle(beltData: BeltData | null): React.CSSProperties {
  if (!beltData || !beltData.stripe_color || beltData.belt_color === beltData.stripe_color) {
    return getBeltIndicatorStyle(beltData)
  }

  // Criar um gradiente para simular listras
  return {
    background: `linear-gradient(45deg, ${beltData.belt_color} 25%, ${beltData.stripe_color} 25%, ${beltData.stripe_color} 50%, ${beltData.belt_color} 50%, ${beltData.belt_color} 75%, ${beltData.stripe_color} 75%)`,
    backgroundSize: '6px 6px'
  }
}

/**
 * Verifica se a faixa tem listras diferentes da cor principal
 * @param beltData - Dados da faixa vindos do banco de dados
 * @returns true se tem listras diferentes, false caso contrário
 */
export function hasBeltStripes(beltData: BeltData | null): boolean {
  return !!(beltData?.stripe_color && beltData.stripe_color !== beltData.belt_color)
}

/**
 * Gera classes CSS para o container da faixa (se necessário para layouts maiores)
 * @param beltData - Dados da faixa vindos do banco de dados
 * @returns String com classes CSS para o container
 */
export function getBeltContainerClasses(beltData: BeltData | null): string {
  if (!beltData || !beltData.belt_color) {
    return 'bg-gray-100 border border-gray-300 rounded-full'
  }

  const isLight = isLightColor(beltData.belt_color)
  const borderClass = isLight ? 'border border-gray-300' : 'border border-gray-600'

  return `rounded-full ${borderClass}`
}
