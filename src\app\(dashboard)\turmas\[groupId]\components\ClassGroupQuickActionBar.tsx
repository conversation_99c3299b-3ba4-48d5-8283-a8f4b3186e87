'use client';

import { useTransition, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Edit3, 
  Users, 
  Calendar, 
  BarChart3, 
  RefreshCw,
  Download,
  Plus,
  UserPlus,
  Trash2,
  PowerOff,
  Power,
  AlertTriangle
} from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { ClassGroupWithDetails } from '@/app/(dashboard)/aulas/types';
import { useClassGroup } from './ClassGroupContext';
import { deleteClassGroup } from '@/app/(dashboard)/turmas/actions/class-group/delete-class-group';
import { changeClassGroupStatus } from '@/app/(dashboard)/turmas/actions/class-group/change-class-group-status';
import { QuickStat, QuickAction } from '../types';
import { QuickStats } from '@/components/ui/quick-stats';

interface ClassGroupQuickActionBarProps {
  classGroup: ClassGroupWithDetails;
  onRefresh?: () => void;
}

export function ClassGroupQuickActionBar({ 
  classGroup, 
  onRefresh 
}: ClassGroupQuickActionBarProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showStatusDialog, setShowStatusDialog] = useState(false);
  
  // Usar o contexto para gerenciar estado
  const { 
    state, 
    refreshAllData, 
    refreshClassGroupData,
    getLastRefreshText, 
    markAsRecentlyUpdated 
  } = useClassGroup();

  const quickActions: QuickAction[] = [
    {
      id: 'edit-group',
      label: 'Editar Turma',
      icon: <Edit3 className="h-4 w-4" />,
      href: `/turmas/${classGroup.id}/editar`,
      primary: true
    },
    {
      id: 'manage-students',
      label: 'Gerenciar Alunos',
      icon: <Users className="h-4 w-4" />,
      href: `/turmas/${classGroup.id}/alunos`,
      variant: 'outline'
    },
    {
      id: 'new-class',
      label: 'Nova Aula',
      icon: <Plus className="h-4 w-4" />,
      href: `/turmas/${classGroup.id}/aulas/nova`,
      variant: 'outline'
    },
    {
      id: 'view-classes',
      label: 'Ver Aulas',
      icon: <Calendar className="h-4 w-4" />,
      href: `/turmas/${classGroup.id}/aulas`,
      variant: 'ghost'
    },
    // {
    //   id: 'export',
    //   label: 'Exportar',
    //   icon: <Download className="h-4 w-4" />,
    //   onClick: () => handleExport(),
    //   variant: 'ghost'
    // },
    {
      id: 'toggle-status',
      label: classGroup.is_active ? 'Desativar Turma' : 'Ativar Turma',
      icon: classGroup.is_active ? <PowerOff className="h-4 w-4" /> : <Power className="h-4 w-4" />,
      onClick: () => setShowStatusDialog(true),
      variant: 'ghost',
      requiresConfirmation: true
    },
    {
      id: 'delete-group',
      label: 'Excluir Turma',
      icon: <Trash2 className="h-4 w-4" />,
      onClick: () => setShowDeleteDialog(true),
      variant: 'destructive',
      requiresConfirmation: true
    }
  ];

  const handleRefresh = () => {
    startTransition(async () => {
      try {
        await refreshAllData();
        toast.success("Dados atualizados", {
          description: "As informações da turma foram atualizadas com sucesso.",
        });
      } catch (error) {
        toast.error("Erro ao atualizar", {
          description: "Não foi possível atualizar os dados da turma.",
        });
      }
    });
  };

  const handleExport = () => {
    toast.info("Exportação iniciada", {
      description: "Os dados da turma estão sendo preparados para download.",
    });
  };

  const handleGenerateReport = () => {
    toast.info("Gerando relatório", {
      description: "O relatório detalhado da turma está sendo preparado.",
    });
  };

  const handleDeleteGroup = () => {
    startTransition(async () => {
      let dialogClosed = false;
      
      // Garantir que o dialog será fechado mesmo em caso de erro
      const closeDialog = () => {
        if (!dialogClosed) {
          dialogClosed = true;
          console.log('🚪 Fechando dialog de exclusão...');
          setShowDeleteDialog(false);
        }
      };

      try {
        console.log('📞 QuickActionBar: Chamando deleteClassGroup...');
        const result = await deleteClassGroup(classGroup.id);
        console.log('📦 QuickActionBar: Resultado recebido:', result);
        
        if (result.success) {
          console.log('✅ QuickActionBar: Exclusão bem-sucedida');
          
          // Fechar dialog imediatamente
          closeDialog();
          
          toast.success("Turma excluída", {
            description: 'message' in result ? result.message : "Turma excluída completamente com sucesso",
          });
          
          // Redirecionamento imediato e força uma nova navegação
          console.log('🔄 Redirecionando imediatamente para lista de turmas...');
          
          // Usar replace para evitar voltar para a página excluída
          router.replace('/turmas');
          
          // Força uma nova navegação como fallback
          setTimeout(() => {
            if (window.location.pathname.includes(classGroup.id)) {
              console.log('🔄 Forçando redirecionamento...');
              window.location.href = '/turmas';
            }
          }, 100);
        } else {
          const errorMessage = 'errors' in result && result.errors && typeof result.errors === 'object' && '_form' in result.errors 
            ? result.errors._form 
            : "Erro inesperado";
            
          console.log('❌ QuickActionBar: Erro ao excluir:', errorMessage);
          closeDialog();
          
          // Verificar se o erro é devido a alunos matriculados
          const isStudentEnrollmentError = typeof errorMessage === 'string' && 
            errorMessage.includes('alunos matriculados');
          
          if (isStudentEnrollmentError) {
            toast.error("Não é possível excluir a turma", {
              description: "Esta turma possui alunos matriculados. Para excluir a turma, primeiro remova todos os alunos ou use a opção 'Desativar Turma' para preservar o histórico.",
              duration: 6000,
            });
          } else {
            toast.error("Erro ao excluir turma", {
              description: errorMessage,
            });
          }
        }
      } catch (error) {
        console.error('💥 QuickActionBar: Erro capturado:', error);
        closeDialog();
        
        toast.error("Erro inesperado", {
          description: "Não foi possível excluir a turma",
        });
      }
      
      // Garantir que o dialog seja fechado mesmo que algo dê errado
      setTimeout(closeDialog, 100);
    });
  };

  const handleChangeStatus = () => {
    startTransition(async () => {
      let dialogClosed = false;
      
      // Garantir que o dialog será fechado mesmo em caso de erro
      const closeDialog = () => {
        if (!dialogClosed) {
          dialogClosed = true;
          console.log('🚪 QuickActionBar: Fechando dialog de status...');
          setShowStatusDialog(false);
        }
      };

      try {
        console.log('🔄 QuickActionBar: Alterando status da turma:', classGroup.id, 'para:', !classGroup.is_active);
        const result = await changeClassGroupStatus({
          id: classGroup.id,
          is_active: !classGroup.is_active
        });
        console.log('📦 QuickActionBar: Resultado alteração status:', result);
        
        if (result.success) {
          console.log('✅ QuickActionBar: Status alterado com sucesso');
          
          // Fechar dialog imediatamente
          closeDialog();
          
          toast.success(classGroup.is_active ? "Turma desativada" : "Turma ativada", {
            description: 'message' in result ? result.message : "Status alterado com sucesso",
          });
          
          // Atualizar dados da turma em tempo real
          setTimeout(async () => {
            console.log('🔄 QuickActionBar: Atualizando dados em tempo real...');
            await refreshClassGroupData();
            markAsRecentlyUpdated();
          }, 300);
        } else {
          let errorMessage = "Erro inesperado";
          if ('errors' in result && result.errors) {
            if (typeof result.errors === 'object' && '_form' in result.errors) {
              errorMessage = result.errors._form as string;
            }
          }
          
          console.log('❌ QuickActionBar: Erro ao alterar status:', errorMessage);
          closeDialog();
          
          toast.error("Erro ao alterar status", {
            description: errorMessage,
          });
        }
      } catch (error) {
        console.error('💥 QuickActionBar: Erro ao alterar status:', error);
        closeDialog();
        
        toast.error("Erro inesperado", {
          description: "Não foi possível alterar o status da turma",
        });
      }
      
      // Garantir que o dialog seja fechado mesmo que algo dê errado
      setTimeout(closeDialog, 100);
    });
  };

  // Estatísticas rápidas para exibir no header
  const quickStats: QuickStat[] = [
    {
      label: 'Alunos',
      value: classGroup._count.enrollments,
      icon: <Users className="h-4 w-4" />,
      color: 'text-blue-600 dark:text-blue-400'
    },
    {
      label: 'Aulas',
      value: classGroup._count.classes,
      icon: <Calendar className="h-4 w-4" />,
      color: 'text-green-600 dark:text-green-400'
    },
    {
      label: classGroup.allow_waitlist ? 'Lista de Espera' : 'Lista de Espera (Desativada)',
      value: classGroup.allow_waitlist ? classGroup._count.waitlist : 0,
      icon: <UserPlus className="h-4 w-4" />,
      color: classGroup.allow_waitlist 
        ? 'text-orange-600 dark:text-orange-400' 
        : 'text-gray-500 dark:text-gray-500',
      disabled: !classGroup.allow_waitlist
    }
  ];

  return (
    <>
      <motion.div 
        className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 rounded-xl p-4 shadow-sm"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex flex-col lg:flex-row gap-4 justify-between items-start lg:items-center">
          
          {/* Estatísticas Rápidas */}
          <QuickStats stats={quickStats} />

          {/* Ações Rápidas */}
          <div className="flex flex-wrap gap-2">
            {quickActions.map((action) => {
              const buttonContent = (
                <>
                  {action.icon}
                  <span className="hidden sm:inline">{action.label}</span>
                </>
              );

              const baseClasses = "h-9 px-3 text-sm font-medium transition-all duration-200";
              
              if (action.href) {
                return (
                  <Button
                    key={action.id}
                    asChild
                    variant={action.primary ? 'default' : action.variant || 'outline'}
                    className={baseClasses}
                  >
                    <Link href={action.href}>
                      {buttonContent}
                    </Link>
                  </Button>
                );
              }

              return (
                <Button
                  key={action.id}
                  variant={action.primary ? 'default' : action.variant || 'outline'}
                  onClick={action.onClick}
                  disabled={isPending}
                  className={`${baseClasses} ${
                    action.variant === 'destructive' ? 'text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300' : ''
                  }`}
                >
                  {buttonContent}
                </Button>
              );
            })}
          </div>
        </div>

        {/* Informações de Status da Turma */}
        <div className="mt-4 pt-4 border-t border-slate-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="text-sm text-slate-600 dark:text-gray-400">
                Instrutor: 
                <span className="font-medium text-slate-900 dark:text-gray-100 ml-1">
                  {classGroup.instructor.full_name || 
                   `${classGroup.instructor.first_name} ${classGroup.instructor.last_name || ''}`}
                </span>
              </div>
              <div className="text-sm text-slate-600 dark:text-gray-400">
                Filial: 
                <span className="font-medium text-slate-900 dark:text-gray-100 ml-1">
                  {classGroup.branch.name}
                </span>
              </div>
            </div>

            {/* Status de Ocupação e Botão de Atualizar */}
            <div className="flex items-center gap-4">
              {classGroup.capacity_usage_percentage && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-slate-600 dark:text-gray-400">
                    Ocupação:
                  </span>
                  <div className="flex items-center gap-2">
                    <div className="w-16 bg-slate-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-blue-600 dark:bg-blue-500 h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${Math.min(classGroup.capacity_usage_percentage, 100)}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium text-slate-900 dark:text-gray-100">
                      {classGroup.capacity_usage_percentage.toFixed(0)}%
                    </span>
                  </div>
                </div>
              )}

              {/* Botão de Atualizar */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefresh}
                disabled={isPending}
                className="h-8 px-3 text-sm"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isPending ? 'animate-spin' : ''}`} />
                <span className="hidden sm:inline">Atualizar</span>
              </Button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Dialog de Confirmação para Alterar Status */}
      <AlertDialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              {classGroup.is_active ? 'Desativar Turma' : 'Ativar Turma'}
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              {classGroup.is_active ? (
                <div>
                  Você está prestes a <strong>desativar</strong> a turma "{classGroup.name}".
                  <br /><br />
                  <strong>A turma será desativada, mas:</strong>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>Todos os dados históricos serão preservados</li>
                    <li>Alunos não precisam ser removidos</li>
                    <li>A turma pode ser reativada a qualquer momento</li>
                  </ul>
                </div>
              ) : (
                <div>
                  Você está prestes a <strong>reativar</strong> a turma "{classGroup.name}".
                  <br /><br />
                  A turma ficará ativa novamente e poderá receber novas matrículas.
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isPending}>
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleChangeStatus}
              disabled={isPending}
              className={classGroup.is_active ? 
                "bg-orange-600 hover:bg-orange-700 dark:bg-orange-600 dark:hover:bg-orange-700" :
                "bg-green-600 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-700"
              }
            >
              {isPending ? 'Processando...' : classGroup.is_active ? 'Desativar' : 'Ativar'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Dialog de Confirmação para Exclusão */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-red-600">
              <Trash2 className="h-5 w-5" />
              Excluir Turma
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div>
                Você está prestes a <strong>excluir permanentemente</strong> a turma "{classGroup.name}".
                <br /><br />

                {/* Mostrar aviso se houver alunos matriculados */}
                {classGroup._count.enrollments > 0 && (
                  <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg border border-amber-200 dark:border-amber-800 mb-4">
                    <div className="flex items-center gap-2 text-amber-800 dark:text-amber-300">
                      <Users className="h-4 w-4" />
                      <strong>Esta turma possui {classGroup._count.enrollments} aluno{classGroup._count.enrollments !== 1 ? 's' : ''} matriculado{classGroup._count.enrollments !== 1 ? 's' : ''}</strong>
                    </div>
                    <p className="text-sm text-amber-700 dark:text-amber-400 mt-1">
                      A exclusão só será permitida após remover todas as matrículas ativas.
                    </p>
                  </div>
                )}

                <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800">
                  <strong className="text-red-800 dark:text-red-300">⚠️ ATENÇÃO - Esta ação:</strong>
                  <ul className="list-disc list-inside mt-2 space-y-1 text-red-700 dark:text-red-400">
                    <li><strong>NÃO PODE ser desfeita</strong></li>
                    <li>Remove a turma e todas as aulas associadas</li>
                    <li>Só é permitida se não houver alunos matriculados</li>
                    <li>Dados históricos serão perdidos</li>
                  </ul>
                </div>
                <br />
                <strong>Alternativa recomendada:</strong> Use "Desativar Turma" para preservar o histórico.
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isPending}>
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteGroup}
              disabled={isPending || classGroup._count.enrollments > 0}
              className="bg-red-600 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isPending ? 'Excluindo...' : 
               classGroup._count.enrollments > 0 ? 'Não é possível excluir' : 
               'Excluir Permanentemente'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 