"use client"

import * as React from "react"
import { format } from "date-fns"
import { ptBR } from "date-fns/locale"
import { CalendarIcon } from "lucide-react"

import { Calendar } from "@/components/ui/calendar"
import { Button } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"

interface DatePickerProps {
  date?: Date | undefined
  onDateChange?: (date: Date | undefined) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  minDate?: Date
  maxDate?: Date
}

export default function DatePicker({ 
  date,
  onDateChange,
  placeholder = "Selecione uma data",
  className,
  disabled = false,
  minDate,
  maxDate
}: DatePickerProps) {
  const [internalDate, setInternalDate] = React.useState<Date | undefined>(date)

  const handleDateSelect = (newDate: Date | undefined) => {
    setInternalDate(newDate)
    onDateChange?.(newDate)
  }

  const formatDate = (selectedDate: Date | undefined) => {
    if (!selectedDate) {
      return placeholder
    }
    return format(selectedDate, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })
  }

  const currentDate = date !== undefined ? date : internalDate

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !currentDate && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {formatDate(currentDate)}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          defaultMonth={currentDate}
          selected={currentDate}
          onSelect={handleDateSelect}
          disabled={(date) => {
            if (minDate && date < minDate) return true
            if (maxDate && date > maxDate) return true
            return false
          }}
          className="rounded-lg"
          locale={ptBR}
        />
      </PopoverContent>
    </Popover>
  )
}
