'use client';

import { useFormContext } from 'react-hook-form';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DollarSign, CreditCard } from 'lucide-react';
import { DespesaFormValues, statusOptions } from '../../schemas/despesa-schema';
import { PaymentMethodOption } from '../../../components/types';
import DatePicker from '@/components/ui/calendar-01';
import { parseLocalDate } from '@/utils/format';

interface PaymentInfoSectionProps {
  paymentMethods: PaymentMethodOption[];
}

export function PaymentInfoSection({ paymentMethods }: PaymentInfoSectionProps) {
  const form = useFormContext<DespesaFormValues>();

  // Função para converter data para formato YYYY-MM-DD usando data local
  const formatDateToLocalString = (date: Date | undefined): string | undefined => {
    if (!date) return undefined;

    // Usar os métodos locais da data para evitar problemas de fuso horário
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  };
  const formatCurrency = (value: string) => {
    // Remove tudo que não é dígito
    const numericValue = value.replace(/\D/g, '');

    // Se não há valor numérico, retorna string vazia
    if (!numericValue || numericValue === '') {
      return '';
    }

    // Converte para número e formata
    const number = parseFloat(numericValue) / 100;

    // Verifica se o número é válido
    if (isNaN(number)) {
      return '';
    }

    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(number);
  };

  const handleAmountChange = (value: string, onChange: (value: string) => void) => {
    // Se o valor está vazio, limpa o campo
    if (!value || value.trim() === '') {
      onChange('');
      return;
    }

    const formatted = formatCurrency(value);
    onChange(formatted);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2 mb-6">
        <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <DollarSign className="h-5 w-5 text-green-600 dark:text-green-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Informações de Pagamento
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Valor e forma de pagamento
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Valor */}
        <FormField
          control={form.control}
          name="amount"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Valor
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                    <DollarSign className="h-4 w-4 text-gray-400" />
                  </div>
                  <Input
                    {...field}
                    placeholder="R$ 0,00"
                    onChange={(e) => handleAmountChange(e.target.value, field.onChange)}
                    className="pl-10 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:border-blue-400 dark:focus:border-blue-500 transition-colors duration-200"
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Método de Pagamento */}
        <FormField
          control={form.control}
          name="paymentMethod"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Método de Pagamento
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 z-10">
                    <CreditCard className="h-4 w-4 text-gray-400" />
                  </div>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <SelectTrigger className="pl-10 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:border-blue-400 dark:focus:border-blue-500">
                      <SelectValue placeholder="Selecione o método" />
                    </SelectTrigger>
                    <SelectContent>
                      {paymentMethods.map((method) => (
                        <SelectItem key={method.id} value={method.id}>
                          {method.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Data da Despesa */}
        <FormField
          control={form.control}
          name="date"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Data da Despesa
              </FormLabel>
              <FormControl>
                <DatePicker
                  date={field.value ? parseLocalDate(field.value) || undefined : undefined}
                  onDateChange={(date) => field.onChange(formatDateToLocalString(date))}
                  placeholder="Selecione a data da despesa"
                  className="bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:border-blue-400 dark:focus:border-blue-500 transition-colors duration-200"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Data de Vencimento */}
        <FormField
          control={form.control}
          name="dueDate"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Data de Vencimento (Opcional)
              </FormLabel>
              <FormControl>
                <DatePicker
                  date={field.value ? parseLocalDate(field.value) || undefined : undefined}
                  onDateChange={(date) => field.onChange(formatDateToLocalString(date))}
                  placeholder="Selecione a data de vencimento"
                  className="bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:border-blue-400 dark:focus:border-blue-500 transition-colors duration-200"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Status */}
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem className="md:col-span-2">
              <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Status
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger className="bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:border-blue-400 dark:focus:border-blue-500">
                    <SelectValue placeholder="Selecione o status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
