'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useFieldPermission } from '@/services/permissions';
import { AddressFields, EMPTY_ADDRESS, UseAddressProps } from './types';
import { parseAddressString, formatAddressFromFields, validateAddressFields, formatPostalCode } from './utils';
import { useUpdateProfileCache } from '@/hooks/user/use-user-context';

export const useAddress = ({ 
  address, 
  userId, 
  useDirectFields = true,
  onAddressUpdated,
  getCurrentValue,
  setFieldValue
}: UseAddressProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [wasRecentlyEdited, setWasRecentlyEdited] = useState(false);
  const [error, setError] = useState<string | undefined>();
  const [addressFields, setAddressFields] = useState<AddressFields>(EMPTY_ADDRESS);
  
  const { canEditField } = useFieldPermission();
  const isAllowed = canEditField('address', userId);
  const { updateAddressCache } = useUpdateProfileCache();

  useEffect(() => {
    if (wasRecentlyEdited) {
      const timer = setTimeout(() => {
        setWasRecentlyEdited(false);
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, [wasRecentlyEdited]);

  useEffect(() => {
    if (!isEditing) {
      if (useDirectFields) {
        const street = getCurrentValue('street');
        const street_number = getCurrentValue('street_number');
        const complement = getCurrentValue('complement');
        const neighborhood = getCurrentValue('neighborhood');
        const city = getCurrentValue('city');
        const state = getCurrentValue('state');
        const postal_code = getCurrentValue('postal_code');
        
        const hasAnyAddressData = street || city || postal_code;
        
        if (!hasAnyAddressData && address) {
          parseAddressToState(address);
        } else {
          const isNeighborhoodDuplicated = neighborhood === street_number && street_number;
          
          const fields: AddressFields = {
            street: street || '',
            street_number: street_number || '',
            complement: complement || '',
            neighborhood: isNeighborhoodDuplicated ? '' : (neighborhood || ''),
            city: city || '',
            state: state || '',
            postal_code: postal_code || ''
          };
          setAddressFields(fields);
        }
      } else {
        const currentAddress = getCurrentValue('address') || address;
        parseAddressToState(currentAddress);
      }
    }
  }, [address, getCurrentValue, isEditing, useDirectFields]);

  const parseAddressToState = (addressString: string) => {
    if (!addressString) {
      setAddressFields(EMPTY_ADDRESS);
      return;
    }
    
    const parsedFields = parseAddressString(addressString);
    setAddressFields(parsedFields);
  };

  const formatAddressForDisplay = (): string[] => {
    if (useDirectFields) {
      const street = getCurrentValue('street') || addressFields.street;
      const streetNumber = getCurrentValue('street_number') || addressFields.street_number;
      const complement = getCurrentValue('complement') || addressFields.complement;
      const neighborhood = getCurrentValue('neighborhood') || addressFields.neighborhood;
      const city = getCurrentValue('city') || addressFields.city;
      const state = getCurrentValue('state') || addressFields.state;
      const postalCode = getCurrentValue('postal_code') || addressFields.postal_code;
      
      const hasAddressInfo = street || streetNumber || complement || neighborhood || city || state || postalCode;
      
      if (!hasAddressInfo) {
        const legacyAddress = getCurrentValue('address');
        if (legacyAddress) {
          return legacyAddress.split('\n').map((line: string) => line.trim()).filter((line: string) => line);
        }
        return ['Não informado'];
      }
      
      const addressParts = [];
      
      if (street) {
        addressParts.push(street);
      }
      
      if (streetNumber) {
        addressParts.push(`Nº ${streetNumber}`);
      }
      
      if (complement || neighborhood) {
        let line = '';
        if (complement) line += complement;
        if (neighborhood) line += complement && neighborhood ? `, ${neighborhood}` : neighborhood;
        addressParts.push(line);
      }
      
      if (city || state) {
        let line = '';
        if (city) line += city;
        if (state) line += city && state ? ` - ${state}` : state;
        addressParts.push(line);
      }
      
      if (postalCode) {
        addressParts.push(`CEP: ${postalCode}`);
      }
      
      return addressParts.length > 0 ? addressParts : ['Não informado'];
    } else {
      if (!address && !getCurrentValue('address')) return ['Não informado'];
      
      const currentAddress = getCurrentValue('address') || address;
      return currentAddress.split('\n').map((line: string) => line.trim()).filter((line: string) => line);
    }
  };

  const handleFieldChange = (field: keyof AddressFields, value: string) => {
    if (field === 'state') {
      value = value.toUpperCase();
    }
    
    setAddressFields(prev => ({
      ...prev,
      [field]: value
    }));
    
    if (error) setError(undefined);
  };

  const handlePostalCodeChange = (value: string) => {
    const sanitizedValue = value.replace(/[^\d-]/g, '');
    
    const limitedValue = sanitizedValue.slice(0, 9);
    
    handleFieldChange('postal_code', limitedValue);
  };

  const handleEdit = () => {
    if (isAllowed) {
      setIsEditing(true);
    }
  };

  const handleCancel = () => {
    setError(undefined);
    setIsEditing(false);
    
    if (useDirectFields) {
      const fields: AddressFields = {
        street: getCurrentValue('street') || '',
        street_number: getCurrentValue('street_number') || '',
        complement: getCurrentValue('complement') || '',
        neighborhood: getCurrentValue('neighborhood') || '',
        city: getCurrentValue('city') || '',
        state: getCurrentValue('state') || '',
        postal_code: getCurrentValue('postal_code') || ''
      };
      setAddressFields(fields);
    } else {
      const currentAddress = getCurrentValue('address') || address;
      parseAddressToState(currentAddress);
    }
  };

  const handleConfirm = async () => {
    const validationError = validateAddressFields(addressFields);
    if (validationError) {
      setError(validationError);
      return;
    }
    
    setIsSaving(true);
    
    try {
      let success = true;
      let fieldsToUpdate: Record<string, string | null> = {};
      let formattedAddress = '';
      
      if (useDirectFields) {
        if (addressFields.postal_code) {
          addressFields.postal_code = formatPostalCode(addressFields.postal_code);
        }
        
        let cleanedFields = { ...addressFields };
        
        if (/^\d+$/.test(cleanedFields.neighborhood)) {
          cleanedFields.neighborhood = '';
        }
        
        fieldsToUpdate = {
          street: cleanedFields.street || null,
          street_number: cleanedFields.street_number || null,
          complement: cleanedFields.complement || null,
          neighborhood: cleanedFields.neighborhood || null,
          city: cleanedFields.city || null,
          state: cleanedFields.state || null,
          postal_code: cleanedFields.postal_code || null
        };
        
        success = await setFieldValue('address_batch', fieldsToUpdate);
      } else {
        formattedAddress = formatAddressFromFields(addressFields);
        success = await setFieldValue('address', formattedAddress);
      }
      
      if (success) {
        setError(undefined);
        setIsEditing(false);
        setWasRecentlyEdited(true);
        
        if (userId) {
          const updatedAddressData = useDirectFields ? fieldsToUpdate : { address: formattedAddress };
          
          updateAddressCache(userId, updatedAddressData);
        }
        
        if (typeof onAddressUpdated === 'function') {
          onAddressUpdated();
        }
        
        toast.success('Endereço atualizado com sucesso');
      } else {
        setError('Não foi possível salvar. Tente novamente ou cancele.');
      }
    } catch (error) {
      console.error('Erro ao salvar endereço:', error);
      setError('Erro ao salvar. Tente novamente ou cancele.');
    } finally {
      setIsSaving(false);
    }
  };

  return {
    isEditing,
    isSaving,
    wasRecentlyEdited,
    error,
    addressFields,
    isAllowed,
    handleEdit,
    handleCancel,
    handleConfirm,
    handleFieldChange,
    handlePostalCodeChange,
    formatAddressForDisplay
  };
}; 