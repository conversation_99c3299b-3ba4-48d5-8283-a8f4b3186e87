'use client';

import { Info } from 'lucide-react';
import { UseFormRegister, FieldErrors, UseFormSetValue } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import FormFieldWrapper from '../FormFieldWrapper';
import { FormValues } from '../validation';
import { categories } from '../constants';

interface BasicInfoSectionProps {
  register: UseFormRegister<FormValues>;
  errors: FieldErrors<FormValues>;
  setValue: UseFormSetValue<FormValues>;
  watchedValues: FormValues;
}

export default function BasicInfoSection({
  register,
  errors,
  setValue,
  watchedValues
}: BasicInfoSectionProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Info className="h-5 w-5" />
          Informações Básicas
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormFieldWrapper 
            label="Nome da Turma" 
            required 
            error={errors.name?.message}
            hint="Use um nome descritivo que identifique claramente a turma"
          >
            <Input
              {...register('name')}
              placeholder="Ex: Jiu-Jitsu Infantil"
              className={errors.name ? 'border-red-500' : ''}
            />
          </FormFieldWrapper>

          <FormFieldWrapper label="Categoria" required error={errors.category?.message}>
            <Select
              value={watchedValues.category}
              onValueChange={(value) => setValue('category', value, { shouldValidate: true })}
            >
              <SelectTrigger className={errors.category ? 'border-red-500' : ''}>
                <SelectValue placeholder="Selecione uma categoria" />
              </SelectTrigger>
              <SelectContent>
                {categories.map(category => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormFieldWrapper>
        </div>

        <FormFieldWrapper label="Descrição" required error={errors.description?.message}>
          <Textarea
            {...register('description')}
            placeholder="Descreva os objetivos e características da turma..."
            rows={3}
            className={errors.description ? 'border-red-500' : ''}
          />
        </FormFieldWrapper>
      </CardContent>
    </Card>
  );
} 