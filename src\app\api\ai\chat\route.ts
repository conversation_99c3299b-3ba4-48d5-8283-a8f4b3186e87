import { NextRequest, NextResponse } from 'next/server';
import { checkApiAuth } from '@/services/auth/actions/api-auth-actions';
import { AIChatUsageLimitService } from '@/services/ai/usage-limit-service';
import { createAuthClient } from '@/services/supabase/server/auth-client';
import { z } from 'zod';

// Schema de validação para a requisição
const chatRequestSchema = z.object({
  message: z.string().min(1, 'Mensagem é obrigatória').max(4000, 'Mensagem muito longa'),
  conversationId: z.string().optional(),
  context: z.object({
    userRole: z.string().optional(),
    tenantId: z.string().optional(),
    feature: z.string().optional(),
  }).optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticação
    const authCheck = await checkApiAuth();
    if (!authCheck.authenticated || !authCheck.user) {
      return NextResponse.json(
        { error: 'Não autorizado. Faça login para continuar.' },
        { status: 401 }
      );
    }

    const userId = authCheck.user.id;
    
    // Buscar tenant_id do usuário via metadata ou outra forma
    const { supabase } = await createAuthClient();
    const { data: userProfile, error: userError } = await supabase
      .from('users')
      .select('tenant_id')
      .eq('id', userId)
      .single();

    if (userError || !userProfile?.tenant_id) {
      return NextResponse.json(
        { error: 'Usuário não possui tenant associado.' },
        { status: 400 }
      );
    }

    const tenantId = userProfile.tenant_id;

    // Validar dados da requisição
    const body = await request.json();
    const validationResult = chatRequestSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Dados inválidos',
          details: validationResult.error.format()
        },
        { status: 400 }
      );
    }

    const { message, conversationId, context } = validationResult.data;

    // Verificar limite de mensagens diárias
    const usageStats = await AIChatUsageLimitService.canUserSendMessage(userId, tenantId);
    
    if (!usageStats.canSendMessage) {
      return NextResponse.json(
        { 
          error: `Limite diário de ${usageStats.messagesLimit} mensagens atingido. Tente novamente amanhã.`,
          usageStats,
          code: 'DAILY_LIMIT_EXCEEDED'
        },
        { status: 429 }
      );
    }

    // Verificar se a API key do Gemini está configurada
    const geminiApiKey = process.env.GEMINI_API_KEY;
    if (!geminiApiKey) {
      console.error('GEMINI_API_KEY não configurada');
      return NextResponse.json(
        { error: 'Serviço de IA temporariamente indisponível' },
        { status: 503 }
      );
    }

    // Preparar o prompt do sistema
    const systemPrompt = generateSystemPrompt(context);
    
    // Chamar a API do Gemini
    const geminiResponse = await callGeminiAPI(geminiApiKey, message, systemPrompt);
    
    if (!geminiResponse.success) {
      console.error('Erro na API do Gemini:', geminiResponse.error);
      return NextResponse.json(
        { error: 'Erro ao processar sua mensagem. Tente novamente.' },
        { status: 500 }
      );
    }

    // Incrementar contador de mensagens (apenas se a resposta foi bem-sucedida)
    try {
      await AIChatUsageLimitService.incrementMessageCount(userId, tenantId);
    } catch (incrementError) {
      console.error('Erro ao incrementar contador de mensagens:', incrementError);
      // Não falhar a resposta por causa disso, mas registrar o erro
    }

    // Obter estatísticas atualizadas
    const updatedUsageStats = await AIChatUsageLimitService.getUserUsageStats(userId);

    // Retornar resposta
    return NextResponse.json({
      success: true,
      response: geminiResponse.text,
      conversationId: conversationId || generateConversationId(),
      timestamp: new Date().toISOString(),
      usageStats: updatedUsageStats,
    });

  } catch (error) {
    console.error('Erro interno na API de chat:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// Função para gerar prompt do sistema baseado no contexto
function generateSystemPrompt(context?: {
  userRole?: string;
  tenantId?: string;
  feature?: string;
}): string {
  let systemPrompt = `Você é um assistente IA especializado em sistemas de gerenciamento de academias de artes marciais. 
Você deve ser útil, preciso e sempre responder em português brasileiro. Você faz parte do sistema ApexSaaS.
Mantenha suas respostas concisas mas informativas.`;

  if (context?.userRole) {
    systemPrompt += `\n\nO usuário atual tem o papel de: ${context.userRole}`;
  }

  if (context?.feature) {
    systemPrompt += `\n\nO usuário está atualmente na funcionalidade: ${context.feature}`;
  }

  systemPrompt += `\n\nÁreas principais do sistema que você pode ajudar:
- Gestão de alunos e instrutores
- Sistema de graduações e faixas
- Controle de mensalidades
- Relatórios e estatísticas
- Configurações gerais

Sempre seja profissional e educado. Se não souber algo específico sobre o sistema, seja honesto e sugira onde o usuário pode encontrar mais informações.`;

  return systemPrompt;
}

// Função para chamar a API do Gemini
async function callGeminiAPI(
  apiKey: string, 
  message: string, 
  systemPrompt: string
): Promise<{ success: boolean; text?: string; error?: string }> {
  try {
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${apiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: `${systemPrompt}\n\nUsuário: ${message}`
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
          },
          safetySettings: [
            {
              category: "HARM_CATEGORY_HARASSMENT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_HATE_SPEECH",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
              category: "HARM_CATEGORY_DANGEROUS_CONTENT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            }
          ]
        })
      }
    );

    if (!response.ok) {
      const errorData = await response.text();
      console.error('Erro da API Gemini:', response.status, errorData);
      return { 
        success: false, 
        error: `API Gemini retornou erro ${response.status}` 
      };
    }

    const data = await response.json();

    if (!data.candidates || data.candidates.length === 0) {
      return { 
        success: false, 
        error: 'Nenhuma resposta foi gerada pela IA' 
      };
    }

    const candidate = data.candidates[0];
    
    if (candidate.finishReason === 'SAFETY') {
      return { 
        success: false, 
        error: 'Mensagem bloqueada por políticas de segurança' 
      };
    }

    if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
      return { 
        success: false, 
        error: 'Resposta vazia da IA' 
      };
    }

    const text = candidate.content.parts[0].text;
    return { success: true, text };

  } catch (error) {
    console.error('Erro ao chamar API do Gemini:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Erro desconhecido' 
    };
  }
}

// Função para gerar ID único da conversa
function generateConversationId(): string {
  return `conv_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
} 