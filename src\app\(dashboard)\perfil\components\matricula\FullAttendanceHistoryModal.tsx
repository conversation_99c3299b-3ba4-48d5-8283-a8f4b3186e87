'use client';

import { useState } from 'react';
import { useQuery, keepPreviousData } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  CheckCircle,
  Clock,
  Search,
  RefreshCw,
  Calendar,
  User,
  Activity,
  ChevronLeft,
  ChevronRight,
  Filter,
  X,
} from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface FullAttendanceHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  studentId: string;
}

interface AttendanceFilters {
  page: number;
  limit: number;
  search: string;
  dateFrom: string;
  dateTo: string;
  sortBy: 'checked_in_at' | 'class_name';
  sortOrder: 'asc' | 'desc';
}

interface AttendanceRecord {
  id: string;
  checked_in_at: string;
  notes?: string;
  student: {
    id: string;
    user: {
      first_name: string;
      last_name?: string;
      full_name?: string;
      avatar_url?: string;
    };
  };
  class: {
    id: string;
    name: string;
    start_time: string;
    end_time: string;
  };
  checked_in_by_user: {
    first_name: string;
    last_name?: string;
    full_name?: string;
  };
}

interface AttendanceResponse {
  success: boolean;
  data?: {
    data: AttendanceRecord[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
  errors?: any;
}

const formatAttendanceDateTime = (dateTime: string) => {
  try {
    const date = new Date(dateTime);
    if (isNaN(date.getTime())) {
      return 'Data inválida';
    }
    return format(date, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
  } catch (error) {
    console.error('Erro ao formatar data de presença:', error);
    return 'Data inválida';
  }
};

const formatClassTime = (startTime: string, endTime: string) => {
  try {
    const startDate = new Date(startTime);
    const endDate = new Date(endTime);
    
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return 'Horário inválido';
    }
    
    const start = format(startDate, "HH:mm");
    const end = format(endDate, "HH:mm");
    return `${start} - ${end}`;
  } catch (error) {
    console.error('Erro ao formatar horário da aula:', error);
    return 'Horário inválido';
  }
};

export function FullAttendanceHistoryModal({
  isOpen,
  onClose,
  studentId,
}: FullAttendanceHistoryModalProps) {
  const [filters, setFilters] = useState<AttendanceFilters>({
    page: 1,
    limit: 20,
    search: '',
    dateFrom: '',
    dateTo: '',
    sortBy: 'checked_in_at',
    sortOrder: 'desc',
  });

  const [showFilters, setShowFilters] = useState(false);

  // Fetch attendance data using React Query
  const {
    data: attendanceData,
    isLoading,
    isError,
    error,
    refetch,
    isFetching,
  } = useQuery<AttendanceResponse>({
    queryKey: ['student-attendance', studentId, filters],
    queryFn: async () => {
      const response = await fetch('/api/students/attendance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          student_id: studentId,
          ...filters,
          date_from: filters.dateFrom || undefined,
          date_to: filters.dateTo || undefined,
          sort_by: filters.sortBy,
          sort_order: filters.sortOrder,
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao buscar histórico de presença');
      }

      return response.json();
    },
    enabled: isOpen && !!studentId,
    staleTime: 1000 * 60 * 5, // 5 minutos
    placeholderData: keepPreviousData,
  });

  const handleFilterChange = (key: keyof AttendanceFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      // Reset page when changing filters
      page: key !== 'page' ? 1 : value,
    }));
  };

  const handleSearch = (searchTerm: string) => {
    handleFilterChange('search', searchTerm);
  };

  const handleClearFilters = () => {
    setFilters({
      page: 1,
      limit: 20,
      search: '',
      dateFrom: '',
      dateTo: '',
      sortBy: 'checked_in_at',
      sortOrder: 'desc',
    });
  };

  const attendance = attendanceData?.data?.data || [];
  const pagination = attendanceData?.data?.pagination;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden flex flex-col bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
        <DialogHeader className="flex-shrink-0 border-b border-slate-200 dark:border-slate-700 pb-4">
          <DialogTitle className="flex items-center gap-3">
            <Activity className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            <span className="text-gray-900 dark:text-gray-100">Histórico Completo de Presença</span>
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col bg-slate-50 dark:bg-slate-900 p-6 -mx-6 -mb-6">
          {/* Filters and Search */}
          <div className="flex-shrink-0 mb-6">
            <div className="flex items-center gap-4 mb-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400 dark:text-slate-500" />
                <Input
                  placeholder="Buscar por nome da aula..."
                  value={filters.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10 bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700 text-gray-900 dark:text-gray-100 placeholder:text-slate-500 dark:placeholder:text-slate-400"
                />
              </div>
              
              {/* <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 border-slate-200 dark:border-slate-700 text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700"
              >
                <Filter className="h-4 w-4" />
                Filtros
              </Button> */}
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                disabled={isFetching}
                className="flex items-center gap-2 border-slate-200 dark:border-slate-700 text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700"
              >
                <RefreshCw className={`h-4 w-4 ${isFetching ? 'animate-spin' : ''}`} />
                Atualizar
              </Button>
            </div>

            {showFilters && (
              <Card className="p-4 bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block text-gray-600 dark:text-gray-400">Data Inicial</label>
                    <Input
                      type="date"
                      value={filters.dateFrom}
                      onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                      className="bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700 text-gray-900 dark:text-gray-100"
                    />
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-2 block text-gray-600 dark:text-gray-400">Data Final</label>
                    <Input
                      type="date"
                      value={filters.dateTo}
                      onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                      className="bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700 text-gray-900 dark:text-gray-100"
                    />
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-2 block text-gray-600 dark:text-gray-400">Ordenar por</label>
                    <select
                      value={`${filters.sortBy}-${filters.sortOrder}`}
                      onChange={(e) => {
                        const [sortBy, sortOrder] = e.target.value.split('-') as [typeof filters.sortBy, typeof filters.sortOrder];
                        setFilters(prev => ({ ...prev, sortBy, sortOrder }));
                      }}
                      className="w-full px-3 py-2 border border-slate-200 dark:border-slate-700 rounded-md bg-white dark:bg-slate-800 text-gray-900 dark:text-gray-100"
                    >
                      <option value="checked_in_at-desc">Mais Recente</option>
                      <option value="checked_in_at-asc">Mais Antigo</option>
                      <option value="class_name-asc">Nome da Aula (A-Z)</option>
                      <option value="class_name-desc">Nome da Aula (Z-A)</option>
                    </select>
                  </div>
                </div>
                
                <div className="flex justify-end mt-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClearFilters}
                    className="flex items-center gap-2 text-slate-600 dark:text-slate-400 hover:text-gray-900 dark:hover:text-gray-100"
                  >
                    <X className="h-4 w-4" />
                    Limpar Filtros
                  </Button>
                </div>
              </Card>
            )}
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-8 w-8 animate-spin text-blue-600 dark:text-blue-400" />
                <span className="ml-2 text-slate-600 dark:text-slate-400">Carregando histórico...</span>
              </div>
            ) : isError ? (
              <div className="text-center py-8">
                <Activity className="h-12 w-12 mx-auto text-red-400 dark:text-red-500 mb-4" />
                <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Erro ao carregar dados
                </h4>
                <p className="text-slate-500 dark:text-slate-400 mb-4">
                  {error instanceof Error ? error.message : 'Erro desconhecido'}
                </p>
                <Button 
                  onClick={() => refetch()} 
                  variant="outline"
                  className="border-slate-200 dark:border-slate-700 text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700"
                >
                  Tentar novamente
                </Button>
              </div>
            ) : attendance.length === 0 ? (
              <div className="text-center py-8">
                <Activity className="h-12 w-12 mx-auto text-slate-400 dark:text-slate-500 mb-4" />
                <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Nenhuma presença encontrada
                </h4>
                <p className="text-slate-500 dark:text-slate-400">
                  Não há registros de presença para os filtros selecionados.
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {attendance.map((record) => (
                  <div
                    key={record.id}
                    className="flex items-center justify-between p-4 rounded-lg bg-white dark:bg-slate-800 hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors border border-slate-200 dark:border-slate-700"
                  >
                    <div className="flex items-start gap-3 flex-1">
                      <div className="flex-shrink-0 mt-1">
                        <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                              {record.class.name}
                            </h4>
                            <div className="flex items-center gap-4 text-sm text-slate-600 dark:text-slate-400 mt-1">
                              <span className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {formatAttendanceDateTime(record.checked_in_at)}
                              </span>
                              <span className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {formatClassTime(record.class.start_time, record.class.end_time)}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        {record.notes && (
                          <div className="mt-2 p-2 bg-slate-50 dark:bg-slate-700/50 rounded text-sm text-gray-900 dark:text-gray-100">
                            <strong>Observações:</strong> {record.notes}
                          </div>
                        )}
                        
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="secondary" className="text-xs bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400">
                            <User className="h-3 w-3 mr-1" />
                            Check-in por: {record.checked_in_by_user.full_name || record.checked_in_by_user.first_name}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="flex-shrink-0 border-t border-slate-200 dark:border-slate-700 pt-4 mt-4">
              <div className="flex items-center justify-between">
                <div className="text-sm text-slate-600 dark:text-slate-400">
                  Mostrando {((pagination.page - 1) * pagination.limit) + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} de {pagination.total} registros
                </div>
                
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleFilterChange('page', pagination.page - 1)}
                    disabled={!pagination.hasPrev || isFetching}
                    className="border-slate-200 dark:border-slate-700 text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700"
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Anterior
                  </Button>
                  
                  <span className="text-sm font-medium px-3 py-1 text-gray-900 dark:text-gray-100">
                    {pagination.page} de {pagination.totalPages}
                  </span>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleFilterChange('page', pagination.page + 1)}
                    disabled={!pagination.hasNext || isFetching}
                    className="border-slate-200 dark:border-slate-700 text-slate-600 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700"
                  >
                    Próximo
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
} 