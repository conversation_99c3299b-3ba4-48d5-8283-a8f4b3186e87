'use client';

import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { 
  MoreHorizontal, 
  Mail, 
  Phone, 
  MapPin, 
  Award, 
  Clock, 
  FileText,
  CreditCard,
  CheckCircle,
  XCircle,
  Star
} from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { Instructor } from "../../types/types";
import { beltColorTranslation, BeltColor } from "@/components/belt";
import { BeltWithDetails } from "@/components/belt/BeltWithDetails";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { formatPhoneNumber } from "@/hooks/form/usePhoneFormat";
import { formatPaymentModel } from "@/src/utils/format-utils";

interface InstructorCardProps {
  instructor: Instructor;
  isSelecting?: boolean;
  isSelected?: boolean;
  onSelect?: () => void;
  onRefresh?: () => void;
}

export function InstructorCard({ 
  instructor, 
  isSelecting = false,
  isSelected = false,
  onSelect,
  onRefresh
}: InstructorCardProps) {
  // Função para gerar as iniciais do nome
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Função para obter o label do status
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Ativo';
      case 'inactive':
        return 'Inativo';
      case 'suspended':
        return 'Suspenso';
      default:
        return 'Desconhecido';
    }
  };

  // Função para obter a variante do badge baseada no status
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'default';
      case 'inactive':
        return 'secondary';
      case 'suspended':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  // Função para obter cor do badge de especialidade
  const getSpecialtyBadgeVariant = (specialty: string) => {
    const specialtyColors: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      'Jiu-Jitsu': 'default',
      'Muay Thai': 'secondary',
      'Boxe': 'destructive',
      'MMA': 'outline',
    };
    return specialtyColors[specialty] || 'outline';
  };

  // Função para obter variante do badge de contrato
  const getContractBadgeVariant = (contractType: string | null): "default" | "secondary" | "destructive" | "outline" => {
    switch (contractType) {
      case 'CLT': return 'default';
      case 'PJ': return 'secondary';
      case 'Freelancer': return 'outline';
      default: return 'outline';
    }
  };

  // Função para formatar nomes de modelo de pagamento
  const formatPaymentModel = (paymentModel: string | null | undefined): string => {
    switch (paymentModel) {
      case 'hora_aula': return 'Por Hora/Aula';
      case 'salario_mensal': return 'Salário Mensal';
      case 'comissao': return 'Comissão';
      case 'participacao_lucros': return 'Participação nos Lucros';
      default: return paymentModel || 'Não informado';
    }
  };

  // Função para obter ícone do tipo de contrato
  const getContractIcon = (contractType: string | null) => {
    switch (contractType) {
      case 'CLT': return <FileText className="h-3 w-3" />;
      case 'PJ': return <CreditCard className="h-3 w-3" />;
      case 'Freelancer': return <Star className="h-3 w-3" />;
      default: return <FileText className="h-3 w-3" />;
    }
  };
  
  // Lidar com a alteração de status
  const handleStatusToggle = async () => {
    try {
      const response = await fetch(`/api/instructors/${instructor.id}/toggle-status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Falha ao atualizar status');
      }
      
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
    }
  };

  // Usar o status centralizado se disponível, senão usar is_active como fallback
  const status = instructor.status || (instructor.is_active ? 'active' : 'inactive');

  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow bg-card">
      <CardContent className="p-0">
        <div className="p-6">
          <div className="flex items-start gap-4">
            {isSelecting && (
              <div className="flex items-center h-14 pr-2">
                <Checkbox 
                  checked={isSelected}
                  onCheckedChange={() => onSelect && onSelect()}
                  className="border-gray-300 dark:border-gray-500"
                />
              </div>
            )}
            
            <Avatar className="h-14 w-14 border-2 border-muted">
              <AvatarImage src={instructor.avatar || ""} alt={instructor.name} />
              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold text-lg">
                {getInitials(instructor.name)}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 space-y-3 min-w-0">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-lg truncate">
                    <Link 
                      href={`/perfil/${instructor.id}?from=instrutores`} 
                      className="hover:text-primary transition-colors hover:underline"
                    >
                      {instructor.name}
                    </Link>
                  </h3>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground mt-1">
                    <Mail className="h-3 w-3 flex-shrink-0" />
                    <span className="truncate">{instructor.email}</span>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0 hover:bg-muted">
                      <span className="sr-only">Abrir menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem asChild>
                      <Link href={`/perfil/${instructor.id}?from=instrutores`} className="flex items-center w-full">
                        Ver perfil
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleStatusToggle} className="cursor-pointer">
                      {status === 'active' ? 'Inativar' : 'Ativar'}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Informações de contato */}
              <div className="space-y-2">
                {instructor.phone && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Phone className="h-3 w-3 flex-shrink-0" />
                    <span>{formatPhoneNumber(instructor.phone)}</span>
                  </div>
                )}
                {instructor.branch_name && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <MapPin className="h-3 w-3 flex-shrink-0" />
                    <span className="truncate">{instructor.branch_name}</span>
                  </div>
                )}
              </div>

              {/* Status */}
              <div className="flex items-center justify-between">
                <Badge 
                  variant={getStatusBadgeVariant(status)}
                  className="flex items-center gap-1 font-medium"
                >
                  {status === 'active' ? (
                    <>
                      <CheckCircle className="h-3 w-3" />
                      {getStatusLabel(status)}
                    </>
                  ) : status === 'suspended' ? (
                    <>
                      <XCircle className="h-3 w-3" />
                      {getStatusLabel(status)}
                    </>
                  ) : (
                    <>
                      <XCircle className="h-3 w-3" />
                      {getStatusLabel(status)}
                    </>
                  )}
                </Badge>
                
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  <span>
                    {instructor.experience_years 
                      ? `${instructor.experience_years} ${instructor.experience_years === 1 ? 'ano' : 'anos'}`
                      : 'N/A'
                    }
                  </span>
                </div>
              </div>

              {/* Especialidades */}
              <div className="space-y-2">
                <div className="flex items-center gap-1 text-sm font-medium">
                  <Award className="h-3 w-3" />
                  <span>Especialidades</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {instructor.specialties && instructor.specialties.length > 0 ? (
                    instructor.specialties.map((specialty, index) => (
                      <Badge 
                        key={index}
                        variant={getSpecialtyBadgeVariant(specialty)}
                        className="text-xs font-medium"
                      >
                        {specialty}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-sm text-muted-foreground">Não informado</span>
                  )}
                </div>
              </div>

              {/* Faixa atual */}
              {instructor.current_belt && (
                <div className="space-y-2">
                  <div className="flex items-center gap-1 text-sm font-medium">
                    <Award className="h-3 w-3" />
                    <span>Faixa Atual</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <BeltWithDetails 
                      color={instructor.current_belt} 
                      degree={instructor.current_belt_degree}
                      label={instructor.beltDetails?.label}
                      stripeColor={instructor.beltDetails?.stripe_color}
                      showCenterLine={instructor.beltDetails?.show_center_line}
                      centerLineColor={instructor.beltDetails?.center_line_color}
                      size="sm" 
                    />
                    <span className="text-sm font-medium">
                      {instructor.current_belt_degree && instructor.current_belt_degree > 0 && (
                        <span className="text-muted-foreground ml-1">
                          ({instructor.current_belt_degree}° grau)
                        </span>
                      )}
                    </span>
                  </div>
                </div>
              )}

              {/* Informações contratuais */}
              <div className="grid grid-cols-2 gap-4 pt-2 border-t border-muted">
                {instructor.contract_type && (
                  <div className="space-y-1">
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      {getContractIcon(instructor.contract_type)}
                      <span>Contrato</span>
                    </div>
                    <Badge 
                      variant={getContractBadgeVariant(instructor.contract_type)}
                      className="text-xs"
                    >
                      {instructor.contract_type}
                    </Badge>
                  </div>
                )}
                
                {instructor.payment_model && (
                  <div className="space-y-1">
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <CreditCard className="h-3 w-3" />
                      <span>Pagamento</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {formatPaymentModel(instructor.payment_model)}
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 