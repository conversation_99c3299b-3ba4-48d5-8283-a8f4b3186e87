'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Progress } from '@/components/ui/progress'
import { Button } from '@/components/ui/button'
import { Belt, beltColorTranslation } from '@/components/belt'
import { 
  GraduationCap, 
  Users, 
  Building2, 
  Calendar,
  Star,
  Trophy,
  TrendingUp,
  Clock,
  MapPin,
  User,
  AlertCircle,
  RotateCcw
} from 'lucide-react'
import { format } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { formatDateBrazil } from '@/utils/format'
import { useInstructorClassGroups, useInstructorTeachingStats, useInstructorTeachingCache } from '@/hooks/instrutores/use-instructor-teaching'
import type { ClassGroupData, EnsinoStats } from './types'

interface EnsinoTabProps {
  userId: string
}

export function EnsinoTab({ userId }: EnsinoTabProps) {
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Buscar turmas que o instrutor leciona usando o hook personalizado
  const { data: classGroups, isLoading, error, refetch, isFetching } = useInstructorClassGroups(userId)
  
  // Calcular estatísticas usando o hook personalizado
  const stats = useInstructorTeachingStats(classGroups)
  
  // Hook para gerenciar cache
  const { refreshClassGroups } = useInstructorTeachingCache(userId)

  // Função para atualizar os dados
  const handleRefresh = async () => {
    if (isRefreshing || isFetching) return

    setIsRefreshing(true)
    try {
      await refreshClassGroups()
    } catch (error) {
      console.error('Erro ao atualizar dados:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  const getCapacityColor = (usage?: number | null) => {
    if (!usage) return 'bg-gray-200'
    if (usage >= 90) return 'bg-red-500'
    if (usage >= 70) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  const getCategoryLabel = (category?: string | null) => {
    const categoryMap: Record<string, string> = {
      'kids': 'Infantil',
      'teens': 'Juvenil',
      'adults': 'Adulto',
      'seniors': 'Sênior'
    }
    return category ? categoryMap[category] || category : 'Não definida'
  }

  const getCategoryColor = (category?: string | null) => {
    const colorMap: Record<string, string> = {
      'kids': 'bg-blue-100 text-blue-800',
      'teens': 'bg-green-100 text-green-800',
      'adults': 'bg-purple-100 text-purple-800',
      'seniors': 'bg-orange-100 text-orange-800'
    }
    return category ? colorMap[category] || 'bg-gray-100 text-gray-800' : 'bg-gray-100 text-gray-800'
  }

  if (isLoading) {
    return (
      <div className="space-y-6 p-6 bg-slate-50 dark:bg-slate-900 rounded-b-lg">
        {/* Cards de Estatísticas com Loading */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="p-4 bg-white dark:bg-slate-800 animate-pulse">
              <div className="h-16 bg-slate-200 dark:bg-slate-700 rounded-md"></div>
            </Card>
          ))}
        </div>
        
        {/* Lista de Turmas com Loading */}
        <Card className="p-6 bg-white dark:bg-slate-800 animate-pulse">
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-32 bg-slate-200 dark:bg-slate-700 rounded-md"></div>
            ))}
          </div>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6 p-6 bg-slate-50 dark:bg-slate-900 rounded-b-lg">
        <Card className="p-6 bg-white dark:bg-slate-800">
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Erro ao carregar dados
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Não foi possível carregar as informações das turmas.
              </p>
            </div>
          </div>
        </Card>
      </div>
    )
  }

  if (!classGroups || classGroups.length === 0) {
    return (
      <div className="space-y-6 p-6 bg-slate-50 dark:bg-slate-900 rounded-b-lg">
        <Card className="p-6 bg-white dark:bg-slate-800">
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <GraduationCap className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Nenhuma turma encontrada
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Este instrutor ainda não possui turmas atribuídas.
              </p>
            </div>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6 bg-slate-50 dark:bg-slate-900 rounded-b-lg">
      {/* Cards de Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className={`p-4 bg-white dark:bg-slate-800 ${isFetching || isRefreshing ? 'opacity-70' : ''}`}>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <GraduationCap className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total de Turmas</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.totalClassGroups}</p>
            </div>
          </div>
        </Card>

        <Card className={`p-4 bg-white dark:bg-slate-800 ${isFetching || isRefreshing ? 'opacity-70' : ''}`}>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <Users className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total de Alunos</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.totalStudents}</p>
            </div>
          </div>
        </Card>

        <Card className={`p-4 bg-white dark:bg-slate-800 ${isFetching || isRefreshing ? 'opacity-70' : ''}`}>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
              <TrendingUp className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Ocupação Média</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.averageCapacityUsage}%</p>
            </div>
          </div>
        </Card>

        <Card className={`p-4 bg-white dark:bg-slate-800 ${isFetching || isRefreshing ? 'opacity-70' : ''}`}>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
              <Trophy className="w-5 h-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Turmas Ativas</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.activeClassGroups}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Lista de Turmas */}
      <Card className="p-6 bg-white dark:bg-slate-800">
        <CardHeader className="px-0 pt-0">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <GraduationCap className="w-5 h-5" />
              Turmas Lecionadas
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing || isFetching || isLoading}
              className="flex items-center gap-2"
            >
              <RotateCcw className={`w-4 h-4 ${(isRefreshing || isFetching) ? 'animate-spin' : ''}`} />
              {isRefreshing || isFetching ? 'Atualizando...' : 'Atualizar'}
            </Button>
          </div>
        </CardHeader>
        <CardContent className="px-0 pb-0">
          <div className="space-y-6">
            {classGroups.map((group) => (
              <div 
                key={group.id} 
                className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                {/* Cabeçalho da Turma */}
                <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4 mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                        {group.name}
                      </h3>
                      <Badge className={getCategoryColor(group.category)}>
                        {getCategoryLabel(group.category)}
                      </Badge>
                      {!group.is_active && (
                        <Badge variant="secondary">Inativa</Badge>
                      )}
                    </div>
                    
                    {group.description && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {group.description}
                      </p>
                    )}

                    <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center gap-1">
                        <Building2 className="w-4 h-4" />
                        {group.branch.name}
                      </div>
                      
                      {group.start_date && (
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          Início: {formatDateBrazil(group.start_date)}
                        </div>
                      )}

                      {(group.min_age || group.max_age) && (
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          Idade: {group.min_age || 0}-{group.max_age || '+'} anos
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Estatísticas da Turma */}
                  <div className="flex flex-col lg:items-end gap-2">
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4 text-gray-500" />
                      <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {group.totalStudents}
                        {group.max_capacity && ` / ${group.max_capacity}`} alunos
                      </span>
                    </div>
                    
                    {group.capacityUsage !== null && (
                      <div className="w-32">
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-xs text-gray-500">Ocupação</span>
                          <span className="text-xs font-medium">{group.capacityUsage}%</span>
                        </div>
                        <Progress 
                          value={group.capacityUsage} 
                          className="h-2"
                        />
                      </div>
                    )}
                  </div>
                </div>

                {/* Lista de Alunos */}
                {group.students.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      Alunos Matriculados ({group.students.length})
                    </h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                      {group.students.slice(0, 8).map((student) => (
                        <div 
                          key={student.id}
                          className="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg"
                        >
                          <Avatar className="w-8 h-8">
                            <AvatarImage src={student.avatar || undefined} />
                            <AvatarFallback className="text-xs">
                              {student.name.split(' ').map(n => n[0]).join('').slice(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                              {student.name}
                            </p>
                            {student.beltColor && (
                              <div className="flex items-center gap-1">
                                <Belt color={student.beltColor as any} size="xs" />
                                <span className="text-xs text-gray-500">
                                  {beltColorTranslation[student.beltColor as keyof typeof beltColorTranslation]}
                                  {typeof student.degree === 'number' && student.degree > 0 ? ` ${student.degree}º` : ''}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                      
                      {group.students.length > 8 && (
                        <div className="flex items-center justify-center p-2 bg-gray-50 dark:bg-gray-800 rounded-lg text-xs text-gray-500">
                          +{group.students.length - 8} alunos
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {group.students.length === 0 && (
                  <div className="text-center py-4">
                    <User className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Nenhum aluno matriculado nesta turma
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 