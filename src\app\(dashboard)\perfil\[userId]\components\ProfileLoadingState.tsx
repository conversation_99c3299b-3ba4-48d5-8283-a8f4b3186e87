'use client';

import { Skeleton } from "@/components/ui/skeleton";

export function ProfileLoadingState() {
  return (
    <div className="min-h-screen pb-12 animate-pulse">
      {/* PageHeader Skeleton */}
      <div className="shadow-sm border-b border-slate-200 dark:border-slate-700 h-16 flex items-center px-4 sm:px-6 lg:px-8">
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-24" />
        </div>
        <div className="ml-auto">
            <Skeleton className="h-8 w-24" />
        </div>
      </div>

      <main>
        {/* Profile Header Skeleton */}
        <div className="shadow-sm mb-6">
          <div className="px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
            <div className="flex flex-col md:flex-row gap-8">
              {/* Avatar Skeleton */}
              <div className="flex-shrink-0">
                <Skeleton className="h-32 w-32 rounded-full" />
              </div>
              
              {/* Info Skeleton */}
              <div className="flex-grow space-y-4">
                <Skeleton className="h-8 w-1/2" />
                <div className="flex items-center gap-4">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-24" />
                </div>
                <Skeleton className="h-4 w-1/4" />
                <div className="flex items-center gap-4 pt-2">
                    <Skeleton className="h-8 w-32" />
                    <Skeleton className="h-8 w-32" />
                </div>
              </div>
            </div>
            
            {/* Contact Cards Skeleton */}
            <div className="mt-8 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
              <Skeleton className="h-24 w-full rounded-lg" />
              <Skeleton className="h-24 w-full rounded-lg" />
              <Skeleton className="h-24 w-full rounded-lg" />
              <Skeleton className="h-24 w-full rounded-lg" />
            </div>
          </div>
        </div>
        
        {/* Tabs Skeleton */}
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="border-b border-slate-200 dark:border-slate-700">
            <div className="flex overflow-x-auto no-scrollbar space-x-6">
              <Skeleton className="h-12 w-24" />
              <Skeleton className="h-12 w-24" />
              <Skeleton className="h-12 w-24" />
              <Skeleton className="h-12 w-24" />
            </div>
          </div>
          
          {/* Tab Content Skeleton */}
          <div className="py-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Skeleton className="h-5 w-1/3" />
                <Skeleton className="h-8 w-2/3" />
              </div>
              <div className="space-y-4">
                <Skeleton className="h-5 w-1/3" />
                <Skeleton className="h-8 w-2/3" />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Skeleton className="h-5 w-1/3" />
                <Skeleton className="h-8 w-2/3" />
              </div>
              <div className="space-y-4">
                <Skeleton className="h-5 w-1/3" />
                <Skeleton className="h-8 w-2/3" />
              </div>
            </div>
            <Skeleton className="h-32 w-full" />
          </div>
        </div>
      </main>
    </div>
  );
} 