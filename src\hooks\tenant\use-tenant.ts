'use client';

import { useContext, useEffect, useState } from 'react';
import { TenantExtractorClient } from '@/services/tenant/tenant-extractor-client';
import { tenantCache } from '@/services/tenant/tenant-cache';
import { TenantUtils } from '@/services/tenant/tenant-utils';
import { TenantExtractionContext, TenantInfo } from '@/services/tenant/types';

interface UseTenantReturn {
  /** Slug do tenant atual */
  slug: string | null;
  /** Informações completas do tenant */
  tenantInfo: TenantInfo | null;
  /** Indica se está carregando informações do tenant */
  isLoading: boolean;
  /** Erro ao carregar tenant */
  error: string | null;
  /** Revalida as informações do tenant */
  revalidate: () => void;
}

/**
 * Hook otimizado para gerenciar informações de tenant no lado do cliente
 * 
 * Características:
 * - Cache automático de slugs extraídos
 * - Detecção multi-fonte (URL, headers, cookies)
 * - Carregamento lazy de informações do tenant
 * - Revalidação manual disponível
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   const { slug, tenantInfo, isLoading, error } = useTenant();
 * 
 *   if (isLoading) return <div>Carregando tenant...</div>;
 *   if (error) return <div>Erro: {error}</div>;
 *   if (!slug) return <div>Nenhum tenant detectado</div>;
 * 
 *   return <div>Tenant: {slug}</div>;
 * }
 * ```
 */
export function useTenant(): UseTenantReturn {
  const [slug, setSlug] = useState<string | null>(null);
  const [tenantInfo, setTenantInfo] = useState<TenantInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Função para extrair slug do tenant
  const extractSlug = (): string | null => {
    try {
      // Construir contexto de extração
      const context: TenantExtractionContext = {
        hostname: window.location.hostname,
        pathname: window.location.pathname,
        headers: {
          // Headers podem ser passados via meta tags ou outros meios
        },
        cookies: {
          // Ler cookies relevantes
          host: document.cookie
            .split('; ')
            .find(row => row.startsWith('host='))
            ?.split('=')[1] || '',
        }
      };

      // Verificar cache primeiro
      const cacheKey = `${context.hostname}|${(context.pathname || '').split('/')[1] || ''}`;
      const cachedSlug = tenantCache.get(cacheKey);
      if (cachedSlug) return cachedSlug;

      // Extrair usando utilitários
      const extractedSlug = TenantUtils.extractTenantSlug(context);
      
      // Validar e armazenar no cache
      if (extractedSlug && TenantUtils.isValidSlug(extractedSlug)) {
        tenantCache.set(cacheKey, extractedSlug);
        return extractedSlug;
      }

      return null;
    } catch (err) {
      console.error('Erro ao extrair slug do tenant:', err);
      return null;
    }
  };

  // Função para carregar informações do tenant
  const loadTenantInfo = async (tenantSlug: string): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      // Buscar informações do tenant via API
      const response = await fetch(`/api/tenant/${tenantSlug}`);
      if (response.status === 404) {
        // Tenant não encontrado – lidar silenciosamente
        setTenantInfo(null);
        setIsLoading(false);
        return;
      }

      if (!response.ok) {
        throw new Error(`Failed to fetch tenant info: ${response.statusText}`);
      }

      const info = await response.json();

      setTenantInfo(info);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao carregar informações do tenant';
      setError(errorMessage);
      console.error('Erro ao carregar tenant info:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Função para revalidar
  const revalidate = (): void => {
    // Limpar cache
    tenantCache.clear();
    
    // Re-extrair slug
    const newSlug = extractSlug();
    setSlug(newSlug);
    
    // Recarregar informações se houver slug
    if (newSlug) {
      loadTenantInfo(newSlug);
    } else {
      setTenantInfo(null);
      setIsLoading(false);
    }
  };

  // Effect principal para extrair slug e carregar informações
  useEffect(() => {
    const extractedSlug = extractSlug();
    setSlug(extractedSlug);

    if (extractedSlug) {
      loadTenantInfo(extractedSlug);
    } else {
      setIsLoading(false);
    }
  }, []);

  // Effect para reagir a mudanças na URL
  useEffect(() => {
    const handleLocationChange = () => {
      const newSlug = extractSlug();
      if (newSlug !== slug) {
        setSlug(newSlug);
        if (newSlug) {
          loadTenantInfo(newSlug);
        } else {
          setTenantInfo(null);
          setIsLoading(false);
        }
      }
    };

    // Escutar mudanças na URL
    window.addEventListener('popstate', handleLocationChange);
    
    return () => {
      window.removeEventListener('popstate', handleLocationChange);
    };
  }, [slug]);

  return {
    slug,
    tenantInfo,
    isLoading,
    error,
    revalidate
  };
}

/**
 * Hook simplificado que retorna apenas o slug do tenant
 * Útil quando você só precisa do slug e não das informações completas
 */
export function useTenantSlug(): string | null {
  const { slug } = useTenant();
  return slug;
}

export default useTenant; 