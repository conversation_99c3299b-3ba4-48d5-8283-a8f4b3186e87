export interface PaginationInfoProps {
  currentPage: number;
  pageSize: number;
  totalItems: number;
}

export function PaginationInfo({
  currentPage,
  pageSize,
  totalItems
}: PaginationInfoProps) {
  const startItem = Math.min((currentPage - 1) * pageSize + 1, totalItems);
  const endItem = Math.min(currentPage * pageSize, totalItems);
  
  return (
    <div className="text-sm text-muted-foreground">
      <p className="font-medium">
        Mostrando{" "}
        <span className="font-medium text-foreground">
          {startItem}
        </span>{" "}
        até{" "}
        <span className="font-medium text-foreground">
          {endItem}
        </span>{" "}
        de{" "}
        <span className="font-medium text-foreground">{totalItems}</span>{" "}
        resultados
      </p>
    </div>
  );
} 