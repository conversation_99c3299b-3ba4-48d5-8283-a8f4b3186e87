"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/services/supabase/server";
import { BulkEnrollStudentsSchema } from "../schemas/class-group-enrollment";
import { validateUserAuthentication, validateClassGroup } from "./shared/validation-helpers";

/**
 * Matricula múltiplos alunos em uma turma
 */
export async function bulkEnrollStudents(data: unknown) {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;

    const validationResult = BulkEnrollStudentsSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const { class_group_id, student_ids, notes } = validationResult.data;
    const supabase = await createAdminClient();

    // Verificar se a turma existe e está ativa
    const classGroupValidation = await validateClassGroup(class_group_id, tenantId);
    if (!classGroupValidation.success) {
      return classGroupValidation;
    }

    const { classGroup } = classGroupValidation;
    if (!classGroup.is_active) {
      return { success: false, errors: { class_group_id: "Turma não encontrada ou inativa" } };
    }

    // Verificar capacidade
    const { count: currentEnrollments } = await supabase
      .from("class_group_enrollments")
      .select("*", { count: "exact", head: true })
      .eq("class_group_id", class_group_id)
      .eq("status", "active");

    if (classGroup.max_capacity && currentEnrollments) {
      const availableSpots = classGroup.max_capacity - currentEnrollments;
      if (student_ids.length > availableSpots) {
        return { 
          success: false, 
          errors: { _form: `Apenas ${availableSpots} vagas disponíveis na turma` } 
        };
      }
    }

    // Verificar se algum aluno já está matriculado
    const { data: existingEnrollments } = await supabase
      .from("class_group_enrollments")
      .select("student_id")
      .eq("class_group_id", class_group_id)
      .eq("status", "active")
      .in("student_id", student_ids);

    if (existingEnrollments && existingEnrollments.length > 0) {
      const enrolledStudentIds = existingEnrollments.map(e => e.student_id);
      return { 
        success: false, 
        errors: { student_ids: `Alguns alunos já estão matriculados na turma` } 
      };
    }

    // Criar matrículas em lote
    const enrollmentsToInsert = student_ids.map((student_id: string) => ({
      class_group_id,
      student_id,
      tenant_id: tenantId,
      enrollment_date: new Date().toISOString(),
      status: "active" as const,
      notes,
    }));

    const { data: enrollments, error } = await supabase
      .from("class_group_enrollments")
      .insert(enrollmentsToInsert)
      .select();

    if (error) {
      console.error("Erro ao matricular alunos:", error);
      return { success: false, errors: { _form: "Erro ao matricular alunos" } };
    }

    revalidatePath("/aulas");
    return { 
      success: true, 
      data: enrollments, 
      message: `${student_ids.length} alunos matriculados com sucesso` 
    };
  } catch (error) {
    console.error("Erro ao matricular alunos:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 