'use server';

import { getPermissionService } from '../service';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';
import { ActionType, ResourceType } from '../types/permission-types';

/**
 * Verifica se o usuário atual possui a permissão especificada
 * @param permissionName Nome da permissão no formato 'resource:action'
 * @param context Contexto adicional para verificação da permissão
 * @returns Resultado da verificação (true/false)
 */
export async function hasPermission(
  permissionName: string,
  context?: Record<string, any>
): Promise<boolean> {
  try {
    const user = await getCurrentUser();
    if (!user) return false;
    
    const [resource, action] = permissionName.split(':');
    if (!resource || !action) {
      console.error(`Formato de permissão inválido: ${permissionName}. Use 'resource:action'`);
      return false;
    }
    
    const permissionService = await getPermissionService();
    const result = await permissionService.hasPermission(
      user.id,
      resource as any,
      action as any,
      context?.targetUserId
    );
    
    return result.granted;
  } catch (error) {
    console.error(`Erro ao verificar permissão ${permissionName}:`, error);
    return false;
  }
}

/**
 * Verifica se o usuário atual possui alguma das permissões especificadas
 * @param permissions Lista de permissões a serem verificadas
 * @param context Contexto adicional para verificação das permissões
 * @returns Resultado da verificação (true se tiver pelo menos uma permissão)
 */
export async function hasAnyPermission(
  permissions: string[],
  context?: Record<string, any>
): Promise<boolean> {
  for (const permission of permissions) {
    const hasPermissionResult = await hasPermission(permission, context);
    if (hasPermissionResult) {
      return true;
    }
  }
  
  return false;
}

/**
 * Server action para testar permissões (útil para debug)
 */
export async function debugCheckPermission(
  resource: ResourceType,
  action: ActionType,
  targetUserId?: string
) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return {
        success: false,
        error: 'Usuário não autenticado'
      };
    }
    
    console.log(`[DEBUG Permission] Testando permissão para usuário ${user.id}:`);
    console.log(`- Resource: ${resource}`);
    console.log(`- Action: ${action}`);
    console.log(`- Target: ${targetUserId || 'N/A'}`);
    
    const permissionService = getPermissionService();
    const result = await permissionService.hasPermission(
      user.id,
      resource,
      action,
      targetUserId
    );
    
    console.log(`[DEBUG Permission] Resultado:`, result);
    
    return {
      success: true,
      granted: result.granted,
      reason: result.reason,
      debugInfo: {
        currentUserId: user.id,
        resource,
        action,
        targetUserId
      }
    };
  } catch (error) {
    console.error('Erro no debug de permissão:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Server action para invalidar cache de permissões e forçar recarregamento
 */
export async function invalidatePermissionCacheAction() {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return {
        success: false,
        error: 'Usuário não autenticado'
      };
    }
    
    // Nota: Esta função apenas retorna sucesso. 
    // O cache real é invalidado no cliente via query client
    console.log(`[DEBUG] Solicitação para invalidar cache de permissões do usuário ${user.id}`);
    
    return {
      success: true,
      message: 'Cache de permissões marcado para invalidação'
    };
  } catch (error) {
    console.error('Erro ao invalidar cache de permissões:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
} 