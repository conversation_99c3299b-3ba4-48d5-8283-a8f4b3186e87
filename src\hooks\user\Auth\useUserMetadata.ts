'use client'

import { useQuery } from '@tanstack/react-query'
import { CACHE_KEYS } from '@/constants/cache-keys'

export interface UserMetadata {
  id: string
  email: string
  first_name: string
  last_name: string
  role: string
  status?: string
  branch_id?: string
  tenant_id?: string
  avatar_url?: string
  avatar_storage_path?: string
  provider?: string
  providers?: string[]
}

export function useUserMetadata() {
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: CACHE_KEYS.USER_METADATA,
    queryFn: async () => {
      try {
        // Importar dinamicamente para evitar erros com Server Components
        const { getCurrentUser } = await import('@/services/auth/actions/auth-actions')
        const user = await getCurrentUser()

        if (!user) {
          throw new Error('Usuário não autenticado')
        }

        const appMetadata = user.app_metadata || {}

        // Usar diretamente os metadados do JWT sem chamar getUserProfile
        const metadata: UserMetadata = {
          id: appMetadata.id || user.id,
          email: appMetadata.email || user.email || '',
          first_name: appMetadata.first_name || '',
          last_name: appMetadata.last_name || '',
          role: appMetadata.role || '',
          status: appMetadata.status,
          branch_id: appMetadata.branch_id,
          tenant_id: appMetadata.tenant_id,
          avatar_url: appMetadata.avatar_url,
          avatar_storage_path: appMetadata.avatar_storage_path,
          provider: appMetadata.provider,
          providers: appMetadata.providers
        }

        return metadata
      } catch (error) {
        console.error('Erro ao obter metadados do usuário:', error)
        throw error
      }
    },
    enabled: true,
    staleTime: 12 * 60 * 60 * 1000, // 12 horas
    gcTime: 24 * 60 * 60 * 1000, // 24 horas
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    retry: (failureCount, error) => {
      console.warn(`Tentativa ${failureCount} de buscar metadados falhou:`, error)
      return failureCount < 2
    }
  })

  return {
    metadata: data,
    isLoading,
    error,
    refetch
  }
}
