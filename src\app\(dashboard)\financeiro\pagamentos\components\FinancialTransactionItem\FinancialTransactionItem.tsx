import { CreditCard } from 'lucide-react';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import {
  TooltipProvider,
  TooltipRoot,
  TooltipTrigger,
  TooltipContent,
  TooltipPortal
} from '@/components/ui/tooltip';
import { TransactionItemProps, Transaction, IncomeTransaction, ExpenseTransaction, LegacyTransaction } from '../types';
import { getPaymentMethodIconBySlug } from '@/utils/payment-method-formatter';

// Type guards para verificar o tipo da transação
function isIncomeTransaction(transaction: any): transaction is IncomeTransaction {
  return 'transactionType' in transaction && transaction.transactionType === 'income';
}

function isExpenseTransaction(transaction: any): transaction is ExpenseTransaction {
  return 'transactionType' in transaction && transaction.transactionType === 'expense';
}

function isLegacyTransaction(transaction: any): transaction is LegacyTransaction {
  return !('transactionType' in transaction) && 'studentName' in transaction;
}

export function FinancialTransactionItem({ transaction }: TransactionItemProps) {
  // Mapear o método de pagamento para o slug correto
  const getPaymentMethodSlug = (paymentMethod: string): string => {
    const method = paymentMethod.toLowerCase();

    // Mapeamento de nomes comuns para slugs
    if (method.includes('pix')) return 'pix';
    if (method.includes('cartão') || method.includes('card')) return 'credit_card';
    if (method.includes('dinheiro') || method.includes('cash')) return 'cash';
    if (method.includes('boleto')) return 'boleto';
    if (method.includes('transferência') || method.includes('transfer')) return 'bank_transfer';

    return method; // Retorna o método original se não encontrar mapeamento
  };

  // Verificar se há método de pagamento válido (não é "Não definido" nem vazio)
  const hasValidPaymentMethod = transaction.paymentMethod && transaction.paymentMethod !== 'Não definido';
  const paymentMethodSlug = hasValidPaymentMethod && transaction.paymentMethod ? getPaymentMethodSlug(transaction.paymentMethod) : '';
  const PaymentIcon = paymentMethodSlug ? (getPaymentMethodIconBySlug(paymentMethodSlug) || CreditCard) : null;

  // Função para obter descrição do método de pagamento para tooltip
  const getPaymentMethodDescription = (method: string): string => {
    const methodLower = method.toLowerCase();
    if (methodLower.includes('pix')) return 'Pagamento via PIX';
    if (methodLower.includes('cartão') || methodLower.includes('card')) return 'Pagamento com cartão';
    if (methodLower.includes('dinheiro') || methodLower.includes('cash')) return 'Pagamento em dinheiro';
    if (methodLower.includes('boleto')) return 'Pagamento via boleto';
    if (methodLower.includes('transferência') || methodLower.includes('transfer')) return 'Transferência bancária';
    return `Pagamento via ${method}`;
  };

  // Função para obter descrição do status para tooltip
  const getStatusDescription = (status: string): string => {
    const statusLower = status.toLowerCase();
    if (statusLower.includes('pago') || statusLower.includes('paid') || statusLower.includes('confirmado')) return 'Pagamento confirmado e processado';
    if (statusLower.includes('pendente') || statusLower.includes('pending')) return 'Aguardando processamento do pagamento';
    if (statusLower.includes('cancelado') || statusLower.includes('canceled')) return 'Pagamento cancelado';
    if (statusLower.includes('aguardando')) return 'Aguardando confirmação do pagamento';
    return `Status: ${status}`;
  };

  // Função para obter descrição da data baseada no status
  const getDateDescription = (status: string, paidAt?: string, dueDate?: string): string | null => {
    const statusLower = status.toLowerCase();

    if (statusLower.includes('pago') || statusLower.includes('paid') || statusLower.includes('confirmado')) {
      return paidAt ? `Pago em: ${paidAt}` : `Data da transação: ${transaction.date}`;
    }

    // Para outros status, mostrar data de vencimento apenas se disponível
    return dueDate ? `Vence em: ${dueDate}` : null;
  };

  // Função para obter a data correta para exibição baseada no status
  const getDisplayDate = (status: string, paidAt?: string, dueDate?: string): string | null => {
    const statusLower = status.toLowerCase();

    // Para pagamentos confirmados/pagos, mostrar a data de pagamento se disponível
    if (statusLower.includes('pago') || statusLower.includes('paid') || statusLower.includes('confirmado')) {
      return paidAt || transaction.date;
    }

    // Para outros status, mostrar data de vencimento apenas se disponível
    // Se não houver data de vencimento, retornar null para não mostrar nada
    return dueDate ? dueDate : null;
  };

  // Definir cor específica para cada método de pagamento
  const getIconColor = (slug: string): string => {
    switch (slug) {
      case 'pix':
        return 'text-green-600';
      case 'cash':
        return 'text-emerald-600';
      case 'credit_card':
      case 'debit_card':
        return 'text-blue-600';
      case 'boleto':
        return 'text-orange-600';
      case 'bank_transfer':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  const iconColor = getIconColor(paymentMethodSlug);

  // Função para renderizar o nome/título da transação
  const renderTransactionTitle = () => {
    if (isIncomeTransaction(transaction)) {
      return (
        <>
          {transaction.studentUserId ? (
            <TooltipRoot>
              <TooltipTrigger asChild>
                <Link
                  href={`/perfil/${transaction.studentUserId}`}
                  className="hover:text-emerald-600 dark:hover:text-emerald-400 transition-colors duration-200"
                >
                  {transaction.studentName}
                </Link>
              </TooltipTrigger>
              <TooltipPortal>
                <TooltipContent side="top" className="font-medium">
                  Clique para ver o perfil de {transaction.studentName}
                </TooltipContent>
              </TooltipPortal>
            </TooltipRoot>
          ) : (
            transaction.studentName
          )}
          <span className="text-gray-500 dark:text-gray-400 font-medium"> - {transaction.type}</span>
        </>
      );
    } else if (isExpenseTransaction(transaction)) {
      return (
        <>
          {transaction.supplierName}
          <span className="text-gray-500 dark:text-gray-400 font-medium"> - {transaction.type}</span>
        </>
      );
    } else if (isLegacyTransaction(transaction)) {
      return (
        <>
          {transaction.studentUserId ? (
            <TooltipRoot>
              <TooltipTrigger asChild>
                <Link
                  href={`/perfil/${transaction.studentUserId}`}
                  className="hover:text-emerald-600 dark:hover:text-emerald-400 transition-colors duration-200"
                >
                  {transaction.studentName}
                </Link>
              </TooltipTrigger>
              <TooltipPortal>
                <TooltipContent side="top" className="font-medium">
                  Clique para ver o perfil de {transaction.studentName}
                </TooltipContent>
              </TooltipPortal>
            </TooltipRoot>
          ) : (
            transaction.studentName
          )}
          <span className="text-gray-500 dark:text-gray-400 font-medium"> - {transaction.type}</span>
        </>
      );
    }

    // Fallback genérico
    return <span>Transação desconhecida</span>;
  };

  // Função para renderizar categoria (apenas para despesas)
  const renderCategory = () => {
    if (isExpenseTransaction(transaction) && transaction.categoryName) {
      return (
        <>
          <span
            className="inline-block w-2 h-2 rounded-full mr-1"
            style={{ backgroundColor: transaction.categoryColor || '#6B7280' }}
          />
          <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md text-xs font-medium">
            {transaction.categoryName}
          </span>
          <span className="text-gray-400">•</span>
        </>
      );
    }
    return null;
  };

  return (
    <TooltipProvider delayDuration={300}>
      <div className="flex items-center justify-between p-4 border-0 rounded-lg bg-gradient-to-r from-white to-gray-50/30 dark:from-gray-800/50 dark:to-gray-700/30 shadow-sm hover:shadow-md transition-all duration-200 group">
        <div className="flex items-center space-x-4">
          {/* Ícone do método de pagamento com tooltip */}
          {PaymentIcon && hasValidPaymentMethod ? (
            <TooltipRoot>
              <TooltipTrigger asChild>
                <div className="cursor-help p-2 bg-white dark:bg-gray-800 rounded-full shadow-sm group-hover:shadow-md transition-all duration-200">
                  <PaymentIcon className={`h-6 w-6 ${iconColor} dark:opacity-90`} />
                </div>
              </TooltipTrigger>
              <TooltipPortal>
                <TooltipContent side="top" className="font-medium">
                  {getPaymentMethodDescription(transaction.paymentMethod || '')}
                </TooltipContent>
              </TooltipPortal>
            </TooltipRoot>
          ) : (
            <div className="w-10 h-10 flex items-center justify-center p-2 bg-white dark:bg-gray-800 rounded-full shadow-sm">
              <div className="w-4 h-4 bg-gradient-to-br from-gray-300 to-gray-400 dark:from-gray-600 dark:to-gray-500 rounded-full"></div>
            </div>
          )}

          <div className="flex-1">
            <p className="font-semibold text-gray-900 dark:text-gray-100 mb-1">
              {renderTransactionTitle()}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400 flex items-center gap-2">
              {renderCategory()}
              {hasValidPaymentMethod ? (
                <>
                  <TooltipRoot>
                    <TooltipTrigger asChild>
                      <span className="cursor-help bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md text-xs font-medium">
                        {transaction.paymentMethod}
                      </span>
                    </TooltipTrigger>
                    <TooltipPortal>
                      <TooltipContent side="top" className="font-medium">
                        Método: {transaction.paymentMethod}
                      </TooltipContent>
                    </TooltipPortal>
                  </TooltipRoot>
                  {getDisplayDate(transaction.status, transaction.paidAt, transaction.dueDate) && (
                    <span className="text-gray-400">•</span>
                  )}
                </>
              ) : null}
              {getDisplayDate(transaction.status, transaction.paidAt, transaction.dueDate) && (
                <TooltipRoot>
                  <TooltipTrigger asChild>
                    <span className="cursor-help">
                      {getDisplayDate(transaction.status, transaction.paidAt, transaction.dueDate)}
                    </span>
                  </TooltipTrigger>
                  <TooltipPortal>
                    <TooltipContent side="top" className="font-medium">
                      {getDateDescription(transaction.status, transaction.paidAt, transaction.dueDate)}
                    </TooltipContent>
                  </TooltipPortal>
                </TooltipRoot>
              )}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-right space-y-2">
            <TooltipRoot>
              <TooltipTrigger asChild>
                <p className="font-bold text-lg text-gray-900 dark:text-gray-100 cursor-help">
                  {transaction.amount}
                </p>
              </TooltipTrigger>
              <TooltipPortal>
                <TooltipContent side="top" className="font-medium">
                  Valor da transação: {transaction.amount}
                </TooltipContent>
              </TooltipPortal>
            </TooltipRoot>

            <TooltipRoot>
              <TooltipTrigger asChild>
                <div className="cursor-help">
                  <Badge
                    variant="secondary"
                    className="text-xs font-medium bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 text-gray-700 dark:text-gray-300 border-0 shadow-sm"
                  >
                    {transaction.status}
                  </Badge>
                </div>
              </TooltipTrigger>
              <TooltipPortal>
                <TooltipContent side="top" className="font-medium">
                  {getStatusDescription(transaction.status)}
                </TooltipContent>
              </TooltipPortal>
            </TooltipRoot>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
}
