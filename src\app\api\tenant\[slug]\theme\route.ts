import { NextResponse } from 'next/server';
import { getTenantData } from '@/services/tenant/tenant-service';

export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    const tenant = await getTenantData(params.slug);
    
    if (!tenant) {
      return NextResponse.json({}, { status: 404 });
    }

    return NextResponse.json({
      name: tenant.name,
      primary_color: tenant.primary_color,
      secondary_color: tenant.secondary_color,
      logo_url: tenant.logo_url
    });
  } catch (error) {
    console.error('Erro ao obter tema do tenant:', error);
    return NextResponse.json({}, { status: 500 });
  }
} 