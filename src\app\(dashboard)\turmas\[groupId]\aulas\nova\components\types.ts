export interface Instructor {
  id: string;
  name: string;
}

export interface Branch {
  id: string;
  name: string;
  address?: string;
}

export interface ClassGroup {
  id: string;
  name: string;
  description?: string | null;
  instructor_id: string;
  branch_id: string;
  max_capacity?: number | null;
  is_active?: boolean | null;
  recurrence_pattern?: any;
}

export interface CreateClassFormProps {
  classGroup: ClassGroup;
  instructors: Instructor[];
  branches: Branch[];
}

export interface FormSuggestion {
  text: string;
  type: 'info' | 'warning' | 'suggestion';
}

export interface FormDuration {
  text: string;
  minutes: number;
} 