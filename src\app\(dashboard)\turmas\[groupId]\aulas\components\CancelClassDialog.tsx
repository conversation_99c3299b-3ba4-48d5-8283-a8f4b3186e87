'use client';

import { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { AlertTriangle } from 'lucide-react';

interface CancelClassDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (reason?: string) => void;
  className?: string;
  classDate?: string;
  classTime?: string;
  isLoading?: boolean;
}

export function CancelClassDialog({
  open,
  onOpenChange,
  onConfirm,
  className,
  classDate,
  classTime,
  isLoading = false,
}: CancelClassDialogProps) {
  const [reason, setReason] = useState('');

  const handleConfirm = () => {
    onConfirm(reason.trim() || undefined);
    setReason(''); // Limpar o campo após confirmar
  };

  const handleCancel = () => {
    setReason(''); // Limpar o campo ao cancelar
    onOpenChange(false);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="sm:max-w-[500px]">
        <AlertDialogHeader>
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <AlertDialogTitle>Cancelar Aula</AlertDialogTitle>
          </div>
          <AlertDialogDescription asChild>
            <div className="space-y-2">
              <p>
                Tem certeza que deseja cancelar{' '}
                {className && (
                  <span className="font-medium">"{className}"</span>
                )}
                ?
              </p>
              {(classDate || classTime) && (
                <p className="text-sm text-muted-foreground">
                  {classDate && classTime && `${classDate} às ${classTime}`}
                  {classDate && !classTime && classDate}
                  {!classDate && classTime && `às ${classTime}`}
                </p>
              )}
              <p className="text-sm text-orange-600 dark:text-orange-400">
                Esta ação não pode ser desfeita. Os alunos serão notificados sobre o cancelamento.
              </p>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="space-y-2">
          <Label htmlFor="cancel-reason" className="text-sm font-medium">
            Motivo do cancelamento (opcional)
          </Label>
          <Textarea
            id="cancel-reason"
            placeholder="Ex: Instrutor indisponível, problemas técnicos, etc."
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            className="min-h-[80px] resize-none"
            maxLength={500}
          />
          <p className="text-xs text-muted-foreground">
            {reason.length}/500 caracteres
          </p>
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancel} disabled={isLoading}>
            Manter Aula
          </AlertDialogCancel>
          <AlertDialogAction 
            onClick={handleConfirm} 
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            {isLoading ? 'Cancelando...' : 'Confirmar Cancelamento'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
} 