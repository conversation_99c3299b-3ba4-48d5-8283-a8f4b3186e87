/**
 * Utilitários para manipulação de nomes
 * 
 * Centraliza a lógica de extração de first_name, last_name e full_name
 * para garantir consistência em todo o sistema.
 */

export interface NameParts {
  firstName: string;
  lastName: string;
  fullName: string;
}

/**
 * Extrai as partes do nome de forma consistente
 * 
 * @param fullName - Nome completo fornecido pelo usuário
 * @returns Objeto com firstName (primeiro nome), lastName (segundo nome apenas) e fullName
 * 
 * @example
 * extractNameParts("<PERSON> Maia <PERSON> Nobrega")
 * // { firstName: "<PERSON>", lastName: "<PERSON>", fullName: "<PERSON> Nobrega" }
 * 
 * extractNameParts("<PERSON>")
 * // { firstName: "João", lastName: "<PERSON>", fullName: "<PERSON>" }
 * 
 * extractNameParts("Ana")
 * // { firstName: "Ana", lastName: "", fullName: "Ana" }
 */
export function extractNameParts(fullName: string | null | undefined): NameParts {
  const cleanFullName = (fullName || '').trim();
  
  if (!cleanFullName) {
    return {
      firstName: '',
      lastName: '',
      fullName: ''
    };
  }
  
  const nameParts = cleanFullName.split(' ').filter(part => part.length > 0);
  
  if (nameParts.length === 0) {
    return {
      firstName: '',
      lastName: '',
      fullName: cleanFullName
    };
  }
  
  if (nameParts.length === 1) {
    return {
      firstName: nameParts[0],
      lastName: '',
      fullName: cleanFullName
    };
  }
  
  // Lista de preposições comuns em sobrenomes portugueses
  const prepositions = ['de', 'da', 'do', 'dos', 'das'];

  let lastName = nameParts[1] || '';

  // Se o segundo elemento é uma preposição e existe pelo menos mais um nome,
  // concatenamos a preposição com a próxima parte para formar o sobrenome.
  if (prepositions.includes(lastName.toLowerCase()) && nameParts.length >= 3) {
    lastName = `${lastName} ${nameParts[2]}`;
  }

  return {
    firstName: nameParts[0],
    lastName,
    fullName: cleanFullName
  };
}

/**
 * Formata um nome para exibição consistente
 * 
 * @param firstName - Primeiro nome
 * @param lastName - Sobrenome(s)
 * @returns Nome formatado para exibição
 */
export function formatDisplayName(firstName: string, lastName?: string): string {
  const cleanFirstName = (firstName || '').trim();
  const cleanLastName = (lastName || '').trim();
  
  if (!cleanFirstName && !cleanLastName) return '';
  if (!cleanLastName) return cleanFirstName;
  if (!cleanFirstName) return cleanLastName;
  
  return `${cleanFirstName} ${cleanLastName}`;
}

/**
 * Valida se um nome está em formato adequado
 * 
 * @param fullName - Nome completo para validação
 * @returns true se o nome é válido, false caso contrário
 */
export function isValidName(fullName: string | null | undefined): boolean {
  const cleanName = (fullName || '').trim();
  
  if (!cleanName) return false;
  if (cleanName.length < 2) return false;
  
  // Verificar se contém pelo menos uma letra
  return /[a-zA-ZÀ-ÿ]/.test(cleanName);
} 