"use client"

import { useTheme } from "next-themes"
import { Toaster as Sonner } from "sonner"

type ToasterProps = React.ComponentProps<typeof Sonner>

const Toaster = (props: ToasterProps) => {
  const { theme = "system" } = useTheme()

  // Extrair position para definir padrão bottom-right
  const { position, ...rest } = props

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      position={position ?? "bottom-right"}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast:
            "group toast !bg-white !text-gray-900 !border-gray-200 shadow-lg dark:!bg-gray-950 dark:!text-gray-50 dark:!border-gray-800",
          description: "!text-gray-600 dark:!text-gray-400",
          actionButton:
            "!bg-gray-900 !text-white hover:!bg-gray-800 dark:!bg-gray-50 dark:!text-gray-900 dark:hover:!bg-gray-200",
          cancelButton:
            "!bg-gray-100 !text-gray-700 hover:!bg-gray-200 dark:!bg-gray-800 dark:!text-gray-300 dark:hover:!bg-gray-700",
        },
      }}
      {...rest}
    />
  )
}

export { Toaster }
