'use server'

import { createClient } from '@/services/supabase/server'
import { getCurrentUser } from '@/services/auth/actions/auth-actions'
import { revalidatePath } from 'next/cache'
import { z } from 'zod'

const instructorProfileSchema = z.object({
  specialties: z.array(z.string()).optional(),
  certification_level: z.string().optional().nullable(),
  federation_registration: z.string().optional().nullable(),
  experience_years: z.number().min(0).optional().nullable(),
  bio: z.string().optional().nullable(),
  contract_type: z.enum(['clt', 'autonomo', 'freelancer', 'terceirizado']).optional(),
  payment_model: z.enum(['hora_aula', 'mensal', 'percentual', 'fixo']).optional().nullable(),
  payment_value: z.string().optional().nullable(),
  payment_percentage: z.string().optional().nullable(),
  has_first_aid_certification: z.boolean().optional(),
  has_cpr_certification: z.boolean().optional(),
  has_rules_course: z.boolean().optional(),
  has_ibjjf_certification: z.boolean().optional(),
  hire_date: z.string().optional().nullable(),
  teaching_notes: z.string().optional().nullable(),
})

const instructorBeltSchema = z.object({
  belt_level_id: z.string().uuid("ID do nível de faixa é obrigatório"),
  awarded_at: z.string(),
  notes: z.string().optional().nullable(),
})

export type InstructorProfileData = z.infer<typeof instructorProfileSchema>
export type InstructorBeltData = z.infer<typeof instructorBeltSchema>

export async function getInstructorProfile(userId: string) {
  try {
    const supabase = await createClient()
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return { success: false, error: 'Não autenticado' }
    }

    // Primeiro, buscar dados do instrutor
    const { data: instructorData, error: instructorError } = await supabase
      .from('instructors')
      .select(`
        *,
        branch:branches(
          id,
          name
        )
      `)
      .eq('user_id', userId)
      .single()

    if (instructorError) {
      console.error('Erro ao buscar dados do instrutor:', instructorError)
      return { success: false, error: 'Instrutor não encontrado' }
    }

    // Buscar dados do usuário separadamente
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select(`
        id,
        first_name,
        last_name,
        full_name,
        email,
        phone,
        avatar_url,
        role,
        branch_id
      `)
      .eq('id', userId)
      .single()

    if (userError) {
      console.error('Erro ao buscar dados do usuário:', userError)
      return { success: false, error: 'Usuário não encontrado' }
    }

    // Buscar faixa atual do instrutor usando RPC
    let currentBeltData = null;
    if (instructorData?.id) {
      const { data: beltData, error: beltError } = await supabase.rpc('get_instructor_current_belt_details', {
        instructor_id_param: instructorData.id
      });
      
      if (!beltError && beltData && beltData.length > 0) {
        currentBeltData = beltData[0];
      }
    }

    // Combinar os dados
    const combinedData = {
      ...instructorData,
      user: userData,
      current_belt: currentBeltData
    }

    return { success: true, data: combinedData }
  } catch (error) {
    console.error('Erro ao buscar perfil do instrutor:', error)
    return { success: false, error: 'Erro interno do servidor' }
  }
}

export async function updateInstructorProfile(userId: string, data: InstructorProfileData) {
  try {
    const supabase = await createClient()
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return { success: false, error: 'Não autenticado' }
    }

    const validation = instructorProfileSchema.safeParse(data)
    if (!validation.success) {
      return { 
        success: false, 
        error: 'Dados inválidos',
        fieldErrors: validation.error.format()
      }
    }

    const isOwnProfile = currentUser.id === userId
    const { data: currentUserData } = await supabase
      .from('users')
      .select('role')
      .eq('id', currentUser.id)
      .single()
    
    const isAdmin = currentUserData?.role === 'admin'

    if (!isAdmin && !isOwnProfile) {
      return { success: false, error: 'Permissão negada' }
    }

    const updateData = {
      ...validation.data,
      updated_at: new Date().toISOString()
    }

    const { error: updateError } = await supabase
      .from('instructors')
      .update(updateData)
      .eq('user_id', userId)

    if (updateError) {
      console.error('Erro ao atualizar dados do instrutor:', updateError)
      return { success: false, error: 'Erro ao atualizar dados' }
    }

    revalidatePath(`/perfil/${userId}`)
    revalidatePath('/instrutores')

    return { success: true }
  } catch (error) {
    console.error('Erro ao atualizar perfil do instrutor:', error)
    return { success: false, error: 'Erro interno do servidor' }
  }
}

export async function getInstructorBeltHistory(instructorId: string) {
  try {
    const supabase = await createClient()
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return { success: false, error: 'Não autenticado' }
    }

    // Buscar dados das faixas com informações completas do belt_level
    const { data: beltsData, error: beltsError } = await supabase
      .from('instructor_belts')
      .select(`
        *,
        belt_level:belt_levels(
          id,
          belt_color,
          degree,
          label,
          stripe_color,
          show_center_line,
          center_line_color,
          sort_order,
          modality:modalities(
            id,
            name
          )
        )
      `)
      .eq('instructor_id', instructorId)
      .order('awarded_at', { ascending: false })

    if (beltsError) {
      console.error('Erro ao buscar histórico de faixas:', beltsError)
      return { success: false, error: 'Erro ao buscar graduações' }
    }

    // Buscar dados dos usuários que concederam as faixas
    const beltsWithUserData = await Promise.all(
      (beltsData || []).map(async (belt: any) => {
        let awardedByUser = null
        
        if (belt.awarded_by) {
          const { data: userData } = await supabase
            .from('users')
            .select('id, first_name, last_name, full_name')
            .eq('id', belt.awarded_by)
            .single()
          
          awardedByUser = userData
        }

        return {
          ...belt,
          awarded_by_user: awardedByUser
        }
      })
    )

    return { success: true, data: beltsWithUserData }
  } catch (error) {
    console.error('Erro ao buscar histórico de faixas:', error)
    return { success: false, error: 'Erro interno do servidor' }
  }
}

export async function addInstructorBelt(
  instructorId: string,
  beltData: {
    belt_level_id: string
    awarded_at: string
    notes?: string
  }
) {
  try {
    const supabase = await createClient()
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return { success: false, error: 'Não autenticado' }
    }

    const awardedById = currentUser.id

    const { data, error } = await supabase
      .from('instructor_belts')
      .insert({
        instructor_id: instructorId,
        belt_level_id: beltData.belt_level_id,
        awarded_at: beltData.awarded_at,
        awarded_by: awardedById,
        notes: beltData.notes || null
      })
      .select()

    if (error) {
      console.error('Erro ao adicionar faixa ao instrutor:', error)
      return { success: false, error: 'Erro ao adicionar graduação' }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Erro ao adicionar faixa ao instrutor:', error)
    return { success: false, error: 'Erro interno do servidor' }
  }
}

export async function getInstructorStats(instructorId: string) {
  try {
    const supabase = await createClient()
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return { success: false, error: 'Não autenticado' }
    }

    const { data: instructorData } = await supabase
      .from('instructors')
      .select('user_id, id')
      .eq('id', instructorId)
      .single()

    if (!instructorData) {
      return { success: false, error: 'Instrutor não encontrado' }
    }

    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const [classesResult, attendanceResult] = await Promise.all([
      supabase
        .from('classes')
        .select('id, name, start_time, end_time')
        .eq('instructor_id', instructorData.user_id)
        .gte('start_time', thirtyDaysAgo.toISOString())
        .order('start_time', { ascending: false }),
      
      supabase
        .from('attendance')
        .select('id, checked_in_at')
        .eq('checked_in_by', instructorData.user_id)
        .gte('checked_in_at', thirtyDaysAgo.toISOString())
    ])

    const totalClasses = classesResult.data?.length || 0
    const recentClasses = classesResult.data?.slice(0, 5) || []
    const totalAttendance = attendanceResult.data?.length || 0

    return { 
      success: true, 
      data: {
        totalClasses,
        recentClasses,
        totalAttendance,
        period: '30 dias'
      }
    }
  } catch (error) {
    console.error('Erro ao buscar estatísticas do instrutor:', error)
    return { success: false, error: 'Erro interno do servidor' }
  }
}

export async function updateInstructorSpecialties(userId: string, specialties: string[]) {
  try {
    const supabase = await createClient()
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return { success: false, error: 'Não autenticado' }
    }

    // Validar especialidades
    const validation = z.array(z.string().min(1).max(50)).safeParse(specialties)
    if (!validation.success) {
      return { 
        success: false, 
        error: 'Especialidades inválidas'
      }
    }

    const isOwnProfile = currentUser.id === userId
    const { data: currentUserData } = await supabase
      .from('users')
      .select('role')
      .eq('id', currentUser.id)
      .single()
    
    const isAdmin = currentUserData?.role === 'admin'

    if (!isAdmin && !isOwnProfile) {
      return { success: false, error: 'Permissão negada' }
    }

    const { error: updateError } = await supabase
      .from('instructors')
      .update({
        specialties: validation.data,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)

    if (updateError) {
      console.error('Erro ao atualizar especialidades:', updateError)
      return { success: false, error: 'Erro ao atualizar especialidades' }
    }

    revalidatePath(`/perfil/${userId}`)
    revalidatePath('/instrutores')

    return { success: true }
  } catch (error) {
    console.error('Erro ao atualizar especialidades:', error)
    return { success: false, error: 'Erro interno do servidor' }
  }
}

export async function getAvailableBeltLevels(modalityId?: string) {
  try {
    const supabase = await createClient()
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return { success: false, error: 'Não autenticado' }
    }

    const tenantId = (currentUser.app_metadata as any)?.tenant_id;

    let query = supabase
      .from('belt_levels')
      .select(`
        id,
        belt_color,
        degree,
        label,
        stripe_color,
        show_center_line,
        center_line_color,
        sort_order,
        modality:modalities(
          id,
          name
        )
      `)
      .eq('tenant_id', tenantId)
      .order('sort_order', { ascending: true })

    // Se modalityId for fornecido, filtrar por modalidade
    if (modalityId) {
      query = query.eq('modality_id', modalityId)
    }

    const { data: beltLevels, error: beltLevelsError } = await query

    if (beltLevelsError) {
      console.error('Erro ao buscar níveis de faixa:', beltLevelsError)
      return { success: false, error: 'Erro ao buscar níveis de faixa' }
    }

    return { success: true, data: beltLevels || [] }
  } catch (error) {
    console.error('Erro ao buscar níveis de faixa:', error)
    return { success: false, error: 'Erro interno do servidor' }
  }
} 