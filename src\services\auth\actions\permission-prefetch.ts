'use server';

import { getPermissionContext } from '@/services/permissions/contexts/permission-context';

/**
 * Server action para pré-carregar o contexto de permissões durante o login
 * Esta função é chamada pelo cliente logo após a autenticação
 * 
 * @param userId ID do usuário logado
 * @returns Verdadeiro se o contexto foi carregado com sucesso
 */
export async function prefetchPermissionContextOnLogin(userId: string): Promise<boolean> {
  try {
    if (!userId) return false;
    
    // Obter o contexto básico de permissão
    const context = await getPermissionContext(userId);
    
    // Retornar verdadeiro se o contexto foi carregado com sucesso
    return !!context;
  } catch (error) {
    console.error('Erro ao pré-carregar contexto de permissões no login:', error);
    return false;
  }
} 