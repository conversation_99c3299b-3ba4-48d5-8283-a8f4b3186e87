/**
 * Configuração centralizada do Supabase
 * 
 * Este arquivo centraliza a validação e fornecimento das variáveis
 * de ambiente do Supabase para uso em toda a aplicação.
 */

interface SupabaseConfig {
  url: string;
  anonKey: string;
  serviceRoleKey?: string;
}

interface SupabaseDiagnostic {
  isConfigured: boolean;
  hasUrl: boolean;
  hasAnonKey: boolean;
  hasServiceRole: boolean;
  urlFormat: 'valid' | 'invalid' | 'missing';
  environment: string;
  timestamp: string;
}

let cachedConfig: SupabaseConfig | null = null;
let cachedServiceKey: string | null = null;

/**
 * Obtém e valida as variáveis de ambiente principais do Supabase
 * @throws {Error} Se as variáveis de ambiente não estiverem configuradas
 */
export function getSupabaseConfig(): SupabaseConfig {
  if (cachedConfig) {
    return cachedConfig;
  }

  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!url || !anonKey) {
    const missing = [];
    if (!url) missing.push('NEXT_PUBLIC_SUPABASE_URL');
    if (!anonKey) missing.push('NEXT_PUBLIC_SUPABASE_ANON_KEY');
    
    console.error('❌ Configuração do Supabase incompleta:', {
      missing,
      hasUrl: !!url,
      hasAnonKey: !!anonKey,
      nodeEnv: process.env.NODE_ENV,
      timestamp: new Date().toISOString(),
    });
    
    throw new Error(
      `Configuração do Supabase incompleta. Variáveis ausentes: ${missing.join(', ')}`
    );
  }

  // Validar formato da URL
  if (!isValidSupabaseUrl(url)) {
    console.error('❌ URL do Supabase inválida:', {
      url: url.substring(0, 30) + '...', // Log parcial para segurança
      nodeEnv: process.env.NODE_ENV,
      timestamp: new Date().toISOString(),
    });
    
    throw new Error('URL do Supabase tem formato inválido');
  }

  cachedConfig = { url, anonKey };
  
  return cachedConfig;
}

/**
 * Obtém a service role key do Supabase (apenas para uso no servidor)
 * @throws {Error} Se a variável de ambiente não estiver configurada
 */
export function getSupabaseServiceRoleKey(): string {
  if (cachedServiceKey) {
    return cachedServiceKey;
  }

  const serviceKey = process.env.SUPABASE_SERVICE_ROLE;
  
  if (!serviceKey) {
    console.error('❌ Service role key do Supabase não encontrada:', {
      nodeEnv: process.env.NODE_ENV,
      timestamp: new Date().toISOString(),
    });
    
    throw new Error(
      'Service role key do Supabase não encontrada. Verifique a variável SUPABASE_SERVICE_ROLE.'
    );
  }

  cachedServiceKey = serviceKey;

  return cachedServiceKey;
}

/**
 * Verifica se todas as variáveis de ambiente do Supabase estão configuradas
 * @returns {boolean} true se todas as variáveis estão presentes
 */
export function isSupabaseConfigured(): boolean {
  try {
    getSupabaseConfig();
    return true;
  } catch {
    return false;
  }
}

/**
 * Verifica se a service role key está configurada
 * @returns {boolean} true se a service role key está presente
 */
export function isSupabaseServiceRoleConfigured(): boolean {
  try {
    getSupabaseServiceRoleKey();
    return true;
  } catch {
    return false;
  }
}

/**
 * Executa diagnóstico completo da configuração do Supabase
 * @returns {SupabaseDiagnostic} Informações detalhadas sobre a configuração
 */
export function diagnoseSupabaseConfig(): SupabaseDiagnostic {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE;

  const hasUrl = !!url;
  const hasAnonKey = !!anonKey;
  const hasServiceRole = !!serviceKey;
  
  let urlFormat: 'valid' | 'invalid' | 'missing' = 'missing';
  if (hasUrl) {
    urlFormat = isValidSupabaseUrl(url) ? 'valid' : 'invalid';
  }

  const diagnostic: SupabaseDiagnostic = {
    isConfigured: hasUrl && hasAnonKey && urlFormat === 'valid',
    hasUrl,
    hasAnonKey,
    hasServiceRole,
    urlFormat,
    environment: process.env.NODE_ENV || 'unknown',
    timestamp: new Date().toISOString(),
  };

  return diagnostic;
}

/**
 * Valida se a URL do Supabase tem formato correto
 * @param url URL para validar
 * @returns true se a URL é válida
 */
function isValidSupabaseUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    // URLs do Supabase geralmente seguem o padrão: https://[project-id].supabase.co
    return urlObj.protocol === 'https:' && 
           (urlObj.hostname.endsWith('.supabase.co') || 
            urlObj.hostname.endsWith('.supabase.in') ||
            urlObj.hostname === 'localhost' || // Para desenvolvimento local
            urlObj.hostname.startsWith('127.0.0.1')); // Para desenvolvimento local
  } catch {
    return false;
  }
}

/**
 * Limpa o cache de configuração (útil para testes)
 */
export function clearSupabaseConfigCache(): void {
  cachedConfig = null;
  cachedServiceKey = null;
}

/**
 * Log de diagnóstico para debugging
 * Útil para chamar durante inicialização da aplicação
 */
export function logSupabaseConfigDiagnostic(): void {
  const diagnostic = diagnoseSupabaseConfig();
  
  if (diagnostic.isConfigured) {
    console.log('🔧 Diagnóstico do Supabase - CONFIGURADO:', diagnostic);
  } else {
    console.error('🔧 Diagnóstico do Supabase - PROBLEMA:', diagnostic);
  }
} 