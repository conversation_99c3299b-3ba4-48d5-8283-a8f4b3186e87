import { Card } from "@/components/ui/card"
import { UpcomingPaymentsProps } from "../types/types"
import { PaymentItem } from "./PaymentItem"
import { LoadingState } from "./LoadingState"
import { EmptyState } from "./EmptyState"

export function UpcomingPayments({
  proximosPagamentos,
  loading,
  onUpdate,
}: UpcomingPaymentsProps) {
  if (loading) {
    return <LoadingState height="h-24" />
  }

  if (proximosPagamentos.length === 0) {
    return <EmptyState message="Nenhum pagamento futuro programado." />
  }

  return (
    <Card className="p-6 bg-white dark:bg-slate-800">
      <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">
        Próximos Pagamentos
      </h3>
      <div className="space-y-4">
        {proximosPagamentos.map((pagamento) => (
          <PaymentItem
            key={pagamento.id}
            pagamento={pagamento}
            showReceipt={false}
            onUpdate={onUpdate}
          />
        ))}
      </div>
    </Card>
  )
}
