'use client'

import Script from 'next/script'
import { useUserProfile } from '@/hooks/user/use-user-context'
import { useEffect, useState } from 'react'
import { isUserAdmin } from '@/services/user/user-client';

declare global {
  interface Window {
    chatwootSDK?: {
      run: (args: { websiteToken: string; baseUrl: string }) => void
    }
  }
}

const ChatwootWidget = () => {
  const { profile, isLoading } = useUserProfile();
  const [isAdmin, setIsAdmin] = useState<boolean>(false);

  useEffect(() => {
    if (profile?.role) {
      setIsAdmin(profile.role === 'admin');
    } else if (!isLoading) {
      // Se o perfil ainda não forneceu role, faz verificação explícita
      isUserAdmin().then(setIsAdmin).catch(() => setIsAdmin(false));
    }
  }, [profile?.role, isLoading]);

  const CHATWOOT_WEBSITE_TOKEN = process.env.NEXT_PUBLIC_CHATWOOT_WEBSITE_TOKEN
  const CHATWOOT_BASE_URL = process.env.NEXT_PUBLIC_CHATWOOT_BASE_URL

  if (
    process.env.NODE_ENV !== 'production' ||
    !CHATWOOT_WEBSITE_TOKEN ||
    !CHATWOOT_BASE_URL ||
    (isLoading && !profile) ||
    !isAdmin
  ) {
    return null
  }

  return (
    <Script
      id="chatwoot-sdk"
      strategy="afterInteractive"
      src={`${CHATWOOT_BASE_URL}/packs/js/sdk.js`}
      onLoad={() => {
        if (window.chatwootSDK) {
          window.chatwootSDK.run({
            websiteToken: CHATWOOT_WEBSITE_TOKEN,
            baseUrl: CHATWOOT_BASE_URL,
          })
        }
      }}
    />
  )
}

export default ChatwootWidget