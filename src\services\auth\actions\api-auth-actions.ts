'use server'

import { createClient } from '@/services/supabase/server'
import { NextResponse } from 'next/server'
import { z } from 'zod'

// Cache para evitar ataques de força bruta
type AuthAttempt = { count: number; lastAttempt: number; blocked: boolean }
const authAttempts = new Map<string, AuthAttempt>()
const MAX_AUTH_ATTEMPTS = 5
const BLOCK_DURATION = 15 * 60 * 1000 // 15 minutos

// Cache para verificações de permissão com TTL de 5 minutos
const permissionCache = new Map<string, { result: any; timestamp: number; accessCount: number }>()
const PERMISSION_CACHE_TTL = 5 * 60 * 1000 // 5 minutos

// Cache para prefetch de permissões comuns
const commonPermissionsCache = new Map<string, { permissions: Set<string>; timestamp: number }>()
const COMMON_PERMISSIONS_TTL = 10 * 60 * 1000 // 10 minutos

/**
 * Lista de permissões que devem ser pré-carregadas para cada tipo de usuário
 */
const COMMON_PERMISSIONS_BY_ROLE = {
  admin: ['profile:view', 'profile:edit', 'users:view', 'users:edit', 'dashboard:view'],
  instructor: ['profile:view', 'profile:edit', 'students:view', 'classes:view', 'attendance:view'],
  student: ['profile:view', 'profile:edit', 'attendance:view', 'schedule:view']
}

/**
 * Registra uma tentativa de autenticação para detecção de ataques
 */
async function recordAuthAttempt(identifier: string): Promise<boolean> {
  const now = Date.now()

  // Limpar entradas antigas do cache (mais de 24h) com otimização
  if (Math.random() < 0.005) {
    // Reduzido para 0.5% para melhor performance
    // Usar Array.from para converter o iterator para array antes de iterar
    const entries = Array.from(authAttempts.entries())
    const cutoff = now - 24 * 60 * 60 * 1000

    for (let i = 0; i < entries.length; i++) {
      const [key, value] = entries[i]
      if (value.lastAttempt < cutoff) {
        authAttempts.delete(key)
      }
    }
  }

  const attempt = authAttempts.get(identifier) || { count: 0, lastAttempt: 0, blocked: false }

  // Verificar se o bloqueio expirou
  if (attempt.blocked && now - attempt.lastAttempt > BLOCK_DURATION) {
    attempt.blocked = false
    attempt.count = 0
  }

  // Se ainda estiver bloqueado, rejeitar a tentativa
  if (attempt.blocked) {
    return false
  }

  // Reiniciar contador se a última tentativa foi há mais de 1 hora
  if (now - attempt.lastAttempt > 60 * 60 * 1000) {
    attempt.count = 0
  }

  // Incrementar contador e atualizar timestamp
  attempt.count++
  attempt.lastAttempt = now

  // Bloquear se exceder o limite
  if (attempt.count > MAX_AUTH_ATTEMPTS) {
    attempt.blocked = true

    // Registrar possível ataque no log
    console.warn(`Possível ataque de força bruta detectado: ${identifier} bloqueado por 15 minutos`)

    // Aqui poderia adicionar código para alertar administradores
    // ou integrar com sistemas de segurança
  }

  authAttempts.set(identifier, attempt)
  return !attempt.blocked
}

/**
 * Valida parâmetros para operações de segurança
 */
function validateSecurityParams(resource: string, action: string, userId?: string): boolean {
  // Schema de validação para parâmetros de segurança
  const resourceSchema = z
    .string()
    .min(1)
    .max(50)
    .regex(/^[a-zA-Z0-9_-]+$/)
  const actionSchema = z
    .string()
    .min(1)
    .max(50)
    .regex(/^[a-zA-Z0-9_-]+$/)
  const userIdSchema = z.string().uuid().optional()

  try {
    resourceSchema.parse(resource)
    actionSchema.parse(action)
    if (userId) userIdSchema.parse(userId)
    return true
  } catch (error) {
    console.warn(`Parâmetros de segurança inválidos: resource=${resource}, action=${action}, userId=${userId}`)
    return false
  }
}

/**
 * Registra ação de segurança para auditoria
 */
async function logSecurityEvent(eventType: string, userId: string | null, resource: string, action: string, success: boolean, clientIp?: string, details?: Record<string, any>) {
  try {
    const supabase = await createClient()

    // Registrar evento no log de auditoria
    await supabase.from('security_audit_logs').insert({
      event_type: eventType,
      user_id: userId,
      resource,
      action,
      success,
      client_ip: clientIp,
      details,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    // Não deixar falhas de log interromperem a operação
    console.error('Erro ao registrar evento de segurança:', error)
  }
}

/**
 * Server action para verificar autenticação em rotas de API
 * Retorna o usuário se autenticado, ou um objeto de resposta de erro se não
 */
export async function checkApiAuth(clientIp?: string, headers?: Headers) {
  const supabase = await createClient()

  try {
    const {
      data: { user }
    } = await supabase.auth.getUser()

    if (!user) {
      await logSecurityEvent('auth_check', null, 'api', 'access', false, clientIp)

      return {
        authenticated: false,
        user: null,
        response: NextResponse.json(
          { error: 'Não autorizado' },
          {
            status: 401,
            headers: {
              'X-Content-Type-Options': 'nosniff',
              'X-Frame-Options': 'DENY',
              'X-XSS-Protection': '1; mode=block',
              'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate'
            }
          }
        )
      }
    }

    await logSecurityEvent('auth_check', user.id, 'api', 'access', true, clientIp)

    return {
      authenticated: true,
      user,
      response: null
    }
  } catch (error) {
    console.error('Erro ao verificar autenticação na API:', error)

    await logSecurityEvent('auth_check', null, 'api', 'access', false, clientIp, { error: error instanceof Error ? error.message : String(error) })

    return {
      authenticated: false,
      user: null,
      error: error instanceof Error ? error.message : 'Erro desconhecido',
      response: NextResponse.json(
        { error: 'Erro ao verificar autenticação' },
        {
          status: 500,
          headers: {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate'
          }
        }
      )
    }
  }
}

/**
 * Limpa entradas expiradas do cache de permissões com melhor algoritmo
 */
function cleanExpiredPermissions() {
  const now = Date.now()
  const keysToDelete: string[] = []
  const accessThreshold = 3 // Manter apenas entradas acessadas pelo menos 3 vezes

  permissionCache.forEach((value, key) => {
    const isExpired = now - value.timestamp > PERMISSION_CACHE_TTL
    const isUnused = value.accessCount < accessThreshold

    if (isExpired || (isUnused && permissionCache.size > 500)) {
      keysToDelete.push(key)
    }
  })

  keysToDelete.forEach((key) => permissionCache.delete(key))

  // Limpar também o cache de permissões comuns
  const commonKeysToDelete: string[] = []
  commonPermissionsCache.forEach((value, key) => {
    if (now - value.timestamp > COMMON_PERMISSIONS_TTL) {
      commonKeysToDelete.push(key)
    }
  })

  commonKeysToDelete.forEach((key) => commonPermissionsCache.delete(key))
}

/**
 * Obtém resultado do cache se ainda válido, com contagem de acesso
 */
function getCachedPermissionResult(cacheKey: string) {
  const cached = permissionCache.get(cacheKey)
  if (cached && Date.now() - cached.timestamp < PERMISSION_CACHE_TTL) {
    // Incrementar contador de acesso
    cached.accessCount++
    permissionCache.set(cacheKey, cached)
    return cached.result
  }
  return null
}

/**
 * Armazena resultado no cache com contador de acesso
 */
function setCachedPermissionResult(cacheKey: string, result: any) {
  // Limpar cache expirado periodicamente com limite maior
  if (permissionCache.size > 2000) {
    cleanExpiredPermissions()
  }

  permissionCache.set(cacheKey, {
    result,
    timestamp: Date.now(),
    accessCount: 1
  })
}

/**
 * Pré-carrega permissões comuns para um usuário baseado em seu role
 */
async function prefetchCommonPermissions(userId: string, userRole: string, permissionService: any) {
  const commonPermissions = COMMON_PERMISSIONS_BY_ROLE[userRole as keyof typeof COMMON_PERMISSIONS_BY_ROLE]

  if (!commonPermissions) return

  // Verificar se já foi feito prefetch recentemente
  const cacheKey = `prefetch:${userId}:${userRole}`
  const cached = commonPermissionsCache.get(cacheKey)
  if (cached && Date.now() - cached.timestamp < COMMON_PERMISSIONS_TTL) {
    return // Já foi feito prefetch recentemente
  }

  // Executar prefetch das permissões comuns
  const prefetchPromises = commonPermissions.map(async (permission) => {
    const [resource, action] = permission.split(':')
    const permissionKey = `${userId}:${resource}:${action}:${userId}`

    // Só fazer prefetch se não estiver em cache
    if (!getCachedPermissionResult(permissionKey)) {
      try {
        const result = await permissionService.hasPermission(userId, resource, action, userId)
        setCachedPermissionResult(permissionKey, result)
      } catch (error) {
        console.warn(`Erro no prefetch da permissão ${permission} para usuário ${userId}:`, error)
      }
    }
  })

  // Aguardar todas as verificações de permissão
  await Promise.allSettled(prefetchPromises)

  // Marcar como prefetchado
  commonPermissionsCache.set(cacheKey, {
    permissions: new Set(commonPermissions),
    timestamp: Date.now()
  })
}

/**
 * Invalida cache de permissões para um usuário específico
 */
function invalidateUserPermissionCache(userId: string) {
  const keysToDelete: string[] = []

  permissionCache.forEach((_, key) => {
    if (key.startsWith(`${userId}:`)) {
      keysToDelete.push(key)
    }
  })

  keysToDelete.forEach((key) => permissionCache.delete(key))

  // Limpar também cache de prefetch
  const prefetchKey = Array.from(commonPermissionsCache.keys()).find((key) => key.includes(userId))
  if (prefetchKey) {
    commonPermissionsCache.delete(prefetchKey)
  }
}

/**
 * Verifica a autenticação e permissão em uma única chamada de API (otimizada)
 * @param userId ID do usuário que está sendo acessado
 * @param resource Recurso sendo acessado (ex: 'profile', 'user')
 * @param action Ação sendo realizada (ex: 'view', 'edit')
 * @param permissionService Serviço de permissões
 * @param clientIp IP do cliente para auditoria
 */
export async function checkApiAuthAndPermission(userId: string, resource: string, action: string, permissionService: any, clientIp?: string) {
  // Validar parâmetros de segurança para evitar injeção
  if (!validateSecurityParams(resource, action, userId)) {
    return {
      authenticated: false,
      user: null,
      permission: false,
      response: NextResponse.json(
        { error: 'Parâmetros de segurança inválidos' },
        {
          status: 400,
          headers: {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate'
          }
        }
      )
    }
  }

  // Primeiro verifica se o usuário está autenticado
  const authResult = await checkApiAuth(clientIp)

  if (!authResult.authenticated || !authResult.user) {
    return authResult
  }

  // Criar chave de cache para esta verificação de permissão
  const cacheKey = `${authResult.user.id}:${resource}:${action}:${userId}`

  // Verificar se já temos resultado em cache
  const cachedResult = getCachedPermissionResult(cacheKey)
  if (cachedResult) {
    return {
      authenticated: true,
      user: authResult.user,
      permission: cachedResult.granted,
      response: cachedResult.granted
        ? null
        : NextResponse.json(
            { error: 'Acesso não autorizado' },
            {
              status: 403,
              headers: {
                'X-Content-Type-Options': 'nosniff',
                'X-Frame-Options': 'DENY',
                'X-XSS-Protection': '1; mode=block',
                'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate'
              }
            }
          )
    }
  }

  // Verificar tentativas de acesso ao recurso (otimizado)
  const accessKey = `${authResult.user.id}:${resource}:${action}:${userId}`
  if (!(await recordAuthAttempt(accessKey))) {
    await logSecurityEvent('permission_check', authResult.user.id, resource, action, false, clientIp, { target_user_id: userId, reason: 'too_many_attempts' })

    return {
      authenticated: true,
      user: authResult.user,
      permission: false,
      response: NextResponse.json(
        { error: 'Muitas tentativas de acesso. Tente novamente mais tarde.' },
        {
          status: 429,
          headers: {
            'Retry-After': '900', // 15 minutos
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate'
          }
        }
      )
    }
  }

  try {
    // Fazer prefetch de permissões comuns em background (não bloquear)
    const userMetadata = authResult.user.app_metadata || {}
    const userRole = userMetadata.role || 'student'

    // Executar prefetch de forma assíncrona
    setTimeout(() => {
      prefetchCommonPermissions(authResult.user.id, userRole, permissionService).catch((error) => {
        console.warn('Erro no prefetch de permissões comuns:', error)
      })
    }, 100)

    // Verifica se tem permissão
    const permissionResult = await permissionService.hasPermission(authResult.user.id, resource, action, userId)

    // Armazenar resultado no cache
    setCachedPermissionResult(cacheKey, permissionResult)

    // Log de segurança apenas para falhas ou primeira verificação
    if (!permissionResult.granted) {
      await logSecurityEvent('permission_check', authResult.user.id, resource, action, permissionResult.granted, clientIp, { target_user_id: userId })
    }

    if (!permissionResult.granted) {
      return {
        authenticated: true,
        user: authResult.user,
        permission: false,
        response: NextResponse.json(
          { error: 'Acesso não autorizado' },
          {
            status: 403,
            headers: {
              'X-Content-Type-Options': 'nosniff',
              'X-Frame-Options': 'DENY',
              'X-XSS-Protection': '1; mode=block',
              'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate'
            }
          }
        )
      }
    }

    return {
      authenticated: true,
      user: authResult.user,
      permission: true,
      response: null
    }
  } catch (error) {
    console.error('Erro ao verificar permissão:', error)

    await logSecurityEvent('permission_check', authResult.user.id, resource, action, false, clientIp, { target_user_id: userId, error: error instanceof Error ? error.message : String(error) })

    return {
      authenticated: true,
      user: authResult.user,
      permission: false,
      response: NextResponse.json(
        { error: 'Erro ao verificar permissão' },
        {
          status: 500,
          headers: {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate'
          }
        }
      )
    }
  }
}

// Exportar funções de utilitário para uso externo
export async function invalidateUserPermissionCacheAction(userId: string) {
  return invalidateUserPermissionCache(userId)
}

export async function cleanExpiredPermissionsAction() {
  return cleanExpiredPermissions()
}
