import { Metada<PERSON> } from "next";
import { ClientFilters } from "./components/filters/client-filters";
import { getBranches } from "./components/filters/server-branches";
import { InstructorListContainer } from "./components/list/instructor-list-container";
import { getInstructorStats } from "./actions/instructor-stats";
import { NewInstructorButton } from "./components/actions/new-instructor-button";
import { RefreshButton } from "./components/actions/refresh-button";

export const metadata: Metadata = {
  title: "Instrutores | ApexSAAS",
  description: "Gerenciamento de instrutores no sistema ApexSAAS"
};

export default async function Instrutores() {
  const branches = await getBranches();
  const stats = await getInstructorStats();

  return (
    <>
      <div className="mb-2 space-y-4">
        <div className="flex flex-col sm:flex-row gap-4 items-stretch sm:items-center justify-between">
          <div className="order-2 sm:order-1">
            {/* Espaço para ações futuras dos instrutores */}
          </div>
          <div className="order-1 sm:order-2 flex gap-2">
            <RefreshButton />
            <NewInstructorButton />
          </div>
        </div>

        {/* Client Component para filtros com filiais do banco */}
        <ClientFilters branches={branches} initialStats={stats} />
      </div>

      {/* Container para lista de instrutores */}
      <InstructorListContainer />
    </>
  );
} 