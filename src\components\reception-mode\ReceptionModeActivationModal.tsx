'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useReceptionMode } from '@/contexts/ReceptionModeContext';
import { useUser } from '@/hooks/user/Auth/useUser';
import { useRolePermissions } from '@/hooks/user/Permissions/use-role-permissions';
import { AlertTriangle, Monitor, Lock, Unlock, XCircle } from 'lucide-react';

interface ReceptionModeActivationModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ReceptionModeActivationModal({ 
  isOpen, 
  onClose 
}: ReceptionModeActivationModalProps) {
  const [isActivating, setIsActivating] = useState(false);
  const { activateMode } = useReceptionMode();
  const { user } = useUser();
  const { userRole, canAccessPresenca } = useRolePermissions();
  const router = useRouter();

  // Verificar se o usuário tem permissão para ativar o modo de recepção
  const canActivateReceptionMode = userRole === 'admin' || userRole === 'instructor';

  const handleActivate = async () => {
    if (!user || !canActivateReceptionMode) return;

    setIsActivating(true);
    
    try {
      // Ativar o modo de recepção
      activateMode(user);
      
      // Redirecionar para a página de checkin
      router.push('/checkin');
      
      // Fechar o modal
      onClose();
    } catch (error) {
      console.error('Erro ao ativar modo de recepção:', error);
    } finally {
      setIsActivating(false);
    }
  };

  // Se o usuário não tem permissão, mostrar mensagem de acesso negado
  if (!canActivateReceptionMode) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-destructive" />
              Acesso Negado
            </DialogTitle>
            <DialogDescription>
              Você não tem permissão para ativar o modo de recepção
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Permissão Insuficiente</AlertTitle>
              <AlertDescription>
                Apenas administradores e instrutores podem ativar o modo de recepção.
              </AlertDescription>
            </Alert>

            <div className="text-sm text-muted-foreground">
              <p>
                O modo de recepção é uma funcionalidade restrita que permite controlar 
                o acesso e presença dos alunos. Para ativar esta funcionalidade, você 
                precisa ter permissões de administrador ou instrutor.
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={onClose}
            >
              Entendido
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5 text-amber-500" />
            Ativar Modo de Recepção
          </DialogTitle>
          <DialogDescription>
            Configure a aplicação para funcionar como um terminal de recepção
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <Alert variant="warning">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Importante</AlertTitle>
            <AlertDescription>
              Este modo limitará o acesso do sistema.
            </AlertDescription>
          </Alert>

          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <Lock className="h-4 w-4 text-destructive mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-foreground">Funcionalidades restritas:</p>
                <ul className="mt-1 text-muted-foreground list-disc list-inside space-y-1">
                  <li>Todas as ferramentas de gerenciamento serão ocultadas</li>
                  <li>Apenas o controle de presença ficará disponível</li>
                  <li>Navegação para outras páginas será bloqueada</li>
                </ul>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Unlock className="h-4 w-4 text-green-600 dark:text-green-500 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-foreground">Para desativar:</p>
                <p className="mt-1 text-muted-foreground">
                  Digite sua senha na tela de check-in para retornar ao modo normal
                </p>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="sm:justify-between">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isActivating}
          >
            Cancelar
          </Button>
          <Button
            onClick={handleActivate}
            disabled={isActivating}
            className="bg-amber-500 text-white hover:bg-amber-600 dark:bg-amber-600 dark:hover:bg-amber-700"
          >
            {isActivating ? 'Ativando...' : 'Ativar Modo de Recepção'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 