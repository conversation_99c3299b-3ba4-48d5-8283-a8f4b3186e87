'use client';

import { useState } from 'react';
import { InstructorList } from "./instructor-list";
import { ListSkeleton } from './list-skeleton';
import { Instructor } from '../../types/types';
import { useInstructorsQuery } from "../../hooks/use-instructors-query";
import { RefreshIndicator } from './refresh-indicator';

export function InstructorListContainer() {
  const [isSelecting, setIsSelecting] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<Instructor[]>([]);
  const { isLoading, isFetching } = useInstructorsQuery();

  const handleToggleSelectionMode = () => {
    setIsSelecting(prev => !prev);
    // Limpar seleção ao desativar o modo de seleção
    if (isSelecting) {
      setSelectedUsers([]);
    }
  };

  // Mostrar o loading completo apenas na primeira carga
  if (isLoading) {
    return <ListSkeleton />;
  }

  return (
    <div className="relative">
      {/* Indicador de atualização quando está buscando mas não no carregamento inicial */}
      {isFetching && !isLoading && <RefreshIndicator />}
      
      <InstructorList 
        isSelecting={isSelecting} 
        selectedUsers={selectedUsers} 
        setSelectedUsers={setSelectedUsers} 
        onCloseSelectionMode={() => setIsSelecting(false)}
        onActivateSelectionMode={() => setIsSelecting(true)}
      />
    </div>
  );
} 