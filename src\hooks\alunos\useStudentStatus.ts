'use client';

import { useUserStatus, useToggleUserStatus, useBulkUserStatus, type UserStatus } from '../user/useUserStatus';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Hook específico para gerenciar status de estudantes
 * Usa o status centralizado da tabela users mas mantém compatibilidade
 * com a interface existente de estudantes
 */
export function useStudentStatus() {
  const userStatusHook = useUserStatus();
  const queryClient = useQueryClient();

  const updateStudentStatus = async (studentUserId: string, status: UserStatus) => {
    const result = await userStatusHook.updateStatus(studentUserId, status);
    
    // Invalidar caches específicos de estudantes de forma mais seletiva
    if (result?.success) {
      // Invalidar apenas queries específicas ao invés de todas as queries de estudantes
      queryClient.invalidateQueries({
        queryKey: ['student-stats'],
        exact: true
      });
      
      // Refetch apenas dados que realmente precisam ser atualizados
      queryClient.refetchQueries({
        queryKey: ['students'],
        type: 'active'
      });
    }
    
    return result;
  };

  const getStudentStatus = async (studentUserId: string) => {
    return await userStatusHook.getStatus(studentUserId);
  };

  return {
    updateStudentStatus,
    getStudentStatus,
    isUpdating: userStatusHook.isUpdating,
    error: userStatusHook.error,
    isSuccess: userStatusHook.isSuccess,
    reset: userStatusHook.reset
  };
}

/**
 * Hook para toggle de status de estudante (ativo/inativo)
 */
export function useToggleStudentStatus() {
  const toggleHook = useToggleUserStatus();
  const queryClient = useQueryClient();

  const toggleStudentStatus = async (studentUserId: string, currentStatus: UserStatus) => {
    const result = await toggleHook.toggleStatus(studentUserId, currentStatus);
    
    // Invalidar caches específicos de estudantes de forma mais seletiva
    if (result?.success) {
      // Invalidar apenas estatísticas que precisam ser recalculadas
      queryClient.invalidateQueries({
        queryKey: ['student-stats'],
        exact: true
      });
      
      // Refetch apenas dados ativos ao invés de invalidar todo o cache
      queryClient.refetchQueries({
        queryKey: ['students'],
        type: 'active'
      });
    }
    
    return result;
  };

  return {
    toggleStudentStatus,
    isToggling: toggleHook.isToggling,
    error: toggleHook.error
  };
}

/**
 * Hook para operações em lote com estudantes
 */
export function useBulkStudentStatus() {
  const bulkHook = useBulkUserStatus();
  const queryClient = useQueryClient();

  const updateBulkStudentStatus = async (studentUserIds: string[], status: UserStatus) => {
    const result = await bulkHook.updateBulkStatus(studentUserIds, status);
    
    // Para operações em lote, invalidar caches é mais justificado
    if (result?.success) {
      queryClient.invalidateQueries({
        queryKey: ['student-stats'],
        exact: true
      });
      
      queryClient.invalidateQueries({
        queryKey: ['students']
      });
    }
    
    return result;
  };

  return {
    updateBulkStudentStatus,
    isUpdating: bulkHook.isUpdating
  };
}

/**
 * Hook para mapear status para formato compatível com UI existente
 */
export function useStudentStatusMapping() {
  const mapStatusToIsActive = (status: UserStatus): boolean => {
    return status === 'active';
  };

  const mapIsActiveToStatus = (isActive: boolean): UserStatus => {
    return isActive ? 'active' : 'inactive';
  };

  const getStatusLabel = (status: UserStatus): string => {
    const labels: Record<UserStatus, string> = {
      active: 'Ativo',
      inactive: 'Inativo',
      suspended: 'Suspenso'
    };
    return labels[status] || 'Desconhecido';
  };

  const getStatusColor = (status: UserStatus): string => {
    const colors: Record<UserStatus, string> = {
      active: 'text-green-600 dark:text-green-300',
      inactive: 'text-gray-600 dark:text-gray-300',
      suspended: 'text-red-600 dark:text-red-300'
    };
    return colors[status] || 'text-gray-600 dark:text-gray-300';
  };

  return {
    mapStatusToIsActive,
    mapIsActiveToStatus,
    getStatusLabel,
    getStatusColor
  };
} 