/**
 * Utilitários para o Dashboard Financeiro
 * Funções de formatação, cálculos e manipulação de dados
 */

import { 
  DateRange, 
  PeriodType, 
  MetricWithGrowth, 
  TrendDirection,
  FormatOptions 
} from '../types/dashboard-types';

// ============================================================================
// FORMATAÇÃO DE VALORES
// ============================================================================

/**
 * Formata valor monetário para exibição
 */
export const formatCurrency = (
  value: number, 
  options: FormatOptions = {}
): string => {
  const {
    currency = 'BRL',
    locale = 'pt-BR',
    minimumFractionDigits = 2,
    maximumFractionDigits = 2
  } = options;

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    minimumFractionDigits,
    maximumFractionDigits
  }).format(value);
};

/**
 * Formata número para exibição
 */
export const formatNumber = (
  value: number, 
  options: FormatOptions = {}
): string => {
  const {
    locale = 'pt-BR',
    minimumFractionDigits = 0,
    maximumFractionDigits = 0
  } = options;

  return new Intl.NumberFormat(locale, {
    minimumFractionDigits,
    maximumFractionDigits
  }).format(value);
};

/**
 * Formata porcentagem para exibição
 */
export const formatPercentage = (
  value: number,
  options: FormatOptions = {}
): string => {
  const {
    locale = 'pt-BR',
    minimumFractionDigits = 1,
    maximumFractionDigits = 1
  } = options;

  return new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits,
    maximumFractionDigits
  }).format(value / 100);
};

// ============================================================================
// CÁLCULOS DE CRESCIMENTO E TENDÊNCIAS
// ============================================================================

/**
 * Calcula crescimento percentual entre dois valores
 */
export const calculateGrowth = (current: number, previous: number): number => {
  if (previous === 0) {
    return current > 0 ? 100 : 0;
  }
  return ((current - previous) / previous) * 100;
};

/**
 * Determina direção da tendência baseada no crescimento
 */
export const getTrendDirection = (growth: number): TrendDirection => {
  if (growth > 1) return 'up';
  if (growth < -1) return 'down';
  return 'stable';
};

/**
 * Cria métrica com crescimento formatada
 */
export const createMetricWithGrowth = (
  current: number,
  previous: number,
  isPercentage = false,
  isCurrency = true
): MetricWithGrowth => {
  const growth = calculateGrowth(current, previous);
  const trend = getTrendDirection(growth);

  const formatValue = (value: number) => {
    if (isPercentage) return formatPercentage(value);
    if (isCurrency) return formatCurrency(value);
    return formatNumber(value);
  };

  return {
    current,
    previous,
    growth,
    trend,
    formatted: {
      current: formatValue(current),
      previous: formatValue(previous),
      growth: formatPercentage(growth)
    }
  };
};

// ============================================================================
// MANIPULAÇÃO DE DATAS E PERÍODOS
// ============================================================================

/**
 * Obtém range de datas para um período específico
 */
export const getDateRangeForPeriod = (period: PeriodType, customDate?: Date): DateRange => {
  const now = customDate || new Date();
  const year = now.getFullYear();
  const month = now.getMonth();

  switch (period) {
    case 'month': {
      const startDate = new Date(year, month, 1);
      const endDate = new Date(year, month + 1, 0);
      return {
        startDate,
        endDate,
        period,
        label: `${startDate.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' })}`
      };
    }

    case 'quarter': {
      const quarterStart = Math.floor(month / 3) * 3;
      const startDate = new Date(year, quarterStart, 1);
      const endDate = new Date(year, quarterStart + 3, 0);
      return {
        startDate,
        endDate,
        period,
        label: `${quarterStart / 3 + 1}º Trimestre ${year}`
      };
    }

    case 'semester': {
      const semesterStart = month < 6 ? 0 : 6;
      const startDate = new Date(year, semesterStart, 1);
      const endDate = new Date(year, semesterStart + 6, 0);
      return {
        startDate,
        endDate,
        period,
        label: `${semesterStart === 0 ? '1º' : '2º'} Semestre ${year}`
      };
    }

    case 'year': {
      const startDate = new Date(year, 0, 1);
      const endDate = new Date(year, 11, 31);
      return {
        startDate,
        endDate,
        period,
        label: `Ano ${year}`
      };
    }

    default:
      return getDateRangeForPeriod('month', customDate);
  }
};

/**
 * Obtém período anterior para comparação
 */
export const getPreviousPeriodRange = (currentRange: DateRange): DateRange => {
  const { startDate, endDate, period } = currentRange;
  const diffMs = endDate.getTime() - startDate.getTime();

  const prevEndDate = new Date(startDate.getTime() - 1);
  const prevStartDate = new Date(prevEndDate.getTime() - diffMs);

  return {
    startDate: prevStartDate,
    endDate: prevEndDate,
    period,
    label: `Período anterior`
  };
};

/**
 * Formata range de datas para exibição
 */
export const formatDateRange = (range: DateRange): string => {
  const { startDate, endDate } = range;
  
  if (startDate.getMonth() === endDate.getMonth() && 
      startDate.getFullYear() === endDate.getFullYear()) {
    return startDate.toLocaleDateString('pt-BR', { 
      month: 'long', 
      year: 'numeric' 
    });
  }

  return `${startDate.toLocaleDateString('pt-BR', { 
    day: '2-digit', 
    month: 'short' 
  })} - ${endDate.toLocaleDateString('pt-BR', { 
    day: '2-digit', 
    month: 'short', 
    year: 'numeric' 
  })}`;
};

// ============================================================================
// UTILITÁRIOS DE VALIDAÇÃO
// ============================================================================

/**
 * Valida se um valor é um número válido
 */
export const isValidNumber = (value: any): value is number => {
  return typeof value === 'number' && !isNaN(value) && isFinite(value);
};

/**
 * Garante que um valor seja um número válido
 */
export const ensureNumber = (value: any, fallback = 0): number => {
  return isValidNumber(value) ? value : fallback;
};

/**
 * Valida se uma data é válida
 */
export const isValidDate = (date: any): date is Date => {
  return date instanceof Date && !isNaN(date.getTime());
};

// ============================================================================
// UTILITÁRIOS DE CORES E TEMAS
// ============================================================================

/**
 * Obtém cor baseada na tendência
 */
export const getTrendColor = (trend: TrendDirection): string => {
  switch (trend) {
    case 'up': return 'text-green-600 dark:text-green-400';
    case 'down': return 'text-red-600 dark:text-red-400';
    case 'stable': return 'text-gray-600 dark:text-gray-400';
  }
};

/**
 * Obtém ícone baseado na tendência
 */
export const getTrendIcon = (trend: TrendDirection): string => {
  switch (trend) {
    case 'up': return '↗';
    case 'down': return '↘';
    case 'stable': return '→';
  }
};

// ============================================================================
// UTILITÁRIOS DE DEBOUNCE E THROTTLE
// ============================================================================

/**
 * Debounce para otimizar chamadas de API
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Throttle para limitar frequência de execução
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};
