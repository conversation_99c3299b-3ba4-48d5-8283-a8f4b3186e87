'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Plus, Calendar, Clock, User, MapPin, Save } from 'lucide-react';
import DatePicker from '@/components/ui/calendar-01';

interface CreateClassModalProps {
  groupId: string;
  groupName: string;
  trigger?: React.ReactNode;
}

export function CreateClassModal({ groupId, groupName, trigger }: CreateClassModalProps) {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    date: undefined as Date | undefined,
    startTime: '',
    endTime: '',
    maxCapacity: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nome da aula é obrigatório';
    }

    if (!formData.date) {
      newErrors.date = 'Data da aula é obrigatória';
    }

    if (!formData.startTime) {
      newErrors.startTime = 'Horário de início é obrigatório';
    }

    if (!formData.endTime) {
      newErrors.endTime = 'Horário de fim é obrigatório';
    }

    if (formData.startTime && formData.endTime) {
      const start = new Date(`2000-01-01T${formData.startTime}`);
      const end = new Date(`2000-01-01T${formData.endTime}`);
      
      if (start >= end) {
        newErrors.endTime = 'Horário de fim deve ser após o horário de início';
      }
    }

    if (formData.maxCapacity && (parseInt(formData.maxCapacity) < 1 || parseInt(formData.maxCapacity) > 100)) {
      newErrors.maxCapacity = 'Capacidade deve ser entre 1 e 100';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Navegar para a página de criação de aula com os dados como query params
      const params = new URLSearchParams({
        name: formData.name,
        description: formData.description,
        date: formData.date!.toISOString().split('T')[0],
        startTime: formData.startTime,
        endTime: formData.endTime,
        ...(formData.maxCapacity && { maxCapacity: formData.maxCapacity }),
      });

      router.push(`/turmas/${groupId}/aulas/nova?${params.toString()}`);
      setIsOpen(false);
    } catch (error) {
      console.error('Erro ao criar aula:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Limpar erro do campo quando o usuário começar a digitar
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      date: undefined,
      startTime: '',
      endTime: '',
      maxCapacity: '',
    });
    setErrors({});
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      resetForm();
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('pt-BR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const defaultTrigger = (
    <Button className="bg-blue-600 hover:bg-blue-700 text-white">
      <Plus className="h-4 w-4 mr-2" />
      Nova Aula
    </Button>
  );

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5 text-blue-600" />
            <span>Nova Aula - {groupName}</span>
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Nome da Aula */}
          <div className="space-y-2">
            <Label htmlFor="name" className="flex items-center space-x-1">
              <span>Nome da Aula</span>
              <span className="text-red-500">*</span>
            </Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Ex: Treino de Jiu-Jitsu"
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name}</p>
            )}
          </div>

          {/* Descrição */}
          <div className="space-y-2">
            <Label htmlFor="description">Descrição (opcional)</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Descrição da aula..."
              rows={3}
            />
          </div>

          {/* Data da Aula */}
          <div className="space-y-2">
            <Label className="flex items-center space-x-1">
              <span>Data da Aula</span>
              <span className="text-red-500">*</span>
            </Label>
            <DatePicker
              date={formData.date}
              onDateChange={(date) => handleInputChange('date', date)}
              placeholder="Selecione a data"
              className={errors.date ? 'border-red-500' : ''}
              minDate={new Date()}
            />
            {errors.date && (
              <p className="text-sm text-red-600">{errors.date}</p>
            )}
            {formData.date && (
              <p className="text-sm text-blue-600">
                {formatDate(formData.date)}
              </p>
            )}
          </div>

          {/* Horários */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startTime" className="flex items-center space-x-1">
                <Clock className="h-4 w-4" />
                <span>Início</span>
                <span className="text-red-500">*</span>
              </Label>
              <Input
                id="startTime"
                type="time"
                value={formData.startTime}
                onChange={(e) => handleInputChange('startTime', e.target.value)}
                className={errors.startTime ? 'border-red-500' : ''}
              />
              {errors.startTime && (
                <p className="text-sm text-red-600">{errors.startTime}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="endTime" className="flex items-center space-x-1">
                <Clock className="h-4 w-4" />
                <span>Fim</span>
                <span className="text-red-500">*</span>
              </Label>
              <Input
                id="endTime"
                type="time"
                value={formData.endTime}
                onChange={(e) => handleInputChange('endTime', e.target.value)}
                className={errors.endTime ? 'border-red-500' : ''}
              />
              {errors.endTime && (
                <p className="text-sm text-red-600">{errors.endTime}</p>
              )}
            </div>
          </div>

          {/* Capacidade Máxima */}
          <div className="space-y-2">
            <Label htmlFor="maxCapacity" className="flex items-center space-x-1">
              <User className="h-4 w-4" />
              <span>Capacidade Máxima (opcional)</span>
            </Label>
            <Input
              id="maxCapacity"
              type="number"
              min="1"
              max="100"
              value={formData.maxCapacity}
              onChange={(e) => handleInputChange('maxCapacity', e.target.value)}
              placeholder="Ex: 20"
              className={errors.maxCapacity ? 'border-red-500' : ''}
            />
            {errors.maxCapacity && (
              <p className="text-sm text-red-600">{errors.maxCapacity}</p>
            )}
          </div>

          {/* Botões */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isSubmitting}
            >
              Cancelar
            </Button>
            
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Criando...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Continuar
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
} 