'use client';

import { useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { CACHE_KEYS } from '@/constants/cache-keys';
import { formatAddressFields } from '@/utils/address-utils';
import { cacheService } from '@/services/cache';

/**
 * Hook para atualizar o cache de perfil do usuário
 * @returns Funções para atualizar o cache de perfil
 */
export function useUpdateProfileCache() {
  const queryClient = useQueryClient();
  
  const updateAddressCache = useCallback((userId: string, addressData: any) => {
    if (!userId || !queryClient) return;
    
    try {
      // Verificar se temos dados no cache usando a chave do usuário autenticado
      let currentData = queryClient.getQueryData(CACHE_KEYS.USER_METADATA);
      
      if (!currentData) {
        console.warn('[CACHE] Dados de perfil não encontrados no cache para atualização de endereço');
        return;
      }
      
      const updatedData = { ...currentData } as any;
      const addressFields = [
        'street',
        'street_number',
        'complement',
        'neighborhood',
        'city',
        'state',
        'postal_code',
        'address'
      ];
      
      // Atualizar campos de endereço
      addressFields.forEach(field => {
        if (addressData[field] !== undefined) {
          updatedData[field] = addressData[field];
        }
      });
      
      // Processar dados para garantir que endereço completo seja formatado corretamente
      const processedData = formatAddressFields(updatedData);
      
      // Atualizar o cache usando o serviço centralizado
      cacheService.setData(CACHE_KEYS.USER_METADATA, processedData);
      console.log('[CACHE] Cache de endereço atualizado para o usuário', userId);
      
      // Invalidar a query para forçar atualização
      cacheService.invalidateQueries(CACHE_KEYS.USER_METADATA);
    } catch (error) {
      console.error('[CACHE] Erro ao atualizar cache de endereço:', error);
    }
  }, [queryClient]);
  
  const invalidateProfileCache = useCallback((userId: string) => {
    if (!userId) return;
    
    // Invalidar o cache de metadados
    cacheService.invalidateQueries(CACHE_KEYS.USER_METADATA);
    cacheService.invalidateQueries(CACHE_KEYS.USER_PROFILE);
    
    // Recarregar os dados
    setTimeout(() => {
      cacheService.refetchQueries(CACHE_KEYS.USER_METADATA);
      cacheService.refetchQueries(CACHE_KEYS.USER_PROFILE);
    }, 100);
    
    console.log('[CACHE] Cache de perfil invalidado para o usuário', userId);
  }, []);
  
  return {
    updateAddressCache,
    invalidateProfileCache
  };
} 