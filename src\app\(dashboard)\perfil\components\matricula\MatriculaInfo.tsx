import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { FileText, Calendar, Shield } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'active':
      return 'default';
    case 'inactive':
      return 'destructive';
    case 'suspended':
      return 'secondary';
    case 'paused':
      return 'secondary';
    default:
      return 'outline';
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-950';
    case 'inactive':
      return 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-950';
    case 'suspended':
      return 'text-yellow-600 bg-yellow-50 dark:text-yellow-400 dark:bg-yellow-950';
    case 'paused':
      return 'text-orange-600 bg-orange-50 dark:text-orange-400 dark:bg-orange-950';
    default:
      return 'text-slate-600 bg-slate-50 dark:text-slate-400 dark:bg-slate-950';
  }
};

const translateStatus = (status: string) => {
  const translations: Record<string, string> = {
    active: 'Ativa',
    inactive: 'Inativa',
    suspended: 'Suspensa',
    paused: 'Pausada',
    completed: 'Concluída',
  };
  return translations[status] || status;
};

export function MatriculaInfo({ enrollment }: { enrollment: any }) {
  if (!enrollment) return null;

  const { enrollmentDate, status } = enrollment;

  return (
    <Card className="p-6 bg-white dark:bg-slate-800">
      <div className="flex items-center gap-3 mb-6">
        <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Detalhes da Matrícula
        </h3>
      </div>

      <div className="grid gap-4 sm:grid-cols-2">
        <div className="flex items-center gap-3 p-4 rounded-lg bg-slate-50 dark:bg-slate-700/50">
          <Calendar className="h-5 w-5 text-slate-600 dark:text-slate-400" />
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Data de Início</p>
            <p className="font-semibold text-gray-900 dark:text-gray-100">
              {(() => {
                try {
                  const date = new Date(enrollmentDate);
                  if (isNaN(date.getTime())) {
                    return 'Data inválida';
                  }
                  return format(date, "dd 'de' MMMM 'de' yyyy", { locale: ptBR });
                } catch (error) {
                  console.error('Erro ao formatar data de matrícula:', error);
                  return 'Data inválida';
                }
              })()}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3 p-4 rounded-lg bg-slate-50 dark:bg-slate-700/50">
          <Shield className="h-5 w-5 text-slate-600 dark:text-slate-400" />
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Status</p>
            <div className="mt-1">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
                {translateStatus(status)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
} 