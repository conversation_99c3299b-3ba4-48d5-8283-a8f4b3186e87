'use server';

import { createTenantServerClient } from '@/services/supabase/server';
import { PaymentMethodOption } from '../components/types';

export async function getPaymentMethods(): Promise<PaymentMethodOption[]> {
  try {
    // Importar next/headers dinamicamente
    const { headers, cookies } = await import('next/headers');
    const [headersList, cookieStore] = await Promise.all([
      headers(),
      cookies()
    ]);

    // Resolvemos o tenant no próprio servidor para não expor o ID para o cliente
    const extractor = new (await import('@/services/tenant/tenant-extractor-server')).TenantExtractorServer();
    const tenant = await extractor.getTenantIdentifier(headersList, cookieStore);

    if (!tenant.id) {
      console.error('[getPaymentMethods] Tenant ID não encontrado');
      return [];
    }

    const supabase = await createTenantServerClient();

    // Buscar todos os métodos de pagamento disponíveis
    const { data: paymentMethods, error: pmError } = await supabase
      .from('payment_methods')
      .select('id, name, slug')
      .order('name');

    if (pmError) {
      console.error('[getPaymentMethods] erro ao buscar payment_methods', pmError);
      return [];
    }

    // Buscar configurações do tenant (apenas métodos habilitados)
    const { data: tenantSettings, error: tsError } = await supabase
      .from('tenant_payment_settings')
      .select('payment_method_id')
      .eq('tenant_id', tenant.id)
      .eq('enabled', true);

    if (tsError) {
      console.error('[getPaymentMethods] erro ao buscar tenant_payment_settings', tsError);
      return [];
    }

    // Criar um Set com os IDs dos métodos habilitados para o tenant
    const enabledSet = new Set(
      (tenantSettings ?? []).map((s) => s.payment_method_id as string)
    );

    // Filtrar apenas os métodos habilitados para o tenant
    const enabledMethods = (paymentMethods ?? []).filter((pm) => enabledSet.has(pm.id));

    // Log para debug
    console.log('Métodos de pagamento habilitados para o tenant:', {
      tenant_id: tenant.id,
      enabledMethods: enabledMethods.map(m => ({ id: m.id, name: m.name, slug: m.slug })),
      enabledSet: Array.from(enabledSet)
    });

    return enabledMethods;
  } catch (error) {
    console.error('[getPaymentMethods] Erro inesperado:', error);
    return [];
  }
}
