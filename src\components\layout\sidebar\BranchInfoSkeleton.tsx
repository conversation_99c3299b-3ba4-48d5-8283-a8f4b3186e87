import { Skeleton } from "@/components/ui/skeleton";
import clsx from "clsx";

interface BranchInfoSkeletonProps {
  isHovered: boolean;
  themeColor: string;
}

export function BranchInfoSkeleton({ isHovered, themeColor }: BranchInfoSkeletonProps) {
  return (
    <div className={clsx(
      "flex items-center gap-x-2 py-3",
      "border-t border-t-gray-100/50 dark:border-t-gray-800/40",
      isHovered ? "px-4" : "justify-center"
    )}
    style={{ borderColor: `${themeColor}20` }}>
      <Skeleton 
        className={clsx(
          "shrink-0 rounded-full",
          isHovered ? "h-7 w-7" : "h-8 w-8"
        )}
      />
      
      {isHovered && (
        <div className="flex flex-col gap-y-1 min-w-0 overflow-hidden">
          <Skeleton className="h-3 w-12" />
          <Skeleton className="h-4 w-24" />
        </div>
      )}
    </div>
  );
} 