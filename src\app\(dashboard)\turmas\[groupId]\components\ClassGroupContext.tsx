'use client';

import { createContext, useContext, useReducer, ReactNode, useCallback } from 'react';
import { ClassGroupWithDetails } from '@/app/(dashboard)/aulas/types';
import { getClassGroupById } from '../../actions/class-group';
import { updateClassGroup as updateClassGroupAction } from '../../actions/class-group/update-class-group';

// Tipos para o estado da página de detalhes da turma
export interface ClassGroupState {
  // Dados principais
  classGroup: ClassGroupWithDetails | null;
  isLoading: boolean;
  error: string | null;
  
  // Estado das abas
  activeTab: 'overview' | 'timeline' | 'statistics' | 'calendar';
  
  // Estado dos dados das abas
  statistics: {
    enrollments: number;
    waitlist: number;
    classes: number;
    attendance: {
      present: number;
      absent: number;
      total: number;
      rate: number;
    };
  } | null;
  
  timeline: {
    events: any[];
    isLoading: boolean;
  };
  
  calendar: {
    events: any[];
    selectedDate: Date | null;
    currentMonth: Date;
    isLoading: boolean;
  };
  
  // Configurações de UI
  refreshing: boolean;
  lastRefresh: Date | null;
  
  // Estados para atualizações em tempo real
  wasRecentlyUpdated: boolean;
  isUpdating: boolean;
}

// Tipos para as ações do reducer
export type ClassGroupAction =
  | { type: 'SET_CLASS_GROUP'; payload: ClassGroupWithDetails }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_ACTIVE_TAB'; payload: ClassGroupState['activeTab'] }
  | { type: 'SET_STATISTICS'; payload: ClassGroupState['statistics'] }
  | { type: 'SET_TIMELINE_EVENTS'; payload: any[] }
  | { type: 'SET_TIMELINE_LOADING'; payload: boolean }
  | { type: 'SET_CALENDAR_EVENTS'; payload: any[] }
  | { type: 'SET_CALENDAR_SELECTED_DATE'; payload: Date | null }
  | { type: 'SET_CALENDAR_CURRENT_MONTH'; payload: Date }
  | { type: 'SET_CALENDAR_LOADING'; payload: boolean }
  | { type: 'SET_REFRESHING'; payload: boolean }
  | { type: 'SET_LAST_REFRESH'; payload: Date }
  | { type: 'SET_RECENTLY_UPDATED'; payload: boolean }
  | { type: 'SET_UPDATING'; payload: boolean }
  | { type: 'RESET_STATE' };

// Estado inicial
const initialState: ClassGroupState = {
  classGroup: null,
  isLoading: false,
  error: null,
  activeTab: 'overview',
  statistics: null,
  timeline: {
    events: [],
    isLoading: false,
  },
  calendar: {
    events: [],
    selectedDate: new Date(),
    currentMonth: new Date(),
    isLoading: false,
  },
  refreshing: false,
  lastRefresh: null,
  wasRecentlyUpdated: false,
  isUpdating: false,
};

// Reducer para gerenciar o estado
function classGroupReducer(
  state: ClassGroupState,
  action: ClassGroupAction
): ClassGroupState {
  switch (action.type) {
    case 'SET_CLASS_GROUP':
      return {
        ...state,
        classGroup: action.payload,
        error: null,
        wasRecentlyUpdated: true,
      };
    
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    
    case 'SET_ACTIVE_TAB':
      return {
        ...state,
        activeTab: action.payload,
      };
    
    case 'SET_STATISTICS':
      return {
        ...state,
        statistics: action.payload,
      };
    
    case 'SET_TIMELINE_EVENTS':
      return {
        ...state,
        timeline: {
          ...state.timeline,
          events: action.payload,
        },
      };
    
    case 'SET_TIMELINE_LOADING':
      return {
        ...state,
        timeline: {
          ...state.timeline,
          isLoading: action.payload,
        },
      };
    
    case 'SET_CALENDAR_EVENTS':
      return {
        ...state,
        calendar: {
          ...state.calendar,
          events: action.payload,
        },
      };
    
    case 'SET_CALENDAR_SELECTED_DATE':
      return {
        ...state,
        calendar: {
          ...state.calendar,
          selectedDate: action.payload,
        },
      };
    
    case 'SET_CALENDAR_CURRENT_MONTH':
      return {
        ...state,
        calendar: {
          ...state.calendar,
          currentMonth: action.payload,
        },
      };
    
    case 'SET_CALENDAR_LOADING':
      return {
        ...state,
        calendar: {
          ...state.calendar,
          isLoading: action.payload,
        },
      };
    
    case 'SET_REFRESHING':
      return {
        ...state,
        refreshing: action.payload,
      };
    
    case 'SET_LAST_REFRESH':
      return {
        ...state,
        lastRefresh: action.payload,
        refreshing: false,
      };
    
    case 'SET_RECENTLY_UPDATED':
      return {
        ...state,
        wasRecentlyUpdated: action.payload,
      };
    
    case 'SET_UPDATING':
      return {
        ...state,
        isUpdating: action.payload,
      };
    
    case 'RESET_STATE':
      return initialState;
    
    default:
      return state;
  }
}

// Contexto
const ClassGroupContext = createContext<ClassGroupContextType | null>(null);

// Interface do contexto
interface ClassGroupContextType {
  state: ClassGroupState;
  dispatch: React.Dispatch<ClassGroupAction>;
  
  // Funções helper para facilitar o uso
  setClassGroup: (classGroup: ClassGroupWithDetails) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setActiveTab: (tab: ClassGroupState['activeTab']) => void;
  setStatistics: (statistics: ClassGroupState['statistics']) => void;
  setTimelineEvents: (events: any[]) => void;
  setTimelineLoading: (loading: boolean) => void;
  setCalendarEvents: (events: any[]) => void;
  setCalendarSelectedDate: (date: Date | null) => void;
  setCalendarCurrentMonth: (date: Date) => void;
  setCalendarLoading: (loading: boolean) => void;
  setRefreshing: (refreshing: boolean) => void;
  markRefreshed: () => void;
  resetState: () => void;
  
  // Novas funções para atualizações em tempo real
  markAsRecentlyUpdated: () => void;
  
  // Funções utilitárias
  refreshAllData: () => Promise<void>;
  refreshClassGroupData: () => Promise<void>;
  getTabLoadingState: (tab: ClassGroupState['activeTab']) => boolean;
  hasData: () => boolean;
  getLastRefreshText: () => string;
  
  // Função para atualizar turma
  updateClassGroup: (data: any) => Promise<any>;
  isUpdating: boolean;
  setUpdating: (updating: boolean) => void;
}

// Provider do contexto
export function ClassGroupProvider({ 
  children, 
  initialClassGroup 
}: { 
  children: ReactNode;
  initialClassGroup?: ClassGroupWithDetails;
}) {
  const [state, dispatch] = useReducer(classGroupReducer, {
    ...initialState,
    classGroup: initialClassGroup || null,
  });

  // Funções helper para facilitar o uso do contexto
  const setClassGroup = useCallback((classGroup: ClassGroupWithDetails) => {
    dispatch({ type: 'SET_CLASS_GROUP', payload: classGroup });
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  }, []);

  const setError = useCallback((error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  }, []);

  const setActiveTab = useCallback((tab: ClassGroupState['activeTab']) => {
    dispatch({ type: 'SET_ACTIVE_TAB', payload: tab });
  }, []);

  const setStatistics = useCallback((statistics: ClassGroupState['statistics']) => {
    dispatch({ type: 'SET_STATISTICS', payload: statistics });
  }, []);

  const setTimelineEvents = useCallback((events: any[]) => {
    dispatch({ type: 'SET_TIMELINE_EVENTS', payload: events });
  }, []);

  const setTimelineLoading = useCallback((loading: boolean) => {
    dispatch({ type: 'SET_TIMELINE_LOADING', payload: loading });
  }, []);

  const setCalendarEvents = useCallback((events: any[]) => {
    dispatch({ type: 'SET_CALENDAR_EVENTS', payload: events });
  }, []);

  const setCalendarSelectedDate = useCallback((date: Date | null) => {
    dispatch({ type: 'SET_CALENDAR_SELECTED_DATE', payload: date });
  }, []);

  const setCalendarCurrentMonth = useCallback((date: Date) => {
    dispatch({ type: 'SET_CALENDAR_CURRENT_MONTH', payload: date });
  }, []);

  const setCalendarLoading = useCallback((loading: boolean) => {
    dispatch({ type: 'SET_CALENDAR_LOADING', payload: loading });
  }, []);

  const setRefreshing = useCallback((refreshing: boolean) => {
    dispatch({ type: 'SET_REFRESHING', payload: refreshing });
  }, []);

  const markRefreshed = useCallback(() => {
    dispatch({ type: 'SET_LAST_REFRESH', payload: new Date() });
  }, []);

  const resetState = useCallback(() => {
    dispatch({ type: 'RESET_STATE' });
  }, []);

  const markAsRecentlyUpdated = useCallback(() => {
    dispatch({ type: 'SET_RECENTLY_UPDATED', payload: true });
  }, []);

  // Função para refresh específico dos dados da turma
  const refreshClassGroupData = useCallback(async () => {
    if (!state.classGroup?.id) return;

    setRefreshing(true);
    
    try {
      const result = await getClassGroupById(state.classGroup.id);
      
      if (result.success && 'data' in result && result.data) {
        setClassGroup(result.data);
        markRefreshed();
      } else {
        setError('Erro ao atualizar dados da turma');
      }
    } catch (error) {
      console.error('Erro ao atualizar dados da turma:', error);
      setError('Erro ao atualizar dados da turma');
    }
  }, [state.classGroup?.id, setRefreshing, setClassGroup, markRefreshed, setError]);

  // Função para refresh completo dos dados
  const refreshAllData = useCallback(async () => {
    if (!state.classGroup?.id) return;

    setRefreshing(true);
    
    try {
      // Primeiro, atualizar os dados principais da turma
      await refreshClassGroupData();
      
      // Aqui você pode implementar a lógica para recarregar todos os outros dados
      // Por exemplo, recarregar estatísticas, timeline e calendário
      
    } catch (error) {
      console.error('Erro ao atualizar dados:', error);
      setError('Erro ao atualizar dados');
    }
  }, [state.classGroup?.id, setRefreshing, refreshClassGroupData, setError]);

  // Função para obter estado de loading de uma aba específica
  const getTabLoadingState = useCallback((tab: ClassGroupState['activeTab']) => {
    switch (tab) {
      case 'overview':
        return state.isLoading;
      case 'timeline':
        return state.timeline.isLoading;
      case 'statistics':
        return state.isLoading;
      case 'calendar':
        return state.calendar.isLoading;
      default:
        return false;
    }
  }, [state.isLoading, state.timeline.isLoading, state.calendar.isLoading]);

  // Verificar se há dados carregados
  const hasData = useCallback(() => {
    return !!state.classGroup;
  }, [state.classGroup]);

  // Obter texto de último refresh
  const getLastRefreshText = useCallback(() => {
    if (!state.lastRefresh) return 'Nunca atualizado';
    
    const now = new Date();
    const diff = now.getTime() - state.lastRefresh.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Atualizado agora';
    if (minutes === 1) return 'Atualizado há 1 minuto';
    if (minutes < 60) return `Atualizado há ${minutes} minutos`;
    
    const hours = Math.floor(minutes / 60);
    if (hours === 1) return 'Atualizado há 1 hora';
    if (hours < 24) return `Atualizado há ${hours} horas`;
    
    return state.lastRefresh.toLocaleDateString('pt-BR');
  }, [state.lastRefresh]);

  // Função para atualizar turma
  const updateClassGroup = useCallback(async (data: any) => {
    if (!state.classGroup?.id) return;

    dispatch({ type: 'SET_UPDATING', payload: true });
    setRefreshing(true);
    
    try {
      // Chamar a action de atualização
      const result = await updateClassGroupAction(data);
      
      if (result.success) {
        // Recarregar dados atualizados
        await refreshClassGroupData();
        return result;
      } else {
        setError('Erro ao atualizar dados da turma');
        return result;
      }
    } catch (error) {
      console.error('Erro ao atualizar dados da turma:', error);
      setError('Erro ao atualizar dados da turma');
      return { 
        success: false, 
        errors: { 
          _form: error instanceof Error ? error.message : "Erro interno do servidor" 
        } 
      };
    } finally {
      setRefreshing(false);
      dispatch({ type: 'SET_UPDATING', payload: false });
    }
  }, [state.classGroup?.id, dispatch, setRefreshing, refreshClassGroupData, setError]);

  const setUpdating = useCallback((updating: boolean) => {
    dispatch({ type: 'SET_UPDATING', payload: updating });
  }, [dispatch]);

  const contextValue: ClassGroupContextType = {
    state,
    dispatch,
    setClassGroup,
    setLoading,
    setError,
    setActiveTab,
    setStatistics,
    setTimelineEvents,
    setTimelineLoading,
    setCalendarEvents,
    setCalendarSelectedDate,
    setCalendarCurrentMonth,
    setCalendarLoading,
    setRefreshing,
    markRefreshed,
    resetState,
    markAsRecentlyUpdated,
    refreshAllData,
    refreshClassGroupData,
    getTabLoadingState,
    hasData,
    getLastRefreshText,
    updateClassGroup,
    isUpdating: state.isUpdating,
    setUpdating,
  };

  return (
    <ClassGroupContext.Provider value={contextValue}>
      {children}
    </ClassGroupContext.Provider>
  );
}

// Hook para usar o contexto
export function useClassGroup() {
  const context = useContext(ClassGroupContext);
  if (!context) {
    throw new Error('useClassGroup deve ser usado dentro de ClassGroupProvider');
  }
  return context;
} 