"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/services/supabase/server";
import { AddToWaitlistSchema } from "../schemas/class-waitlist";
import { validateUserAuthentication, validateClassGroup, validateStudent } from "./shared/validation-helpers";

/**
 * Adiciona um aluno à lista de espera de uma turma
 */
export async function addToWaitlist(data: unknown) {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;

    const validationResult = AddToWaitlistSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const { class_group_id, student_id, notes } = validationResult.data;
    const supabase = await createAdminClient();

    // Verificar se a turma existe
    const classGroupValidation = await validateClassGroup(class_group_id, tenantId);
    if (!classGroupValidation.success) {
      return classGroupValidation;
    }

    // Verificar se o aluno existe
    const studentValidation = await validateStudent(student_id, tenantId);
    if (!studentValidation.success) {
      return studentValidation;
    }

    // Verificar se o aluno já está na lista de espera
    const { data: existingWaitlist } = await supabase
      .from("class_waitlist")
      .select("id")
      .eq("class_group_id", class_group_id)
      .eq("student_id", student_id)
      .eq("status", "waiting")
      .single();

    if (existingWaitlist) {
      return { success: false, errors: { student_id: "Aluno já está na lista de espera" } };
    }

    // Verificar se o aluno já está matriculado
    const { data: existingEnrollment } = await supabase
      .from("class_group_enrollments")
      .select("id")
      .eq("class_group_id", class_group_id)
      .eq("student_id", student_id)
      .eq("status", "active")
      .single();

    if (existingEnrollment) {
      return { success: false, errors: { student_id: "Aluno já está matriculado nesta turma" } };
    }

    // Obter próxima posição na lista de espera
    const { count: waitlistCount } = await supabase
      .from("class_waitlist")
      .select("*", { count: "exact", head: true })
      .eq("class_group_id", class_group_id)
      .eq("status", "waiting");

    const nextPosition = (waitlistCount || 0) + 1;

    // Adicionar à lista de espera
    const { data: waitlistEntry, error } = await supabase
      .from("class_waitlist")
      .insert({
        class_group_id,
        student_id,
        tenant_id: tenantId,
        position: nextPosition,
        status: "waiting",
        notes,
      })
      .select()
      .single();

    if (error) {
      console.error("Erro ao adicionar à lista de espera:", error);
      return { success: false, errors: { _form: "Erro ao adicionar à lista de espera" } };
    }

    revalidatePath("/aulas");
    return { 
      success: true, 
      data: waitlistEntry, 
      message: `Aluno adicionado à lista de espera na posição ${nextPosition}` 
    };
  } catch (error) {
    console.error("Erro ao adicionar à lista de espera:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 