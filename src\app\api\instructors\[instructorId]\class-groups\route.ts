import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/services/supabase/server";
import { getCurrentUser } from "@/services/auth/actions/auth-actions";
import { z } from 'zod';

const instructorIdSchema = z.string().uuid({
  message: "ID de instrutor inválido"
});

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ instructorId: string }> }
) {
  try {
    const supabase = await createClient();
    const { instructorId } = await params;
    
    const validatedParams = instructorIdSchema.safeParse(instructorId);
    if (!validatedParams.success) {
      return NextResponse.json(
        { error: 'ID de instrutor inválido' },
        { status: 400 }
      );
    }
    
    const validatedInstructorId = validatedParams.data;
    
    const currentUser = await getCurrentUser();
    if (!currentUser) {
      return NextResponse.json(
        { error: 'Não autenticado' },
        { status: 401 }
      );
    }

    const tenantId = currentUser.app_metadata?.tenant_id;
    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant não identificado' },
        { status: 400 }
      );
    }

    // Buscar turmas que o instrutor leciona
    const { data: classGroups, error } = await supabase
      .from('class_groups')
      .select(`
        id,
        name,
        description,
        category,
        is_active,
        max_capacity,
        min_age,
        max_age,
        min_belt_level,
        max_belt_level,
        start_date,
        end_date,
        created_at,
        branch:branches!class_groups_branch_id_fkey(
          id,
          name
        )
      `)
      .eq('instructor_id', validatedInstructorId)
      .eq('tenant_id', tenantId)
      .is('deleted_at', null)
      .order('name');

    if (error) {
      console.error('Erro ao buscar turmas do instrutor:', error);
      return NextResponse.json(
        { error: 'Erro ao buscar turmas' },
        { status: 500 }
      );
    }

    // Para cada turma, buscar as matrículas ativas e dados dos alunos
    const transformedData = await Promise.all(
      (classGroups || []).map(async (group) => {
        // Buscar matrículas ativas para esta turma
        const { data: enrollments, error: enrollmentError } = await supabase
          .from('class_group_enrollments')
          .select(`
            id,
            status,
            student:students!class_group_enrollments_student_id_fkey(
              id,
              user:users!students_user_id_fkey(
                id,
                first_name,
                last_name,
                full_name,
                avatar_url
              )
            )
          `)
          .eq('class_group_id', group.id)
          .eq('status', 'active');

        if (enrollmentError) {
          console.error('Erro ao buscar matrículas:', enrollmentError);
        }

        const activeEnrollments = enrollments || [];
        const totalStudents = activeEnrollments.length;
        const capacityUsage = group.max_capacity 
          ? Math.round((totalStudents / group.max_capacity) * 100)
          : null;

        const students = await Promise.all(activeEnrollments.map(async (enrollment: any) => {
          const student = enrollment.student;
          const user = student?.user;

          // Obter faixa atual via RPC
          let beltColor: string | undefined;
          let degree: number | undefined;
          if (student) {
            const { data: beltDetails } = await supabase.rpc('get_student_current_belt_details', { student_id_param: student.id });
            if (beltDetails && beltDetails.length > 0) {
              beltColor = beltDetails[0].belt_color;
              degree = beltDetails[0].degree;
            }
          }

          return {
            id: student?.id,
            name: user?.full_name || `${user?.first_name || ''} ${user?.last_name || ''}`.trim(),
            avatar: user?.avatar_url,
            beltColor,
            degree,
          };
        }));

        return {
          ...group,
          totalStudents,
          capacityUsage,
          students
        };
      })
    );

    return NextResponse.json({
      success: true,
      data: transformedData
    });

  } catch (error) {
    console.error('Erro inesperado ao buscar turmas do instrutor:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 