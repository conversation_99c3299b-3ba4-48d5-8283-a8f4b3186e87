'use client';

import { useEffect } from 'react';
import { useToast } from '@/components/toast-notification';

/**
 * Componente para exibir notificações de acesso negado
 * Exibe um toast animado que desaparece automaticamente
 */
export function AccessDeniedNotification({ 
  errorType, 
  duration = 5000 
}: { 
  errorType: string;
  duration?: number;
}) {
  const { showToast } = useToast();
  
  useEffect(() => {
    // Mapear tipos de erro para mensagens amigáveis
    let message = "Você não tem permissão para acessar esta área.";
    
    if (errorType === 'acesso-negado-alunos') {
      message = "Apenas administradores podem acessar a área de gerenciamento de alunos.";
    } else if (errorType === 'acesso-negado') {
      message = "Você não tem permissão para acessar a área solicitada.";
    } else if (errorType === 'sem-permissao-criar') {
      message = "Você não tem permissão para criar novos registros.";
    }
    
    // Exibir o toast com a mensagem apropriada
    showToast({
      message,
      variant: 'error',
      duration,
      position: 'bottom-right'
    });
  }, [errorType, duration, showToast]);
  
  // Este componente não renderiza nada visualmente
  return null;
} 