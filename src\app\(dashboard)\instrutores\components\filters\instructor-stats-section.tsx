'use client'

import { useInstructorStats } from '../../hooks/use-instructor-stats'
import { InstructorStats } from '../../types/types'
import { Skeleton } from '@/components/ui/skeleton'

interface InstructorStatsSectionProps {
  initialStats?: InstructorStats
}

export function InstructorStatsSection({ initialStats }: InstructorStatsSectionProps) {
  const { stats, isLoading, error } = useInstructorStats(initialStats)

  if (error) {
    return (
      <div className="p-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="rounded-md border bg-destructive/10 p-3">
          <div className="text-sm text-destructive">Erro ao carregar estatísticas</div>
          <div className="text-xs text-muted-foreground mt-1">Tente recarregar a página</div>
        </div>
      </div>
    )
  }

  // Calcular total de especialidades
  const totalSpecialties = Object.values(stats.specialistCount).reduce((sum, count) => sum + count, 0)

  return (
    <div className="p-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      <div className="rounded-md border bg-background/50 p-3">
        <div className="text-sm text-muted-foreground">Total de Instrutores</div>
        {isLoading ? (
          <Skeleton className="h-8 w-16 mt-1" />
        ) : (
          <div className="text-2xl font-bold">{stats.total}</div>
        )}
      </div>
      <div className="rounded-md border bg-background/50 p-3">
        <div className="text-sm text-muted-foreground">Ativos</div>
        {isLoading ? (
          <Skeleton className="h-8 w-16 mt-1" />
        ) : (
          <div className="text-2xl font-bold text-primary">{stats.active}</div>
        )}
      </div>
      <div className="rounded-md border bg-background/50 p-3">
        <div className="text-sm text-muted-foreground">Inativos</div>
        {isLoading ? (
          <Skeleton className="h-8 w-16 mt-1" />
        ) : (
          <div className="text-2xl font-bold text-muted-foreground">{stats.inactive}</div>
        )}
      </div>
      <div className="rounded-md border bg-background/50 p-3">
        <div className="text-sm text-muted-foreground">Especialidades</div>
        {isLoading ? (
          <Skeleton className="h-8 w-16 mt-1" />
        ) : (
          <div className="text-2xl font-bold text-accent-foreground">{Object.keys(stats.specialistCount).length}</div>
        )}
      </div>
    </div>
  )
} 