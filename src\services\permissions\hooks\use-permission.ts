'use client';

import { useQuery } from '@tanstack/react-query';
import { ActionType, PermissionCheckResult, ResourceType } from '../types/permission-types';
import { useUserRole } from '@/hooks/user/Permissions';
import { useUser } from '@/hooks/user/Auth';
import { getPermissionClientService } from '../client-service';

/**
 * Hook para verificar permissões no lado do cliente
 * Usa o serviço de permissões cliente
 */
export function usePermission(
  resource: ResourceType,
  action: ActionType,
  targetId?: string
) {
  const { role, isLoading: isRoleLoading } = useUserRole();
  const { user, isLoading: isUserLoading } = useUser();
  
  const cacheKey = ['permission', resource, action, targetId, role];
  
  const { data, isLoading, error } = useQuery({
    queryKey: cacheKey,
    queryFn: async (): Promise<PermissionCheckResult> => {
      if (!user?.id) {
        return { 
          granted: false, 
          reason: '<PERSON>u<PERSON><PERSON> não autenticado' 
        };
      }
      
      try {
        const permissionService = getPermissionClientService();
        const result = await permissionService.hasPermission(
          user.id,
          resource,
          action,
          targetId
        );
        
        return result;
      } catch (error) {
        console.error('Erro ao verificar permissão:', error);
        return { 
          granted: false, 
          reason: 'Erro ao verificar permissão' 
        };
      }
    },
    enabled: !isRoleLoading && !isUserLoading && !!user?.id,
    staleTime: 5 * 60 * 1000,
  });
  
  return {
    isAllowed: data?.granted ?? false,
    reason: data?.reason,
    isLoading: isLoading || isRoleLoading || isUserLoading,
    error,
  };
} 