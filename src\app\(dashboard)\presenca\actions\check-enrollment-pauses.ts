'use server';

import { createClient } from '@/services/supabase/server';

interface EnrollmentPauseCheck {
  enrollment_id: string;
  class_start_time: string; // ISO string da aula
  class_end_time: string;   // ISO string da aula
}

interface PauseCheckResult {
  enrollment_id: string;
  was_paused: boolean;
  pause_periods?: Array<{
    paused_at: string;
    resumed_at: string | null;
  }>;
}

/**
 * Verifica se uma ou mais matrículas estavam pausadas durante um período específico de aula
 */
export async function checkEnrollmentPausesDuringClass(
  checks: EnrollmentPauseCheck[]
): Promise<{ success: boolean; data?: PauseCheckResult[]; errors?: any }> {
  try {
    const supabase = await createClient();
    
    const enrollmentIds = checks.map(check => check.enrollment_id);
    
    // Buscar todas as pausas para as matrículas fornecidas
    const { data: pauseRecords, error } = await supabase
      .from('enrollment_pauses')
      .select('enrollment_id, paused_at, resumed_at')
      .in('enrollment_id', enrollmentIds);

    if (error) {
      console.error('Erro ao buscar pausas de matrícula:', error);
      return { success: false, errors: error };
    }

    // Processar cada verificação
    const results: PauseCheckResult[] = checks.map(check => {
      const enrollmentPauses = pauseRecords?.filter(
        pause => pause.enrollment_id === check.enrollment_id
      ) || [];

      let wasPaused = false;
      const relevantPauses: Array<{ paused_at: string; resumed_at: string | null }> = [];

      for (const pause of enrollmentPauses) {
        const pausedAt = new Date(pause.paused_at);
        const resumedAt = pause.resumed_at ? new Date(pause.resumed_at) : null;
        const classStart = new Date(check.class_start_time);
        const classEnd = new Date(check.class_end_time);

        // Verificar se houve sobreposição entre o período de pausa e o período da aula
        const pauseStartBeforeClassEnd = pausedAt <= classEnd;
        const pauseEndAfterClassStart = !resumedAt || resumedAt >= classStart;

        if (pauseStartBeforeClassEnd && pauseEndAfterClassStart) {
          wasPaused = true;
          relevantPauses.push({
            paused_at: pause.paused_at,
            resumed_at: pause.resumed_at
          });
        }
      }

      return {
        enrollment_id: check.enrollment_id,
        was_paused: wasPaused,
        pause_periods: relevantPauses.length > 0 ? relevantPauses : undefined
      };
    });

    return { success: true, data: results };
  } catch (error) {
    console.error('Erro ao verificar pausas de matrícula:', error);
    return { success: false, errors: error };
  }
}

/**
 * Versão simplificada para verificar uma única matrícula
 */
export async function checkSingleEnrollmentPause(
  enrollment_id: string,
  class_start_time: string,
  class_end_time: string
): Promise<{ success: boolean; was_paused?: boolean; errors?: any }> {
  const result = await checkEnrollmentPausesDuringClass([{
    enrollment_id,
    class_start_time,
    class_end_time
  }]);

  if (!result.success) {
    return { success: false, errors: result.errors };
  }

  const checkResult = result.data?.[0];
  return { 
    success: true, 
    was_paused: checkResult?.was_paused || false 
  };
} 