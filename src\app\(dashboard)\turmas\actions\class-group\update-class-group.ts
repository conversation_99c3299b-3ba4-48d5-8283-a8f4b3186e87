"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/services/supabase/server";
import { UpdateClassGroupFormSchema } from "../schemas/class-group";
import { validateUserAuthentication, validateInstructor, validateBranch, validateClassGroup } from "./shared/validation-helpers";

/**
 * Atualiza uma turma existente
 */
export async function updateClassGroup(data: unknown) {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;

    // Validar com o schema de formulário que inclui unlimited_capacity
    const validationResult = UpdateClassGroupFormSchema.safeParse(data);
    if (!validationResult.success) {
      // Formatar erros para exibição mais clara
      const formattedErrors: Record<string, string> = {};
      
      // Processar erros do Zod para formato mais amigável
      Object.entries(validationResult.error.format()).forEach(([key, value]) => {
        if (key === '_errors') return;
        
        if (typeof value === 'object' && '_errors' in value && Array.isArray(value._errors) && value._errors.length > 0) {
          formattedErrors[key] = value._errors[0];
        }
      });
      
      return { 
        success: false, 
        errors: {
          ...formattedErrors,
          _form: "Dados inválidos. Verifique os campos destacados."
        } 
      };
    }

    const { id, ...formData } = validationResult.data;

    // Verificar se a turma existe e pertence ao tenant
    const classGroupValidation = await validateClassGroup(id, tenantId);
    if (!classGroupValidation.success) {
      return classGroupValidation;
    }

    // Se há mudança de instrutor, verificar se é válido
    if (formData.instructor_id) {
      const instructorValidation = await validateInstructor(formData.instructor_id, tenantId);
      if (!instructorValidation.success) {
        return instructorValidation;
      }
    }

    const supabase = await createAdminClient();

    // Preparar dados para atualização no banco
    const updatePayload = {
      ...formData,
      // Se unlimited_capacity for true, max_capacity deve ser null
      max_capacity: formData.unlimited_capacity ? null : formData.max_capacity,
      start_date: formData.start_date ? formData.start_date.split('T')[0] : undefined,
      end_date: formData.end_date ? formData.end_date.split('T')[0] : undefined,
      // Garantir que recurrence_pattern undefined seja convertido para null no banco
      recurrence_pattern: formData.recurrence_pattern === undefined ? null : formData.recurrence_pattern,
      updated_at: new Date().toISOString(),
    };

    // Remover campos undefined para não sobrescrever com null onde não deve
    const cleanUpdatePayload = Object.fromEntries(
      Object.entries(updatePayload).filter(([_, value]) => value !== undefined)
    );

    // Atualizar a turma
    const { data: updatedGroup, error } = await supabase
      .from("class_groups")
      .update(cleanUpdatePayload)
      .eq("id", id)
      .eq("tenant_id", tenantId)
      .select()
      .single();

    if (error) {
      console.error("Erro ao atualizar turma:", error);
      return { 
        success: false, 
        errors: { 
          _form: `Erro ao atualizar turma: ${error.message || 'Verifique os dados e tente novamente'}`
        } 
      };
    }

    // Revalidar múltiplos caminhos para garantir atualização completa
    revalidatePath("/aulas");
    revalidatePath(`/turmas/${id}`);
    revalidatePath("/turmas");

    return { success: true, data: updatedGroup };
  } catch (error) {
    console.error("Erro ao atualizar turma:", error);
    return { 
      success: false, 
      errors: { 
        _form: error instanceof Error ? `Erro interno: ${error.message}` : "Erro interno do servidor"
      } 
    };
  }
} 