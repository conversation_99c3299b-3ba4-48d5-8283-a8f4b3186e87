'use client';

import { createContext, useContext, useState, ReactN<PERSON>, Dispatch, SetStateAction } from 'react';

interface PageTitleContextType {
  pageTitle: string | null;
  setPageTitle: Dispatch<SetStateAction<string | null>>;
  pageSubtitle: string | null;
  setPageSubtitle: Dispatch<SetStateAction<string | null>>;
  pageIcon: ReactNode | null;
  setPageIcon: Dispatch<SetStateAction<ReactNode | null>>;
}

const PageTitleContext = createContext<PageTitleContextType | undefined>(undefined);

export function PageTitleProvider({ children }: { children: ReactNode }) {
  const [pageTitle, setPageTitle] = useState<string | null>(null);
  const [pageSubtitle, setPageSubtitle] = useState<string | null>(null);
  const [pageIcon, setPageIcon] = useState<ReactNode | null>(null);

  return (
    <PageTitleContext.Provider value={{ 
      pageTitle, 
      setPageTitle, 
      pageSubtitle, 
      setPageSubtitle,
      pageIcon,
      setPageIcon
    }}>
      {children}
    </PageTitleContext.Provider>
  );
}

export function usePageTitle() {
  const context = useContext(PageTitleContext);
  if (context === undefined) {
    throw new Error('usePageTitle must be used within a PageTitleProvider');
  }
  return context;
} 