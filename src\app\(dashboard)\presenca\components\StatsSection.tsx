'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Calendar, Users, CheckCircle, Clock, TrendingUp, TrendingDown } from 'lucide-react';
import { motion } from 'framer-motion';
import { RadialProgress } from './RadialProgress';
import { <PERSON><PERSON><PERSON>, TrendLine } from './MiniChart';

interface StatsData {
  title: string;
  value: number;
  subtitle: string;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
    data: number[]; // Dados históricos para o mini-gráfico
  };
  color: string;
  maxValue?: number;
  chartType?: 'radial' | 'bar' | 'line';
}

interface TrendData {
  value: number;
  isPositive: boolean;
  data: number[];
}

interface StatsSectionProps {
  totalClasses: number;
  ongoingClasses: number;
  upcomingClasses: number;
  completedClasses: number;
  trends?: {
    total: TrendData;
    ongoing: TrendData;
    upcoming: TrendData;
    completed: TrendData;
  };
}

export function StatsSection({ 
  totalClasses, 
  ongoingClasses, 
  upcomingClasses, 
  completedClasses,
  trends
}: StatsSectionProps) {
  // Usar dados reais de tendência calculados a partir do histórico do banco de dados
  // Fallback apenas para casos onde não há dados disponíveis (ex: tenant novo)
  const trendData = trends || {
    total: { value: 0, isPositive: true, data: Array(7).fill(0) },
    ongoing: { value: 0, isPositive: true, data: Array(7).fill(0) },
    upcoming: { value: 0, isPositive: true, data: Array(7).fill(0) },
    completed: { value: 0, isPositive: true, data: Array(7).fill(0) }
  };

  const stats: StatsData[] = [
    {
      title: 'Total de Aulas',
      value: totalClasses,
      subtitle: 'Últimos 7 dias',
      icon: <Calendar className="h-5 w-5" />,
      trend: trendData.total,
      color: '#3b82f6',
      chartType: 'line'
    },
    {
      title: 'Em Andamento',
      value: ongoingClasses,
      subtitle: 'Agora',
      icon: <Clock className="h-5 w-5" />,
      trend: trendData.ongoing,
      color: '#10b981',
      maxValue: Math.max(10, ongoingClasses + 2),
      chartType: 'radial'
    },
    {
      title: 'Próximas Aulas',
      value: upcomingClasses,
      subtitle: 'Próximos 7 dias',
      icon: <Users className="h-5 w-5" />,
      trend: trendData.upcoming,
      color: '#f59e0b',
      // maxValue: Math.max(20, upcomingClasses + 5),
      chartType: 'bar'
    },
    {
      title: 'Concluídas',
      value: completedClasses,
      subtitle: 'Últimos 7 dias',
      icon: <CheckCircle className="h-5 w-5" />,
      trend: trendData.completed,
      color: '#8b5cf6',
      // maxValue: Math.max(25, completedClasses + 5),
      chartType: 'line'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3
      }
    }
  };

  return (
    <motion.div 
      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {stats.map((stat, index) => (
        <motion.div key={stat.title} variants={itemVariants}>
          <Card className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="space-y-3 flex-1">
                  {/* Header com ícone e título */}
                  <div className="flex items-center gap-3">
                    <div 
                      className="p-2 rounded-lg"
                      style={{ backgroundColor: `${stat.color}15` }}
                    >
                      <div style={{ color: stat.color }}>
                        {stat.icon}
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-slate-600 dark:text-gray-300">
                        {stat.title}
                      </h3>
                      <p className="text-xs text-slate-400 dark:text-gray-500">{stat.subtitle}</p>
                    </div>
                  </div>

                  {/* Valor principal */}
                  <div className="flex items-center gap-3">
                    <motion.span 
                      className="text-2xl font-bold text-slate-900 dark:text-gray-100"
                      initial={{ opacity: 0, scale: 0.5 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
                    >
                      {stat.value}
                    </motion.span>
                    
                    {/* Tendência */}
                    {/* {stat.trend && (
                      <div className="flex items-center gap-1">
                        {stat.trend.isPositive ? (
                          <TrendingUp className="h-3 w-3 text-green-500" />
                        ) : (
                          <TrendingDown className="h-3 w-3 text-red-500" />
                        )}
                        <span 
                          className={`text-xs font-medium ${
                            stat.trend.isPositive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                          }`}
                        >
                          {stat.trend.value}%
                        </span>
                      </div>
                    )} */}
                  </div>
                </div>

                {/* Gráfico lateral */}
                <div className="flex flex-col items-end gap-2">
                  {stat.chartType === 'radial' && stat.maxValue && (
                    <RadialProgress
                      value={stat.value}
                      maxValue={stat.maxValue}
                      size={60}
                      strokeWidth={6}
                      color={stat.color}
                      showValue={false}
                      duration={1.5}
                    />
                  )}
                  
                  {stat.chartType === 'bar' && stat.trend && (
                    <MiniChart
                      data={stat.trend.data}
                      height={35}
                      width={50}
                      color={stat.color}
                      startDate={new Date()}
                    />
                  )}
                  
                  {stat.chartType === 'line' && stat.trend && (
                    <TrendLine
                      data={stat.trend.data}
                      height={35}
                      width={50}
                      color={stat.color}
                      strokeWidth={2}
                    />
                  )}
                </div>
              </div>

              {/* Mini indicador de progresso na parte inferior */}
              {stat.maxValue && stat.title !== 'Em Andamento' && (
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-xs text-slate-500 dark:text-gray-400">
                    <span>Progresso</span>
                    <span>{Math.round((stat.value / stat.maxValue) * 100)}%</span>
                  </div>
                  <div className="w-full bg-slate-100 dark:bg-gray-700 rounded-full h-1.5">
                    <motion.div
                      className="h-1.5 rounded-full"
                      style={{ backgroundColor: stat.color }}
                      initial={{ width: 0 }}
                      animate={{ width: `${Math.min((stat.value / stat.maxValue) * 100, 100)}%` }}
                      transition={{ duration: 1, delay: index * 0.1 + 0.5 }}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </motion.div>
  );
} 