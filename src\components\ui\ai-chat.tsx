"use client";

import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  XMarkIcon, 
  PaperAirplaneIcon, 
  SparklesIcon,
  ChatBubbleLeftRightIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon 
} from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { useTenantTheme } from "@/hooks/tenant/use-tenant-theme";
import { chatService, ChatService, type UsageStats } from "@/services/ai/chat-service";

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  conversationId?: string;
}

interface AIChatProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AIChat = ({ isOpen, onClose }: AIChatProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [conversationId, setConversationId] = useState<string | undefined>();
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
  const [isLoadingStats, setIsLoadingStats] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const { primaryColor } = useTenantTheme();

  // Auto scroll para a última mensagem
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Carregar estatísticas de uso quando o chat abrir
  useEffect(() => {
    if (isOpen) {
      loadUsageStats();
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }
  }, [isOpen]);

  // Função para carregar estatísticas de uso
  const loadUsageStats = async () => {
    setIsLoadingStats(true);
    try {
      const result = await chatService.getUsageStats();
      if (result.success && result.usageStats) {
        setUsageStats(result.usageStats);
      }
    } catch (error) {
      console.error('Erro ao carregar estatísticas de uso:', error);
    } finally {
      setIsLoadingStats(false);
    }
  };

  // Função para chamar a API real do Gemini
  const callAIService = async (userMessage: string): Promise<{ success: boolean; response?: string; error?: string; newConversationId?: string; usageStats?: UsageStats; code?: string }> => {
    try {
      const context = ChatService.extractContext();
      const result = await chatService.sendMessage(userMessage, conversationId, context);
      
      if (result.success && result.response) {
        return {
          success: true,
          response: result.response,
          newConversationId: result.conversationId,
          usageStats: result.usageStats
        };
      } else {
        return {
          success: false,
          error: result.error || 'Erro desconhecido',
          code: result.code,
          usageStats: result.usageStats
        };
      }
    } catch (error) {
      console.error('Erro ao chamar serviço de IA:', error);
      return {
        success: false,
        error: 'Erro de conexão com o serviço de IA'
      };
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    // Verificar limite antes de enviar
    if (usageStats && !usageStats.canSendMessage) {
      const errorMessage: Message = {
        id: `msg-${crypto.randomUUID()}`,
        content: `Você atingiu o limite diário de ${usageStats.messagesLimit} mensagens. Tente novamente amanhã.`,
        role: 'assistant',
        timestamp: new Date(),
        conversationId
      };
      setMessages(prev => [...prev, errorMessage]);
      return;
    }

    const userMessage: Message = {
      id: `msg-${crypto.randomUUID()}`,
      content: inputValue.trim(),
      role: 'user',
      timestamp: new Date(),
      conversationId
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue("");
    setIsLoading(true);

    try {
      const aiResult = await callAIService(userMessage.content);
      
      if (aiResult.success && aiResult.response) {
        // Atualizar conversation ID se recebido
        if (aiResult.newConversationId) {
          setConversationId(aiResult.newConversationId);
        }
        
        const assistantMessage: Message = {
          id: `msg-${crypto.randomUUID()}`,
          content: aiResult.response,
          role: 'assistant',
          timestamp: new Date(),
          conversationId: aiResult.newConversationId || conversationId
        };

        setMessages(prev => [...prev, assistantMessage]);
        
        // Atualizar estatísticas de uso se retornadas
        if (aiResult.usageStats) {
          setUsageStats(aiResult.usageStats);
        } else {
          // Recarregar estatísticas após envio bem-sucedido
          loadUsageStats();
        }
      } else {
        // Verificar se é erro de limite
        if (aiResult.error?.includes('limite diário') || aiResult.error?.includes('DAILY_LIMIT_EXCEEDED')) {
          // Recarregar estatísticas para obter dados atualizados
          loadUsageStats();
        }
        
        // Mensagem de erro específica
        const errorMessage: Message = {
          id: `msg-${crypto.randomUUID()}`,
          content: aiResult.error || "Desculpe, ocorreu um erro. Tente novamente.",
          role: 'assistant',
          timestamp: new Date(),
          conversationId
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      const errorMessage: Message = {
        id: `msg-${crypto.randomUUID()}`,
        content: "Desculpe, ocorreu um erro de conexão. Verifique sua internet e tente novamente.",
        role: 'assistant',
        timestamp: new Date(),
        conversationId
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const clearChat = () => {
    setMessages([]);
    setConversationId(undefined);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Overlay para mobile */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 lg:hidden"
            onClick={onClose}
          />

          {/* Chat Container */}
          <motion.div
            initial={{ x: "100%", opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: "100%", opacity: 0 }}
            transition={{ type: "spring", damping: 25, stiffness: 200 }}
            className={cn(
              "fixed right-0 top-0 h-full w-full sm:w-96 z-50",
              "lg:top-4 lg:right-4 lg:h-[calc(100vh-2rem)] lg:w-80 lg:rounded-xl",
              "shadow-2xl border-l lg:border lg:border-gray-200 dark:lg:border-gray-800"
            )}
          >
            <Card className="h-full w-full flex flex-col bg-white/95 dark:bg-gray-900/95 backdrop-blur-lg border-0 lg:border">
              {/* Header */}
              <CardHeader className="shrink-0 pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div 
                      className="p-2 rounded-lg bg-purple-100 dark:bg-purple-900/30"
                      style={{ backgroundColor: `${primaryColor}15` }}
                    >
                      <SparklesIcon 
                        className="h-5 w-5 text-purple-600 dark:text-purple-400"
                        style={{ color: primaryColor || '#7c3aed' }}
                      />
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                          Assistente IA
                        </h3>
                        <span 
                          className="px-1.5 py-0.5 text-xs font-medium rounded-md bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300"
                          style={{ 
                            backgroundColor: `${primaryColor}20` || '#fed7aa',
                            color: primaryColor || '#c2410c'
                          }}
                        >
                          BETA
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Como posso ajudar?
                      </p>
                      
                      {/* Usage Statistics */}
                      {usageStats && (
                        <div className="flex items-center gap-1 text-xs mt-1">
                          <InformationCircleIcon className="h-3 w-3 text-gray-400" />
                          <span className={cn(
                            "text-gray-500 dark:text-gray-400",
                            !usageStats.canSendMessage && "text-orange-600 dark:text-orange-400"
                          )}>
                            {usageStats.messagesUsed}/{usageStats.messagesLimit} mensagens hoje
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    {messages.length > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={clearChat}
                        className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      >
                        <span className="sr-only">Limpar conversa</span>
                        <div className="h-4 w-4 text-xs">🗑️</div>
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onClose}
                      className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    >
                      <span className="sr-only">Fechar chat</span>
                      <XMarkIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                {/* Warning when near limit */}
                {usageStats && usageStats.remainingMessages <= 1 && usageStats.canSendMessage && (
                  <div className="px-3 py-2 bg-orange-50 dark:bg-orange-900/20 border-l-4 border-orange-400">
                    <div className="flex items-center gap-2">
                      <ExclamationTriangleIcon className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                      <p className="text-xs text-orange-800 dark:text-orange-200">
                        {usageStats.remainingMessages === 1 
                          ? "Última mensagem disponível hoje" 
                          : "Limite diário quase atingido"
                        }
                      </p>
                    </div>
                  </div>
                )}
              </CardHeader>

              {/* Messages Area */}
              <CardContent className="flex-1 overflow-hidden p-0">
                <div className="h-full flex flex-col">
                  <div className="flex-1 overflow-y-auto px-4 py-2 space-y-4">
                    {messages.length === 0 ? (
                      <div className="flex flex-col items-center justify-center h-full text-center px-4">
                        <div 
                          className="p-4 rounded-2xl bg-gray-50 dark:bg-gray-800/50 mb-4"
                          style={{ backgroundColor: `${primaryColor}10` }}
                        >
                          <ChatBubbleLeftRightIcon 
                            className="h-8 w-8 text-gray-400"
                            style={{ color: primaryColor || '#6b7280' }}
                          />
                        </div>
                        <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                          Olá! 👋
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400 leading-relaxed">
                          Sou seu assistente IA. Faça uma pergunta para começarmos nossa conversa!
                        </p>
                      </div>
                    ) : (
                      <>
                        {messages.map((message) => (
                          <div
                            key={message.id}
                            className={cn(
                              "flex w-full",
                              message.role === 'user' ? "justify-end" : "justify-start"
                            )}
                          >
                            <div
                              className={cn(
                                "max-w-[85%] rounded-2xl px-4 py-2 text-sm",
                                message.role === 'user'
                                  ? "bg-blue-500 text-white"
                                  : "bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                              )}
                              style={message.role === 'user' ? {
                                backgroundColor: primaryColor || '#3b82f6'
                              } : {}}
                            >
                              <p className="whitespace-pre-wrap leading-relaxed">
                                {message.content}
                              </p>
                              <div 
                                className={cn(
                                  "text-xs mt-1 opacity-70",
                                  message.role === 'user' 
                                    ? "text-white" 
                                    : "text-gray-500 dark:text-gray-400"
                                )}
                              >
                                {message.timestamp.toLocaleTimeString('pt-BR', {
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                              </div>
                            </div>
                          </div>
                        ))}

                        {/* Loading indicator */}
                        {isLoading && (
                          <motion.div 
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex justify-start"
                          >
                            <div className="bg-gray-100 dark:bg-gray-800 rounded-2xl px-4 py-3">
                              <div className="flex space-x-1 items-center">
                                <motion.div 
                                  className="w-2 h-2 rounded-full bg-gray-400"
                                  style={{ backgroundColor: primaryColor || '#6b7280' }}
                                  animate={{ 
                                    scale: [1, 1.2, 1],
                                    opacity: [0.7, 1, 0.7]
                                  }}
                                  transition={{ 
                                    duration: 0.8,
                                    repeat: Infinity,
                                    ease: "easeInOut"
                                  }}
                                />
                                <motion.div 
                                  className="w-2 h-2 rounded-full bg-gray-400"
                                  style={{ backgroundColor: primaryColor || '#6b7280' }}
                                  animate={{ 
                                    scale: [1, 1.2, 1],
                                    opacity: [0.7, 1, 0.7]
                                  }}
                                  transition={{ 
                                    duration: 0.8,
                                    repeat: Infinity,
                                    ease: "easeInOut",
                                    delay: 0.2
                                  }}
                                />
                                <motion.div 
                                  className="w-2 h-2 rounded-full bg-gray-400"
                                  style={{ backgroundColor: primaryColor || '#6b7280' }}
                                  animate={{ 
                                    scale: [1, 1.2, 1],
                                    opacity: [0.7, 1, 0.7]
                                  }}
                                  transition={{ 
                                    duration: 0.8,
                                    repeat: Infinity,
                                    ease: "easeInOut",
                                    delay: 0.4
                                  }}
                                />
                                <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                                  IA está digitando...
                                </span>
                              </div>
                            </div>
                          </motion.div>
                        )}
                      </>
                    )}
                    <div ref={messagesEndRef} />
                  </div>

                  {/* Input Area */}
                  <div className="shrink-0 p-4 border-t border-gray-200 dark:border-gray-800">
                    {/* Blocked message when limit is reached */}
                    {usageStats && !usageStats.canSendMessage ? (
                      <div className="flex items-center justify-center py-4">
                        <div className="text-center">
                          <ExclamationTriangleIcon className="h-8 w-8 text-orange-500 mx-auto mb-2" />
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                            Limite diário atingido
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-500">
                            Volte amanhã para fazer mais perguntas
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="flex gap-2">
                        <Textarea
                          ref={inputRef}
                          value={inputValue}
                          onChange={(e) => setInputValue(e.target.value)}
                          onKeyDown={handleKeyPress}
                          placeholder={
                            usageStats 
                              ? `Digite sua mensagem... (${usageStats.remainingMessages} restantes hoje)`
                              : "Digite sua mensagem..."
                          }
                          className="min-h-[42px] max-h-32 resize-none rounded-xl border-gray-200 dark:border-gray-700 focus:ring-1 focus:ring-blue-500"
                          disabled={isLoading}
                        />
                        <Button
                          onClick={handleSendMessage}
                          disabled={!inputValue.trim() || isLoading}
                          size="icon"
                          className="h-[42px] w-[42px] rounded-xl"
                          style={{
                            backgroundColor: inputValue.trim() && !isLoading 
                              ? (primaryColor || '#3b82f6') 
                              : undefined
                          }}
                        >
                          <PaperAirplaneIcon className="h-4 w-4" />
                          <span className="sr-only">Enviar mensagem</span>
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}; 