'use server';

import { createClient } from "@/services/supabase/server";
import { getCurrentUser } from "@/services/auth/actions/auth-actions";

type Branch = {
  id: string;
  name: string;
};

type BranchesResult = {
  success: boolean;
  data?: Branch[];
  error?: string;
};

export async function getBranchesByTenant(): Promise<BranchesResult> {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return {
        success: false,
        error: 'Usuário não autenticado'
      };
    }

    const supabase = await createClient();
    
    // Buscar dados do usuário para obter tenant_id
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('tenant_id')
      .eq('id', user.id)
      .single();

    if (userError || !userData?.tenant_id) {
      return {
        success: false,
        error: 'Usuário sem tenant_id associado'
      };
    }
    
    // Buscar filiais ativas do tenant (usar deleted_at IS NULL para filiais ativas)
    const { data: branches, error } = await supabase
      .from('branches')
      .select('id, name')
      .eq('tenant_id', userData.tenant_id)
      .is('deleted_at', null)
      .order('name');

    if (error) {
      console.error('Erro ao buscar filiais:', error);
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: true,
      data: branches || []
    };
  } catch (error) {
    console.error('Erro inesperado ao buscar filiais:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
} 