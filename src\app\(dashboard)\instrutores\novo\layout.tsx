import { Metadata } from "next";
import { requireAuth } from "@/services/auth/server";

export const metadata: Metadata = {
  title: "Novo Instrutor | ApexSAAS",
  description: "Cadastro de novo instrutor no sistema ApexSAAS"
};

export default async function NovoInstrutorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Verificar se o usuário está autenticado (middleware já verifica permissões)
  await requireAuth();

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Cadastrar Novo Instrutor</h1>
        <p className="text-muted-foreground">Preencha os dados para cadastrar um novo instrutor no sistema.</p>
      </div>
      
      {children}
    </div>
  );
} 