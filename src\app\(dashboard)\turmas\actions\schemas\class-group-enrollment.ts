import { z } from 'zod';
import { ClassGroupEnrollmentStatusEnum } from './enums';
import { PaginationSchema, SortOrderSchema, SearchFilterSchema } from './shared';

// Schema base para Class Group Enrollment
const BaseClassGroupEnrollmentSchema = z.object({
  id: z.string().uuid().optional(),
  tenant_id: z.string().uuid(),
  class_group_id: z.string().uuid(),
  student_id: z.string().uuid(),
  enrollment_date: z.string().datetime().optional(),
  status: ClassGroupEnrollmentStatusEnum.default('active'),
  notes: z.string().max(500).optional(),
  metadata: z.record(z.any()).optional(),
});

// Schema completo
export const ClassGroupEnrollmentSchema = BaseClassGroupEnrollmentSchema;

// Schema para criação
export const CreateClassGroupEnrollmentSchema = BaseClassGroupEnrollmentSchema.omit({ 
  id: true,
  tenant_id: true 
});

// Schema para atualização
export const UpdateClassGroupEnrollmentSchema = BaseClassGroupEnrollmentSchema.partial().omit({ 
  id: true,
  tenant_id: true,
  class_group_id: true,
  student_id: true 
});

// Schema para matrícula simples
export const EnrollStudentSchema = z.object({
  class_group_id: z.string().uuid({ message: 'ID da turma é obrigatório' }),
  student_id: z.string().uuid({ message: 'ID do aluno é obrigatório' }),
  notes: z.string().max(500).optional(),
});

// Schema para múltiplas matrículas
export const BulkEnrollStudentsSchema = z.object({
  class_group_id: z.string().uuid({ message: 'ID da turma é obrigatório' }),
  student_ids: z.array(z.string().uuid()).min(1, { message: 'Selecione pelo menos um aluno' }),
  notes: z.string().max(500).optional(),
});

// Schema para transferência de alunos
export const TransferStudentsSchema = z.object({
  from_class_group_id: z.string().uuid({ message: 'ID da turma de origem é obrigatório' }),
  to_class_group_id: z.string().uuid({ message: 'ID da turma de destino é obrigatório' }),
  student_ids: z.array(z.string().uuid()).min(1, { message: 'Selecione pelo menos um aluno' }),
  notes: z.string().max(500).optional(),
});

// Schema para filtros de matrícula
export const ClassGroupEnrollmentFilterSchema = SearchFilterSchema.extend({
  class_group_id: z.string().uuid().optional(),
  student_id: z.string().uuid().optional(),
  status: ClassGroupEnrollmentStatusEnum.optional(),
  enrollment_date_from: z.string().datetime().optional(),
  enrollment_date_to: z.string().datetime().optional(),
}).merge(PaginationSchema).extend({
  sort_by: z.enum(['enrollment_date', 'student_name', 'status']).default('enrollment_date'),
  sort_order: SortOrderSchema,
});

// Tipos inferidos
export type ClassGroupEnrollment = z.infer<typeof ClassGroupEnrollmentSchema>;
export type CreateClassGroupEnrollment = z.infer<typeof CreateClassGroupEnrollmentSchema>;
export type UpdateClassGroupEnrollment = z.infer<typeof UpdateClassGroupEnrollmentSchema>;
export type EnrollStudent = z.infer<typeof EnrollStudentSchema>;
export type BulkEnrollStudents = z.infer<typeof BulkEnrollStudentsSchema>;
export type TransferStudents = z.infer<typeof TransferStudentsSchema>;
export type ClassGroupEnrollmentFilter = z.infer<typeof ClassGroupEnrollmentFilterSchema>; 