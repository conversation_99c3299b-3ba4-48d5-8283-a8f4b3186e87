import { TenantCacheEntry } from './types';
import { TENANT_CONFIG } from './tenant-config';

/**
 * Cache LRU otimizado para armazenamento de slugs e IDs de tenant
 * Funciona tanto no cliente quanto no servidor
 */
class TenantCache {
  private cache = new Map<string, TenantCacheEntry>();
  private readonly maxSize: number;
  private readonly ttl: number;

  constructor(maxSize = 100, ttl = TENANT_CONFIG.cacheTTL) {
    this.maxSize = maxSize;
    this.ttl = ttl;
  }

  get(key: string): string | null {
    if (!TENANT_CONFIG.cacheEnabled) return null;
    
    const entry = this.cache.get(key);
    if (!entry) return null;

    // Verificar se expirou
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    // Mover para o final (LRU)
    this.cache.delete(key);
    this.cache.set(key, entry);
    
    return entry.slug;
  }

  set(key: string, slug: string, customTtl?: number): void {
    if (!TENANT_CONFIG.cacheEnabled) return;

    // Limpar entrada mais antiga se o cache estiver cheio
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) this.cache.delete(firstKey);
    }

    const entry: TenantCacheEntry = {
      slug,
      timestamp: Date.now(),
      ttl: customTtl || this.ttl
    };

    this.cache.set(key, entry);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  // Limpar entradas expiradas
  cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    this.cache.forEach((entry, key) => {
      if (now - entry.timestamp > entry.ttl) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => this.cache.delete(key));
  }

  // Estatísticas do cache (útil para debug)
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.calculateHitRate(),
      enabled: TENANT_CONFIG.cacheEnabled
    };
  }

  private calculateHitRate(): number {
    // Implementação simplificada - em produção você poderia manter contadores
    return this.cache.size > 0 ? 0.85 : 0; // Estimativa
  }
}

// Instância singleton do cache
export const tenantCache = new TenantCache();

// Cleanup automático a cada 5 minutos
if (typeof window !== 'undefined' || typeof global !== 'undefined') {
  setInterval(() => {
    tenantCache.cleanup();
  }, 5 * 60 * 1000);
} 