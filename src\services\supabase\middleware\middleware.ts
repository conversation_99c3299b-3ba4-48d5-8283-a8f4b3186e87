import { createServerClient } from "@supabase/ssr";
import { NextResponse, NextRequest } from "next/server";
import { tenantConfig, navigationConfig } from '@/src/config';
import { getSupabaseConfig } from '@/config/supabase';

interface Subdomain {
  value: string | null;
}

// Interface para o objeto tenant
interface Tenant {
  id: string;
  slug: string | null;
  name?: string;
  primary_color?: string;
  secondary_color?: string;
}

class SubdomainExtractor {
  private readonly reservedPaths = ['_next', 'api', 'login', 'home', 'dashboard', 'perfil'];
  
  extractFrom(url: string): Subdomain {
    try {
      return this.parseUrl(url);
    } catch {
      return { value: null };
    }
  }
  
  private parseUrl(url: string): Subdomain {
    const urlObj = new URL(url);
    
    if (this.isLocalhost(urlObj.hostname)) {
      return this.extractFromLocalhostPath(urlObj.pathname);
    }
    
    if (this.isLocalhostWithSubdomain(urlObj.hostname)) {
      return this.extractFromLocalhostWithSubdomain(urlObj.hostname);
    }
    
    if (this.isStandardDomain(urlObj.hostname)) {
      return this.extractFromStandardDomain(urlObj.hostname);
    }
    
    return { value: null };
  }
  
  private isLocalhost(hostname: string): boolean {
    return hostname === 'localhost';
  }
  
  private isLocalhostWithSubdomain(hostname: string): boolean {
    return hostname.endsWith('.localhost');
  }
  
  private isStandardDomain(hostname: string): boolean {
    const baseDomainWithoutPort = tenantConfig.baseDomain.split(':')[0];
    return (hostname.endsWith(baseDomainWithoutPort) || hostname.includes('.')) && 
           !hostname.startsWith('www.');
  }
  
  private extractFromLocalhostPath(pathname: string): Subdomain {
    const parts = pathname.split('/').filter(Boolean);
    
    if (this.hasValidFirstPathPart(parts)) {
      return { value: parts[0] };
    }
    
    return { value: null };
  }
  
  private hasValidFirstPathPart(parts: string[]): boolean {
    if (parts.length === 0) return false;
    
    if (this.reservedPaths.includes(parts[0])) return false;
    
    const isProtectedRoute = navigationConfig.routes.protected.some(route => {
      const routeParts = route.split('/').filter(Boolean);
      return routeParts.length > 0 && routeParts[0] === parts[0];
    });
    
    return !isProtectedRoute;
  }
  
  private extractFromLocalhostWithSubdomain(hostname: string): Subdomain {
    return { value: hostname.split('.')[0] };
  }
  
  private extractFromStandardDomain(hostname: string): Subdomain {
    const parts = hostname.split('.');
    return { value: parts.length >= 2 ? parts[0] : null };
  }
}

class SearchParamsDecoder {
  decode(url: URL): URLSearchParams {
    const params = new URLSearchParams();
    this.processParams(url.searchParams, params);
    return params;
  }
  
  formatAsQueryString(params: URLSearchParams): string {
    const paramString = params.toString();
    return paramString ? `?${paramString}` : '';
  }
  
  private processParams(source: URLSearchParams, target: URLSearchParams): void {
    for (const [key, value] of Array.from(source.entries())) {
      this.setDecodedValue(target, key, value);
    }
  }
  
  private setDecodedValue(params: URLSearchParams, key: string, value: string): void {
    try {
      const decodedValue = this.needsDecoding(value) ? decodeURIComponent(value) : value;
      params.set(key, decodedValue);
    } catch {
      params.set(key, value);
    }
  }
  
  private needsDecoding(value: string): boolean {
    return value.includes('%');
  }
}

class SupabaseClientFactory {
  create(request: NextRequest, response: NextResponse): ReturnType<typeof createServerClient> | null {
    let updatedResponse = response;
    
    try {
      const config = getSupabaseConfig();
      
      return createServerClient(
        config.url,
        config.anonKey,
        {
          cookies: {
            getAll: () => {
              return request.cookies.getAll();
            },
            setAll: (cookiesToSet) => {
              cookiesToSet.forEach(({ name, value }) => {
                try {
                  request.cookies.set(name, value);
                  if (process.env.NODE_ENV === 'development') {
                    console.log(`Middleware - Cookie adicionado à requisição: ${name}`);
                  }
                } catch (error) {
                  console.error(`Erro ao atualizar cookie ${name} na requisição:`, error);
                }
              });
              
              // Criar uma nova resposta baseada na requisição atualizada
              updatedResponse = NextResponse.next({ request });
              
              // Definir os cookies na resposta para o cliente
              cookiesToSet.forEach(({ name, value, options }) => {
                try {
                  // Configuração base para todos os cookies
                  const cookieOptions = {
                    ...options,
                    path: '/',
                    sameSite: 'lax' as const,
                    secure: process.env.NODE_ENV === 'production',
                    // Importante: não configurar domain para desenvolvimento local
                  };
                  
                  // Em ambiente de desenvolvimento, remover configurações que possam causar conflitos
                  if (process.env.NODE_ENV === 'development') {
                    // Verificar se estamos em um subdomínio no localhost
                    const isLocalSubdomain = request.headers.get('host')?.includes('.localhost');
                    
                    // Para subdomínios no localhost, não definir o domínio explicitamente
                    // para evitar problemas de compatibilidade com o navegador
                    delete cookieOptions.domain;
                    
                    // Verificar se é um cookie do Supabase
                    const isSupabaseCookie = name.startsWith('sb-') || name === 'supabase-auth-token';
                    
                    if (isSupabaseCookie) {
                      // Para cookies de autenticação, garantir que sejam acessíveis ao cliente
                      cookieOptions.httpOnly = false;
                      // Aumentar o tempo de expiração para evitar perda rápida de sessão
                      if (!cookieOptions.maxAge || cookieOptions.maxAge < 3600) {
                        cookieOptions.maxAge = 7 * 24 * 60 * 60; // 7 dias
                      }
                    }
                  }
                  
                  // Definir o cookie na resposta
                  updatedResponse.cookies.set(name, value, cookieOptions);
                  
                  if (process.env.NODE_ENV === 'development') {
                    console.log(`Middleware - Cookie definido na resposta: ${name}`);
                    if (name.startsWith('sb-')) {
                      console.log(`Middleware - Detalhes do cookie ${name}:`, { 
                        valueLength: value ? value.length : 0,
                        options: JSON.stringify(cookieOptions)
                      });
                    }
                  }
                } catch (error) {
                }
              });
            },
          },
        }
      );
    } catch (error) {
      console.error('🚨 Middleware - Erro ao criar cliente Supabase:', error);
      return null;
    }
  }
}

class AuthMessageManager {
  setErrorMessage(response: NextResponse, message: string): NextResponse {
    response.cookies.set('auth_error', message, {
      maxAge: 60, 
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });
    return response;
  }
  
  setSuccessMessage(response: NextResponse, message: string): NextResponse {
    response.cookies.set('auth_success', message, {
      maxAge: 60, 
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });
    return response;
  }
}

class RequestAnalyzer {
  constructor(
    private readonly request: NextRequest,
    private readonly subdomainExtractor: SubdomainExtractor,
    private readonly paramsDecoder: SearchParamsDecoder
  ) {}
  
  analyze(): {
    subdomain: Subdomain,
    hostHeader: string,
    isSubdomainRequest: boolean,
    hasValidSubdomain: boolean,
    url: URL,
    decodedParams: URLSearchParams,
    queryParamsString: string
  } {
    const subdomain = this.subdomainExtractor.extractFrom(this.request.url);
    const hostHeader = this.request.headers.get('host') || '';
    const isSubdomainRequest = this.determineIfSubdomainRequest(hostHeader);
    const hasValidSubdomain = !!(subdomain.value || isSubdomainRequest);
    
    const url = new URL(this.request.url);
    const decodedParams = this.paramsDecoder.decode(url);
    const queryParamsString = this.paramsDecoder.formatAsQueryString(decodedParams);
    
    return {
      subdomain,
      hostHeader,
      isSubdomainRequest,
      hasValidSubdomain,
      url,
      decodedParams,
      queryParamsString
    };
  }
  
  getEffectiveSubdomain(subdomain: Subdomain, isSubdomainRequest: boolean, hostHeader: string): string | null {
    if (subdomain.value) {
      return subdomain.value;
    }
    
    if (isSubdomainRequest) {
      return hostHeader.split('.')[0];
    }
    
    return null;
  }
  
  private determineIfSubdomainRequest(hostHeader: string): boolean {
    return hostHeader.includes('.') && !hostHeader.startsWith('www.');
  }
}

class RouteHandler {
  private readonly baseDomain = tenantConfig.baseDomain;
  private readonly authMessageManager = new AuthMessageManager();
  
  constructor(
    private readonly request: NextRequest,
    private readonly supabase: ReturnType<typeof createServerClient> | null,
    private readonly analyzer: RequestAnalyzer
  ) {}
  
  async handleLoginPost(response: NextResponse): Promise<NextResponse | null> {
    if (this.request.method === 'POST' && this.request.nextUrl.pathname === '/login') {
      return response;
    }
    
    return null;
  }
  
  async handleBypassRoutes(response: NextResponse): Promise<NextResponse | null> {
    const path = this.request.nextUrl.pathname;
    
    if (navigationConfig.routes.shouldBypass(path)) {
      return response;
    }
    
    return null;
  }
  
  async handleLoginWithParams(response: NextResponse, analysis: ReturnType<RequestAnalyzer['analyze']>): Promise<NextResponse | null> {
    if (navigationConfig.routes.isAuth(this.request.nextUrl.pathname) && analysis.hasValidSubdomain) {
      return response;
    }
    
    return null;
  }
  
  async handleLoginWithoutSubdomain(analysis: ReturnType<RequestAnalyzer['analyze']>): Promise<NextResponse | null> {
    if (navigationConfig.routes.isAuth(this.request.nextUrl.pathname) && !analysis.hasValidSubdomain) {
      return NextResponse.redirect(new URL(`http://${this.baseDomain}`, this.request.url));
    }
    
    return null;
  }
  
  async handleSubdomainAccess(analysis: ReturnType<RequestAnalyzer['analyze']>, user: any): Promise<NextResponse | null> {
    if (!analysis.hasValidSubdomain) {
      return null;
    }
    
    const subdomainToUse = this.analyzer.getEffectiveSubdomain(
      analysis.subdomain, 
      analysis.isSubdomainRequest, 
      analysis.hostHeader
    );
    
    if (!subdomainToUse) {
      return NextResponse.next({
        request: {
          headers: this.request.headers,
        },
      });
    }
    
    const initialResponse = NextResponse.next({
      request: {
        headers: this.request.headers,
      },
    });
    
    if (subdomainToUse && analysis.hostHeader) {
      initialResponse.cookies.set('host', analysis.hostHeader, {
        path: '/',
        maxAge: 60 * 60 * 24 * 365,
        sameSite: 'lax',
        httpOnly: false // Permitir acesso via JavaScript
      });
    }
    
    try {
      return await this.processTenantAccess(subdomainToUse, user, initialResponse);
    } catch (error) {
      return initialResponse;
    }
  }
  
  private async processTenantAccess(subdomainToUse: string, user: any, response?: NextResponse): Promise<NextResponse | null> {    
    if (!this.supabase) {
      console.error('🚨 Middleware - Cliente Supabase não disponível para processTenantAccess');
      const redirectResponse = NextResponse.redirect(new URL(`http://${this.baseDomain}`, this.request.url));
      return this.authMessageManager.setErrorMessage(redirectResponse, "Erro de configuração do sistema");
    }
    
    const { data: tenant, error } = await this.supabase
      .from('tenants')
      .select('id, slug, primary_color, secondary_color')
      .eq('slug', subdomainToUse)
      .single();
    
    if ((error || !tenant) && process.env.NODE_ENV === 'development') {
      console.log("Tenant não encontrado diretamente. Buscando de forma alternativa em modo dev...");
      
      // Tentar listar todos os tenants para debug
      const { data: allTenants, error: listError } = await this.supabase
        .from('tenants')
        .select('id, slug, primary_color, secondary_color')
        .limit(5);
        
      if (listError) {
        console.error("Erro ao listar tenants:", listError.message);
      } else if (allTenants && allTenants.length > 0) {
        console.log("Tenants disponíveis:", allTenants.map((t: Tenant) => ({ id: t.id, slug: t.slug })));
        
        // Tentar encontrar um tenant por correspondência parcial
        const matchingTenant = allTenants.find((t: Tenant) => 
          t.slug?.toLowerCase() === subdomainToUse.toLowerCase()
        );
        
        if (matchingTenant) {
          console.log("Tenant encontrado por correspondência alternativa:", matchingTenant);
          
          // Usar este tenant para o resto do processamento
          return this.finalizeTenantAccess(matchingTenant, user, response);
        }
        
        // Em modo DEV com Docker, permitir usar o primeiro tenant disponível como fallback
        if (process.env.NEXT_PUBLIC_TENANT_FALLBACK === 'true') {
          console.log("Usando primeiro tenant como fallback:", allTenants[0]);
          
          // Usar o primeiro tenant disponível
          return this.finalizeTenantAccess(allTenants[0], user, response);
        }
      }
    }
    
    // Se não encontrou o tenant e não conseguiu usar fallback
    if (error || !tenant) {
      console.error("Erro ao buscar tenant ou tenant não encontrado:", error?.message || "Not found");
      const redirectResponse = NextResponse.redirect(new URL(`http://${this.baseDomain}`, this.request.url));
      return this.authMessageManager.setErrorMessage(redirectResponse, "Tenant não encontrado");
    }
    
    // Se encontrou o tenant, finaliza o processo normalmente
    return this.finalizeTenantAccess(tenant, user, response);
  }
  
  // Método extraído para reutilização no fallback
  private async finalizeTenantAccess(tenant: any, user: any, response?: NextResponse): Promise<NextResponse | null> {
    let finalResponse = response ? 
      response : 
      NextResponse.next({
        request: {
          headers: this.request.headers,
        },
      });
    
    if (tenant.primary_color) {
      finalResponse.cookies.set('tenant_primary_color', tenant.primary_color, {
        path: '/',
        maxAge: 60 * 60 * 24 * 7, // 7 dias
        sameSite: 'lax',
        httpOnly: false // Permitir acesso via JavaScript
      });
      console.log("Definindo cookie tenant_primary_color:", tenant.primary_color);
    }
    
    if (tenant.secondary_color) {
      finalResponse.cookies.set('tenant_secondary_color', tenant.secondary_color, {
        path: '/',
        maxAge: 60 * 60 * 24 * 7,
        sameSite: 'lax',
        httpOnly: false // Permitir acesso via JavaScript
      });
      console.log("Definindo cookie tenant_secondary_color:", tenant.secondary_color);
    }
    
    if (user) {
      const accessResponse = await this.validateUserTenantAccess(user.id, tenant.id, tenant.slug);
      if (accessResponse) return accessResponse;
    }
    
    if (this.isRootPath() && !navigationConfig.routes.shouldBypass(this.request.nextUrl.pathname)) {
      const loginResponse = NextResponse.redirect(new URL(`/login`, this.request.url));
      
      if (tenant.primary_color) {
        loginResponse.cookies.set('tenant_primary_color', tenant.primary_color, {
          path: '/',
          maxAge: 60 * 60 * 24 * 7,
          httpOnly: false // Permitir acesso via JavaScript
        });
      }
      if (tenant.secondary_color) {
        loginResponse.cookies.set('tenant_secondary_color', tenant.secondary_color, {
          path: '/',
          maxAge: 60 * 60 * 24 * 7, // 7 dias
          sameSite: 'lax',
          httpOnly: false // Permitir acesso via JavaScript
        });
      }
      return loginResponse;
    }
    
    const requestHeaders = new Headers(this.request.headers);
    requestHeaders.set('x-tenant-id', tenant.id);
    
    const requestWithTenantId = new NextRequest(this.request.nextUrl, {
      headers: requestHeaders,
      method: this.request.method,
      body: this.request.body
    });
    
    const finalNextResponse = NextResponse.next({
      request: requestWithTenantId,
    });
    
    if (tenant.primary_color) {
      finalNextResponse.cookies.set('tenant_primary_color', tenant.primary_color, {
        path: '/',
        maxAge: 60 * 60 * 24 * 7,
        sameSite: 'lax',
        httpOnly: false // Permitir acesso via JavaScript
      });
    }
    
    if (tenant.secondary_color) {
      finalNextResponse.cookies.set('tenant_secondary_color', tenant.secondary_color, {
        path: '/',
        maxAge: 60 * 60 * 24 * 7,
        sameSite: 'lax',
        httpOnly: false // Permitir acesso via JavaScript
      });
    }
    
    const responseHeaders = new Headers(finalNextResponse.headers);
    responseHeaders.set('x-tenant-id', tenant.id);
    
    Object.entries(Object.fromEntries(responseHeaders.entries())).forEach(([key, value]) => {
      finalNextResponse.headers.set(key, value);
    });
    
    return finalNextResponse;
  }
  
  private async validateUserTenantAccess(userId: string, tenantId: string, subdomain: string): Promise<NextResponse | null> {
    if (!this.supabase) {
      console.error('🚨 Middleware - Cliente Supabase não disponível para validateUserTenantAccess');
      const response = NextResponse.redirect(
        new URL(`http://${tenantConfig.getFullDomain(subdomain)}/login`, this.request.url)
      );
      return this.authMessageManager.setErrorMessage(
        response, 
        "Erro de configuração do sistema. Tente novamente."
      );
    }
    
    const { data: userData, error: userError } = await this.supabase
      .from('users')
      .select('tenant_id')
      .eq('id', userId)
      .single();
    
    if (userError || !userData || userData.tenant_id !== tenantId) {
      await this.supabase.auth.signOut();
      const response = NextResponse.redirect(
        new URL(`http://${tenantConfig.getFullDomain(subdomain)}/login`, this.request.url)
      );
      return this.authMessageManager.setErrorMessage(
        response, 
        "Credenciais inválidas. Verifique seu email e senha."
      );
    }
    
    return null;
  }
  
  private isRootPath(): boolean {
    return this.request.nextUrl.pathname === "/" || this.request.nextUrl.pathname === "";
  }
  
  async handleProtectedRouteWithoutAuth(analysis: ReturnType<RequestAnalyzer['analyze']>, user: any): Promise<NextResponse | null> {
    const path = this.request.nextUrl.pathname;
    
    if (!navigationConfig.routes.isProtected(path) || user || navigationConfig.routes.shouldBypass(path)) {
      return null;
    }
    
    if (!analysis.hasValidSubdomain) {
      const response = NextResponse.redirect(new URL(`http://${this.baseDomain}`, this.request.url));
      return this.authMessageManager.setErrorMessage(response, "Tenant não encontrado");
    }
    
    const response = NextResponse.redirect(new URL(`/login`, this.request.url));
    return this.authMessageManager.setErrorMessage(response, "Autenticação necessária para acessar esta área");
  }
  
  async handleRootWithAuth(user: any): Promise<NextResponse | null> {
    if (this.request.nextUrl.pathname === "/" && user) {
      if (!this.supabase) {
        console.error('🚨 Middleware - Cliente Supabase não disponível para handleRootWithAuth');
        return NextResponse.redirect(new URL("/home", this.request.url));
      }
      
      // Obter a role do usuário para determinar o redirecionamento
      const { data: userData, error } = await this.supabase
        .from('users')
        .select('role')
        .eq('id', user.id)
        .single();
      
      if (error) {
        console.error("Erro ao obter role do usuário:", error);
        return NextResponse.redirect(new URL("/home", this.request.url));
      }
      
      const redirectPath = userData?.role === 'admin' ? '/dashboard' : '/home';
      return NextResponse.redirect(new URL(redirectPath, this.request.url));
    }
    
    return null;
  }
  
  async handleAuthPagesWithAuth(user: any): Promise<NextResponse | null> {
    if (!user) return null;
    
    const path = this.request.nextUrl.pathname;
    
    // Verifica se a rota atual é de autenticação ou especificamente /login
    if (navigationConfig.routes.isAuth(path) || path === '/login') {
      if (!this.supabase) {
        console.error('🚨 Middleware - Cliente Supabase não disponível para handleAuthPagesWithAuth');
        return NextResponse.redirect(new URL("/home", this.request.url));
      }
      
      try {
        // Obter a role do usuário para determinar o redirecionamento
        const { data: userData, error } = await this.supabase
          .from('users')
          .select('role')
          .eq('id', user.id)
          .single();
        
        if (error) {
          console.error("Erro ao obter role do usuário:", error);
          return NextResponse.redirect(new URL("/home", this.request.url));
        }
        
        console.log("Redirecionando usuário autenticado da página de login:", user.id, userData?.role);
        const redirectPath = userData?.role === 'admin' ? '/dashboard' : '/home';
        return NextResponse.redirect(new URL(redirectPath, this.request.url));
      } catch (error) {
        console.error("Erro no redirecionamento de usuário autenticado:", error);
        return NextResponse.redirect(new URL("/home", this.request.url));
      }
    }
    
    return null;
  }
  
  async handleRoleBasedAccess(user: any): Promise<NextResponse | null> {
    if (!user) return null;
    
    const path = this.request.nextUrl.pathname;
    
    if (path === '/dashboard' || path === '/alunos') {
      if (!this.supabase) {
        console.error('🚨 Middleware - Cliente Supabase não disponível para handleRoleBasedAccess');
        return null;
      }
      
      try {
        const { data: userData, error } = await this.supabase
          .from('users')
          .select('role')
          .eq('id', user.id)
          .single();
        
        if (error) {
          console.error("Erro ao obter role do usuário:", error);
          return null;
        }
        
        if (userData?.role === 'student') {
          console.log("Redirecionando usuário sem permissão:", path, user.id);
          const redirectResponse = NextResponse.redirect(new URL("/home", this.request.url));
          return this.authMessageManager.setErrorMessage(
            redirectResponse,
            "Você não tem permissão para acessar esta área."
          );
        }
        
        if (path === '/alunos' && userData?.role !== 'admin') {
          console.log("Redirecionando usuário sem permissão para alunos:", user.id);
          const redirectResponse = NextResponse.redirect(new URL("/home", this.request.url));
          return this.authMessageManager.setErrorMessage(
            redirectResponse,
            "Apenas administradores podem acessar a área de alunos."
          );
        }
      } catch (error) {
        console.error("Erro na verificação de acesso baseado em role:", error);
        return null;
      }
    }
    
    return null;
  }
}

class SessionManager {
  private readonly subdomainExtractor = new SubdomainExtractor();
  private readonly paramsDecoder = new SearchParamsDecoder();
  private readonly clientFactory = new SupabaseClientFactory();
  
  async updateSession(request: NextRequest): Promise<NextResponse> {
    const response = this.createDefaultResponse(request);
    if (this.isPublicPath(request.nextUrl.pathname)) {
      return response;
    }

    // Cria uma nova instância do cliente Supabase com os cookies do request
    // e a capacidade de atualizar cookies na resposta
    const supabase = this.clientFactory.create(request, response);
    
    if (!supabase) {
      console.error('🚨 Middleware - Falha ao criar cliente Supabase');
      return response;
    }
    
    // Verifica e atualiza a sessão usando getUser() para maior segurança
    // ao invés de getSession() diretamente
    const { data: { user } } = await supabase.auth.getUser();

    if (process.env.NODE_ENV === 'development') {
      console.log('Middleware SessionManager - User authenticated:', !!user);
      
      const allCookies = request.cookies.getAll();
      const supabaseCookies = allCookies.filter(c => 
        c.name.startsWith('sb-') || c.name === 'supabase-auth-token'
      );
      
      if (supabaseCookies.length > 0) {
        console.log('Middleware SessionManager - Supabase cookies:', 
                    supabaseCookies.map(c => c.name).join(', '));
      }
    }

    return this.processRequest(request);
  }
  
  private isPublicPath(path: string): boolean {
    return navigationConfig.routes.isPublic(path) || 
           navigationConfig.routes.isAuth(path) || 
           navigationConfig.routes.shouldBypass(path) ||
           path.includes('/_next/') || 
           path.includes('/static/') || 
           path.includes('.');
  }
  
  private async processRequest(request: NextRequest): Promise<NextResponse> {
    let response = this.createDefaultResponse(request);
    const supabase = this.clientFactory.create(request, response);
    
    if (!supabase) {
      console.error('🚨 Middleware - Falha ao criar cliente Supabase para processamento');
      return response;
    }
    
    const analyzer = new RequestAnalyzer(request, this.subdomainExtractor, this.paramsDecoder);
    const routeHandler = new RouteHandler(request, supabase, analyzer);
    
    const analysis = analyzer.analyze();
    const { data: { user } } = await supabase.auth.getUser();
    
    // Verificar primeiro se o usuário está autenticado e tentando acessar páginas de autenticação
    const authPagesWithAuthResult = await routeHandler.handleAuthPagesWithAuth(user);
    if (authPagesWithAuthResult) return authPagesWithAuthResult;
    
    // Verificar acesso baseado em role (nova verificação)
    const roleBasedAccessResult = await routeHandler.handleRoleBasedAccess(user);
    if (roleBasedAccessResult) return roleBasedAccessResult;
    
    const bypassResult = await routeHandler.handleBypassRoutes(response);
    if (bypassResult) return bypassResult;
    
    const loginPostResult = await routeHandler.handleLoginPost(response);
    if (loginPostResult) return loginPostResult;
    
    const loginWithParamsResult = await routeHandler.handleLoginWithParams(response, analysis);
    if (loginWithParamsResult) return loginWithParamsResult;
    
    const loginWithoutSubdomainResult = await routeHandler.handleLoginWithoutSubdomain(analysis);
    if (loginWithoutSubdomainResult) return loginWithoutSubdomainResult;
    
    const subdomainAccessResult = await routeHandler.handleSubdomainAccess(analysis, user);
    if (subdomainAccessResult) return subdomainAccessResult;
    
    const protectedRouteResult = await routeHandler.handleProtectedRouteWithoutAuth(analysis, user);
    if (protectedRouteResult) return protectedRouteResult;
    
    const rootWithAuthResult = await routeHandler.handleRootWithAuth(user);
    if (rootWithAuthResult) return rootWithAuthResult;
    
    return response;
  }
  
  public createDefaultResponse(request: NextRequest): NextResponse {
    return NextResponse.next({
      request: {
        headers: request.headers,
      },
    });
  }
}

export const updateSession = async (request: NextRequest): Promise<NextResponse> => {
  const sessionManager = new SessionManager();
  return sessionManager.updateSession(request);
};