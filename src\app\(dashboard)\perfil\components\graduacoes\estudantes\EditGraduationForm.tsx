'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Calendar, Clock, Award, User, AlertTriangle, CheckCircle, Edit, Trash2, X } from 'lucide-react';
import { BeltWithDetails } from '@/components/belt';
import {
  getModalitiesForGraduation,
  getBeltLevelsForModality,
  getInstructorsForGraduation,
  updateStudentGraduation,
  deleteStudentGraduation,
  type UpdateGraduationFormData
} from '../../../actions/graduation-actions';
import { Graduacao } from './index';
import { formatToDatetimeLocal, datetimeLocalToBrasiliaISO, nowInBrasilia } from '@/utils/timezone-utils';

interface EditGraduationFormProps {
  graduation: Graduacao;
  isOpen: boolean;
  onSuccess?: () => void;
  onCancel?: () => void;
}

interface Modality {
  id: string;
  slug: string;
  name: string;
  enabled: boolean;
}

interface BeltLevel {
  id: string;
  belt_color: string;
  degree: number;
  label: string;
  sort_order: number;
  stripe_color?: string | null;
  show_center_line?: boolean | null;
  center_line_color?: string | null;
}

interface Instructor {
  id: string;
  first_name: string;
  last_name: string | null;
  full_name: string | null;
}

export function EditGraduationForm({ 
  graduation,
  isOpen,
  onSuccess, 
  onCancel 
}: EditGraduationFormProps) {
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // Dados do formulário
  const [modalities, setModalities] = useState<Modality[]>([]);
  const [beltLevels, setBeltLevels] = useState<BeltLevel[]>([]);
  const [instructors, setInstructors] = useState<Instructor[]>([]);
  
  // Estado do formulário
  const [selectedModality, setSelectedModality] = useState<string>('');
  const [selectedBeltLevel, setSelectedBeltLevel] = useState<string>('');
  const [selectedInstructor, setSelectedInstructor] = useState<string>('');
  const [graduationDateTime, setGraduationDateTime] = useState<string>('');

  // Função para definir data/hora atual do Brasil
  const setCurrentDateTime = () => {
    const brasilDateTime = formatToDatetimeLocal(nowInBrasilia());
    setGraduationDateTime(brasilDateTime);
  };

  // Carregar dados iniciais
  useEffect(() => {
    const loadInitialData = async () => {
      setLoading(true);
      try {
        // Carregar modalidades e instrutores em paralelo
        const [modalitiesResult, instructorsResult] = await Promise.all([
          getModalitiesForGraduation(),
          getInstructorsForGraduation()
        ]);

        if (modalitiesResult.success && modalitiesResult.data) {
          setModalities(modalitiesResult.data);
          
          // Pré-selecionar modalidade baseada no nome da modalidade da graduação
          if (graduation?.modalidade) {
            const matchingModality = modalitiesResult.data.find(m => 
              m.name.toLowerCase() === graduation.modalidade.toLowerCase()
            );
            if (matchingModality) {
              setSelectedModality(matchingModality.id);
            }
          }
        } else {
          setErrors(prev => ({ ...prev, _form: modalitiesResult.errors?._form || 'Erro ao carregar modalidades' }));
        }

        if (instructorsResult.success && instructorsResult.data) {
          setInstructors(instructorsResult.data);
          
          // Pré-selecionar instrutor baseado no nome do professor da graduação
          if (graduation?.professor) {
            const matchingInstructor = instructorsResult.data.find(i => {
              const fullName = i.full_name || `${i.first_name} ${i.last_name || ''}`.trim();
              return fullName.toLowerCase() === graduation.professor.toLowerCase();
            });
            if (matchingInstructor) {
              setSelectedInstructor(matchingInstructor.id);
            }
          }
        } else {
          setErrors(prev => ({ ...prev, _form: 'Erro ao carregar instrutores' }));
        }

        // Pré-preencher data e hora usando timezone do Brasil
        if (graduation) {
          const formattedDateTime = formatToDatetimeLocal(graduation.data);
          setGraduationDateTime(formattedDateTime);
        }
      } catch (error) {
        console.error('Erro ao carregar dados iniciais:', error);
        setErrors({ _form: 'Erro ao carregar dados do formulário' });
      } finally {
        setLoading(false);
      }
    };

    if (isOpen) {
      loadInitialData();
    } else {
      // Resetar estado quando modal for fechado
      setSelectedModality('');
      setSelectedBeltLevel('');
      setSelectedInstructor('');
      setGraduationDateTime('');
      setErrors({});
    }
  }, [isOpen, graduation]);

  // Carregar níveis de faixa quando modalidade for selecionada
  useEffect(() => {
    const loadBeltLevels = async () => {
      if (!selectedModality) {
        setBeltLevels([]);
        setSelectedBeltLevel('');
        return;
      }

      const selectedModalityData = modalities.find(m => m.id === selectedModality);
      if (!selectedModalityData) return;

      try {
        const result = await getBeltLevelsForModality(selectedModalityData.slug);
        if (result.success && result.data) {
          setBeltLevels(result.data);
          
          // Pré-selecionar nível de faixa baseado na faixa e grau da graduação
          if (graduation?.faixa && graduation?.graus !== undefined) {
            const matchingBeltLevel = result.data.find(level => 
              level.belt_color === graduation.faixa && level.degree === graduation.graus
            );
            if (matchingBeltLevel) {
              setSelectedBeltLevel(matchingBeltLevel.id);
            }
          }
        } else {
          setErrors(prev => ({ ...prev, modality: 'Erro ao buscar níveis de faixa' }));
        }
      } catch (error) {
        console.error('Erro ao carregar níveis de faixa:', error);
        setErrors(prev => ({ ...prev, modality: 'Erro ao buscar níveis de faixa' }));
      }
    };

    loadBeltLevels();
  }, [selectedModality, modalities, graduation]);

  // Submissão do formulário para atualização
  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setErrors({});

    try {
      const formData: UpdateGraduationFormData = {
        graduationId: graduation.id,
        modalityId: selectedModality,
        beltLevelId: selectedBeltLevel,
        instructorId: selectedInstructor,
        awardedAt: datetimeLocalToBrasiliaISO(graduationDateTime)
      };

      const result = await updateStudentGraduation(formData);

      if (result.success) {
        onSuccess?.();
      } else {
        // Convert Zod errors to simple string record
        const formattedErrors: Record<string, string> = {};
        if (result.errors) {
          Object.entries(result.errors).forEach(([key, value]) => {
            if (typeof value === 'string') {
              formattedErrors[key] = value;
            } else if (value && typeof value === 'object' && 'message' in value) {
              formattedErrors[key] = value.message as string;
            }
          });
        }
        setErrors(Object.keys(formattedErrors).length > 0 ? formattedErrors : { _form: 'Erro desconhecido' });
      }
    } catch (error) {
      console.error('Erro ao atualizar graduação:', error);
      setErrors({ _form: 'Erro interno do servidor' });
    } finally {
      setSubmitting(false);
    }
  };

  // Submissão para deletar graduação
  const handleDelete = async () => {
    if (!confirm('Tem certeza que deseja excluir esta graduação? Esta ação não pode ser desfeita.')) {
      return;
    }

    setDeleting(true);
    setErrors({});

    try {
      const result = await deleteStudentGraduation(graduation.id);

      if (result.success) {
        onSuccess?.();
      } else {
        setErrors({ _form: result.errors?._form || 'Erro ao excluir graduação' });
      }
    } catch (error) {
      console.error('Erro ao excluir graduação:', error);
      setErrors({ _form: 'Erro interno do servidor' });
    } finally {
      setDeleting(false);
    }
  };

  const selectedBeltLevelData = beltLevels.find(bl => bl.id === selectedBeltLevel);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onCancel?.()}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-2 pb-4">
          <div className="flex items-center gap-2">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Edit className="w-5 h-5 text-primary" />
            </div>
            <div className="flex-1">
              <DialogTitle className="text-lg">Editar Graduação</DialogTitle>
              <DialogDescription>
                Editar ou excluir uma graduação existente
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Graduação Atual */}
          <Card className="border border-muted bg-muted/30">
            <CardContent className="pt-4 pb-4">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Award className="w-4 h-4 text-primary" />
                </div>
                <span className="text-sm font-medium">Graduação Atual</span>
              </div>
              
              <div className="flex items-center gap-3">
                <BeltWithDetails 
                  color={graduation.faixa} 
                  degree={graduation.graus || 0} 
                  label={graduation.faixaLabel}
                  stripeColor={graduation.stripe_color}
                  showCenterLine={graduation.show_center_line}
                  centerLineColor={graduation.center_line_color}
                  size="md"
                  className="w-16 h-4"
                />
                <div>
                  <p className="text-sm font-medium">{graduation.modalidade} - {graduation.faixaLabel}</p>
                  <p className="text-xs text-muted-foreground">
                    {graduation.data.toLocaleDateString('pt-BR')} • {graduation.professor}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Erros Gerais */}
          {errors._form && (
            <Alert variant="destructive" className="rounded-lg">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{errors._form}</AlertDescription>
            </Alert>
          )}

          {loading ? (
            <div className="space-y-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-10 bg-muted rounded-lg animate-pulse" />
              ))}
            </div>
          ) : (
            <form onSubmit={handleUpdate} className="space-y-6">
              {/* Seleção de Modalidade */}
              <div className="space-y-3">
                <Label htmlFor="modality" className="text-sm font-medium">
                  Modalidade
                </Label>
                <Select value={selectedModality} onValueChange={setSelectedModality}>
                  <SelectTrigger className="rounded-lg">
                    <SelectValue placeholder="Selecione a modalidade" />
                  </SelectTrigger>
                  <SelectContent>
                    {modalities.map((modality) => (
                      <SelectItem key={modality.id} value={modality.id}>
                        {modality.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.modalityId && (
                  <p className="text-sm text-destructive flex items-center gap-1">
                    <AlertTriangle className="w-3 h-3" />
                    {errors.modalityId}
                  </p>
                )}
              </div>

              <Separator />

              {/* Seleção de Nível de Faixa */}
              <div className="space-y-3">
                <Label htmlFor="beltLevel" className="text-sm font-medium">
                  Nível de Graduação
                </Label>
                <Select 
                  value={selectedBeltLevel} 
                  onValueChange={setSelectedBeltLevel}
                  disabled={!selectedModality || beltLevels.length === 0}
                >
                  <SelectTrigger className="rounded-lg">
                    <SelectValue placeholder="Selecione o nível" />
                  </SelectTrigger>
                  <SelectContent>
                    {beltLevels.map((level) => (
                      <SelectItem key={level.id} value={level.id}>
                        <div className="flex items-center gap-3">
                          <BeltWithDetails
                            color={level.belt_color}
                            degree={level.degree}
                            stripeColor={level.stripe_color}
                            showCenterLine={level.show_center_line}
                            centerLineColor={level.center_line_color}
                            size="sm"
                            className="w-8 h-2"
                          />
                          <span>{level.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.beltLevelId && (
                  <p className="text-sm text-destructive flex items-center gap-1">
                    <AlertTriangle className="w-3 h-3" />
                    {errors.beltLevelId}
                  </p>
                )}
              </div>

              {/* Preview da Nova Faixa */}
              {selectedBeltLevelData && (
                <Card className="border-primary/20 bg-primary/5">
                  <CardContent className="pt-4 pb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <CheckCircle className="w-4 h-4 text-primary" />
                      </div>
                      <BeltWithDetails
                        color={selectedBeltLevelData.belt_color}
                        degree={selectedBeltLevelData.degree}
                        stripeColor={selectedBeltLevelData.stripe_color}
                        showCenterLine={selectedBeltLevelData.show_center_line}
                        centerLineColor={selectedBeltLevelData.center_line_color}
                        size="md"
                        className="w-16 h-4"
                      />
                      <div>
                        <p className="text-sm font-medium text-primary">
                          Nova Graduação: {selectedBeltLevelData.label}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              <Separator />

              {/* Seleção de Instrutor */}
              <div className="space-y-3">
                <Label htmlFor="instructor" className="text-sm font-medium">
                  Instrutor Responsável
                </Label>
                <Select value={selectedInstructor} onValueChange={setSelectedInstructor}>
                  <SelectTrigger className="rounded-lg">
                    <SelectValue placeholder="Selecione o instrutor" />
                  </SelectTrigger>
                  <SelectContent>
                    {instructors.map((instructor) => (
                      <SelectItem key={instructor.id} value={instructor.id}>
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4" />
                          <span>{instructor.full_name || `${instructor.first_name} ${instructor.last_name || ''}`.trim()}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.instructorId && (
                  <p className="text-sm text-destructive flex items-center gap-1">
                    <AlertTriangle className="w-3 h-3" />
                    {errors.instructorId}
                  </p>
                )}
              </div>

              {/* Data e Hora da Graduação */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="dateTime" className="text-sm font-medium">
                    Data e Hora da Graduação
                  </Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={setCurrentDateTime}
                    className="h-7 px-3 text-xs rounded-lg"
                  >
                    <Clock className="w-3 h-3 mr-1" />
                    Agora
                  </Button>
                </div>
                <Input
                  id="dateTime"
                  type="datetime-local"
                  value={graduationDateTime}
                  onChange={(e) => setGraduationDateTime(e.target.value)}
                  required
                  className="rounded-lg"
                />
                {errors.awardedAt && (
                  <p className="text-sm text-destructive flex items-center gap-1">
                    <AlertTriangle className="w-3 h-3" />
                    {errors.awardedAt}
                  </p>
                )}
              </div>

              <Separator />

              {/* Botões de Ação */}
              <div className="flex gap-3 pt-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={submitting || deleting}
                  className="flex-1 rounded-lg"
                >
                  Cancelar
                </Button>
                
                <Button
                  type="button"
                  variant="destructive"
                  onClick={handleDelete}
                  disabled={submitting || deleting}
                  className="rounded-lg"
                >
                  {deleting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Excluindo...
                    </>
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4 mr-2" />
                      Excluir
                    </>
                  )}
                </Button>
                
                <Button
                  type="submit"
                  disabled={submitting || deleting || !selectedModality || !selectedBeltLevel || !selectedInstructor || !graduationDateTime}
                  className="flex-1 rounded-lg"
                >
                  {submitting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Salvando...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Salvar
                    </>
                  )}
                </Button>
              </div>
            </form>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
} 