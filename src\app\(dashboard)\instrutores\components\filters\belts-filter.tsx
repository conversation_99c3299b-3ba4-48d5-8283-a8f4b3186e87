'use client';

import { useState } from 'react';
import { X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Belt, beltColorTranslation, BeltColor } from '@/components/belt';
import { cn } from '@/lib/utils';

interface BeltsFilterProps {
  selectedBelts: string[];
  onChange: (belts: string[]) => void;
  className?: string;
}

// Cores de faixa disponíveis para instrutores
const availableBelts: { value: BeltColor; label: string }[] = [
  { value: 'white', label: 'Branca' },
  { value: 'blue', label: 'Azul' },
  { value: 'purple', label: 'Roxa' },
  { value: 'brown', label: '<PERSON><PERSON>' },
  { value: 'black', label: 'Preta' },
];

export function BeltsFilter({ selectedBelts, onChange, className }: BeltsFilterProps) {
  const [open, setOpen] = useState(false);

  const toggleBelt = (beltValue: string) => {
    if (selectedBelts.includes(beltValue)) {
      onChange(selectedBelts.filter(b => b !== beltValue));
    } else {
      onChange([...selectedBelts, beltValue]);
    }
  };

  const removeBelt = (beltValue: string) => {
    onChange(selectedBelts.filter(b => b !== beltValue));
  };

  const clearAll = () => {
    onChange([]);
  };

  return (
    <div className={cn("space-y-2", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button 
            variant="outline" 
            className="w-full justify-between h-10"
            role="combobox"
            aria-expanded={open}
          >
            Filtrar por faixas
            <span className="ml-2 rounded-full bg-primary text-primary-foreground px-2 py-0.5 text-xs">
              {selectedBelts.length}
            </span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput placeholder="Buscar faixa..." />
            <CommandEmpty>Nenhuma faixa encontrada.</CommandEmpty>
            <CommandGroup className="max-h-64 overflow-auto">
              {availableBelts.map((belt) => {
                const isSelected = selectedBelts.includes(belt.value);
                return (
                  <CommandItem
                    key={belt.value}
                    value={belt.value}
                    onSelect={() => toggleBelt(belt.value)}
                    className={cn(
                      'flex items-center gap-3 py-2',
                      isSelected ? 'bg-muted' : ''
                    )}
                  >
                    <div
                      className={cn(
                        'flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                        isSelected
                          ? 'bg-primary text-primary-foreground'
                          : 'opacity-50'
                      )}
                    >
                      {isSelected && (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          width="14"
                          height="14"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="3"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      )}
                    </div>
                    <Belt color={belt.value} size="xs" />
                    <span className="flex-1">{belt.label}</span>
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      {selectedBelts.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {selectedBelts.map((beltValue) => {
            const beltLabel = beltColorTranslation[beltValue as BeltColor] || beltValue;
            return (
              <Badge key={beltValue} variant="secondary" className="px-2 py-1 flex items-center gap-1">
                <Belt color={beltValue as BeltColor} size="xs" />
                <span className="text-xs">{beltLabel}</span>
                <button
                  type="button"
                  onClick={() => removeBelt(beltValue)}
                  className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                >
                  <X className="h-3 w-3" />
                  <span className="sr-only">Remover faixa {beltLabel}</span>
                </button>
              </Badge>
            );
          })}
          
          {selectedBelts.length > 1 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAll}
              className="h-7 px-2 text-xs"
            >
              Limpar todas
            </Button>
          )}
        </div>
      )}
    </div>
  );
} 