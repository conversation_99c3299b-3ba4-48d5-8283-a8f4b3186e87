"use server";

import { createClient } from "@/services/supabase/server";
import { validateUserAuthentication } from "./shared/validation-helpers";
import type { ClassGroupWithDetails } from "../../../aulas/types";

/**
 * Obtém uma turma específica com detalhes
 */
export async function getClassGroupById(id: string) {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;
    const supabase = await createClient();

    const { data: classGroup, error } = await supabase
      .from("class_groups")
      .select(`
        *,
        instructor:users!class_groups_instructor_id_fkey(id, first_name, last_name, full_name, avatar_url),
        branch:branches!class_groups_branch_id_fkey(id, name),
        enrollments:class_group_enrollments(count),
        waitlist:class_waitlist(count),
        classes(count)
      `)
      .eq("id", id)
      .eq("tenant_id", tenantId)
      .single();

    if (error) {
      console.error("Erro ao buscar turma:", error);
      return {
        success: false,
        error: "Turma não encontrada",
      };
    }

    if (!classGroup) {
      return {
        success: false,
        error: "Turma não encontrada",
      };
    }

    // Transformar os dados para corresponder ao tipo esperado
    const transformedClassGroup: ClassGroupWithDetails = {
      ...classGroup,
      _count: {
        enrollments: classGroup.enrollments?.[0]?.count || 0,
        waitlist: classGroup.waitlist?.[0]?.count || 0,
        classes: classGroup.classes?.[0]?.count || 0,
      },
      current_enrollment_count: classGroup.enrollments?.[0]?.count || 0,
      capacity_usage_percentage: classGroup.max_capacity 
        ? ((classGroup.enrollments?.[0]?.count || 0) / classGroup.max_capacity) * 100
        : null,
    };

    // Remover os campos temporários usados para contagem
    delete (transformedClassGroup as any).enrollments;
    delete (transformedClassGroup as any).waitlist;
    delete (transformedClassGroup as any).classes;

    return {
      success: true,
      data: transformedClassGroup,
    };
  } catch (error) {
    console.error("Erro ao buscar turma:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 