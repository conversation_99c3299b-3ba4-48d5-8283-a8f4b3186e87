'use client';

import { cn } from '@/lib/utils';
import { RefreshCw, Clock } from 'lucide-react';

interface LiveIndicatorProps {
  isConnected: boolean;
  lastUpdated?: Date;
  isActive?: boolean;
  className?: string;
}

export function LiveIndicator({ 
  isConnected, 
  lastUpdated, 
  isActive = true,
  className 
}: LiveIndicatorProps) {
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <div className={cn(
      "flex items-center gap-2 text-xs text-muted-foreground",
      className
    )}>
      <div className="flex items-center gap-1">
        <div className={cn(
          "w-2 h-2 rounded-full transition-colors",
          isConnected 
            ? "bg-green-500 animate-pulse" 
            : "bg-red-500"
        )} />
        <RefreshCw className={cn(
          "h-3 w-3",
          isConnected ? (isActive ? "text-green-600 animate-spin" : "text-blue-600") : "text-red-600",
        )} />
        <span className="font-medium">
          {isConnected ? "Polling Ativo" : "Polling Pausado"}
        </span>
      </div>
      {lastUpdated && (
        <span className="text-muted-foreground">
          • Atualizado às {formatTime(lastUpdated)}
        </span>
      )}
    </div>
  );
} 