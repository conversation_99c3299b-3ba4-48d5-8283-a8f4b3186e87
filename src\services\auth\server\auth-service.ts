'use server'

import { createClient, createAdminClient } from '@/services/supabase/server'
import { cookieRedirect } from '@/utils/utils'
import { syncUserStatusWithMetadata } from '@/services/auth/utils/server-utils'
import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'
import { type CookieOptions } from '@supabase/ssr'
import { getSupabaseConfig } from '@/config/supabase'

// Cache para dados de welcome do usuário (TTL: 2 minutos)
const welcomeDataCache = new Map<string, { data: any; timestamp: number }>()
const WELCOME_CACHE_TTL = 2 * 60 * 1000 // 2 minutos

// Cache para evitar queries simultâneas do mesmo usuário
const ongoingWelcomeQueries = new Map<string, Promise<any>>()

/**
 * Limpa entradas expiradas do cache de welcome data
 */
function cleanExpiredWelcomeCache() {
  const now = Date.now()
  const keysToDelete: string[] = []

  welcomeDataCache.forEach((value, key) => {
    if (now - value.timestamp > WELCOME_CACHE_TTL) {
      keysToDelete.push(key)
    }
  })

  keysToDelete.forEach((key) => welcomeDataCache.delete(key))
}

async function getTenantIdFromHeader(headerValue: string | null): Promise<string | null> {
  return headerValue || null
}

export const signUpAction = async (formData: FormData, originUrl: string, tenantIdHeader: string | null) => {
  const email = formData.get('email')?.toString()
  const password = formData.get('password')?.toString()
  const supabase = await createClient()
  const adminClient = await createAdminClient()
  const origin = originUrl

  const tenantId = await getTenantIdFromHeader(tenantIdHeader)

  if (!email || !password) {
    return await cookieRedirect('error', '/login', 'Email e senha são obrigatórios')
  }

  if (!tenantId) {
    return await cookieRedirect('error', '/login', 'Tenant não encontrado. Por favor, tente novamente.')
  }

  const { error, data } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${origin}/auth/callback`,
      data: {
        tenant_id: tenantId
      }
    }
  })

  if (error) {
    console.error(error.code + ' ' + error.message)
    return await cookieRedirect('error', '/login', error.message)
  } else {
    if (data.user) {
      await adminClient.auth.admin.updateUserById(data.user.id, { app_metadata: { tenant_id: tenantId } })

      const { error: userError } = await supabase.from('users').update({ tenant_id: tenantId }).eq('id', data.user.id)

      if (userError) {
        console.error('Erro ao associar usuário ao tenant:', userError)
      }
    }

    return await cookieRedirect('success', '/login', 'Cadastro realizado com sucesso! Verifique seu email para confirmar sua conta.')
  }
}

export const signInAction = async (formData: FormData, hostHeader: string | null) => {
  const supabase = await createClient()
  // Array para armazenar os cookies gerados pelo Supabase durante o login
  const authCookies: { name: string; value: string; options?: CookieOptions }[] = []

  const email = formData.get('email') as string
  const password = formData.get('password') as string

  if (!email || !password) {
    return {
      error: 'Campos obrigatórios não preenchidos',
      error_code: 'missing_fields',
      status: 400
    }
  }

  try {
    const host = hostHeader || ''
    console.log('Host header:', host)

    const subdomain = host.split('.')[0]
    console.log('Subdomain:', subdomain)

    let tenantId = null

    // Obter tenant ID do subdomínio, exceto para localhost
    if (subdomain !== 'localhost:3000' && !subdomain.includes('127.0.0.1')) {
      const { data: tenant, error: tenantError } = await supabase.from('tenants').select('id, slug').eq('slug', subdomain).single()

      if (tenantError || !tenant) {
        console.log('Tenant não encontrado para o subdomínio:', subdomain)
        return {
          error: 'Tenant não encontrado',
          error_code: 'tenant_not_found'
        }
      }

      console.log('Tenant encontrado:', tenant.id, tenant.slug)
      tenantId = tenant.id
    }

    // Verificar acesso ao tenant, se aplicável
    if (tenantId) {
      const { data, error } = await supabase.rpc('verify_tenant_access', {
        p_email: email,
        p_tenant_id: tenantId
      })

      if (error || !data.success) {
        return {
          error: 'Credenciais inválidas (tenant)',
          error_code: 'invalid_credentials',
          status: 401
        }
      }
    }

    console.log('Iniciando processo de login...')

    // Substituir o cliente padrão por um cliente modificado que captura cookies
    const cookieStore = await cookies()
    const config = getSupabaseConfig()

    const modifiedSupabase = createServerClient(config.url, config.anonKey, {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) => {
              console.log(`Auth Service - Capturando cookie durante login: ${name}`)
              authCookies.push({ name, value, options })
            })
          } catch (error) {
            console.error('Erro ao processar cookies no serviço de autenticação:', error)
          }
        }
      },
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: false // Desativado no servidor
      }
    })

    // Realizar o login com email/senha usando o cliente modificado
    console.log('Iniciando login com email/senha')
    const { data: signInData, error } = await modifiedSupabase.auth.signInWithPassword({
      email,
      password
    })

    // Se ocorrer um erro no login
    if (error) {
      console.error('Erro no login:', error.message, error.cause)
      return {
        error: error.message,
        error_code: 'auth_error',
        status: 401
      }
    }

    // Verificar se o login foi bem-sucedido, mas sem sessão
    if (!signInData || !signInData.session) {
      console.error('Login bem-sucedido mas sem sessão retornada')
      return {
        error: 'Falha ao criar sessão',
        error_code: 'session_creation_failed',
        status: 500
      }
    }

    // Login bem-sucedido
    console.log('Login bem-sucedido. User ID:', signInData.user.id)
    console.log(`Auth Service - Cookies capturados durante login: ${authCookies.length}`)

    // Verificar se o usuário existe na tabela users e seus dados estão completos (OTIMIZADO)
    // Usar modifiedSupabase que tem a sessão ativa em vez de adminClient
    const { data: userData, error: userError } = await modifiedSupabase
      .from('users')
      .select(
        `
        id,
        email,
        tenant_id,
        role,
        first_name,
        last_name,
        status
      `
      )
      .eq('id', signInData.user.id)
      .single()

    // Buscar dados específicos do role se necessário (de forma assíncrona para não bloquear login)
    let roleSpecificData = null
    if (userData && !userError) {
      if (userData.role === 'student') {
        const { data: studentData } = await modifiedSupabase.from('students').select('id').eq('user_id', signInData.user.id).single()
        roleSpecificData = { students: studentData ? [studentData] : [] }
      } else if (userData.role === 'instructor') {
        const { data: instructorData } = await modifiedSupabase.from('instructors').select('id').eq('user_id', signInData.user.id).single()
        roleSpecificData = { instructors: instructorData ? [instructorData] : [] }
      }
    }

    if (userError) {
      console.error('Erro ao buscar usuário na tabela users:', userError)
      await supabase.auth.signOut()
      return {
        error: 'Erro ao verificar dados do usuário',
        error_code: 'user_query_error',
        status: 500
      }
    }

    if (!userData) {
      await supabase.auth.signOut()
      console.log('Usuário autenticado no Auth mas não encontrado na tabela users:', signInData.user.id)
      return {
        error: 'Usuário não encontrado na base de dados',
        error_code: 'user_not_found_in_db',
        status: 401
      }
    }

    // Verificação de status da conta - bloquear login para contas inativas ou suspensas
    if (userData.status === 'inactive') {
      await supabase.auth.signOut()
      console.log('Tentativa de login com conta inativa:', signInData.user.id, userData.email)
      return {
        error: 'Sua conta está inativa. Entre em contato com o administrador para reativá-la.',
        error_code: 'account_inactive',
        status: 403
      }
    }

    if (userData.status === 'suspended') {
      await supabase.auth.signOut()
      console.log('Tentativa de login com conta suspensa:', signInData.user.id, userData.email)
      return {
        error: 'Sua conta está suspensa. Entre em contato com o administrador para mais informações.',
        error_code: 'account_suspended',
        status: 403
      }
    }

    // Verificar se é aluno sem registro na tabela students (OTIMIZADO)
    if (userData.role === 'student' && roleSpecificData && (!roleSpecificData.students || roleSpecificData.students.length === 0)) {
      console.log('Usuário é um aluno mas não tem registro na tabela students:', signInData.user.id)
      // Não impedir login, mas registrar o problema
    }

    // Verificar acesso ao tenant
    if (tenantId && userData.tenant_id !== tenantId) {
      await supabase.auth.signOut()
      return {
        error: 'Você não tem acesso a este tenant',
        error_code: 'invalid_tenant_access',
        status: 401
      }
    }

    const userAppMetadata = (signInData.user.app_metadata as Record<string, any>) || {}

    // Atualizar app_metadata apenas se necessário
    if (tenantId && userAppMetadata.tenant_id !== tenantId) {
      // Criar um adminClient temporário para operação administrativa
      const tempAdminClient = await createAdminClient()
      await tempAdminClient.auth.admin.updateUserById(signInData.user.id, { app_metadata: { ...userAppMetadata, tenant_id: tenantId } })
    }

    // Sincronizar status do usuário com app_metadata de forma assíncrona (não bloqueante)
    console.log('Agendando sincronização de metadados...')
    // Executar de forma assíncrona para não bloquear o login
    setTimeout(async () => {
      try {
        await syncUserStatusWithMetadata(signInData.user.id)
      } catch (error) {
        console.error('Erro na sincronização assíncrona de metadados:', error)
      }
    }, 100)

    return {
      success: true,
      user: {
        id: signInData?.user?.id,
        email: signInData?.user?.email,
        login_timestamp: Date.now()
      },
      // Retornar os cookies capturados para que possam ser aplicados na resposta
      authCookies
    }
  } catch (error) {
    console.error('Erro no login:', error)
    return {
      error: 'Ocorreu um erro durante o login',
      error_code: 'unknown_error',
      status: 500
    }
  }
}

export const forgotPasswordAction = async (formData: FormData, originUrl: string) => {
  const email = formData.get('email')?.toString()
  const supabase = await createClient()
  const origin = originUrl
  const callbackUrl = formData.get('callbackUrl')?.toString()

  if (!email) {
    return await cookieRedirect('error', '/reset-password', 'Email é obrigatório')
  }

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${origin}/auth/callback?redirect_to=/home/<USER>
  })

  if (error) {
    console.error(error.message)
    return await cookieRedirect('error', '/reset-password', 'Não foi possível redefinir a senha')
  }

  if (callbackUrl) {
    return { redirect: callbackUrl }
  }

  return await cookieRedirect('success', '/login', 'Instruções para redefinir sua senha foram enviadas para seu email')
}

export const resetPasswordAction = async (formData: FormData) => {
  const password = formData.get('password')?.toString()
  const confirmPassword = formData.get('confirmPassword')?.toString()
  const supabase = await createClient()

  if (!password || !confirmPassword) {
    return await cookieRedirect('error', '/reset-password', 'Senha e confirmação de senha são obrigatórios')
  }

  if (password !== confirmPassword) {
    return await cookieRedirect('error', '/reset-password', 'As senhas não coincidem')
  }

  if (password.length < 6) {
    return await cookieRedirect('error', '/reset-password', 'A senha deve ter pelo menos 6 caracteres')
  }

  const { error } = await supabase.auth.updateUser({
    password
  })

  if (error) {
    console.error(error.message)
    return await cookieRedirect('error', '/reset-password', error.message)
  }

  return await cookieRedirect('success', '/login', 'Senha atualizada com sucesso! Faça login com sua nova senha.')
}

export const signOutAction = async () => {
  const supabase = await createClient()
  await supabase.auth.signOut()
  return await cookieRedirect('success', '/login', 'Logout realizado com sucesso')
}

export async function getUserWelcomeData() {
  try {
    const supabase = await createClient()

    // Verificar sessão primeiro
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession()

    console.log('getUserWelcomeData - Sessão encontrada:', sessionData && sessionData.session ? 'Sim' : 'Não', 'Session UUID:', sessionData?.session?.user?.id || 'Não disponível')

    // Se não há sessão, verificar outras opções
    if (!sessionData?.session) {
      console.log('Tentando método alternativo para obter usuário...')

      const { data: userData, error: userError } = await supabase.auth.getUser()

      if (userError || !userData.user) {
        console.error('Sessão não encontrada:', userError ? userError.message : 'Usuário não encontrado')
        return {
          displayName: 'Usuário',
          error: 'Sessão não encontrada'
        }
      }

      // Usar query otimizada mesmo sem sessão ativa
      const welcomeData = await getOptimizedWelcomeDataWithCache(supabase, userData.user.id)
      return (
        welcomeData || {
          displayName: userData.user.email?.split('@')[0] || 'Usuário',
          error: 'Dados incompletos'
        }
      )
    }

    // Obter dados do usuário autenticado
    const {
      data: { user },
      error: authError
    } = await supabase.auth.getUser()

    if (authError || !user) {
      console.error('Erro ao obter usuário:', authError?.message || 'Usuário não encontrado')
      return {
        displayName: 'Usuário',
        error: authError?.message || 'Usuário não encontrado'
      }
    }

    // Usar query otimizada para obter todos os dados necessários
    const welcomeData = await getOptimizedWelcomeDataWithCache(supabase, user.id)

    return (
      welcomeData || {
        displayName: user.email?.split('@')[0] || 'Usuário',
        error: 'Dados não encontrados'
      }
    )
  } catch (error) {
    console.error('Erro ao obter dados do usuário:', error)
    return {
      displayName: 'Usuário',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    }
  }
}

/**
 * Versão otimizada com cache em memória para dados de welcome
 */
async function getOptimizedWelcomeDataWithCache(supabase: any, userId: string) {
  // Verificar cache primeiro
  const cached = welcomeDataCache.get(userId)
  if (cached && Date.now() - cached.timestamp < WELCOME_CACHE_TTL) {
    console.log(`Cache hit para welcome data do usuário ${userId}`)
    return cached.data
  }

  // Verificar se já há uma query em andamento para este usuário
  const ongoingQuery = ongoingWelcomeQueries.get(userId)
  if (ongoingQuery) {
    console.log(`Aguardando query em andamento para usuário ${userId}`)
    return await ongoingQuery
  }

  // Criar e executar nova query
  const queryPromise = getOptimizedWelcomeData(supabase, userId)
  ongoingWelcomeQueries.set(userId, queryPromise)

  try {
    const result = await queryPromise

    // Armazenar no cache se sucesso
    if (result) {
      // Limpar cache expirado periodicamente
      if (welcomeDataCache.size > 100) {
        cleanExpiredWelcomeCache()
      }

      welcomeDataCache.set(userId, {
        data: result,
        timestamp: Date.now()
      })
    }

    return result
  } finally {
    // Remover da lista de queries em andamento
    ongoingWelcomeQueries.delete(userId)
  }
}

/**
 * Função otimizada que busca todos os dados necessários com uma única query
 */
async function getOptimizedWelcomeData(supabase: any, userId: string) {
  try {
    // Query única que busca dados do usuário, tenant e referência ao registro de estudante/instrutor
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select(
        `
        first_name,
        last_name,
        full_name,
        tenant_id,
        role,
        tenants:tenant_id ( name ),
        students:students!user_id ( id ),
        instructors:instructors!user_id ( id )
      `
      )
      .eq('id', userId)
      .single()

    if (userError || !userData) {
      console.error('Erro ao buscar dados otimizados do usuário:', userError)
      return null
    }

    // Processar dados obtidos
    const firstName = userData.first_name || ''
    const lastName = userData.last_name || ''
    const fullName = userData.full_name || ''

    // Determinar o nome de exibição com prioridade: full_name > first_name + last_name
    let displayName = ''
    if (fullName && fullName.trim().split(' ').length >= 2) {
      displayName = fullName
    } else if (firstName) {
      displayName = `${firstName}${lastName ? ' ' + lastName : ''}`
    } else {
      displayName = 'Usuário'
    }

    // Extrair nome do tenant
    const tenantName = userData.tenants?.name || null

    // Processar dados de faixa para alunos
    let belt = undefined
    let stripes = undefined
    let beltLabel = undefined
    let beltData = undefined

    if (userData.role === 'student' && userData.students?.[0]) {
      const studentId = userData.students[0].id
      const { data: beltDetails } = await supabase.rpc('get_student_current_belt_details', { student_id_param: studentId })
      if (beltDetails && beltDetails.length > 0) {
        const details = beltDetails[0]
        belt = details.belt_color
        stripes = details.degree ?? undefined
        beltLabel = details.label ?? undefined
        beltData = {
          belt_color: details.belt_color,
          stripe_color: details.stripe_color,
          label: details.label,
          degree: details.degree,
          show_center_line: details.show_center_line,
          center_line_color: details.center_line_color
        }
      }
    } else if (userData.role === 'instructor' && userData.instructors?.[0]) {
      const instructorId = userData.instructors[0].id
      const { data: beltDetails } = await supabase.rpc('get_instructor_current_belt_details', { instructor_id_param: instructorId })
      if (beltDetails && beltDetails.length > 0) {
        const details = beltDetails[0]
        belt = details.belt_color
        stripes = details.degree ?? undefined
        beltLabel = details.label ?? undefined
        beltData = {
          belt_color: details.belt_color,
          stripe_color: details.stripe_color,
          label: details.label,
          degree: details.degree,
          show_center_line: details.show_center_line,
          center_line_color: details.center_line_color
        }
      }
    }

    return {
      displayName,
      tenantName,
      belt,
      stripes,
      beltLabel,
      beltData,
      role: userData.role || 'student'
    }
  } catch (error) {
    console.error('Erro ao buscar dados otimizados:', error)
    return null
  }
}
