import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/services/supabase/types/database.types';
import { createClient as createServerClient } from './server';

import {
  TenantClientFactory
} from '../shared/tenant-utils';

export const createTenantServerClient = async (): Promise<SupabaseClient<Database>> => {
  try {
    // Importar next/headers dinamicamente apenas no servidor
    const { headers, cookies } = await import('next/headers');
    const [headersList, cookieStore] = await Promise.all([
      headers(),
      cookies()
    ]);

    // Lazy import para evitar problemas com next/headers em componentes client-side
    const { TenantExtractorServer } = await import('@/services/tenant/tenant-extractor-server');

    const extractor = new TenantExtractorServer();
    const tenant = await extractor.getTenantIdentifier(headersList, cookieStore);

    if (!tenant.id) {
      return await createServerClient();
    }

    const supabase = await createServerClient();

    const factory = new TenantClientFactory();
    return factory.createProxy(supabase, tenant);
  } catch (error) {
    console.error('Erro ao criar cliente do servidor com tenant:', error);
    return await createServerClient();
  }
};