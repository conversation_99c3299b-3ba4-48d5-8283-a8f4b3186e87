'use client';

import { createContext, useContext, useState, ReactNode } from 'react';

type AvatarUploadStatus = 'idle' | 'uploading' | 'success' | 'error' | 'deleting';

interface AvatarUploadContextType {
  file: File | null;
  status: AvatarUploadStatus;
  progress: number;
  previewUrl: string | null;
  error: string | null;
  avatarUrl: string | null;
  isDeleting: boolean;
  setFile: (file: File | null) => void;
  setStatus: (status: AvatarUploadStatus) => void;
  setProgress: (progress: number) => void;
  setError: (error: string | null) => void;
  setAvatarUrl: (url: string | null) => void;
  setIsDeleting: (isDeleting: boolean) => void;
  reset: () => void;
}

const AvatarUploadContext = createContext<AvatarUploadContextType | undefined>(undefined);

export const useAvatarUpload = () => {
  const context = useContext(AvatarUploadContext);
  
  if (context === undefined) {
    throw new Error('useAvatarUpload deve ser usado dentro de um AvatarUploadProvider');
  }
  
  return context;
};

interface AvatarUploadProviderProps {
  children: ReactNode;
  initialAvatarUrl?: string | null;
}

export const AvatarUploadProvider = ({ 
  children, 
  initialAvatarUrl = null 
}: AvatarUploadProviderProps) => {
  const [file, setFile] = useState<File | null>(null);
  const [status, setStatus] = useState<AvatarUploadStatus>('idle');
  const [progress, setProgress] = useState(0);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(initialAvatarUrl);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleSetFile = (newFile: File | null) => {
    setFile(newFile);
    
    if (newFile) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target?.result as string);
      };
      reader.readAsDataURL(newFile);
    } else {
      setPreviewUrl(null);
    }
  };

  const reset = () => {
    setFile(null);
    setStatus('idle');
    setProgress(0);
    setPreviewUrl(null);
    setError(null);
    setIsDeleting(false);
  };

  return (
    <AvatarUploadContext.Provider
      value={{
        file,
        status,
        progress,
        previewUrl,
        error,
        avatarUrl,
        isDeleting,
        setFile: handleSetFile,
        setStatus,
        setProgress,
        setError,
        setAvatarUrl,
        setIsDeleting,
        reset
      }}
    >
      {children}
    </AvatarUploadContext.Provider>
  );
}; 