import { redirect } from "next/navigation";
import { cookies } from "next/headers";
import { NextResponse } from 'next/server';

export async function setAuthMessageCookie(
  type: "error" | "success",
  message: string,
) {
  const cookieStore = await cookies();
  
  cookieStore.set(`auth_${type}`, message, {
    maxAge: 60, 
    path: '/',
    httpOnly: true, // Apenas acessível pelo servidor
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  });
}

export async function getAndClearAuthMessageCookie(
  type: "error" | "success",
): Promise<string | null> {
  const cookieStore = await cookies();
  const message = cookieStore.get(`auth_${type}`)?.value;
  
  if (message) {
    cookieStore.delete(`auth_${type}`);
  }
  
  return message || null;
}

export async function cookieRedirect(type: 'error' | 'success', path: string, message: string) {
  const response = NextResponse.redirect(new URL(path, process.env.NEXT_PUBLIC_SITE_URL));
  
  response.cookies.set(`auth_${type}`, message, {
    maxAge: 60, // 1 minuto
    path: '/',
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  });
  
  return response;
}

export function encodedRedirect(
  type: "error" | "success",
  path: string,
  message: string,
) {
  const encodedMessage = message.includes('%') 
    ? message
    : encodeURIComponent(message);
  
  const [basePath, existingQuery] = path.split('?');
  const queryParams = new URLSearchParams(existingQuery || '');
  
  queryParams.set(type, encodedMessage);
  
  const redirectUrl = `${basePath}?${queryParams.toString()}`;
  
  return redirect(redirectUrl);
}
