import { UserRole } from './permission-types';

export type ProfileField = 
  | 'fullName'
  | 'email'
  | 'phone'
  | 'address'
  | 'emergency_contact'
  | 'emergency_phone'
  | 'emergency_contact_relationship'
  | 'emergency_contact_name'
  | 'emergency_contact_phone'
  | 'birthDate'
  | 'notes'
  | 'gender'
  | 'avatar'
  | 'healthNotes'
  | 'allergies'
  | 'medicalConditions'
  | 'medications'
  // Campos específicos de instrutores
  | 'currentBelt'
  | 'specialties'
  | 'cbjj_certified'
  | 'ibjjf_certified'
  | 'first_aid_certified'
  | 'cpr_certified'
  | 'contract_type'
  | 'payment_model'
  | 'hourly_rate'
  | 'monthly_salary'
  | 'commission_percentage'
  | 'branch_id'
  | 'experience_years'
  | 'bio';

export type EditCondition = 'self' | 'others' | 'same_tenant';

export interface IFieldPermissionStrategy {
  canEdit(field: ProfileField, context: FieldPermissionContext): boolean;
}

export interface FieldPermissionContext {
  currentUserId: string;
  currentUserRole: UserRole;
  targetUserId: string;
  currentTenantId?: string;
  targetTenantId?: string;
}

// Interface básica para permissão de campo
export interface FieldPermission {
  field: ProfileField;
  roles: UserRole[];
  editConditions: EditCondition[];
}

// Permissão com condição personalizada baseada no papel
export interface RoleBasedFieldPermission extends Omit<FieldPermission, 'editConditions'> {
  getEditConditions: (role: UserRole) => EditCondition[];
}

// Interface para fábrica de permissões
export interface FieldPermissionFactory {
  createPermission(field: ProfileField, config: FieldPermissionConfig): FieldPermission | RoleBasedFieldPermission;
}

// Configuração para criar permissões
export interface FieldPermissionConfig {
  roles: UserRole[];
  editConditions: EditCondition[] | Record<string, EditCondition[]>;
  adminCanEditOthers?: boolean;
} 