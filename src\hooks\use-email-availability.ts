"use client";

import { useQuery } from "@tanstack/react-query";
import { useDebounce } from "@/hooks/ui/use-debounce";

export interface AvailabilityResponse {
  available: boolean;
  error?: string;
}

export interface EmailAvailabilityOptions {
  /** Endpoint HTTP a ser consultado. Ex.: "/api/instructors/email-availability" */
  endpoint: string;
  /** Quantidade mínima de caracteres para começar a verificar */
  minLength?: number;
  /** Lista de valores que devem ser ignorados (não fazer request) */
  skipValues?: string[];
}

const DEFAULT_MIN_LENGTH = 5;

/**
 * Hook genérico para checar disponibilidade de e-mail.
 *
 * Exemplo de uso:
 * ```ts
 * const { data, isLoading } = useEmailAvailability(email, {
 *   endpoint: '/api/instructors/email-availability'
 * })
 * ```
 */
export function useEmailAvailability(rawEmail: string, options: EmailAvailabilityOptions) {
  const { endpoint, minLength = DEFAULT_MIN_LENGTH, skipValues = [] } = options;

  // debounce para reduzir chamadas enquanto o usuário digita
  const debounced = useDebounce(rawEmail, 500);
  const trimmed = debounced.trim();

  const shouldSkip =
    trimmed.length <= minLength || skipValues.includes(trimmed);

  const fetcher = async (): Promise<AvailabilityResponse> => {
    const res = await fetch(endpoint, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email: trimmed }),
    });

    if (!res.ok) {
      return { available: false, error: "Erro ao verificar e-mail" };
    }

    return res.json();
  };

  return useQuery<AvailabilityResponse>({
    queryKey: ["email-availability", endpoint, trimmed],
    queryFn: fetcher,
    enabled: !shouldSkip,
    staleTime: 1000 * 60, // 1 min
  });
} 