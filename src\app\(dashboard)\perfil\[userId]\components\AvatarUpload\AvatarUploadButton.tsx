'use client';

import { useRef, useState } from 'react';
import { Edit, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAvatarUpload } from './AvatarUploadContext';
import { cn } from '@/lib/utils';
import { AvatarUploadModal } from './AvatarUploadModal';
import { useFieldPermission } from '@/services/permissions';

interface AvatarUploadButtonProps {
  userId: string;
  className?: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg' | 'icon';
  onAvatarUpdated?: (url: string) => void;
}

export const AvatarUploadButton = ({
  userId,
  className,
  variant = 'outline',
  size = 'icon',
  onAvatarUpdated
}: AvatarUploadButtonProps) => {
  const { status, isDeleting } = useAvatarUpload();
  
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { canEditField } = useFieldPermission();
  
  // Verificar se o usuário pode editar o avatar
  const canEditAvatar = canEditField('avatar', userId);

  const handleClick = () => {
    setIsModalOpen(true);
  };

  // Se o usuário não tem permissão para editar o avatar, não renderizar o botão
  if (!canEditAvatar) {
    return null;
  }

  const isLoading = status === 'uploading' || isDeleting;

  return (
    <>
      <Button 
        size="icon" 
        variant="outline" 
        className={cn(
          "absolute bottom-0 right-0 rounded-full bg-white dark:bg-slate-700 shadow-lg hover:bg-slate-100 dark:hover:bg-slate-600 border border-slate-200 dark:border-slate-600",
          className
        )}
        onClick={handleClick}
        aria-label="Alterar foto"
        disabled={isLoading}
      >
        {isLoading ? (
          <Loader2 className="w-4 h-4 animate-spin" />
        ) : (
          <Edit className="w-4 h-4" />
        )}
      </Button>

      <AvatarUploadModal 
        userId={userId}
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        onAvatarUpdated={onAvatarUpdated}
      />
    </>
  );
}; 