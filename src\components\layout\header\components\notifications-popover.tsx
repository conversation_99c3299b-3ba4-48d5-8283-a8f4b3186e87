'use client';

import { Fragment } from "react";
import { Popover, PopoverButton, PopoverPanel, Transition } from "@headlessui/react";
import { BellIcon } from "@heroicons/react/24/outline";
import { CheckCircleIcon, XCircleIcon } from "@heroicons/react/20/solid";
import Link from "next/link";
import { useTenantTheme } from "@/hooks/tenant/use-tenant-theme";

// Dados mockados para exemplo
const notifications = [
  {
    id: 1,
    type: "success",
    title: "Mensalidade Recebida",
    description: "<PERSON> - R$ 200,00",
    time: "Agora mesmo",
  },
  {
    id: 2,
    type: "error",
    title: "Pagamento Atrasado",
    description: "<PERSON> Oliveira - 5 dias",
    time: "1h atrás",
  },
  {
    id: 3,
    type: "success",
    title: "Nova Matrícula",
    description: "Pedro Santos",
    time: "2h atrás",
  },
];

export function NotificationsPopover() {
  const { primaryColor } = useTenantTheme();
  
  return (
    <Popover className="dropdown-overlay relative outline-none">
      <PopoverButton className="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500 focus:outline-none dark:text-gray-500 dark:hover:text-gray-400 outline-none">
        <span className="sr-only">Ver notificações</span>
        <div className="relative">
          <BellIcon className="h-6 w-6" aria-hidden="true" />
          <span 
            className="absolute -top-1.5 -right-1.5 flex h-4 w-4 items-center justify-center rounded-full text-[10px] font-medium text-white"
            style={{ backgroundColor: primaryColor || '#ef4444' }}
          >
            {notifications.length}
          </span>
        </div>
      </PopoverButton>
      <Transition
        as={Fragment}
        enter="transition ease-out duration-200"
        enterFrom="opacity-0 translate-y-1"
        enterTo="opacity-100 translate-y-0"
        leave="transition ease-in duration-150"
        leaveFrom="opacity-100 translate-y-0"
        leaveTo="opacity-0 translate-y-1"
      >
        <PopoverPanel className="dropdown-overlay fixed sm:absolute right-0 sm:right-0 top-16 sm:top-auto sm:mt-2 w-screen sm:w-80 transform sm:max-w-sm">
                      <div className="overflow-hidden rounded-none sm:rounded-lg shadow-xl ring-1 ring-border/20 bg-white/95 backdrop-blur-sm dark:bg-gray-800/95 mx-0 sm:mx-0">
            <div className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">Notificações</h3>
                <button 
                  className="text-xs hover:opacity-80 transition-opacity"
                  style={{ color: primaryColor || '#2563eb' }}
                >
                  Marcar todas como lidas
                </button>
              </div>
              <div className="mt-3 sm:mt-4 space-y-2 sm:space-y-3">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className="flex gap-2 sm:gap-3 rounded-md p-2 hover:bg-muted/60 dark:hover:bg-gray-700 transition-colors duration-150"
                  >
                    <div className="flex-shrink-0">
                      {notification.type === "success" ? (
                        <CheckCircleIcon className="h-5 w-5 sm:h-6 sm:w-6 text-green-500" />
                      ) : (
                        <XCircleIcon className="h-5 w-5 sm:h-6 sm:w-6 text-red-500" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {notification.title}
                      </p>
                      <p className="mt-0.5 text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                        {notification.description}
                      </p>
                      <p className="mt-0.5 text-xs text-gray-400 dark:text-gray-500">
                        {notification.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="bg-muted/30 p-2 sm:p-3 dark:bg-gray-700">
              <Link 
                href="/notificacoes" 
                className="block w-full rounded-md p-2 text-center text-sm font-medium hover:opacity-90 transition-opacity"
                style={{ color: primaryColor || '#2563eb' }}
              >
                Ver todas as notificações
              </Link>
            </div>
          </div>
        </PopoverPanel>
      </Transition>
    </Popover>
  );
} 