'use server'

import { redirect } from 'next/navigation';
import { getPermissionService } from '@/services/permissions/service';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';

/**
 * Middleware específico para proteger acesso às páginas de perfil de usuário
 * Permite que um usuário acesse seu próprio perfil, independentemente da role
 * Administradores podem acessar perfis de qualquer usuário
 */
export async function protectProfileRoute(profileUserId: string) {
  // Verificar autenticação
  const user = await getCurrentUser();
  
  if (!user) {
    return redirect('/login');
  }
  
  // Obter serviço de permissões e usar função específica para perfis
  const permissionService = getPermissionService();
  
  return permissionService.protectProfileRoute(
    user.id,
    profileUserId,
    '/home?erro=acesso-negado&motivo=perfil-nao-autorizado'
  );
} 