import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { MetricCardProps } from '../types';
import { Users, Clock } from 'lucide-react';
import { MdPaid } from "react-icons/md";
import { TbCalendarDue } from "react-icons/tb";

const colorConfig = {
  green: {
    text: 'text-emerald-600 dark:text-emerald-400',
    bg: 'bg-emerald-100 dark:bg-emerald-900/30',
    header: 'from-emerald-50/50 to-transparent dark:from-emerald-900/20 dark:to-transparent',
    icon: 'text-emerald-600 dark:text-emerald-400'
  },
  blue: {
    text: 'text-blue-600 dark:text-blue-400',
    bg: 'bg-blue-100 dark:bg-blue-900/30',
    header: 'from-blue-50/50 to-transparent dark:from-blue-900/20 dark:to-transparent',
    icon: 'text-blue-600 dark:text-blue-400'
  },
  orange: {
    text: 'text-orange-600 dark:text-orange-400',
    bg: 'bg-orange-100 dark:bg-orange-900/30',
    header: 'from-orange-50/50 to-transparent dark:from-orange-900/20 dark:to-transparent',
    icon: 'text-orange-600 dark:text-orange-400'
  },
  red: {
    text: 'text-red-600 dark:text-red-400',
    bg: 'bg-red-100 dark:bg-red-900/30',
    header: 'from-red-50/50 to-transparent dark:from-red-900/20 dark:to-transparent',
    icon: 'text-red-600 dark:text-red-400'
  },
};

const iconMap = {
  green: MdPaid,
  blue: Users,
  orange: Clock,
  red: TbCalendarDue,
};

export function MetricCard({ metric }: MetricCardProps) {
  const config = colorConfig[metric.color];
  const IconComponent = iconMap[metric.color];

  return (
    <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden group hover:shadow-xl transition-all duration-300">
      <CardHeader className={`pb-4 bg-gradient-to-r ${config.header}`}>
        <CardTitle className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
          <div className={`p-1 ${config.bg} rounded-full`}>
            <IconComponent className={`w-4 h-4 ${config.icon}`} />
          </div>
          {metric.label}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-2">
        <div className="text-center space-y-1">
          <p className={`text-2xl font-bold ${config.text} tracking-tight`}>
            {metric.value}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
