import { useState, useCallback, useEffect } from 'react';

const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

export const useAutoSave = (formData: any, isValid: boolean, enabled: boolean = true) => {
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isAutoSaving, setIsAutoSaving] = useState(false);

  // Função debounced para auto-save
  const debouncedAutoSave = useCallback(
    debounce((data: any) => {
      if (!enabled || !data.name || !isValid) return;
      
      setIsAutoSaving(true);
      try {
        localStorage.setItem('draft-class-form', JSON.stringify(data));
        setTimeout(() => {
          setIsAutoSaving(false);
          setLastSaved(new Date());
        }, 300);
      } catch (error) {
        console.error('Erro ao salvar rascunho:', error);
        setIsAutoSaving(false);
      }
    }, 5000), // Aguarda 5 segundos de inatividade
    [isValid, enabled]
  );

  useEffect(() => {
    if (!enabled || !isValid || !formData.name) return;
    debouncedAutoSave(formData);
  }, [formData, isValid, debouncedAutoSave, enabled]);

  // Recuperar draft na inicialização
  const getDraft = () => {
    try {
      const draft = localStorage.getItem('draft-class-form');
      return draft ? JSON.parse(draft) : null;
    } catch {
      localStorage.removeItem('draft-class-form');
      return null;
    }
  };

  const clearDraft = () => {
    localStorage.removeItem('draft-class-form');
    setLastSaved(null);
  };

  return { lastSaved, isAutoSaving, clearDraft, getDraft };
}; 