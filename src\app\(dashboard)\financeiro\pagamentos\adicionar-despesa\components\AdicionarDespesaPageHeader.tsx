'use client';

import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

export function AdicionarDespesaPageHeader() {
  return (
    <div className="max-w-4xl mx-auto mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/financeiro/pagamentos?tab=expense">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Adicionar Despesa</h1>
            <p className="text-muted-foreground">
              Registre uma nova despesa da academia.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
