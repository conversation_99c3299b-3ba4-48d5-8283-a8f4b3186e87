import { z } from 'zod';

// Schema para padrão de recorrência
export const RecurrencePatternSchema = z.object({
  frequency: z.enum(['daily', 'weekly', 'monthly']),
  interval: z.number().min(1).max(52), // Até 52 semanas
  daysOfWeek: z.array(z.number().min(0).max(6)).optional(), // 0-6 (domingo a <PERSON>)
  endDate: z.string().datetime().optional(),
  maxOccurrences: z.number().positive().optional(),
}).optional();

// Schema para paginação
export const PaginationSchema = z.object({
  page: z.union([z.number(), z.string().transform(val => parseInt(val) || 1)]).pipe(z.number().min(1)).default(1),
  limit: z.union([z.number(), z.string().transform(val => parseInt(val) || 10)]).pipe(z.number().min(1).max(100)).default(10),
});

// Schema para ordenação
export const SortOrderSchema = z.enum(['asc', 'desc']).default('asc');

// Schema para filtros de data
export const DateRangeFilterSchema = z.object({
  date_from: z.string().datetime().optional(),
  date_to: z.string().datetime().optional(),
});

// Schema base para busca
export const SearchFilterSchema = z.object({
  search: z.string().optional(),
});

// Validações comuns reutilizáveis
export const validateDateRange = (startDate?: string, endDate?: string) => {
  if (startDate && endDate) {
    return new Date(endDate) > new Date(startDate);
  }
  return true;
};

export const validateAgeRange = (minAge?: number | null, maxAge?: number | null) => {
  // Se maxAge for null (sem limite máximo), sempre válido
  if (maxAge === null || maxAge === undefined) {
    return true;
  }
  
  // Se minAge for null/undefined mas maxAge for definido, sempre válido
  if ((minAge === null || minAge === undefined) && maxAge !== null && maxAge !== undefined) {
    return true;
  }
  
  // Se ambos definidos, maxAge deve ser >= minAge
  if (minAge !== null && minAge !== undefined && maxAge !== null && maxAge !== undefined) {
    return maxAge >= minAge;
  }
  
  return true;
};

// Tipos inferidos
export type RecurrencePattern = z.infer<typeof RecurrencePatternSchema>;
export type Pagination = z.infer<typeof PaginationSchema>;
export type SortOrder = z.infer<typeof SortOrderSchema>;
export type DateRangeFilter = z.infer<typeof DateRangeFilterSchema>;
export type SearchFilter = z.infer<typeof SearchFilterSchema>; 