'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { CACHE_KEYS } from '@/constants/cache-keys';
import { cancelClass, completeClass } from '@/app/(dashboard)/aulas/actions/cancel-class-actions';

interface UseClassActionsProps {
  groupId: string;
  onRefresh?: () => void;
}

interface CancelDialogState {
  isOpen: boolean;
  classId: string | null;
  className: string | null;
  classDate: string | null;
  classTime: string | null;
}

export function useClassActions({ groupId, onRefresh }: UseClassActionsProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  
  // Estado para controlar o dialog de cancelamento
  const [cancelDialog, setCancelDialog] = useState<CancelDialogState>({
    isOpen: false,
    classId: null,
    className: null,
    classDate: null,
    classTime: null,
  });

  // Mutation para cancelar aula usando Server Action
  const cancelClassMutation = useMutation({
    mutationFn: async ({ classId, reason }: { classId: string; reason?: string }) => {
      const result = await cancelClass(classId, reason);
      
      if (!result.success) {
        throw new Error(result.errors?._form || 'Erro ao cancelar aula');
      }

      return result.data;
    },
    onSuccess: () => {
      // Invalidar caches relacionados
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.CLASS_GROUPS.CLASSES(groupId, {}),
      });
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.CLASS_GROUPS.CLASS_STATS(groupId),
      });
      onRefresh?.();
    },
    onError: (error) => {
      console.error('Erro ao cancelar aula:', error);
      toast.error(
        error instanceof Error 
          ? error.message 
          : 'Erro inesperado ao cancelar aula'
      );
    },
  });

  // Mutation para completar aula usando Server Action
  const completeClassMutation = useMutation({
    mutationFn: async ({ classId, notes }: { classId: string; notes?: string }) => {
      const result = await completeClass(classId, notes);
      
      if (!result.success) {
        throw new Error(result.errors?._form || 'Erro ao completar aula');
      }

      return result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.CLASS_GROUPS.CLASSES(groupId, {}),
      });
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.CLASS_GROUPS.CLASS_STATS(groupId),
      });
      onRefresh?.();
    },
    onError: (error) => {
      console.error('Erro ao concluir aula:', error);
      toast.error(
        error instanceof Error 
          ? error.message 
          : 'Erro inesperado ao concluir aula'
      );
    },
  });

  // Mutation para reagendar aula - temporariamente desabilitada até criar Server Action
  const rescheduleClassMutation = useMutation({
    mutationFn: async ({ 
      classId, 
      newDate, 
      newStartTime, 
      newEndTime 
    }: { 
      classId: string; 
      newDate: Date; 
      newStartTime: string; 
      newEndTime: string; 
    }) => {
      // TODO: Implementar Server Action para reagendar aula
      throw new Error('Funcionalidade não implementada ainda');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.CLASS_GROUPS.CLASSES(groupId, {}),
      });
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.CLASS_GROUPS.CLASS_STATS(groupId),
      });
      toast.success('Aula reagendada com sucesso!');
      onRefresh?.();
    },
    onError: (error) => {
      console.error('Erro ao reagendar aula:', error);
      toast.error(
        error instanceof Error 
          ? error.message 
          : 'Erro inesperado ao reagendar aula'
      );
    },
  });

  // Mutation para excluir aula - temporariamente desabilitada até criar Server Action
  const deleteClassMutation = useMutation({
    mutationFn: async ({ classId }: { classId: string }) => {
      // TODO: Implementar Server Action para excluir aula
      throw new Error('Funcionalidade não implementada ainda');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.CLASS_GROUPS.CLASSES(groupId, {}),
      });
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.CLASS_GROUPS.CLASS_STATS(groupId),
      });
      onRefresh?.();
    },
    onError: (error) => {
      console.error('Erro ao excluir aula:', error);
      toast.error(
        error instanceof Error 
          ? error.message 
          : 'Erro inesperado ao excluir aula'
      );
    },
  });

  // Navegar para página de edição da aula
  const handleEditClass = (classId: string) => {
    router.push(`/turmas/${groupId}/aulas/editar/${classId}`);
  };

  // Navegar para página de presença
  const handleViewAttendance = (classId: string) => {
    router.push(`/turmas/${groupId}/presenca?aulaId=${classId}`);
  };

  // Abrir dialog de cancelamento de aula
  const handleCancelClass = (classId: string, className: string, classDate?: string, classTime?: string) => {
    setCancelDialog({
      isOpen: true,
      classId,
      className,
      classDate: classDate || null,
      classTime: classTime || null,
    });
  };

  // Fechar dialog de cancelamento
  const closeCancelDialog = () => {
    setCancelDialog({
      isOpen: false,
      classId: null,
      className: null,
      classDate: null,
      classTime: null,
    });
  };

  // Confirmar cancelamento da aula
  const confirmCancelClass = async (reason?: string) => {
    if (!cancelDialog.classId || !cancelDialog.className) return;

    try {
      await cancelClassMutation.mutateAsync({ 
        classId: cancelDialog.classId, 
        reason 
      });
      toast.success(`Aula "${cancelDialog.className}" cancelada com sucesso!`);
      closeCancelDialog();
    } catch (error) {
      // Error handling já é feito na mutation
      console.error('Erro no confirmCancelClass:', error);
    }
  };

  // Completar aula com confirmação
  const handleCompleteClass = async (classId: string, className: string) => {
    if (!confirm(`Tem certeza que deseja marcar a aula "${className}" como concluída?`)) {
      return;
    }

    try {
      await completeClassMutation.mutateAsync({ classId });
      toast.success(`Aula "${className}" marcada como concluída!`);
    } catch (error) {
      console.error('Erro no handleCompleteClass:', error);
    }
  };

  // Reagendar aula
  const handleRescheduleClass = async (
    classId: string, 
    newDate: Date, 
    newStartTime: string, 
    newEndTime: string
  ) => {
    try {
      await rescheduleClassMutation.mutateAsync({
        classId,
        newDate,
        newStartTime,
        newEndTime,
      });
    } catch (error) {
      console.error('Erro no handleRescheduleClass:', error);
    }
  };

  // Excluir aula com confirmação
  const handleDeleteClass = async (classId: string, className: string) => {
    if (!confirm(`Tem certeza que deseja excluir permanentemente a aula "${className}"? Esta ação não pode ser desfeita.`)) {
      return;
    }

    try {
      await deleteClassMutation.mutateAsync({ classId });
      toast.success(`Aula "${className}" excluída com sucesso!`);
    } catch (error) {
      console.error('Erro no handleDeleteClass:', error);
    }
  };

  // Duplicar aula - navegar para nova aula com dados preenchidos
  const handleDuplicateClass = (classId: string) => {
    router.push(`/turmas/${groupId}/aulas/nova?duplicar=${classId}`);
  };

  // Verificar se alguma ação está carregando para uma aula específica
  const isActionLoading = (classId: string) => {
    return (
      (cancelClassMutation.isPending && cancelDialog.classId === classId) ||
      (completeClassMutation.isPending) ||
      (rescheduleClassMutation.isPending) ||
      (deleteClassMutation.isPending)
    );
  };

  return {
    // Handlers
    handleEditClass,
    handleViewAttendance,
    handleCancelClass,
    handleCompleteClass,
    handleRescheduleClass,
    handleDeleteClass,
    handleDuplicateClass,
    
    // Dialog de cancelamento
    cancelDialog,
    closeCancelDialog,
    confirmCancelClass,
    
    // Estado de loading
    isActionLoading,
    isCancelling: cancelClassMutation.isPending,
    isCompleting: completeClassMutation.isPending,
    isRescheduling: rescheduleClassMutation.isPending,
    isDeleting: deleteClassMutation.isPending,
  };
} 