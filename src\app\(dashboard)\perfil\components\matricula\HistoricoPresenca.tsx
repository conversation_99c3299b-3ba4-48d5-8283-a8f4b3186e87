import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { List, CheckCircle, Clock, Activity, Eye } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useState } from 'react';
import { FullAttendanceHistoryModal } from './FullAttendanceHistoryModal';

const formatAttendanceTime = (dateTime: string) => {
  try {
    const date = new Date(dateTime);
    if (isNaN(date.getTime())) {
      return '--:--';
    }
    return format(date, "HH:mm");
  } catch (error) {
    console.error('Erro ao formatar horário de presença:', error);
    return '--:--';
  }
};

const formatAttendanceDate = (dateTime: string) => {
  try {
    const date = new Date(dateTime);
    if (isNaN(date.getTime())) {
      return 'Data inválida';
    }
    return format(date, "dd/MM/yyyy");
  } catch (error) {
    console.error('Erro ao formatar data de presença:', error);
    return 'Data inválida';
  }
};

interface HistoricoPresencaProps {
  attendance: any[];
  studentId: string;
}

export function HistoricoPresenca({ attendance, studentId }: HistoricoPresencaProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const totalAttendance = attendance.length;

  return (
    <>
      <Card className="p-6 bg-white dark:bg-slate-800">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <List className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Histórico de Presença
            </h3>
            {totalAttendance > 0 && (
              <span className="text-sm text-slate-500 dark:text-slate-400">
                (Últimas {totalAttendance})
              </span>
            )}
          </div>
          
          {totalAttendance > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsModalOpen(true)}
              className="flex items-center gap-2"
            >
              <Eye className="h-4 w-4" />
              Ver Todos
            </Button>
          )}
        </div>

        {totalAttendance === 0 ? (
          <div className="text-center py-8">
            <Activity className="h-12 w-12 mx-auto text-slate-400 dark:text-slate-500 mb-4" />
            <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Nenhuma Presença Registrada
            </h4>
            <p className="text-slate-500 dark:text-slate-400">
              Ainda não há registros de presença para este aluno.
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {attendance.map((item, index) => (
              <div 
                key={index} 
                className="flex items-center justify-between p-4 rounded-lg bg-slate-50 dark:bg-slate-700/50 hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0">
                    <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900 dark:text-gray-100">
                      {item.classes.name}
                    </p>
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      {formatAttendanceDate(item.checked_in_at)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2 text-slate-600 dark:text-slate-400">
                  <Clock className="h-4 w-4" />
                  <span className="text-sm font-medium">
                    {formatAttendanceTime(item.checked_in_at)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>

      <FullAttendanceHistoryModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        studentId={studentId}
      />
    </>
  );
} 