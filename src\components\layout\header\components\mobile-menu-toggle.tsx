"use client";

import { Bars3Icon } from "@heroicons/react/24/outline";

interface MobileMenuToggleProps {
  setSidebarOpen: (open: boolean) => void;
}

export const MobileMenuToggle = ({ setSidebarOpen }: MobileMenuToggleProps) => {
  return (
    <button
      type="button"
      className="lg:hidden -m-2 p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 transition-colors"
      onClick={() => setSidebarOpen(true)}
    >
      <span className="sr-only">Abrir menu</span>
      <Bars3Icon className="h-6 w-6" aria-hidden="true" />
    </button>
  );
}; 