'use client';

import { useRouter } from 'next/navigation';
import { useCallback, useState, useEffect } from 'react';
import { StudentList, StudentEmptyState, StudentEnrollmentModal } from '@/components/students';
import type { Student } from '@/components/students/types';
import { useQueryClient } from '@tanstack/react-query';
import { CACHE_KEYS } from '@/constants/cache-keys';
import { useEnrollmentActions } from '../hooks/useEnrollmentActions';
import { useBulkEnrollmentActions } from '../hooks/useBulkEnrollmentActions';
import { 
  EnrollmentActionModal, 
  type EnrollmentActionType 
} from './EnrollmentActionModal';
import { BulkActionsToolbar } from './BulkActionsToolbar';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import React from 'react';

interface ClassGroupStudentsClientProps {
  students: Student[];
  classGroupId: string;
  classGroupName: string;
  classGroupMaxCapacity?: number;
  hasFilters: boolean;
  hasStudents: boolean;
}

export function ClassGroupStudentsClient({
  students,
  classGroupId,
  classGroupName,
  classGroupMaxCapacity,
  hasFilters,
  hasStudents
}: ClassGroupStudentsClientProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isEnrollmentModalOpen, setIsEnrollmentModalOpen] = useState(false);
  const [selectedEnrollments, setSelectedEnrollments] = useState<string[]>([]);
  const [currentStudents, setCurrentStudents] = useState<Student[]>(students);
  const [lastSelectedId, setLastSelectedId] = useState<string | null>(null);
  
  // Sincronizar o estado dos alunos com as props recebidas
  useEffect(() => {
    setCurrentStudents(students);
  }, [students]);
  
  // Estados para o modal de ações de matrícula (single)
  const [actionModal, setActionModal] = useState<{
    isOpen: boolean;
    type: EnrollmentActionType;
    enrollmentId: string;
    studentName: string;
  }>({
    isOpen: false,
    type: 'cancel',
    enrollmentId: '',
    studentName: ''
  });

  // Estado para o modal de confirmação de ação em massa
  const [bulkActionDialog, setBulkActionDialog] = useState<{
    isOpen: boolean;
    title: string;
    description: string;
    action?: () => void;
  }>({ isOpen: false, title: '', description: '' });

  // Hook para gerenciar ações de matrícula (single)
  const {
    actionState,
    handleUnenrollStudent,
    handlePauseEnrollment,
    handleReactivateEnrollment
  } = useEnrollmentActions(classGroupId);

  // Hook para gerenciar ações em massa
  const {
    isLoading: isBulkLoading,
    pauseSelected,
    resumeSelected,
    unenrollSelected,
  } = useBulkEnrollmentActions(classGroupId, currentStudents);

  const handleClearFilters = useCallback(() => {
    router.push(window.location.pathname);
  }, [router]);

  const handleOpenEnrollmentModal = useCallback(() => {
    setIsEnrollmentModalOpen(true);
  }, []);

  const handleEnrollmentSuccess = useCallback(() => {
    // Invalidar cache para atualizar a lista de alunos
    queryClient.invalidateQueries({
      queryKey: CACHE_KEYS.ENROLLMENT.CLASS_GROUP_ENROLLMENTS(classGroupId)
    });
    
    // Forçar recarregamento da página para atualizar as estatísticas e lista
    router.refresh();
  }, [queryClient, classGroupId, router]);

  // Funções para abrir modais de ação
  const handleOpenUnenrollModal = useCallback((enrollmentId: string, studentName: string) => {
    setActionModal({
      isOpen: true,
      type: 'cancel',
      enrollmentId,
      studentName
    });
  }, []);

  const handleOpenPauseModal = useCallback((enrollmentId: string, studentName: string) => {
    setActionModal({
      isOpen: true,
      type: 'pause',
      enrollmentId,
      studentName
    });
  }, []);

  const handleOpenReactivateModal = useCallback((enrollmentId: string, studentName: string) => {
    setActionModal({
      isOpen: true,
      type: 'reactivate',
      enrollmentId,
      studentName
    });
  }, []);

  const handleCloseActionModal = useCallback(() => {
    setActionModal(prev => ({ ...prev, isOpen: false }));
  }, []);

  // Função para confirmar ação baseada no tipo
  const handleConfirmAction = useCallback(async (reason?: string) => {
    const { type, enrollmentId, studentName } = actionModal;
    
    switch (type) {
      case 'cancel':
        return await handleUnenrollStudent(enrollmentId, studentName, reason);
      case 'pause':
        return await handlePauseEnrollment(enrollmentId, studentName, reason);
      case 'reactivate':
        return await handleReactivateEnrollment(enrollmentId, studentName, reason);
      default:
        return { success: false, error: 'Ação inválida' };
    }
  }, [actionModal, handleUnenrollStudent, handlePauseEnrollment, handleReactivateEnrollment]);

  const handleSelectionChange = useCallback((ids: string[]) => {
    setSelectedEnrollments(ids);
  }, []);

  const handleRowSelection = useCallback((id: string, event: React.MouseEvent) => {
    const isShiftPressed = event.nativeEvent.shiftKey;
    
    if (isShiftPressed && lastSelectedId) {
      const lastIndex = currentStudents.findIndex(s => s.id === lastSelectedId);
      const currentIndex = currentStudents.findIndex(s => s.id === id);
      
      if (lastIndex !== -1 && currentIndex !== -1) {
        const start = Math.min(lastIndex, currentIndex);
        const end = Math.max(lastIndex, currentIndex);
        const rangeIds = currentStudents.slice(start, end + 1).map(s => s.id);
        
        // Adiciona o intervalo à seleção, evitando duplicados
        setSelectedEnrollments(prev => Array.from(new Set([...prev, ...rangeIds])));
        return;
      }
    }

    // Comportamento normal de clique (toggle)
    setSelectedEnrollments(prev => {
      const newSelection = new Set(prev);
      if (newSelection.has(id)) {
        newSelection.delete(id);
      } else {
        newSelection.add(id);
      }
      return Array.from(newSelection);
    });

    setLastSelectedId(id);
  }, [lastSelectedId, currentStudents]);

  const handleConfirmBulkAction = () => {
    if (bulkActionDialog.action) {
      bulkActionDialog.action();
    }
    setBulkActionDialog({ isOpen: false, title: '', description: '' });
  };

  // Determinar se as ações de pausa/reativação são válidas de acordo com os selecionados
  const canPause = selectedEnrollments.length > 0 && selectedEnrollments.every(id => {
    const s = currentStudents.find(st => st.id === id);
    return s && !s.is_paused;
  });

  const canResume = selectedEnrollments.length > 0 && selectedEnrollments.every(id => {
    const s = currentStudents.find(st => st.id === id);
    return s && s.is_paused;
  });

  const openBulkActionDialog = (
    type: 'pause' | 'resume' | 'unenroll'
  ) => {
    const count = selectedEnrollments.length;
    const studentText = count === 1 ? 'aluno' : 'alunos';
    const config = {
      pause: {
        title: `Pausar ${count} ${studentText}?`,
        description: `Você tem certeza que deseja pausar as matrículas dos ${count} alunos selecionados?`,
        action: () => pauseSelected(selectedEnrollments).then(() => {
          setCurrentStudents(prev => prev.map(s => selectedEnrollments.includes(s.id) ? { ...s, is_paused: true } : s));
          setSelectedEnrollments([]);
        }),
      },
      resume: {
        title: `Reativar ${count} ${studentText}?`,
        description: `Você tem certeza que deseja reativar as matrículas dos ${count} alunos selecionados?`,
        action: () => resumeSelected(selectedEnrollments).then(() => {
          setCurrentStudents(prev => prev.map(s => selectedEnrollments.includes(s.id) ? { ...s, is_paused: false } : s));
          setSelectedEnrollments([]);
        }),
      },
      unenroll: {
        title: `Cancelar matrícula de ${count} ${studentText}?`,
        description: `Esta ação não pode ser desfeita. Você tem certeza que deseja cancelar as matrículas dos ${count} alunos selecionados?`,
        action: () => unenrollSelected(selectedEnrollments).then((result: any) => {
          const successIds: string[] = result?.data?.results
            ? result.data.results.filter((r: any) => r.success).map((r: any) => r.enrollmentId)
            : [];

          if (successIds.length > 0) {
            setCurrentStudents(prev => prev.filter(s => !successIds.includes(s.id)));
          }

          setSelectedEnrollments([]);
        }),
      },
    };
    setBulkActionDialog({ isOpen: true, ...config[type] });
  };

  return (
    <>
      <section 
        className="bg-card border rounded-lg"
        aria-label="Lista de alunos"
        role="region"
      >
        {!hasStudents && !hasFilters ? (
          // No students at all - show empty state
          <StudentEmptyState 
            type="no-students"
            classGroupId={classGroupId}
            onEnrollment={handleOpenEnrollmentModal}
          />
        ) : !hasStudents && hasFilters ? (
          // No results for current filters
          <StudentEmptyState 
            type="no-results"
            classGroupId={classGroupId}
            onClearFilters={handleClearFilters}
            onEnrollment={handleOpenEnrollmentModal}
          />
        ) : (
          // We have students to show
          <div className="p-4 sm:p-6">
            <StudentList 
              students={currentStudents}
              classGroupId={classGroupId}
              onClearFilters={handleClearFilters}
              onUnenrollStudent={handleOpenUnenrollModal}
              onPauseEnrollment={handleOpenPauseModal}
              onReactivateEnrollment={handleOpenReactivateModal}
              selectedEnrollments={selectedEnrollments}
              onSelectionChange={handleSelectionChange}
              onRowSelection={handleRowSelection}
            />
          </div>
        )}
      </section>

      {/* Modal de matrícula */}
      <StudentEnrollmentModal
        isOpen={isEnrollmentModalOpen}
        onClose={() => setIsEnrollmentModalOpen(false)}
        classGroup={{
          id: classGroupId,
          name: classGroupName,
          max_capacity: classGroupMaxCapacity
        }}
        onEnrollmentSuccess={handleEnrollmentSuccess}
      />

      {/* Modal de ações de matrícula */}
      <EnrollmentActionModal
        isOpen={actionModal.isOpen}
        onClose={handleCloseActionModal}
        onConfirm={handleConfirmAction}
        actionType={actionModal.type}
        studentName={actionModal.studentName}
        isLoading={actionState.isLoading}
      />

      <BulkActionsToolbar
        selectedCount={selectedEnrollments.length}
        onClearSelection={() => setSelectedEnrollments([])}
        onPause={() => openBulkActionDialog('pause')}
        onResume={() => openBulkActionDialog('resume')}
        onUnenroll={() => openBulkActionDialog('unenroll')}
        isLoading={isBulkLoading}
        pauseDisabled={!canPause}
        resumeDisabled={!canResume}
      />

      <AlertDialog open={bulkActionDialog.isOpen} onOpenChange={(isOpen) => !isOpen && setBulkActionDialog(prev => ({ ...prev, isOpen }))}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{bulkActionDialog.title}</AlertDialogTitle>
            <AlertDialogDescription>
              {bulkActionDialog.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setBulkActionDialog(prev => ({...prev, isOpen: false}))}>
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmBulkAction}>
              Confirmar
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 