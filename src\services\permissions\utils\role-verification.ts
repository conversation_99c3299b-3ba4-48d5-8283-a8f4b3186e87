import { createTenantServerClient } from '@/services/supabase/server';
import { redirect } from 'next/navigation';

export interface UserPermissionContext {
  user: any;
  userRole: string;
}

/**
 * Busca os IDs das turmas que um instrutor é responsável
 */
export async function getInstructorClassGroups(instructorId: string): Promise<string[]> {
  const supabase = await createTenantServerClient();
  
  try {
    const { data: classGroups, error } = await supabase
      .from('class_groups')
      .select('id')
      .eq('instructor_id', instructorId)
      .eq('is_active', true);

    if (error) {
      console.error('Erro ao buscar turmas do instrutor:', error);
      return [];
    }

    return classGroups?.map(cg => cg.id) || [];
  } catch (error) {
    console.error('Erro ao buscar turmas do instrutor:', error);
    return [];
  }
}

/**
 * Verifica se o usuário tem permissão de admin
 */
export async function checkAdminPermission(): Promise<UserPermissionContext> {
  const supabase = await createTenantServerClient();
  
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      redirect('/login');
    }

    // Verificar role do usuário - primeiro do JWT, depois do banco
    let userRole = user.app_metadata?.role;
    
    if (!userRole) {
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('user_id', user.id)
        .single();
      
      if (userError || !userData) {
        console.error('Erro ao buscar role do usuário:', userError);
        redirect('/home?erro=acesso-negado');
      }
      
      userRole = userData.role;
    }

    // Apenas admins podem acessar
    if (userRole !== 'admin') {
      redirect('/home?erro=acesso-negado');
    }

    return { user, userRole };
  } catch (error) {
    console.error('Erro ao verificar permissões de admin:', error);
    redirect('/home?erro=acesso-negado');
  }
}

/**
 * Verifica se o usuário tem permissão para acessar funcionalidades de presença
 * teaching_only: Instrutores podem ver apenas suas aulas/turmas
 */
export async function checkAttendancePermission(): Promise<UserPermissionContext> {
  const supabase = await createTenantServerClient();
  
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      redirect('/login');
    }

    // Verificar role do usuário - primeiro do JWT, depois do banco
    let userRole = user.app_metadata?.role;
    
    if (!userRole) {
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('user_id', user.id)
        .single();
      
      if (userError || !userData) {
        console.error('Erro ao buscar role do usuário:', userError);
        redirect('/home?erro=acesso-negado');
      }
      
      userRole = userData.role;
    }

    // Verificar se tem acesso a presença: admin (full_access) ou instructor (teaching_only)
    if (!['admin', 'instructor'].includes(userRole)) {
      redirect('/home?erro=acesso-negado');
    }

    return { user, userRole };
  } catch (error) {
    console.error('Erro ao verificar permissões de presença:', error);
    redirect('/home?erro=acesso-negado');
  }
}

/**
 * Verifica se o usuário tem permissão para acessar turmas
 * admin: full_access, instructor: own_only
 */
export async function checkClassGroupPermission(): Promise<UserPermissionContext> {
  const supabase = await createTenantServerClient();
  
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      redirect('/login');
    }

    // Verificar role do usuário - primeiro do JWT, depois do banco
    let userRole = user.app_metadata?.role;
    
    if (!userRole) {
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('role')
        .eq('user_id', user.id)
        .single();
      
      if (userError || !userData) {
        console.error('Erro ao buscar role do usuário:', userError);
        redirect('/home?erro=acesso-negado');
      }
      
      userRole = userData.role;
    }

    // Verificar se tem acesso a turmas: admin (full_access) ou instructor (own_only)
    if (!['admin', 'instructor'].includes(userRole)) {
      redirect('/home?erro=acesso-negado');
    }

    return { user, userRole };
  } catch (error) {
    console.error('Erro ao verificar permissões de turmas:', error);
    redirect('/home?erro=acesso-negado');
  }
}

/**
 * Aplica filtros automáticos baseados na role do usuário
 * NOTA: Esta função agora retorna dados especiais para instrutores que precisam ser processados pela aplicação
 */
export async function applyRoleBasedFilters(
  userContext: UserPermissionContext,
  filters: any,
  resourceType: 'attendance' | 'classes' | 'classGroups'
): Promise<any> {
  const { user, userRole } = userContext;

  // Admins têm acesso completo, não precisam de filtros adicionais
  if (userRole === 'admin') {
    return filters;
  }

  // Para instrutores, aplicar filtros baseados em teaching_only + turmas que instruem
  if (userRole === 'instructor') {
    switch (resourceType) {
      case 'attendance':
        // Para presença, retornar filtros que incluem aulas diretas e de turmas do instrutor
        const classGroupsForAttendance = await getInstructorClassGroups(user.id);
        return {
          ...filters,
          // Indicar que precisa filtrar por instrutor E turmas
          _instructorFilter: {
            instructorId: user.id,
            classGroupIds: classGroupsForAttendance
          }
        };
      
      case 'classes':
        // Para aulas, retornar filtros que incluem aulas diretas e de turmas do instrutor
        const classGroupsForClasses = await getInstructorClassGroups(user.id);
        return {
          ...filters,
          // Indicar que precisa filtrar por instrutor E turmas
          _instructorFilter: {
            instructorId: user.id,
            classGroupIds: classGroupsForClasses
          }
        };
      
      case 'classGroups':
        // Filtrar apenas turmas que o instrutor ensina
        return {
          ...filters,
          instructor_id: user.id
        };
      
      default:
        return filters;
    }
  }

  // Para outros roles, retornar filtros sem modificação
  return filters;
}

/**
 * Verifica se o usuário pode acessar um recurso específico
 */
export async function checkResourceAccess(
  resourceId: string,
  resourceType: 'class' | 'classGroup' | 'attendance',
  userContext: UserPermissionContext
): Promise<boolean> {
  const { user, userRole } = userContext;
  
  // Admins têm acesso completo
  if (userRole === 'admin') {
    return true;
  }

  // Para instrutores, verificar se o recurso pertence a eles
  if (userRole === 'instructor') {
    const supabase = await createTenantServerClient();
    
    try {
      switch (resourceType) {
        case 'class':
          const { data: classData } = await supabase
            .from('classes')
            .select('instructor_id, class_group_id')
            .eq('id', resourceId)
            .single();
          
          if (!classData) return false;
          
          // Verificar se o instrutor ministra a aula diretamente
          if (classData.instructor_id === user.id) {
            return true;
          }
          
          // Verificar se a aula pertence a uma turma que o instrutor é responsável
          if (classData.class_group_id) {
            const { data: groupData } = await supabase
              .from('class_groups')
              .select('instructor_id')
              .eq('id', classData.class_group_id)
              .single();
            
            return groupData?.instructor_id === user.id;
          }
          
          return false;
        
        case 'classGroup':
          const { data: groupData } = await supabase
            .from('class_groups')
            .select('instructor_id')
            .eq('id', resourceId)
            .single();
          
          return groupData?.instructor_id === user.id;
        
        case 'attendance':
          const { data: attendanceData } = await supabase
            .from('attendance')
            .select('class:classes(instructor_id, class_group_id)')
            .eq('id', resourceId)
            .single();
          
          if (!attendanceData?.class) return false;
          
          // Verificar se o instrutor ministra a aula diretamente
          if (attendanceData.class.instructor_id === user.id) {
            return true;
          }
          
          // Verificar se a aula pertence a uma turma que o instrutor é responsável
          if (attendanceData.class.class_group_id) {
            const { data: groupData } = await supabase
              .from('class_groups')
              .select('instructor_id')
              .eq('id', attendanceData.class.class_group_id)
              .single();
            
            return groupData?.instructor_id === user.id;
          }
          
          return false;
        
        default:
          return false;
      }
    } catch (error) {
      console.error(`Erro ao verificar acesso ao recurso ${resourceType}:`, error);
      return false;
    }
  }

  // Para outros roles, negar acesso por padrão
  return false;
} 