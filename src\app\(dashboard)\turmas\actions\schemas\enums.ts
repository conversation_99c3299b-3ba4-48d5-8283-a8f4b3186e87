import { z } from 'zod';

// Enums para Class Groups
export const ClassGroupCategoryEnum = z.enum(['kids', 'teens', 'adults', 'seniors']);
export const ClassGroupEnrollmentStatusEnum = z.enum(['active', 'inactive', 'paused']);

// Enums para Classes
export const ClassStatusEnum = z.enum(['scheduled', 'ongoing', 'completed', 'cancelled', 'rescheduled']);

// Enums para Waitlist
export const ClassWaitlistStatusEnum = z.enum(['waiting', 'notified', 'enrolled', 'expired', 'cancelled']);

// Enums para Belt Colors (baseado no database)
export const BeltColorEnum = z.enum(['white', 'blue', 'purple', 'brown', 'black']);

// Tipos inferidos dos enums
export type ClassGroupCategory = z.infer<typeof ClassGroupCategoryEnum>;
export type ClassGroupEnrollmentStatus = z.infer<typeof ClassGroupEnrollmentStatusEnum>;
export type ClassStatus = z.infer<typeof ClassStatusEnum>;
export type ClassWaitlistStatus = z.infer<typeof ClassWaitlistStatusEnum>;
export type BeltColor = z.infer<typeof BeltColorEnum>; 