import { cookies } from 'next/headers';

const RECEPTION_MODE_COOKIE = 'apexsaas_reception_mode';

export interface ReceptionModeState {
  isActive: boolean;
  activatedBy: {
    id: string;
    email: string;
    name?: string;
  } | null;
  activatedAt: string | null;
}

/**
 * Hook para verificar o estado do modo de recepção no servidor
 * Usado principalmente no middleware para verificar se deve redirecionar
 */
export async function getReceptionModeState(): Promise<ReceptionModeState> {
  try {
    const cookieStore = await cookies();
    const receptionModeCookie = cookieStore.get(RECEPTION_MODE_COOKIE);

    if (!receptionModeCookie) {
      return {
        isActive: false,
        activatedBy: null,
        activatedAt: null,
      };
    }

    const state = JSON.parse(receptionModeCookie.value);
    return state;
  } catch (error) {
    console.error('Erro ao verificar estado do modo de recepção:', error);
    return {
      isActive: false,
      activatedBy: null,
      activatedAt: null,
    };
  }
}

/**
 * Verifica se o modo de recepção está ativo de forma simplificada
 */
export async function isReceptionModeActive(): Promise<boolean> {
  const state = await getReceptionModeState();
  return state.isActive;
} 