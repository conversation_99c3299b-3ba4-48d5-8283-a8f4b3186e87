'use client'

import { UserProfile, UserStatus } from '@/services/user'
import { useUserMetadata } from '@/hooks/user/Auth'
import { formatFullName } from '@/utils/format-utils'

/**
 * Hook para obter o perfil do usuário atual.
 * Usa useUserMetadata como fonte canônica de dados e transforma
 * os metadados do JWT no formato UserProfile esperado pelos componentes.
 *
 * @returns Objeto contendo o perfil do usuário e estados relacionados
 */
export function useUserProfile() {
  const { metadata, isLoading, error, refetch } = useUserMetadata()

  // Transformação simples de metadados para perfil
  const profile: UserProfile | undefined = metadata
    ? {
        id: metadata.id,
        email: metadata.email,
        firstName: metadata.first_name,
        lastName: metadata.last_name,
        fullName: formatFullName(metadata.first_name, metadata.last_name),
        role: metadata.role,
        status: (metadata.status as UserStatus) || 'active',
        avatarUrl: metadata.avatar_url || null,
        avatarStoragePath: metadata.avatar_storage_path || null,
        ...(metadata.branch_id ? { branchId: metadata.branch_id } : {}),
        ...(metadata.tenant_id ? { tenantId: metadata.tenant_id } : {})
      }
    : undefined

  return {
    profile,
    isLoading,
    isFetching: isLoading,
    error,
    refetch
  }
}
