'use server';

import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { z } from 'zod';
import { getSupabaseConfig } from '@/config/supabase';
import { SupabaseErrorHandler } from '@/utils/supabase-error-handler';

const validatePasswordSchema = z.object({
  email: z.string().email('Email inválido'),
  password: z.string().min(1, 'Senha é obrigatória'),
});

interface ValidatePasswordResult {
  success: boolean;
  error?: string;
}

export async function validatePassword(
  email: string,
  password: string
): Promise<ValidatePasswordResult> {
  try {
    // Validar entrada
    const validation = validatePasswordSchema.safeParse({ email, password });
    if (!validation.success) {
      return {
        success: false,
        error: 'Dados inválidos fornecidos',
      };
    }

    const cookieStore = await cookies();
    const config = getSupabaseConfig();
    
    // Criar cliente Supabase para validação de credenciais
    const supabase = createServerClient(
      config.url,
      config.anonKey,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              );
            } catch {
              // O cookie store está selado, mas isso não afeta a validação
            }
          },
        },
      }
    );

    // Tentar fazer login com as credenciais fornecidas
    const { error } = await supabase.auth.signInWithPassword({
      email: validation.data.email,
      password: validation.data.password,
    });

    if (error) {
      return {
        success: false,
        error: 'Senha incorreta',
      };
    }

    // Se chegou aqui, a senha está correta
    return {
      success: true,
    };
  } catch (error) {
    return SupabaseErrorHandler.createServerActionErrorResponse(
      error,
      'validatePassword',
      'Erro ao validar senha'
    );
  }
} 