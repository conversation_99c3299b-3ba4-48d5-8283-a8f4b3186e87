"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/services/supabase/server";
import { EnrollStudentSchema } from "../schemas/class-group-enrollment";
import { validateUserAuthentication, validateClassGroup, validateStudent } from "./shared/validation-helpers";

/**
 * Matricula um aluno em uma turma
 */
export async function enrollStudent(data: unknown) {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;

    const validationResult = EnrollStudentSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const { class_group_id, student_id, notes } = validationResult.data;
    const supabase = await createAdminClient();

    // Verificar se a turma existe e está ativa
    const classGroupValidation = await validateClassGroup(class_group_id, tenantId);
    if (!classGroupValidation.success) {
      return classGroupValidation;
    }

    const { classGroup } = classGroupValidation;
    if (!classGroup.is_active) {
      return { success: false, errors: { class_group_id: "Turma não encontrada ou inativa" } };
    }

    // Verificar se o aluno existe
    const studentValidation = await validateStudent(student_id, tenantId);
    if (!studentValidation.success) {
      return studentValidation;
    }

    // Verificar se o aluno já está matriculado
    const { data: existingEnrollment, error: enrollmentCheckError } = await supabase
      .from("class_group_enrollments")
      .select("id, status")
      .eq("class_group_id", class_group_id)
      .eq("student_id", student_id)
      .eq("tenant_id", tenantId)
      .single();

    if (existingEnrollment && existingEnrollment.status === "active") {
      return { success: false, errors: { student_id: "Aluno já está matriculado nesta turma" } };
    }

    // Verificar capacidade (não contar alunos pausados)
    if (classGroup.max_capacity) {
      // Buscar matrículas ativas
      const { data: activeEnrollments, error: enrollmentsError } = await supabase
        .from("class_group_enrollments")
        .select("id")
        .eq("class_group_id", class_group_id)
        .eq("status", "active")
        .eq("tenant_id", tenantId);

      if (enrollmentsError) {
        console.error("Erro ao verificar capacidade:", enrollmentsError);
        return { success: false, errors: { _form: "Erro ao verificar capacidade da turma" } };
      }

      if (activeEnrollments && activeEnrollments.length > 0) {
        // Verificar quantas dessas matrículas estão pausadas
        const enrollmentIds = activeEnrollments.map(e => e.id);
        
        const { count: pausedCount } = await supabase
          .from("enrollment_pauses")
          .select("*", { count: "exact", head: true })
          .in("enrollment_id", enrollmentIds)
          .eq("tenant_id", tenantId)
          .is("resumed_at", null);

        // Calcular matrículas realmente ativas (não pausadas)
        const realActiveCount = activeEnrollments.length - (pausedCount || 0);

        if (realActiveCount >= classGroup.max_capacity) {
          return { success: false, errors: { _form: "Turma já atingiu a capacidade máxima" } };
        }
      }
    }

    // Criar matrícula
    const { data: enrollment, error } = await supabase
      .from("class_group_enrollments")
      .insert({
        class_group_id,
        student_id,
        tenant_id: tenantId,
        enrollment_date: new Date().toISOString(),
        status: "active",
        notes,
      })
      .select()
      .single();

    if (error) {
      console.error("Erro ao matricular aluno:", error);
      return { success: false, errors: { _form: "Erro ao matricular aluno" } };
    }

    revalidatePath("/aulas");
    return { success: true, data: enrollment, message: "Aluno matriculado com sucesso" };
  } catch (error) {
    console.error("Erro ao matricular aluno:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 