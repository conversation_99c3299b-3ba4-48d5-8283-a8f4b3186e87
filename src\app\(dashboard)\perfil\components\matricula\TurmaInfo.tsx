import { Card } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Users, Calendar, Info, User } from 'lucide-react';

const getInitials = (name: string) => {
  if (!name) return '?';
  const names = name.split(' ');
  const initials = names.map(n => n[0]).join('');
  return initials.substring(0, 2).toUpperCase();
};

const formatSchedule = (schedule: any) => {
  if (!schedule || !Array.isArray(schedule.days) || schedule.days.length === 0) {
    return 'Horário não definido';
  }

  const daysOfWeek = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quin<PERSON>', 'Sexta', 'Sábado'];
  const formattedDays = schedule.days
    .map((day: number) => daysOfWeek[day])
    .join(', ');

  return `${formattedDays} às ${schedule.time}`;
};

export function TurmaInfo({ classGroup }: { classGroup: any }) {
  if (!classGroup) return null;

  const { name, description, instructor, schedule } = classGroup;

  return (
    <Card className="p-6 bg-white dark:bg-slate-800">
      <div className="flex items-center gap-3 mb-6">
        <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Minha Turma
        </h3>
      </div>

      <div className="space-y-6">
        <div>
          <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            {name}
          </h4>
          {description && (
            <div className="flex items-start gap-3 text-slate-600 dark:text-slate-400">
              <Info className="h-4 w-4 mt-0.5 flex-shrink-0" />
              <p className="text-sm leading-relaxed">{description}</p>
            </div>
          )}
        </div>

        <div className="grid gap-4 sm:grid-cols-2">
          {instructor && (
            <div className="flex items-center gap-3 p-4 rounded-lg bg-slate-50 dark:bg-slate-700/50">
              <Avatar className="h-10 w-10">
                <AvatarImage src={instructor.avatarUrl} alt={instructor.fullName} />
                <AvatarFallback className="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300">
                  {getInitials(instructor.fullName)}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Instrutor</p>
                <p className="font-semibold text-gray-900 dark:text-gray-100">
                  {instructor.fullName}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
} 