import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/services/supabase/types/database.types';
import { TenantIdentifier } from '@/services/tenant/types';

export type TenantScopedTable = 
  | 'users' 
  | 'branches' 
  | 'classes' 
  | 'students' 
  | 'student_belts' 
  | 'attendance' 
  | 'payments' 
  | 'subscriptions';

export class TableCollection {
  private readonly tenantTables: TenantScopedTable[] = [
    'users', 'branches', 'classes', 'students', 
    'student_belts', 'attendance', 'payments', 'subscriptions'
  ];

  hasTenantScope(tableName: string): boolean {
    return this.tenantTables.includes(tableName as TenantScopedTable);
  }
}

export class QueryInterceptor {
  constructor(private readonly tenant: TenantIdentifier, private readonly tables: TableCollection) {}
  
  applyTenantScope(query: any, tableName: string): any {
    if (!this.shouldApplyScope(tableName)) {
      return query;
    }
    
    return this.interceptMethods(query);
  }
  
  private shouldApplyScope(tableName: string): boolean {
    return this.tables.hasTenantScope(tableName) && !!this.tenant.id;
  }
  
  private interceptMethods(query: any): any {
    const { id: tenantId } = this.tenant;
    
    this.interceptSelectMethod(query, tenantId!);
    this.interceptInsertMethod(query, tenantId!);
    this.interceptUpdateMethod(query, tenantId!);
    this.interceptDeleteMethod(query, tenantId!);
    
    return query;
  }
  
  private interceptSelectMethod(query: any, tenantId: string): void {
    const originalSelect = query.select;
    
    query.select = function(...selectArgs: any[]) {
      const selectQuery = originalSelect.apply(this, selectArgs);
      return selectQuery.eq('tenant_id', tenantId);
    };
  }
  
  private interceptInsertMethod(query: any, tenantId: string): void {
    const originalInsert = query.insert;
    
    const addTenantToData = (data: any, tenantId: string) => {
      if (Array.isArray(data)) {
        return data.map(item => ({ ...item, tenant_id: tenantId }));
      }
      
      return { ...data, tenant_id: tenantId };
    };
    
    query.insert = function(data: any, options: any) {
      const dataWithTenant = addTenantToData(data, tenantId);
      return originalInsert.call(this, dataWithTenant, options);
    };
  }
  
  private interceptUpdateMethod(query: any, tenantId: string): void {
    const originalUpdate = query.update;
    
    query.update = function(data: any, options: any) {
      return originalUpdate.call(this, data, options).eq('tenant_id', tenantId);
    };
  }
  
  private interceptDeleteMethod(query: any, tenantId: string): void {
    const originalDelete = query.delete;
    
    query.delete = function(options: any) {
      return originalDelete.call(this, options).eq('tenant_id', tenantId);
    };
  }
}

export class TenantClientFactory {
  private readonly tables = new TableCollection();
  
  createProxy(client: SupabaseClient<Database>, tenant: TenantIdentifier): SupabaseClient<Database> {
    const interceptor = new QueryInterceptor(tenant, this.tables);
    
    return new Proxy(client, {
      get: (target, prop, receiver) => {
        const originalValue = Reflect.get(target, prop, receiver);
        
        if (prop === 'from' && typeof originalValue === 'function') {
          return this.wrapFromMethod(originalValue, interceptor);
        }
        
        return originalValue;
      }
    }) as SupabaseClient<Database>;
  }
  
  private wrapFromMethod(originalFrom: Function, interceptor: QueryInterceptor): Function {
    return function(this: any, ...args: any[]) {
      const query = originalFrom.apply(this, args);
      const tableName = args[0];
      
      return interceptor.applyTenantScope(query, tableName);
    };
  }
} 