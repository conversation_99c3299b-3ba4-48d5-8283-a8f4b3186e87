'use client'

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { useTheme } from 'next-themes'

interface TenantThemeContextType {
  primaryColor: string | null
  secondaryColor: string | null
  logoUrl: string | null
  tenantName: string | null
  description: string | null
  setPrimaryColor: (color: string) => void
  setSecondaryColor: (color: string) => void
}

interface TenantThemeProviderProps {
  children: ReactNode
  initialPrimaryColor?: string | null
  initialSecondaryColor?: string | null
  initialLogoUrl?: string | null
  initialTenantName?: string | null
  initialDescription?: string | null
}

const defaultPrimaryColor = '#000000'
const defaultSecondaryColor = '#6b7280'

const TenantThemeContext = createContext<TenantThemeContextType>({
  primaryColor: null,
  secondaryColor: null,
  logoUrl: null,
  tenantName: null,
  description: null,
  setPrimaryColor: () => {},
  setSecondaryColor: () => {}
})

export function TenantThemeProvider({
  children,
  initialPrimaryColor = null,
  initialSecondaryColor = null,
  initialLogoUrl = null,
  initialTenantName = null,
  initialDescription = null
}: TenantThemeProviderProps) {
  const { theme } = useTheme()
  const [primaryColor, setPrimaryColor] = useState<string | null>(initialPrimaryColor)
  const [secondaryColor, setSecondaryColor] = useState<string | null>(initialSecondaryColor)
  const [logoUrl, setLogoUrl] = useState<string | null>(initialLogoUrl)
  const [tenantName, setTenantName] = useState<string | null>(initialTenantName)
  const [description, setDescription] = useState<string | null>(initialDescription)

  const getCssVariables = () => {
    const primary = primaryColor || defaultPrimaryColor
    const secondary = secondaryColor || defaultSecondaryColor
    
    const isDark = theme === 'dark'
    
    const primaryLight = adjustColorLightness(primary, +30)
    const secondaryLight = adjustColorLightness(secondary, +30)
    
    const primaryDark = adjustColorLightness(primary, -20)
    const secondaryDark = adjustColorLightness(secondary, -20)
    
    return {
      '--tenant-primary': isDark ? primaryDark : primary,
      '--tenant-primary-light': primaryLight,
      '--tenant-primary-dark': primaryDark,
      '--tenant-secondary': isDark ? secondaryDark : secondary,
      '--tenant-secondary-light': secondaryLight,
      '--tenant-secondary-dark': secondaryDark,
      '--primary': isDark ? primaryDark : primary,
      '--primary-foreground': '#ffffff'
    }
  }

  useEffect(() => {
    const variables = getCssVariables()
    Object.entries(variables).forEach(([key, value]) => {
      document.documentElement.style.setProperty(key, value)
    })
  }, [primaryColor, secondaryColor, theme])

  useEffect(() => {
    if (initialPrimaryColor) {
      setPrimaryColor(initialPrimaryColor)
      document.documentElement.style.setProperty('--tenant-primary', initialPrimaryColor)
      document.documentElement.style.setProperty('--tenant-primary-light', initialPrimaryColor)
      document.documentElement.style.setProperty('--tenant-primary-dark', initialPrimaryColor)
    }
    
    if (initialSecondaryColor) {
      setSecondaryColor(initialSecondaryColor)
      document.documentElement.style.setProperty('--tenant-secondary', initialSecondaryColor)
      document.documentElement.style.setProperty('--tenant-secondary-light', initialSecondaryColor)
      document.documentElement.style.setProperty('--tenant-secondary-dark', initialSecondaryColor)
    }
    
    if (initialLogoUrl) {
      setLogoUrl(initialLogoUrl)
    }
    
    if (initialTenantName) {
      setTenantName(initialTenantName)
    }
    
    if (initialDescription) {
      setDescription(initialDescription)
    }
  }, [initialPrimaryColor, initialSecondaryColor, initialLogoUrl, initialTenantName, initialDescription])

  return (
    <TenantThemeContext.Provider value={{ 
      primaryColor, 
      secondaryColor, 
      logoUrl, 
      tenantName,
      description,
      setPrimaryColor,
      setSecondaryColor
    }}>
      {children}
    </TenantThemeContext.Provider>
  )
}

function adjustColorLightness(color: string, amount: number): string {
  if (!/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color)) {
    return color
  }

  let hex = color.replace('#', '')
  
  // Converte hex para RGB
  let r = parseInt(hex.length === 3 ? hex.slice(0, 1).repeat(2) : hex.slice(0, 2), 16)
  let g = parseInt(hex.length === 3 ? hex.slice(1, 2).repeat(2) : hex.slice(2, 4), 16)
  let b = parseInt(hex.length === 3 ? hex.slice(2, 3).repeat(2) : hex.slice(4, 6), 16)

  // Converte RGB para HSL
  r /= 255
  g /= 255
  b /= 255
  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h = 0
  let s = 0
  let l = (max + min) / 2

  if (max !== min) {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
    
    if (max === r) h = (g - b) / d + (g < b ? 6 : 0)
    else if (max === g) h = (b - r) / d + 2
    else h = (r - g) / d + 4
    
    h *= 60
  }

  // Ajusta a luminosidade
  l = Math.max(0, Math.min(1, l + amount / 100))

  // Converte de volta para RGB
  const c = (1 - Math.abs(2 * l - 1)) * s
  const x = c * (1 - Math.abs((h / 60) % 2 - 1))
  const m = l - c / 2
  let r1, g1, b1

  if (h >= 0 && h < 60) {
    [r1, g1, b1] = [c, x, 0]
  } else if (h >= 60 && h < 120) {
    [r1, g1, b1] = [x, c, 0]
  } else if (h >= 120 && h < 180) {
    [r1, g1, b1] = [0, c, x]
  } else if (h >= 180 && h < 240) {
    [r1, g1, b1] = [0, x, c]
  } else if (h >= 240 && h < 300) {
    [r1, g1, b1] = [x, 0, c]
  } else {
    [r1, g1, b1] = [c, 0, x]
  }

  r = Math.round((r1 + m) * 255)
  g = Math.round((g1 + m) * 255)
  b = Math.round((b1 + m) * 255)

  // Converte de volta para hex
  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`
}

export function useTenantTheme() {
  return useContext(TenantThemeContext)
} 