'use server'

import { createClient } from '@/services/supabase/server';
import { PermissionContext, UserRole } from '../types/permission-types';

/**
 * Classe responsável por obter o contexto necessário para avaliação de permissões
 */
class PermissionContextResolver {
  /**
   * Obtém o contexto completo para avaliação de permissões
   */
  async getContext(currentUserId: string, targetUserId?: string): Promise<PermissionContext | null> {
    try {
      const supabase = await createClient();
      
      const { data: currentUser, error: currentUserError } = await supabase
        .from('users')
        .select('role, tenant_id')
        .eq('id', currentUserId)
        .single();
        
      if (currentUserError || !currentUser) {
        console.error('Erro ao obter dados do usuário atual:', currentUserError);
        return null;
      }
      
      // Log para debug da role do usuário
      console.log(`[PermissionContext] Usuário ${currentUserId} tem role ${currentUser.role}`);
      
      const context: PermissionContext = {
        currentUserId,
        currentUserRole: currentUser.role as UserRole,
        currentUserTenantId: currentUser.tenant_id,
      };
      
      if (targetUserId && targetUserId !== currentUserId) {
        const { data: targetUser, error: targetUserError } = await supabase
          .from('users')
          .select('role, tenant_id')
          .eq('id', targetUserId)
          .single();
          
        if (!targetUserError && targetUser) {
          context.targetUserId = targetUserId;
          context.targetUserRole = targetUser.role as UserRole;
          context.targetTenantId = targetUser.tenant_id;
        } else {
          console.warn('Usuário alvo não encontrado:', targetUserId);
        }
      } else if (targetUserId === currentUserId) {
        context.targetUserId = currentUserId;
        context.targetUserRole = currentUser.role as UserRole;
        context.targetTenantId = currentUser.tenant_id;
      }
      
      return context;
    } catch (error) {
      console.error('Erro ao resolver contexto de permissão:', error);
      return null;
    }
  }
  
  /**
   * Versão mais leve que utiliza apenas os IDs para verificação
   * Usado em casos onde o contexto completo não é necessário
   */
  async getBasicContext(currentUserId: string): Promise<PermissionContext | null> {
    try {
      const supabase = await createClient();
      
      const { data: currentUser, error: currentUserError } = await supabase
        .from('users')
        .select('role, tenant_id')
        .eq('id', currentUserId)
        .single();
        
      if (currentUserError || !currentUser) {
        console.error('Erro ao obter dados básicos do usuário:', currentUserError);
        return null;
      }
      
      return {
        currentUserId,
        currentUserRole: currentUser.role as UserRole,
        currentUserTenantId: currentUser.tenant_id,
      };
    } catch (error) {
      console.error('Erro ao resolver contexto básico de permissão:', error);
      return null;
    }
  }
  
  /**
   * Verificação específica para determinar se um usuário é administrador
   * Consulta diretamente o banco de dados para evitar problemas com cache
   */
  async isUserAdmin(userId: string): Promise<boolean> {
    try {
      const supabase = await createClient();
      
      const { data: currentUser, error: currentUserError } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();
        
      if (currentUserError || !currentUser) {
        console.error('Erro ao verificar se usuário é admin:', currentUserError);
        return false;
      }
      
      const isAdmin = currentUser.role === 'admin';
      console.log(`[PermissionContext] Verificação de admin para usuário ${userId}: ${isAdmin} (role: ${currentUser.role})`);
      
      return isAdmin;
    } catch (error) {
      console.error('Erro ao verificar se usuário é admin:', error);
      return false;
    }
  }
}

// Instância única do resolver
const resolver = new PermissionContextResolver();

// Server actions para serem chamadas pelo cliente
export async function getPermissionContext(currentUserId: string, targetUserId?: string): Promise<PermissionContext | null> {
  return resolver.getContext(currentUserId, targetUserId);
}

export async function getBasicPermissionContext(currentUserId: string): Promise<PermissionContext | null> {
  return resolver.getBasicContext(currentUserId);
}

export async function isUserAdminServer(userId: string): Promise<boolean> {
  return resolver.isUserAdmin(userId);
} 