'use client';

import { motion } from 'framer-motion';
import { CheckCircle2, Info, Settings, Users, MapPin, Calendar, AlertTriangle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import PreviewCard from '../PreviewCard';
import { FormValues } from '../validation';
import { ClassGroup, Instructor, Branch } from '../types';
import { formatPreviewValue } from '../utils';

interface PreviewSectionProps {
  initialData: ClassGroup;
  watchedValues: FormValues;
  dirtyFields: Record<string, boolean>;
  instructors: Instructor[];
  branches: Branch[];
}

export default function PreviewSection({
  initialData,
  watchedValues,
  dirtyFields,
  instructors,
  branches
}: PreviewSectionProps) {
  const hasChanges = Object.keys(dirtyFields).length > 0;

  if (!hasChanges) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle2 className="h-5 w-5 text-blue-600" />
            Preview das Alterações
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Revise as mudanças antes de salvar. Apenas os campos alterados são exibidos.
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Renderizar apenas campos que mudaram */}
            {dirtyFields.name && (
              <PreviewCard
                title="Nome da Turma"
                current={initialData.name}
                new={watchedValues.name}
                icon={Settings}
              />
            )}
            
            {dirtyFields.category && (
              <PreviewCard
                title="Categoria"
                current={initialData.category}
                new={watchedValues.category}
                icon={Settings}
                formatter={(value) => formatPreviewValue(value, 'category')}
              />
            )}
            
            {dirtyFields.description && (
              <PreviewCard
                title="Descrição"
                current={initialData.description}
                new={watchedValues.description}
                icon={Info}
                formatter={(value) => value ? (value.length > 50 ? `${value.substring(0, 50)}...` : value) : 'Não definida'}
              />
            )}
            
            {dirtyFields.max_capacity && (
              <PreviewCard
                title="Capacidade Máxima"
                current={initialData.max_capacity}
                new={watchedValues.max_capacity}
                icon={Users}
                formatter={(value) => formatPreviewValue(value, 'capacity')}
              />
            )}
            
            {dirtyFields.allow_waitlist && (
              <PreviewCard
                title="Lista de Espera"
                current={initialData.allow_waitlist}
                new={watchedValues.allow_waitlist}
                icon={Users}
                formatter={(value) => formatPreviewValue(value, 'waitlist')}
              />
            )}
            
            {dirtyFields.instructor_id && (
              <PreviewCard
                title="Instrutor"
                current={initialData.instructor_id}
                new={watchedValues.instructor_id}
                icon={Users}
                formatter={(instructorId) => {
                  const instructor = instructors.find(i => i.id === instructorId);
                  return instructor ? 
                    (instructor.full_name || `${instructor.first_name} ${instructor.last_name || ''}`.trim()) : 
                    'Não selecionado';
                }}
              />
            )}
            
            {dirtyFields.branch_id && (
              <PreviewCard
                title="Filial"
                current={initialData.branch_id}
                new={watchedValues.branch_id}
                icon={MapPin}
                formatter={(branchId) => {
                  const branch = branches.find(b => b.id === branchId);
                  return branch ? branch.name : 'Não selecionada';
                }}
              />
            )}
            
            {(dirtyFields.min_age || dirtyFields.max_age) && (
              <PreviewCard
                title="Faixa Etária"
                current={`${initialData.min_age} - ${initialData.max_age}`}
                new={`${watchedValues.min_age} - ${watchedValues.max_age}`}
                icon={Calendar}
                formatter={(ageRange) => `${ageRange} anos`}
              />
            )}
            
            {dirtyFields.min_belt_level && (
              <PreviewCard
                title="Faixa Mínima"
                current={initialData.min_belt_level}
                new={watchedValues.min_belt_level}
                icon={AlertTriangle}
                formatter={(value) => formatPreviewValue(value, 'belt')}
              />
            )}
            
            {dirtyFields.max_belt_level && (
              <PreviewCard
                title="Faixa Máxima"
                current={initialData.max_belt_level}
                new={watchedValues.max_belt_level}
                icon={AlertTriangle}
                formatter={(value) => formatPreviewValue(value, 'belt')}
              />
            )}
            
            {dirtyFields.is_active && (
              <PreviewCard
                title="Status da Turma"
                current={initialData.is_active}
                new={watchedValues.is_active}
                icon={Settings}
                formatter={(value) => formatPreviewValue(value, 'status')}
              />
            )}
            
            {dirtyFields.start_date && (
              <PreviewCard
                title="Data de Início"
                current={initialData.start_date}
                new={watchedValues.start_date}
                icon={Calendar}
                formatter={(value) => formatPreviewValue(value, 'date')}
              />
            )}
            
            {dirtyFields.end_date && (
              <PreviewCard
                title="Data de Término"
                current={initialData.end_date}
                new={watchedValues.end_date}
                icon={Calendar}
                formatter={(value) => formatPreviewValue(value, 'date')}
              />
            )}
          </div>
          
          {/* Resumo das Alterações */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mt-6 p-4 bg-blue-100 dark:bg-blue-900 rounded-lg border border-blue-200 dark:border-blue-800"
          >
            <div className="flex items-center gap-2 mb-2">
              <Info className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-900 dark:text-blue-100">
                Resumo das Alterações
              </span>
            </div>
            <p className="text-sm text-blue-800 dark:text-blue-200">
              {Object.keys(dirtyFields).length} campo(s) será(ão) atualizado(s). 
              Clique em "Salvar" para confirmar as alterações ou "Editar" para fazer mais mudanças.
            </p>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
} 