import { useState, useCallback, useEffect } from 'react';

interface UseCurrencyFormatProps {
  value: number;
  onChange: (value: number) => void;
  prefix?: string;
}

function formatCurrencyFromDigits(digits: string, prefix = 'R$'): string {
  // Garante pelo menos 3 dígitos para centavos
  let clean = digits.replace(/\D/g, '');
  if (clean.length === 0) clean = '0';
  if (clean.length === 1) clean = '00' + clean;
  if (clean.length === 2) clean = '0' + clean;
  const intValue = parseInt(clean, 10);
  const floatValue = intValue / 100;
  return `${prefix} ${floatValue.toLocaleString('pt-BR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
}

function digitsFromValue(value: number): string {
  // Converte número para string de dígitos (ex: 120.94 -> "12094")
  return Math.round(Math.abs(value * 100)).toString();
}

export function useCurrencyFormat({ value, onChange, prefix = 'R$' }: UseCurrencyFormatProps) {
  // Estado interno: string de dígitos (ex: "12094")
  const [digits, setDigits] = useState(() => digitsFromValue(value));

  // Atualiza estado interno se value externo mudar (ex: reset do form)
  useEffect(() => {
    setDigits(digitsFromValue(value));
  }, [value]);

  // Valor formatado para exibição
  const displayValue = formatCurrencyFromDigits(digits, prefix);

  // Handler de mudança
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    let input = e.target.value;
    // Remove prefixo e tudo que não for dígito
    if (input.startsWith(prefix)) {
      input = input.slice(prefix.length).trim();
    }
    const clean = input.replace(/\D/g, '');
    // Limita a 12 dígitos (até 999.999.999,99)
    const limited = clean.slice(0, 12);
    setDigits(limited);
    // Atualiza valor numérico
    const intValue = parseInt(limited.length ? limited : '0', 10);
    const floatValue = intValue / 100;
    onChange(floatValue);
  }, [onChange, prefix]);

  // Handler de blur: força formatação
  const handleBlur = useCallback(() => {
    setDigits((d) => {
      // Garante pelo menos 3 dígitos
      if (d.length === 0) return '000';
      if (d.length === 1) return '00' + d;
      if (d.length === 2) return '0' + d;
      return d;
    });
  }, []);

  return {
    displayValue,
    handleChange,
    handleBlur,
  };
} 