/**
 * Utilitários para manipulação de strings
 */

/**
 * Gera uma string aleatória de tamanho especificado
 * @param length Tamanho da string a ser gerada
 * @returns String aleatória
 */
export function generateRandomString(length: number): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * Formata uma string para um formato amigável de URL
 * @param text Texto a ser formatado
 * @returns String formatada para URL
 */
export function slugify(text: string): string {
  return text
    .toString()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '')
    .replace(/--+/g, '-');
}

/**
 * Capitaliza a primeira letra de cada palavra na string
 * @param text Texto a ser capitalizado
 * @returns String com a primeira letra de cada palavra em maiúscula
 */
export function capitalizeWords(text: string): string {
  if (!text) return '';
  return text
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

/**
 * Normaliza um endereço de e-mail, removendo diacríticos da parte local.
 * Isso é útil para sistemas que não suportam caracteres UTF-8 em e-mails.
 * Ex: joão<EMAIL> -> <EMAIL>
 * @param email O e-mail a ser normalizado
 * @returns O e-mail normalizado
 */
export function normalizeEmail(email: string): string {
  if (!email) return '';

  const [localPart, domain] = email.split('@');

  if (!localPart || !domain) {
    // Retorna o e-mail em minúsculas se não for um formato válido
    return email.toLowerCase();
  }

  const normalizedLocalPart = localPart
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '');

  return `${normalizedLocalPart}@${domain}`.toLowerCase();
} 