"use server";

import { revalidatePath } from "next/cache";
import { createClient } from "@/services/supabase/server";
import { getCurrentUser } from "@/services/auth/actions/auth-actions";
import {
  CheckInStudentSchema,
  CheckInByQRSchema,
} from "../../aulas/actions/schemas/index";
import { calculateTimeElapsed, createExpirationMessage } from '@/lib/utils/time-formatter';
import type { QRCodeData } from './attendance-utils';
import { isClassAvailableForCheckIn } from './attendance-utils';
import { checkSingleEnrollmentPause } from './check-enrollment-pauses';

/**
 * Registra presença manual de um aluno em uma aula
 */
export async function checkInStudent(data: unknown) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const validationResult = CheckInStudentSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const validatedData = validationResult.data;
    const supabase = await createClient();

    const { data: classData, error: classError } = await supabase
      .from("classes")
      .select("id, name, start_time, end_time, status, instructor_id, branch_id, class_group_id")
      .eq("id", validatedData.class_id)
      .eq("tenant_id", tenantId)
      .single();

    if (classError || !classData) {
      return { success: false, errors: { class_id: "Aula não encontrada" } };
    }

    if (!isClassAvailableForCheckIn(classData)) {
      return { success: false, errors: { class_id: "Aula não está disponível para check-in" } };
    }

    const { data: student, error: studentError } = await supabase
      .from("students")
      .select(`
        id,
        user:users (
          id,
          first_name,
          last_name,
          full_name,
          status
        )
      `)
      .eq("id", validatedData.student_id)
      .eq("tenant_id", tenantId)
      .single();

    if (studentError || !student) {
      return { success: false, errors: { student_id: "Aluno não encontrado" } };
    }

    const userData = student.user as any;
    if (userData && userData.status !== "active") {
      return { success: false, errors: { student_id: "Aluno inativo" } };
    }

    // Verificar se a matrícula do aluno está pausada (para aulas de turma)
    if (classData.class_group_id) {
      const { data: enrollment, error: enrollmentError } = await supabase
        .from("class_group_enrollments")
        .select("id, status")
        .eq("student_id", validatedData.student_id)
        .eq("class_group_id", classData.class_group_id)
        .eq("status", "active")
        .eq("tenant_id", tenantId)
        .single();

      if (enrollmentError || !enrollment) {
        return { success: false, errors: { student_id: "Aluno não está matriculado nesta turma" } };
      }

      // Verificar se a matrícula estava pausada durante o período da aula
      const pauseCheckResult = await checkSingleEnrollmentPause(
        enrollment.id,
        classData.start_time,
        classData.end_time
      );

      if (pauseCheckResult.success && pauseCheckResult.was_paused) {
        return { 
          success: false, 
          errors: { 
            student_id: 'Matrícula estava pausada durante esta aula' 
          } 
        };
      }
    }

    const { data: existingAttendance } = await supabase
      .from("attendance")
      .select(`
        id,
        checked_in_at,
        checked_in_by_user:users!attendance_checked_in_by_fkey (
          first_name,
          last_name,
          full_name
        )
      `)
      .eq("student_id", validatedData.student_id)
      .eq("class_id", validatedData.class_id)
      .eq("tenant_id", tenantId)
      .single();

    if (existingAttendance) {
      const checkedInBy = existingAttendance.checked_in_by_user as any;
      const checkedInTime = new Date(existingAttendance.checked_in_at).toLocaleString('pt-BR');
      const professorName = checkedInBy?.full_name || checkedInBy?.first_name || 'Professor';
      const studentName = userData?.full_name || userData?.first_name || 'O aluno';
      
      const currentUserRole = user.app_metadata?.role;
      
      return { 
        success: false, 
        errors: { 
          student_id: `${studentName} já possui check-in registrado nesta aula às ${checkedInTime} com o professor ${professorName}. Não é possível fazer check-in duplicado.`,
          _meta: {
            duplicate_checkin: true,
            existing_checkin_time: existingAttendance.checked_in_at,
            checked_in_by: professorName,
            student_name: studentName,
            is_instructor_checkin: true,
            current_user_role: currentUserRole
          }
        } 
      };
    }

    const { data: attendance, error: attendanceError } = await supabase
      .from("attendance")
      .insert({
        id: crypto.randomUUID(),
        student_id: validatedData.student_id,
        class_id: validatedData.class_id,
        checked_in_by: user.id,
        tenant_id: tenantId,
        checked_in_at: new Date().toISOString(),
        notes: validatedData.notes,
      })
      .select()
      .single();

    if (attendanceError) {
      console.error("Erro ao registrar presença:", attendanceError);
      
      if (attendanceError.code === '23505' && attendanceError.message?.includes('unique_attendance_per_class')) {
        return { 
          success: false, 
          errors: { 
            student_id: `${userData?.full_name || userData?.first_name || 'O aluno'} já possui check-in registrado nesta aula. Não é possível fazer check-in duplo.`,
            _meta: {
              duplicate_checkin: true,
              constraint_violation: true
            }
          } 
        };
      }
      
      return { success: false, errors: { _form: "Erro ao registrar presença" } };
    }

    await supabase
      .from("students")
      .update({ 
        last_attendance_date: new Date().toISOString() 
      })
      .eq("id", validatedData.student_id)
      .eq("tenant_id", tenantId);

    revalidatePath("/aulas");
    return { success: true, data: attendance };
  } catch (error) {
    console.error("Erro ao fazer check-in:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Registra presença via QR Code
 */
export async function checkInByQR(data: unknown) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const validationResult = CheckInByQRSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const validatedData = validationResult.data;
    const supabase = await createClient();

    let qrData: QRCodeData;
    try {
      qrData = JSON.parse(Buffer.from(validatedData.qr_code, 'base64').toString());
    } catch {
      return { success: false, errors: { qr_code: "QR Code inválido" } };
    }

    const now = new Date();
    const expirationDate = new Date(qrData.expires_at);
    
    if (now > expirationDate) {
      const elapsed = calculateTimeElapsed(expirationDate, now);
      const expiredMessage = createExpirationMessage(expirationDate, now);
      
      return { 
        success: false, 
        errors: { 
          qr_code: expiredMessage,
          _meta: {
            expired_at: qrData.expires_at,
            hours_expired: elapsed.hours,
            minutes_expired: elapsed.minutes,
            can_regenerate: true
          }
        } 
      };
    }

    const { data: classData, error: classError } = await supabase
      .from("classes")
      .select("id, name, start_time, end_time, status, class_group_id")
      .eq("id", qrData.class_id)
      .eq("tenant_id", tenantId)
      .single();

    if (classError || !classData) {
      return { success: false, errors: { qr_code: "Aula não encontrada" } };
    }

    if (!isClassAvailableForCheckIn(classData)) {
      return { success: false, errors: { qr_code: "Aula não está disponível para check-in" } };
    }

    const { data: student, error: studentError } = await supabase
      .from("students")
      .select(`
        id,
        user:users (
          id,
          first_name,
          last_name,
          full_name,
          status
        )
      `)
      .eq("check_in_code", validatedData.student_check_in_code)
      .eq("tenant_id", tenantId)
      .single();

    if (studentError || !student) {
      return { success: false, errors: { student_check_in_code: "Código de aluno inválido" } };
    }

    const userData = student.user as any;
    if (userData && userData.status !== "active") {
      return { success: false, errors: { student_check_in_code: "Aluno inativo" } };
    }

    // Verificar se a matrícula do aluno está pausada (para aulas de turma)
    if (classData.class_group_id) {
      const { data: enrollment, error: enrollmentError } = await supabase
        .from("class_group_enrollments")
        .select("id, status")
        .eq("student_id", student.id)
        .eq("class_group_id", classData.class_group_id)
        .eq("status", "active")
        .eq("tenant_id", tenantId)
        .single();

      if (enrollmentError || !enrollment) {
        return { success: false, errors: { student_check_in_code: "Você não está matriculado nesta turma" } };
      }

      // Verificar se a matrícula estava pausada durante o período da aula
      const pauseCheckResult = await checkSingleEnrollmentPause(
        enrollment.id,
        classData.start_time,
        classData.end_time
      );

      if (pauseCheckResult.success && pauseCheckResult.was_paused) {
        return { 
          success: false, 
          errors: { 
            student_check_in_code: 'Sua matrícula estava pausada durante esta aula' 
          } 
        };
      }
    }

    const { data: existingAttendance } = await supabase
      .from("attendance")
      .select(`
        id,
        checked_in_at,
        checked_in_by_user:users!attendance_checked_in_by_fkey (
          first_name,
          last_name,
          full_name
        )
      `)
      .eq("student_id", student.id)
      .eq("class_id", qrData.class_id)
      .eq("tenant_id", tenantId)
      .single();

    if (existingAttendance) {
      const checkedInBy = existingAttendance.checked_in_by_user as any;
      const checkedInTime = new Date(existingAttendance.checked_in_at).toLocaleString('pt-BR');
      const professorName = checkedInBy?.full_name || checkedInBy?.first_name || 'Professor';
      const studentName = userData?.full_name || userData?.first_name || 'O aluno';
      
      const currentUserRole = user.app_metadata?.role;
      const isStudentSelfCheckIn = currentUserRole === 'student' && userData?.id === user.id;
      const isInstructorOrAdmin = ['admin', 'instructor', 'teacher'].includes(currentUserRole);
      
      let errorMessage: string;
      
      if (isStudentSelfCheckIn) {
        errorMessage = `Você já fez check-in nesta aula às ${checkedInTime} com o professor ${professorName}. Não é possível fazer check-in novamente.`;
      } else if (isInstructorOrAdmin) {
        errorMessage = `${studentName} já possui check-in registrado nesta aula às ${checkedInTime} com o professor ${professorName}. Não é possível fazer check-in duplicado.`;
      } else {
        errorMessage = `Check-in já realizado às ${checkedInTime} com o professor ${professorName}.`;
      }
      
      return { 
        success: false, 
        errors: { 
          student_check_in_code: errorMessage,
          _meta: {
            duplicate_checkin: true,
            existing_checkin_time: existingAttendance.checked_in_at,
            checked_in_by: professorName,
            student_name: studentName,
            is_self_checkin: isStudentSelfCheckIn,
            current_user_role: currentUserRole
          }
        } 
      };
    }

    const { data: attendance, error: attendanceError } = await supabase
      .from("attendance")
      .insert({
        id: crypto.randomUUID(),
        student_id: student.id,
        class_id: qrData.class_id,
        checked_in_by: qrData.created_by,
        tenant_id: tenantId,
        checked_in_at: new Date().toISOString(),
        notes: validatedData.notes,
      })
      .select()
      .single();

    if (attendanceError) {
      console.error("Erro ao registrar presença via QR:", attendanceError);
      
      if (attendanceError.code === '23505' && attendanceError.message?.includes('unique_attendance_per_class')) {
        return { 
          success: false, 
          errors: { 
            student_check_in_code: `Você já possui check-in registrado nesta aula. Não é possível fazer check-in duplo.`,
            _meta: {
              duplicate_checkin: true,
              constraint_violation: true,
              student_name: userData?.full_name || userData?.first_name
            }
          } 
        };
      }
      
      return { success: false, errors: { _form: "Erro ao registrar presença" } };
    }

    await supabase
      .from("students")
      .update({ 
        last_attendance_date: new Date().toISOString() 
      })
      .eq("id", student.id)
      .eq("tenant_id", tenantId);

    revalidatePath("/aulas");
    
    const userInfo = userData || {};
    return { 
      success: true, 
      data: {
        attendance,
        student: {
          name: userInfo.full_name || `${userInfo.first_name || ''} ${userInfo.last_name || ''}`.trim(),
          id: student.id
        },
        class: {
          name: classData.name,
          id: classData.id
        }
      }
    };
  } catch (error) {
    console.error("Erro ao fazer check-in via QR:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 