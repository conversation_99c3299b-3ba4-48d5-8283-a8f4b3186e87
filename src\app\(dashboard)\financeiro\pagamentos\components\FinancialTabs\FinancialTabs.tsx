'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback } from 'react';
import { FinancialTab, FinancialTabsProps } from '../types';

interface TabButtonProps {
  tab: FinancialTab;
  activeTab: FinancialTab;
  onClick: (tab: FinancialTab) => void;
  children: React.ReactNode;
  icon: React.ReactNode;
}

function TabButton({ tab, activeTab, onClick, children, icon }: TabButtonProps) {
  const isActive = activeTab === tab;

  return (
    <button
      onClick={() => onClick(tab)}
      className={`
        flex items-center gap-2 px-6 py-3 text-sm font-medium rounded-lg transition-all duration-200
        ${isActive
          ? 'bg-tenant-primary/10 text-tenant-primary border-2 border-tenant-primary/20'
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50 border-2 border-transparent'
        }
      `}
    >
      {icon}
      {children}
    </button>
  );
}

export function FinancialTabs({ activeTab, onTabChange }: FinancialTabsProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString());
      if (value) {
        params.set(name, value);
      } else {
        params.delete(name);
      }
      return params.toString();
    },
    [searchParams]
  );

  const handleTabChange = (tab: FinancialTab) => {
    // Atualizar URL com o parâmetro da tab
    const queryString = createQueryString('tab', tab);
    router.push(`/financeiro/pagamentos?${queryString}`, { scroll: false });

    // Chamar callback do componente pai
    onTabChange(tab);
  };

  return (
    <div className="flex gap-2 mb-6">
      <TabButton
        tab="income"
        activeTab={activeTab}
        onClick={handleTabChange}
        icon={
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
          </svg>
        }
      >
        Receitas
      </TabButton>

      <TabButton
        tab="expense"
        activeTab={activeTab}
        onClick={handleTabChange}
        icon={
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
          </svg>
        }
      >
        Despesas
      </TabButton>
    </div>
  );
}
