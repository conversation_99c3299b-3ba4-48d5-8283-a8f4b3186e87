'use server';

import { createClient } from '@/services/supabase/server';

/**
 * Interface para filtros de despesas
 */
interface ExpenseFilters {
  limit?: number;
  offset?: number;
  status?: string | string[];
  category_id?: string | string[];
  payment_method_id?: string | string[];
  search?: string;
  start_date?: string;
  end_date?: string;
}

/**
 * Resultado da busca de despesas
 */
interface ExpenseSearchResult {
  success: boolean;
  data?: any[];
  hasMore?: boolean;
  error?: string;
}

/**
 * Buscar despesas com filtros
 */
export async function getExpenses(filters: ExpenseFilters = {}): Promise<ExpenseSearchResult> {
  try {
    const supabase = await createClient();

    let query = supabase
      .from('expenses')
      .select(`
        *,
        expense_categories (
          id,
          name,
          color
        ),
        payment_methods (
          id,
          name,
          slug
        )
      `)
      .order('created_at', { ascending: false });

    // Aplicar filtros
    if (filters.status) {
      if (Array.isArray(filters.status)) {
        query = query.in('status', filters.status);
      } else {
        query = query.eq('status', filters.status);
      }
    }

    if (filters.category_id) {
      if (Array.isArray(filters.category_id)) {
        query = query.in('category_id', filters.category_id);
      } else {
        query = query.eq('category_id', filters.category_id);
      }
    }

    if (filters.payment_method_id) {
      if (Array.isArray(filters.payment_method_id)) {
        query = query.in('payment_method_id', filters.payment_method_id);
      } else {
        query = query.eq('payment_method_id', filters.payment_method_id);
      }
    }

    if (filters.search) {
      query = query.or(`supplier_name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
    }

    if (filters.start_date) {
      query = query.gte('due_date', filters.start_date);
    }

    if (filters.end_date) {
      query = query.lte('due_date', filters.end_date);
    }

    // Paginação
    const limit = filters.limit || 20;
    const offset = filters.offset || 0;

    query = query.range(offset, offset + limit - 1);

    const { data, error } = await query;

    if (error) {
      console.error('Erro ao buscar despesas:', error);
      return {
        success: false,
        error: error.message
      };
    }

    // Verificar se há mais dados
    const hasMore = data && data.length === limit;

    return {
      success: true,
      data: data || [],
      hasMore
    };
  } catch (error) {
    console.error('Erro ao buscar despesas:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Buscar métricas de despesas com valores monetários por status
 */
export async function getExpenseMetrics(): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const supabase = await createClient();

    // Buscar estatísticas das despesas com valores monetários
    const { data, error } = await supabase
      .rpc('get_expense_metrics_with_amounts');

    if (error) {
      console.error('Erro ao buscar métricas de despesas:', error);

      // Fallback: calcular métricas manualmente
      const { data: expenses, error: expensesError } = await supabase
        .from('expenses')
        .select('amount, status');

      if (expensesError) {
        return {
          success: false,
          error: expensesError.message
        };
      }

      // Calcular métricas manualmente com valores monetários
      const totalExpenses = expenses?.reduce((sum, expense) => sum + parseFloat(expense.amount), 0) || 0;
      const paidExpenses = expenses?.filter(e => e.status === 'paid').length || 0;
      const pendingExpenses = expenses?.filter(e => e.status === 'pending').length || 0;
      const overdueExpenses = expenses?.filter(e => e.status === 'overdue').length || 0;

      // Calcular valores monetários por status
      const paidAmount = expenses?.filter(e => e.status === 'paid').reduce((sum, expense) => sum + parseFloat(expense.amount), 0) || 0;
      const pendingAmount = expenses?.filter(e => e.status === 'pending').reduce((sum, expense) => sum + parseFloat(expense.amount), 0) || 0;
      const overdueAmount = expenses?.filter(e => e.status === 'overdue').reduce((sum, expense) => sum + parseFloat(expense.amount), 0) || 0;

      return {
        success: true,
        data: {
          totalExpenses,
          paidExpenses,
          pendingExpenses,
          overdueExpenses,
          paidAmount,
          pendingAmount,
          overdueAmount
        }
      };
    }

    return {
      success: true,
      data: data || {
        totalExpenses: 0,
        paidExpenses: 0,
        pendingExpenses: 0,
        overdueExpenses: 0,
        paidAmount: 0,
        pendingAmount: 0,
        overdueAmount: 0
      }
    };
  } catch (error) {
    console.error('Erro ao buscar métricas de despesas:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

// Exportar tipos para uso em outros arquivos
export type { ExpenseFilters, ExpenseSearchResult };
