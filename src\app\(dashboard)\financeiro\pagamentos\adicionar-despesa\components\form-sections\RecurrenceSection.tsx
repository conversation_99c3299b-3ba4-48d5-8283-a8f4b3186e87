'use client';

import { useFormContext } from 'react-hook-form';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Calendar, RotateCcw, AlertCircle } from 'lucide-react';
import { DespesaFormValues, recurrenceFrequencyOptions } from '../../schemas/despesa-schema';
import { Alert, AlertDescription } from '@/components/ui/alert';

export function RecurrenceSection() {
  const form = useFormContext<DespesaFormValues>();
  const isRecurring = form.watch('isRecurring');

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2 mb-6">
        <div className="p-2 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
          <RotateCcw className="h-5 w-5 text-orange-600 dark:text-orange-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Recorrência
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Configure se esta despesa se repete periodicamente
          </p>
        </div>
      </div>

      {/* Aviso sobre funcionalidade em desenvolvimento */}
      <Alert className="border-amber-200 bg-amber-50 dark:bg-amber-900/20 dark:border-amber-800">
        <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
        <AlertDescription className="text-amber-800 dark:text-amber-200">
          <strong>Funcionalidade em desenvolvimento:</strong> A configuração de recorrência está temporariamente desabilitada. 
          Esta funcionalidade será implementada em uma próxima atualização.
        </AlertDescription>
      </Alert>

      {/* Switch para ativar recorrência */}
      <FormField
        control={form.control}
        name="isRecurring"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-800/50">
            <div className="space-y-0.5">
              <FormLabel className="text-base font-medium text-gray-700 dark:text-gray-300">
                Despesa Recorrente
              </FormLabel>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Esta despesa se repete automaticamente
              </div>
            </div>
            <FormControl>
              <Switch
                checked={field.value}
                onCheckedChange={field.onChange}
                disabled={true} // Desabilitado temporariamente
                className="opacity-50"
              />
            </FormControl>
          </FormItem>
        )}
      />

      {/* Configurações de recorrência (visíveis apenas quando ativado) */}
      {isRecurring && (
        <div className="space-y-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800/30 opacity-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Frequência */}
            <FormField
              control={form.control}
              name="recurrenceFrequency"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Frequência
                  </FormLabel>
                  <Select 
                    onValueChange={field.onChange} 
                    defaultValue={field.value}
                    disabled={true}
                  >
                    <FormControl>
                      <SelectTrigger className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                        <SelectValue placeholder="Selecione a frequência" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {recurrenceFrequencyOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Número de repetições */}
            <FormField
              control={form.control}
              name="recurrenceCount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Número de Repetições (Opcional)
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="number"
                      min="1"
                      placeholder="Ex: 12 (deixe vazio para infinito)"
                      disabled={true}
                      className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:border-blue-400 dark:focus:border-blue-500 transition-colors duration-200"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Data de término */}
          <FormField
            control={form.control}
            name="recurrenceEndDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Data de Término (Opcional)
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      {...field}
                      type="date"
                      disabled={true}
                      className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:border-blue-400 dark:focus:border-blue-500 transition-colors duration-200 pl-10"
                    />
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  </div>
                </FormControl>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Se definida, a recorrência para nesta data
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      )}
    </div>
  );
}
