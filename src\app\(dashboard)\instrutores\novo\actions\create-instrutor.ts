'use server';

/**
 * FLUXO DE CRIAÇÃO DE INSTRUTOR - SISTEMA MULTITENANCY
 * 
 * Este arquivo implementa o fluxo completo de criação de um instrutor seguindo as melhores
 * práticas de segurança e integridade de dados em um sistema multitenancy.
 * 
 * FLUXO IMPLEMENTADO:
 * 1. Validação dos dados de entrada com Zod
 * 2. Verificação do tenant_id do usuário atual
 * 3. Validação se a filial (branch_id) pertence ao tenant
 * 4. Verificação de email duplicado no escopo do tenant
 * 5. Criação do usuário no auth.users com app_metadata e user_metadata corretos
 * 6. Upload e processamento do avatar (se fornecido)
 * 7. Criação do registro na tabela public.users
 * 8. Criação do registro na tabela instructors com todos os dados
 * 9. Criação da faixa inicial (se especificada) na tabela instructor_belts
 * 10. Revalidação do cache
 * 
 * CAMPOS MAPEADOS:
 * - auth.users.app_metadata: tenant_id, branch_id, role, provider, email, first_name, last_name, avatar_url, avatar_storage_path
 * - auth.users.user_metadata: first_name, last_name, full_name, phone, email_verified
 * - public.users: id, tenant_id, branch_id, email, role, first_name, last_name, full_name, phone, avatar_url
 * - instructors: user_id, tenant_id, branch_id, dados pessoais, dados profissionais, endereço, contato emergência
 * - instructor_belts: instructor_id, tenant_id, belt_color, degree, awarded_by, awarded_at
 * 
 * SEGURANÇA MULTITENANCY:
 * - Todos os registros incluem tenant_id
 * - Validação de branch_id contra o tenant
 * - Verificação de email único por tenant
 * - Isolamento total entre tenants
 */

import { createAdminClient } from '@/services/supabase/server';
import { novoInstrutorSchema, type NovoInstrutorFormValues } from './schemas/instrutor-schema';
import { syncUserStatusToAuth } from '@/services/user/status-service';
import { revalidatePath } from 'next/cache';
import { v4 as uuidv4 } from 'uuid';
import { extractNameParts } from '@/utils/name-utils';

// Tipos para retorno das funções
interface CreateResult {
  success: boolean;
  errors?: any;
  data?: any;
}

function sanitizePhone(phone: string | null | undefined): string | null {
  if (!phone) return null;
  const digitsOnly = phone.replace(/\D/g, '');
  if (!digitsOnly || digitsOnly === '55') return null;
  return digitsOnly;
}

// Função para gerar senha segura
function generateSecurePassword(length: number = 12): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%&*';
  let password = '';
  for (let i = 0; i < length; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

// Função para upload de avatar base64
async function uploadAvatarFromBase64(base64Data: string, userId: string): Promise<string | null> {
  try {
    const supabase = await createAdminClient();
    
    const matches = base64Data.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);
    if (!matches || matches.length !== 3) {
      return null;
    }
    
    const contentType = matches[1];
    const base64Content = matches[2];
    
    const extension = contentType.includes('jpeg') || contentType.includes('jpg') ? 'jpg' : 
                     contentType.includes('png') ? 'png' : 
                     contentType.includes('webp') ? 'webp' : 'jpg';
    
    const buffer = Buffer.from(base64Content, 'base64');
    const fileName = `${userId}_${Date.now()}.${extension}`;
    
    const { data, error } = await supabase.storage
      .from('avatars')
      .upload(fileName, buffer, {
        contentType,
        upsert: false
      });
    
    if (error) {
      console.error('Erro ao fazer upload do avatar:', error);
      return null;
    }
    
    const { data: { publicUrl } } = supabase.storage
      .from('avatars')
      .getPublicUrl(fileName);
    
    return publicUrl;
  } catch (error) {
    console.error('Erro no upload do avatar:', error);
    return null;
  }
}

// Função para processar avatar
async function processAvatarUrl(avatarUrl: string | null | undefined, userId: string): Promise<string | null> {
  if (!avatarUrl) return null;
  
  if (avatarUrl.startsWith('data:image/')) {
    return await uploadAvatarFromBase64(avatarUrl, userId);
  }
  
  if (avatarUrl.startsWith('http')) {
    return avatarUrl;
  }
  
  return null;
}

// Função para obter tenant do usuário atual
async function getCurrentUserTenant(): Promise<{ tenantId: string; userId: string } | null> {
  const supabase = await createAdminClient();
  
  const { data: authUser } = await supabase.auth.getUser();
  if (!authUser.user) return null;
  
  const { data: userTenant } = await supabase
    .from('users')
    .select('tenant_id')
    .eq('id', authUser.user.id)
    .single();
    
  if (!userTenant?.tenant_id) return null;
  
  return {
    tenantId: userTenant.tenant_id,
    userId: authUser.user.id
  };
}

// Helper to ensure numeric values are sent as strings with 2 decimal places (avoids precision loss)
function toNumericString(value: number | undefined | null): string | null {
  if (value === undefined || value === null || Number.isNaN(value)) return null;
  return value.toFixed(2);
}

// 1. Criar usuário no auth.users
async function createAuthUser(
  validatedData: NovoInstrutorFormValues, 
  tenantId: string, 
  branchId: string
): Promise<{ userId: string; tempPassword: string | null }> {
  const supabase = await createAdminClient();
  
  // Extrair nome e sobrenome usando função utilitária
  const { firstName, lastName, fullName } = extractNameParts(validatedData.full_name);
  
  const sanitizedPhone = sanitizePhone(validatedData.phone);
  const tempPassword = '123456789'; // Senha padrão
  
  const { data: userData, error: userError } = await supabase.auth.admin.createUser({
    email: validatedData.email,
    email_confirm: true,
    password: tempPassword,
    user_metadata: {
      first_name: firstName,
      last_name: lastName,
      full_name: fullName,
      phone: sanitizedPhone,
      email_verified: true
    },
    app_metadata: {
      tenant_id: tenantId,
      branch_id: branchId,
      role: 'instructor',
      status: 'active',
      provider: 'email',
      email: validatedData.email,
      first_name: firstName,
      last_name: lastName,
      avatar_url: null,
      avatar_storage_path: null,
    }
  });
  
  // LOG PARA DEBUG: Verificar metadados criados no auth.users
  if (userData.user) {
    console.log('📊 Metadados salvos no auth.users:');
    console.log('  user_metadata.full_name:', userData.user.user_metadata?.full_name);
    console.log('  app_metadata.first_name:', userData.user.app_metadata?.first_name);
    console.log('  app_metadata.last_name:', userData.user.app_metadata?.last_name);
  }

  if (userError || !userData.user) {
    throw new Error(`Erro ao criar usuário na autenticação: ${userError?.message}`);
  }

  return {
    userId: userData.user.id,
    tempPassword
  };
}

// 2. Criar registro no public.users
async function createPublicUser(
  userId: string,
  validatedData: NovoInstrutorFormValues,
  tenantId: string,
  branchId: string,
  avatarUrl: string | null
): Promise<void> {
  const supabase = await createAdminClient();
  
  // Extrair nome e sobrenome usando função utilitária
  const { firstName, lastName } = extractNameParts(validatedData.full_name);
  const sanitizedPhone = sanitizePhone(validatedData.phone);
  
  const { error } = await supabase
    .from('users')
    .insert({
      id: userId,
      tenant_id: tenantId,
      branch_id: branchId,
      email: validatedData.email,
      role: 'instructor',
      status: 'active',
      first_name: firstName,
      last_name: lastName,
      full_name: validatedData.full_name,
      phone: sanitizedPhone,
      avatar_url: avatarUrl
    });

  if (error) {
    throw new Error(`Erro ao criar registro do usuário: ${error.message}`);
  }
}

// 3. Criar registro do instrutor
async function createInstructor(
  userId: string,
  validatedData: NovoInstrutorFormValues,
  tenantId: string,
  branchId: string
): Promise<string> {
  const supabase = await createAdminClient();
  
  const instructorId = uuidv4();
  
  const hireDate = new Date().toISOString().slice(0, 10);

  const emergencyContactPhoneSanitized = sanitizePhone(validatedData.emergency_contact_phone);

  const emergencyContactPhone = emergencyContactPhoneSanitized ? emergencyContactPhoneSanitized : null;

  const { error } = await supabase
    .from('instructors')
    .insert({
      id: instructorId,
      user_id: userId,
      tenant_id: tenantId,
      branch_id: branchId,
      contract_type: validatedData.contract_type || 'employee',
      // Dados pessoais
      birth_date: validatedData.birth_date || null,
      gender: validatedData.gender || null,
      // Dados profissionais
      experience_years: validatedData.experience_years || 0,
      has_first_aid_certification: validatedData.first_aid_certified || false,
      has_cpr_certification: validatedData.cpr_certified || false,
      has_rules_course: validatedData.cbjj_certified || false,
      has_ibjjf_certification: validatedData.ibjjf_certified || false,
      specialties: validatedData.specialties || [],
      payment_model: validatedData.payment_model || null,
      // Enviar como string para preservar casas decimais no banco
      payment_value: toNumericString(validatedData.hourly_rate ?? validatedData.monthly_salary),
      payment_percentage: toNumericString(validatedData.commission_percentage),
      bio: validatedData.notes || null,
      teaching_notes: validatedData.teaching_notes || null,
      // Endereço
      street: validatedData.street || null,
      street_number: validatedData.street_number || null,
      complement: validatedData.complement || null,
      neighborhood: validatedData.neighborhood || null,
      city: validatedData.city || null,
      state: validatedData.state || null,
      postal_code: validatedData.postal_code || null,
      // Contato de emergência
      emergency_contact_name: validatedData.emergency_contact_name || null,
      emergency_contact_phone: emergencyContactPhone,
      emergency_contact_relationship: validatedData.emergency_contact_relationship || null,
      // Data de contratação
      hire_date: hireDate,
    });

  if (error) {
    throw new Error(`Erro ao criar instrutor: ${error.message}`);
  }

  return instructorId;
}

// 4. Criar faixa do instrutor (se especificada)
async function createInstructorBelt(
  instructorId: string,
  validatedData: NovoInstrutorFormValues,
  tenantId: string,
  awardedBy: string
): Promise<string | null> {
  // Persistir qualquer faixa selecionada
  if (!validatedData.current_belt_level_id) {
    return null;
  }

  const supabase = await createAdminClient();
  const beltId = uuidv4();
  
  const { error } = await supabase
    .from('instructor_belts')
    .insert({
      id: beltId,
      instructor_id: instructorId,
      belt_level_id: validatedData.current_belt_level_id,
      awarded_by: awardedBy,
      awarded_at: new Date().toISOString(),
      tenant_id: tenantId,
    });

  if (error) {
    throw new Error(`Erro ao criar faixa do instrutor: ${error.message}`);
  }

  // Atualizar current_belt_id no instrutor
  await supabase
    .from('instructors')
    .update({ current_belt_id: beltId })
    .eq('id', instructorId);

  return beltId;
}

// Função principal
async function createInstrutorAction(data: NovoInstrutorFormValues): Promise<CreateResult> {
  // Validação
  const validation = novoInstrutorSchema.safeParse(data);
  if (!validation.success) {
    return {
      success: false,
      errors: validation.error.format(),
    };
  }

  const validatedData = validation.data;

  try {
    // Obter tenant do usuário atual
    const currentUser = await getCurrentUserTenant();
    if (!currentUser) {
      return {
        success: false,
        errors: {
          _form: ['Não foi possível identificar o tenant do usuário atual']
        }
      };
    }

    const { tenantId, userId: currentUserId } = currentUser;

    // Verificar se o branch_id é válido para este tenant
    const supabase = await createAdminClient();
    const { data: branch, error: branchError } = await supabase
      .from('branches')
      .select('id')
      .eq('id', validatedData.branch_id)
      .eq('tenant_id', tenantId)
      .single();

    if (branchError || !branch) {
      return {
        success: false,
        errors: {
          branch_id: ['Filial não encontrada ou não pertence ao seu tenant']
        }
      };
    }

    // Verificar se email já existe
    const { data: existingUser } = await supabase
      .from('users')
      .select('id, role')
      .eq('email', validatedData.email)
      .eq('tenant_id', tenantId)
      .single();

    if (existingUser) {
      return {
        success: false,
        errors: {
          email: ['Este email já está cadastrado no sistema']
        }
      };
    }

    // 1. Criar usuário no auth.users
    const { userId, tempPassword } = await createAuthUser(
      validatedData, 
      tenantId, 
      validatedData.branch_id
    );

    // 2. Processar avatar
    const avatarUrl = await processAvatarUrl(validatedData.avatar_url, userId);

    // 3. Atualizar app_metadata com avatar e ID
    if (avatarUrl) {
      await supabase.auth.admin.updateUserById(userId, {
        app_metadata: {
          ...((await supabase.auth.admin.getUserById(userId)).data.user?.app_metadata || {}),
          id: userId,
          avatar_url: avatarUrl,
          avatar_storage_path: avatarUrl.split('/avatars/')[1] || null
        }
      });
    } else {
      // Garantir que o ID esteja definido no app_metadata
      await supabase.auth.admin.updateUserById(userId, {
        app_metadata: {
          ...((await supabase.auth.admin.getUserById(userId)).data.user?.app_metadata || {}),
          id: userId
        }
      });
    }

    // Sincronizar status no auth.users usando o serviço centralizado
    await syncUserStatusToAuth(userId, 'active');

    // 4. Criar registro no public.users
    await createPublicUser(userId, validatedData, tenantId, validatedData.branch_id, avatarUrl);

    // 5. Criar instrutor
    const instructorId = await createInstructor(
      userId, 
      validatedData, 
      tenantId, 
      validatedData.branch_id
    );

    // 6. Criar faixa (se especificada)
    await createInstructorBelt(instructorId, validatedData, tenantId, currentUserId);

    // Revalidar cache
    revalidatePath('/instrutores');
    revalidatePath('/dashboard');

    return {
      success: true,
      data: {
        instructorId,
        userId,
        message: `Instrutor criado com sucesso! Senha temporária: ${tempPassword}`,
        temporaryPassword: tempPassword
      }
    };

  } catch (error) {
    console.error('Erro ao criar instrutor:', error);
    return {
      success: false,
      errors: {
        _form: [error instanceof Error ? error.message : 'Erro inesperado ao criar instrutor']
      }
    };
  }
}

export const createInstrutor = createInstrutorAction;