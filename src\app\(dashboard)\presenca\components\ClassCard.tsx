'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Clock, 
  Users, 
  MapPin, 
  MoreVertical, 
  Play, 
  Pause, 
  CheckCircle, 
  AlertCircle,
  Calendar,
  User,
  Unlock
} from 'lucide-react';
import { motion } from 'framer-motion';
import Link from 'next/link';

interface ClassCardProps {
  id: string;
  title: string;
  instructor: {
    name: string;
    avatar?: string;
  };
  time: string;
  duration: number;
  enrolledCount: number;
  maxCapacity: number | null;
  location: string;
  status: 'ongoing' | 'upcoming' | 'completed' | 'cancelled';
  startDate: string;
  description?: string;
  isOpenClass?: boolean;
}

export function ClassCard({ 
  id, 
  title, 
  instructor, 
  time, 
  duration, 
  location, 
  enrolledCount, 
  maxCapacity, 
  status,
  startDate,
  description,
  isOpenClass = false
}: ClassCardProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'ongoing':
        return {
          label: 'Em andamento',
          color: 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800',
          icon: <Play className="h-3 w-3" />,
          pulse: true
        };
      case 'upcoming':
      case 'scheduled':
        return {
          label: 'Próxima',
          color: 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800',
          icon: <Clock className="h-3 w-3" />,
          pulse: false
        };
      case 'completed':
        return {
          label: 'Concluída',
          color: 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-400 border-gray-200 dark:border-gray-600',
          icon: <CheckCircle className="h-3 w-3" />,
          pulse: false
        };
      case 'cancelled':
        return {
          label: 'Cancelada',
          color: 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 border-red-200 dark:border-red-800',
          icon: <AlertCircle className="h-3 w-3" />,
          pulse: false
        };
      default:
        return {
          label: 'Agendada',
          color: 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800',
          icon: <Clock className="h-3 w-3" />,
          pulse: false
        };
    }
  };

  const statusConfig = getStatusConfig(status);
  const isFullyBooked = maxCapacity ? enrolledCount >= maxCapacity : false;
  const occupancyPercentage = maxCapacity ? Math.round((enrolledCount / maxCapacity) * 100) : 0;

  const getInstructorInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getOccupancyColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600 dark:text-red-400';
    if (percentage >= 70) return 'text-orange-600 dark:text-orange-400';
    return 'text-green-600 dark:text-green-400';
  };

  const getAttendanceDisplay = () => {
    if (isOpenClass) {
      return (
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-slate-400 dark:text-gray-500 flex-shrink-0" />
          <div className="flex-1">
            <div className="flex items-center justify-between text-sm">
              <span className="text-slate-600 dark:text-gray-400">
                {enrolledCount} aluno{enrolledCount !== 1 ? 's' : ''}
              </span>
              <Badge variant="outline" className="text-xs bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-400 border-purple-200 dark:border-purple-800">
                <Unlock className="h-3 w-3 mr-1" />
                Livre
              </Badge>
            </div>
            <p className="text-xs text-slate-500 dark:text-gray-400 mt-1">
              Sem limite de capacidade
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className="flex items-center gap-2">
        <Users className="h-4 w-4 text-slate-400 dark:text-gray-500 flex-shrink-0" />
        <div className="flex-1">
          <div className="flex items-center justify-between text-sm">
            <span className="text-slate-600 dark:text-gray-400">
              {maxCapacity ? `${enrolledCount}/${maxCapacity} alunos` : `${enrolledCount} alunos`}
            </span>
            {maxCapacity && (
              <span className={`text-xs font-medium ${getOccupancyColor(occupancyPercentage)}`}>
                {occupancyPercentage}%
              </span>
            )}
          </div>
          {maxCapacity && (
            <div className="mt-1 w-full bg-slate-100 dark:bg-gray-700 rounded-full h-1.5">
              <motion.div
                className={`h-1.5 rounded-full ${
                  occupancyPercentage >= 90 
                    ? 'bg-red-500' 
                    : occupancyPercentage >= 70 
                      ? 'bg-orange-500' 
                      : 'bg-green-500'
                }`}
                initial={{ width: 0 }}
                animate={{ width: `${occupancyPercentage}%` }}
                transition={{ duration: 1, delay: 0.2 }}
              />
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ 
        scale: 1.02,
        transition: { duration: 0.2 }
      }}
      className="h-full"
    >
      <Card className={`bg-white dark:bg-gray-900 border ${
        isOpenClass 
          ? 'border-purple-200 dark:border-purple-800' 
          : 'border-slate-200 dark:border-gray-700'
      } shadow-sm hover:shadow-md dark:hover:shadow-lg transition-all duration-200 h-full`}>
        <CardContent className="p-5">
          <div className="flex flex-col h-full">
            
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1 space-y-2">
                <div className="flex items-center gap-2">
                  {isOpenClass && (
                    <Unlock className="h-4 w-4 text-purple-600 dark:text-purple-400 flex-shrink-0" />
                  )}
                  <h3 className="font-semibold text-slate-900 dark:text-gray-100 text-lg leading-tight">
                    {title}
                  </h3>
                  {statusConfig.pulse && (
                    <motion.div
                      animate={{ 
                        scale: [1, 1.2, 1],
                        opacity: [1, 0.7, 1]
                      }}
                      transition={{ 
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                      className="w-2 h-2 bg-green-500 rounded-full"
                    />
                  )}
                </div>
                
                <div className="flex items-center gap-2 flex-wrap">
                  <Badge 
                    variant="outline" 
                    className={`${statusConfig.color} text-xs font-medium w-fit flex items-center gap-1`}
                  >
                    {statusConfig.icon}
                    {statusConfig.label}
                  </Badge>
                  
                  {isOpenClass && (
                    <Badge variant="outline" className="text-xs bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-400 border-purple-200 dark:border-purple-800">
                      Aula Livre
                    </Badge>
                  )}
                </div>
              </div>
              
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-8 w-8 p-0 text-slate-400 dark:text-gray-500 hover:text-slate-600 dark:hover:text-gray-300 hover:bg-slate-50 dark:hover:bg-gray-800"
              >
                <MoreVertical className="h-4 w-4" />
              </Button>
            </div>

            {/* Instructor */}
            <div className="flex items-center gap-3 mb-4">
              <Avatar className="h-9 w-9 border-2 border-slate-100 dark:border-gray-700">
                <AvatarImage src={instructor.avatar} alt={instructor.name} />
                <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-sm font-medium">
                  {getInstructorInitials(instructor.name)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-slate-900 dark:text-gray-100 text-sm truncate">
                  {instructor.name}
                </p>
                <p className="text-xs text-slate-500 dark:text-gray-400">Instrutor</p>
              </div>
            </div>

            {/* Details */}
            <div className="space-y-3 mb-4 flex-1">
              <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-gray-400">
                <Calendar className="h-4 w-4 flex-shrink-0" />
                <span className="truncate">{startDate}</span>
              </div>
              
              <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-gray-400">
                <Clock className="h-4 w-4 flex-shrink-0" />
                <span>{time} • {duration}min</span>
              </div>
              
              <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-gray-400">
                <MapPin className="h-4 w-4 flex-shrink-0" />
                <span className="truncate">{location}</span>
              </div>
              
              {/* Attendance Display */}
              {getAttendanceDisplay()}

              {description && (
                <p className="text-xs text-slate-500 dark:text-gray-400 line-clamp-2">
                  {description}
                </p>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2 pt-3 border-t border-slate-100 dark:border-gray-700">
              <Link href={`/presenca/${id}`} className="flex-1">
                <Button 
                  size="sm" 
                  className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700 text-white"
                >
                  Ver Detalhes
                </Button>
              </Link>
              
              {status === 'ongoing' && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="border-slate-200 dark:border-gray-600 text-slate-600 dark:text-gray-400 hover:bg-slate-50 dark:hover:bg-gray-800"
                  title="Pausar aula"
                >
                  <Pause className="h-4 w-4" />
                </Button>
              )}
              
              {(status === 'upcoming' || status === 'scheduled') && (
                <Button 
                  variant="outline" 
                  size="sm"
                  className="border-slate-200 dark:border-gray-600 text-slate-600 dark:text-gray-400 hover:bg-slate-50 dark:hover:bg-gray-800"
                  title="Gerenciar alunos"
                >
                  <User className="h-4 w-4" />
                </Button>
              )}
            </div>

            {/* Full booking warning */}
            {isFullyBooked && status !== 'completed' && !isOpenClass && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.3 }}
                className="mt-3 p-2 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-md"
              >
                <div className="flex items-center gap-2 text-xs text-orange-700 dark:text-orange-400">
                  <AlertCircle className="h-3 w-3" />
                  <span>Turma lotada</span>
                </div>
              </motion.div>
            )}

            {/* Open class info */}
            {isOpenClass && status !== 'completed' && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.3 }}
                className="mt-3 p-2 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-md"
              >
                <div className="flex items-center gap-2 text-xs text-purple-700 dark:text-purple-400">
                  <Unlock className="h-3 w-3" />
                  <span>Aula aberta para todos os alunos</span>
                </div>
              </motion.div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
} 