import { beltLevels, categories } from './constants';

// Função para normalizar data para formato YYYY-MM-DD preservando a data local
export const normalizeDateString = (dateInput: string | Date | null | undefined): string => {
  if (!dateInput) return '';
  
  try {
    let date: Date;
    
    if (typeof dateInput === 'string') {
      // Se já está no formato YYYY-MM-DD, retornar como está
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateInput)) {
        return dateInput;
      }
      // Tentar parsear a string
      date = new Date(dateInput);
    } else {
      date = dateInput;
    }
    
    if (isNaN(date.getTime())) {
      return '';
    }
    
    // Para evitar problemas de timezone, vamos garantir que usamos a data local
    // Criar uma nova data ajustada para o timezone local
    const adjustedDate = new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
    
    // Usar toISOString e pegar apenas a parte da data (YYYY-MM-DD)
    const isoString = adjustedDate.toISOString();
    const dateOnly = isoString.split('T')[0];
    
    // Verificar se o formato está correto
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateOnly)) {
      return dateOnly;
    }
    
    // Fallback manual usando os métodos locais
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('Erro ao normalizar data:', error);
    return '';
  }
};

// Função para obter a data atual no timezone de Brasília
export const getBrazilianDate = (): Date => {
  const now = new Date();
  const formatter = new Intl.DateTimeFormat('en-CA', {
    timeZone: 'America/Sao_Paulo',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
  
  const parts = formatter.formatToParts(now);
  const year = parseInt(parts.find(p => p.type === 'year')?.value || '0');
  const month = parseInt(parts.find(p => p.type === 'month')?.value || '0') - 1; // 0-indexed
  const day = parseInt(parts.find(p => p.type === 'day')?.value || '0');
  
  return new Date(year, month, day);
};

// Função para obter a data atual no formato YYYY-MM-DD (timezone Brasília)
export const getTodayDateString = (): string => {
  const now = new Date();
  const formatter = new Intl.DateTimeFormat('en-CA', {
    timeZone: 'America/Sao_Paulo'
  });
  
  return formatter.format(now); // Retorna no formato YYYY-MM-DD
};

// Função para debug de datas (apenas em desenvolvimento)
export const debugDateValidation = (dateString: string): void => {
  if (process.env.NODE_ENV === 'development') {
    console.log('=== DEBUG DATE VALIDATION ===');
    console.log('Input date string:', dateString);
    console.log('Today string:', getTodayDateString());
    console.log('Is valid?:', isDateTodayOrFuture(dateString));
    console.log('=============================');
  }
};

// Função para comparar datas considerando timezone de Brasília
export const isDateTodayOrFuture = (dateString: string): boolean => {
  if (!dateString) return false;
  
  try {
    // Normalizar a data de entrada
    const normalizedDate = normalizeDateString(dateString);
    if (!normalizedDate) return false;
    
    // Obter data atual no formato YYYY-MM-DD no timezone de Brasília
    const todayString = getTodayDateString();
    
    // Debug em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      console.log('Date validation:', {
        input: dateString,
        normalized: normalizedDate,
        today: todayString,
        isValid: normalizedDate >= todayString
      });
    }
    
    // Comparação simples de strings no formato YYYY-MM-DD
    return normalizedDate >= todayString;
  } catch (error) {
    console.error('Erro ao validar data:', error);
    return false;
  }
};

// Função para validar se data de término é posterior à data de início
export const isEndDateAfterStartDate = (startDate: string, endDate: string): boolean => {
  if (!startDate || !endDate) return true; // Se não há data de término, é válido
  
  try {
    const normalizedStart = normalizeDateString(startDate);
    const normalizedEnd = normalizeDateString(endDate);
    
    if (!normalizedStart || !normalizedEnd) return false;
    
    const result = normalizedEnd > normalizedStart;
    
    // Debug em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      console.log('End date validation:', {
        startDate: normalizedStart,
        endDate: normalizedEnd,
        isValid: result
      });
    }
    
    return result;
  } catch (error) {
    console.error('Erro ao validar data de término:', error);
    return false;
  }
};

// Função para formatar valores no preview
export const formatPreviewValue = (value: any, type?: string): string => {
  if (value === null || value === undefined) return 'Não definido';
  
  switch (type) {
    case 'category':
      const category = categories.find(c => c.value === value);
      return category ? category.label : 'Não definida';
    
    case 'belt':
      const belt = beltLevels.find(b => b.value === value);
      return belt ? belt.label : 'Não definida';
    
    case 'date':
      return value ? new Date(value).toLocaleDateString('pt-BR') : 'Não definida';
    
    case 'boolean':
      return value ? '✅ Sim' : '❌ Não';
    
    case 'capacity':
      return value === null || value === '' ? 'Ilimitada' : `${value} alunos`;
    
    case 'age':
      return `${value} anos`;
    
    case 'status':
      return value ? '🟢 Ativa' : '🔴 Inativa';
    
    case 'waitlist':
      return value ? '✅ Permitida' : '❌ Não permitida';
    
    default:
      return String(value);
  }
}; 