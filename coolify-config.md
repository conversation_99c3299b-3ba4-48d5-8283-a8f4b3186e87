# Configuração do Traefik no Coolify

```
version: '3.8'
networks:
  coolify:
    external: true
services:
  traefik:
    container_name: coolify-proxy
    image: 'traefik:v3.1'
    restart: unless-stopped
    environment:
      - CF_DNS_API_TOKEN=95f528d0838cf14cf7a3a84ef84ea4c671ca5 # Replace with your API token
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    networks:
      - coolify
    ports:
      - '80:80'
      - '443:443'
      - '443:443/udp'
      - '8080:8080'
    healthcheck:
      test: 'wget -qO- http://localhost:80/ping || exit 1'
      interval: 4s
      timeout: 2s
      retries: 5
    volumes:
      - '/var/run/docker.sock:/var/run/docker.sock:ro'
      - '/data/coolify/proxy:/traefik'
    command:
      - '--ping=true'
      - '--ping.entrypoint=http'
      - '--api.dashboard=true'
      - '--api.insecure=false'
      - '--entrypoints.http.address=:80'
      - '--entrypoints.https.address=:443'
      - '--entrypoints.http.http.encodequerysemicolons=true'
      - '--entryPoints.http.http2.maxConcurrentStreams=50'
      - '--entrypoints.https.http.encodequerysemicolons=true'
      - '--entryPoints.https.http2.maxConcurrentStreams=50'
      - '--entrypoints.https.http3'
      - '--providers.docker.exposedbydefault=false'
      - '--providers.file.directory=/traefik/dynamic/'
      - '--providers.file.watch=true'
      - '--certificatesresolvers.letsencrypt.acme.dnschallenge.provider=cloudflare' # Use Cloudflare for DNS challenge
      - '--certificatesresolvers.letsencrypt.acme.dnschallenge.delaybeforecheck=0'
      - '--certificatesresolvers.letsencrypt.acme.storage=/traefik/acme.json'
      - '--providers.docker=true'
    labels:
      - traefik.enable=true
      - traefik.http.routers.traefik.entrypoints=http
      - traefik.http.routers.traefik.service=api@internal
      - traefik.http.routers.traefik.tls.certresolver=letsencrypt
      - traefik.http.routers.traefik.tls.domains[0].main=sondtheanime.site # Replace with your main domain
      - traefik.http.routers.traefik.tls.domains[0].sans=*.sondtheanime.site # Replace with your wildcard domain
      - traefik.http.services.traefik.loadbalancer.server.port=8080
      - traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https
      - traefik.http.middlewares.gzip.compress=true
      - coolify.managed=true
      - coolify.proxy=true
```

# Dynamic Configurations

## File: Caddyfile

```
import /dynamic/*.caddy
```

## File: cloudflare-origin-certs.yaml

```
tls:
  certificates:
    -
      certFile: /traefik/certs/sondtheanime.cert
      keyFile: /traefik/certs/sondtheanime.key
```

## File: coolify.yaml

```
# This file is automatically generated by Coolify.
# Do not edit it manually (only if you know what are you doing).

http:
  middlewares:
    redirect-to-https:
      redirectscheme:
        scheme: https
    gzip:
      compress: true
  routers:
    coolify-http:
      middlewares:
        - redirect-to-https
      entryPoints:
        - http
      service: coolify
      rule: Host(`coolify.sondtheanime.site`)
    coolify-realtime-ws:
      entryPoints:
        - http
      service: coolify-realtime
      rule: 'Host(`coolify.sondtheanime.site`) && PathPrefix(`/app`)'
    coolify-terminal-ws:
      entryPoints:
        - http
      service: coolify-terminal
      rule: 'Host(`coolify.sondtheanime.site`) && PathPrefix(`/terminal/ws`)'
    coolify-https:
      entryPoints:
        - https
      service: coolify
      rule: Host(`coolify.sondtheanime.site`)
      tls:
        certresolver: letsencrypt
    coolify-realtime-wss:
      entryPoints:
        - https
      service: coolify-realtime
      rule: 'Host(`coolify.sondtheanime.site`) && PathPrefix(`/app`)'
      tls:
        certresolver: letsencrypt
    coolify-terminal-wss:
      entryPoints:
        - https
      service: coolify-terminal
      rule: 'Host(`coolify.sondtheanime.site`) && PathPrefix(`/terminal/ws`)'
      tls:
        certresolver: letsencrypt
  services:
    coolify:
      loadBalancer:
        servers:
          -
            url: 'http://coolify:8080'
    coolify-realtime:
      loadBalancer:
        servers:
          -
            url: 'http://coolify-realtime:6001'
    coolify-terminal:
      loadBalancer:
        servers:
          -
            url: 'http://coolify-realtime:6002'

```

## File: default_redirect_503.yaml

```
# This file is generated by Coolify, do not edit it manually.
# Disable the default redirect to customize (only if you know what are you doing).

http:
  routers:
    catchall:
      entryPoints:
        - http
        - https
      service: noop
      rule: PathPrefix(`/`)
      tls:
        certResolver: letsencrypt
      priority: -1000
  services:
    noop:
      loadBalancer:
        servers: {  }
```
## File: traefik-dashboard.yaml

```
http:
  middlewares:
    auth:
      basicAuth:
        users:
          - 'sondtheanime:Coldthecold1@'
    redirect-to-https:
      redirectScheme:
        scheme: https
  routers:
    dashboard-http:
      rule: 'Host(`traefik.sondtheanime.site`) && (PathPrefix(`/dashboard`) || PathPrefix(`/api`))'
      entryPoints:
        - http
      service: api@internal
      middlewares:
        - redirect-to-https
    dashboard-https:
      rule: 'Host(`traefik.sondtheanime.site`) && (PathPrefix(`/dashboard`) || PathPrefix(`/api`))'
      entryPoints:
        - https
      service: api@internal
      tls:
        certResolver: letsencrypt
      middlewares:
        - auth
```

# Server - Coolify

**Status:** 🟢 Proxy Running  
**Servidor:** `localhost`

---

## ⚙️ Configuração

### General
- **Name:** `localhost`
- **Description:** _This is the server where Coolify is running on. Don't delete this!_
- **Wildcard Domain:** [`https://sondtheanime.site`](https://sondtheanime.site)

---

### Ações Disponíveis
- 🔁 **Restart Proxy**
- ⛔ **Stop Proxy**

---

## 🧭 Outras Abas
- Proxy
- Resources
- Terminal
- Security
