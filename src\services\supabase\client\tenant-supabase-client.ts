import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/services/supabase/types/database.types';
import { TenantIdentifier } from '@/services/tenant/types';
import { TenantExtractorClient } from '@/services/tenant/tenant-extractor-client';
import { TenantClientFactory } from '../shared/tenant-utils';
import { createClient } from './client';

let browserClient: SupabaseClient<Database> | null = null;

export const createTenantBrowserClient = (): SupabaseClient<Database> => {
  if (browserClient) return browserClient;
  
  const supabase = createClient() as SupabaseClient<Database>;

  const emptyTenant: TenantIdentifier = { id: null, slug: null };
  
  const factory = new TenantClientFactory();
  browserClient = factory.createProxy(supabase, emptyTenant);
  
  if (typeof window !== 'undefined') {
    initializeTenantInBackground(browserClient);
  }
  
  return browserClient;
};

// Inicializar tenant em background para não bloquear a interface
async function initializeTenantInBackground(supabase: SupabaseClient<Database>) {
  try {
    // Usar a versão cliente do extrator
    const tenantExtractor = new TenantExtractorClient();
    const slug = tenantExtractor.extractTenantSlug();
    
    // Se não tiver slug, não há nada para fazer
    if (!slug) return;
    
    // Buscar ID do tenant via API
    const response = await fetch(
      `/api/tenant/${slug}/id`,
      { credentials: 'same-origin' }
    );
    
    if (!response.ok) return;
    
    const { id } = await response.json();
    if (id) {
      localStorage.setItem('tenant-id', id);
      localStorage.setItem('tenant-slug', slug);
    }
  } catch (error) {
    console.error('Erro ao inicializar tenant:', error);
  }
}
