'use server';

import { ClientFilters } from "./client-filters";
import { InstructorFilterState, Branch } from "../../types/types";
import { getBranches } from "./server-branches";

interface ServerFiltersProps {
  filters: InstructorFilterState;
  onFilterChange: (filters: InstructorFilterState) => void;
}

export async function ServerFilters({ filters, onFilterChange }: ServerFiltersProps) {
  const branches = await getBranches();
  
  return (
    <ClientFilters 
      branches={branches} 
    />
  );
} 