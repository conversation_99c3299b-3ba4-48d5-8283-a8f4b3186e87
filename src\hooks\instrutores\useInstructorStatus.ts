'use client';

import { useUserStatus, useToggleUserStatus, useBulkUserStatus, type UserStatus } from '../user/useUserStatus';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Hook específico para gerenciar status de instrutores
 * Usa o status centralizado da tabela users mas mantém compatibilidade
 * com a interface existente de instrutores
 */
export function useInstructorStatus() {
  const userStatusHook = useUserStatus();
  const queryClient = useQueryClient();

  const updateInstructorStatus = async (instructorUserId: string, status: UserStatus) => {
    const result = await userStatusHook.updateStatus(instructorUserId, status);
    
    // Invalidar caches específicos de instrutores
    if (result?.success) {
      queryClient.invalidateQueries({
        queryKey: ['instructors']
      });
      queryClient.invalidateQueries({
        queryKey: ['instructor-stats']
      });
    }
    
    return result;
  };

  const getInstructorStatus = async (instructorUserId: string) => {
    return await userStatusHook.getStatus(instructorUserId);
  };

  return {
    updateInstructorStatus,
    getInstructorStatus,
    isUpdating: userStatusHook.isUpdating,
    error: userStatusHook.error,
    isSuccess: userStatusHook.isSuccess,
    reset: userStatusHook.reset
  };
}

/**
 * Hook para toggle de status de instrutor (ativo/inativo)
 */
export function useToggleInstructorStatus() {
  const toggleHook = useToggleUserStatus();
  const queryClient = useQueryClient();

  const toggleInstructorStatus = async (instructorUserId: string, currentStatus: UserStatus) => {
    const result = await toggleHook.toggleStatus(instructorUserId, currentStatus);
    
    // Invalidar caches específicos de instrutores
    queryClient.invalidateQueries({
      queryKey: ['instructors']
    });
    queryClient.invalidateQueries({
      queryKey: ['instructor-stats']
    });
    
    return result;
  };

  return {
    toggleInstructorStatus,
    isToggling: toggleHook.isToggling,
    error: toggleHook.error
  };
}

/**
 * Hook para operações em lote com instrutores
 */
export function useBulkInstructorStatus() {
  const bulkHook = useBulkUserStatus();
  const queryClient = useQueryClient();

  const updateBulkInstructorStatus = async (instructorUserIds: string[], status: UserStatus) => {
    const result = await bulkHook.updateBulkStatus(instructorUserIds, status);
    
    // Invalidar caches específicos de instrutores
    queryClient.invalidateQueries({
      queryKey: ['instructors']
    });
    queryClient.invalidateQueries({
      queryKey: ['instructor-stats']
    });
    
    return result;
  };

  return {
    updateBulkInstructorStatus,
    isUpdating: bulkHook.isUpdating
  };
}

/**
 * Hook para mapear status para formato compatível com UI existente
 */
export function useInstructorStatusMapping() {
  const mapStatusToIsActive = (status: UserStatus): boolean => {
    return status === 'active';
  };

  const mapIsActiveToStatus = (isActive: boolean): UserStatus => {
    return isActive ? 'active' : 'inactive';
  };

  const getStatusLabel = (status: UserStatus): string => {
    const labels: Record<UserStatus, string> = {
      active: 'Ativo',
      inactive: 'Inativo',
      suspended: 'Suspenso'
    };
    return labels[status] || 'Desconhecido';
  };

  const getStatusColor = (status: UserStatus): string => {
    const colors: Record<UserStatus, string> = {
      active: 'text-green-600',
      inactive: 'text-gray-600',
      suspended: 'text-red-600'
    };
    return colors[status] || 'text-gray-600';
  };

  const getStatusBadgeVariant = (status: UserStatus): 'default' | 'secondary' | 'destructive' => {
    const variants: Record<UserStatus, 'default' | 'secondary' | 'destructive'> = {
      active: 'default',
      inactive: 'secondary',
      suspended: 'destructive'
    };
    return variants[status] || 'secondary';
  };

  return {
    mapStatusToIsActive,
    mapIsActiveToStatus,
    getStatusLabel,
    getStatusColor,
    getStatusBadgeVariant
  };
} 