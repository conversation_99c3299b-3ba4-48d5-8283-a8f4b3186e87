# 🐳 Docker Setup para ApexSaas

## Configuração Standalone

Este projeto usa `output: 'standalone'` do Next.js para otimizar a execução em contêineres. Isso significa que:

- A aplicação é compilada como um servidor independente
- Não é necessário instalar dependências na imagem final
- O comando de inicialização é `node server.js` ao invés de `npm start`

## Targets Disponíveis

### Development
- **Target**: `development`
- **Comando**: `npm run dev`
- **Uso**: Desenvolvimento local com hot reload
- **Porto**: 3000

### Production
- **Target**: `production`
- **Comando**: `node server.js`
- **Uso**: Produção otimizada
- **Porto**: 3000

## 🚀 Como usar

### Desenvolvimento

```bash
# Iniciar em modo desenvolvimento
docker-compose up apexsaas-dev

# Com rebuild
docker-compose up --build apexsaas-dev
```

### Produção

```bash
# Iniciar em modo produção
docker-compose up apexsaas-prod

# Com rebuild
docker-compose up --build apexsaas-prod
```

### Com Nginx (completo)

```bash
# Iniciar ambiente completo (app + nginx)
docker-compose up

# Parar todos os serviços
docker-compose down
```

## 📁 Estrutura de arquivos

```
/app/
├── .next/standalone/     # Servidor standalone do Next.js
├── .next/static/        # Assets estáticos
├── public/              # Arquivos públicos
└── server.js           # Ponto de entrada do servidor
```

## 🔧 Otimizações

- **Multi-stage build** para reduzir tamanho da imagem final
- **Dependencies caching** para builds mais rápidos
- **Non-root user** para segurança
- **Standalone output** para melhor performance

## 🐛 Troubleshooting

### Erro: "next start" does not work with "output: standalone"
**Solução**: Use `node server.js` ao invés de `npm start`

### Build falha por falta de memória
**Solução**: Aumente a memória disponível para o Docker

### Hot reload não funciona
**Solução**: Certifique-se de usar o target `development` e que as variáveis de polling estão definidas

## 📝 Logs

```bash
# Ver logs do container de desenvolvimento
docker-compose logs -f apexsaas-dev

# Ver logs do container de produção
docker-compose logs -f apexsaas-prod
```

# Configuração do Docker para ApexSaaS

Este documento descreve como executar a aplicação ApexSaaS usando Docker, permitindo o funcionamento do sistema multi-tenant para academias de jiu-jitsu.

## Pré-requisitos

- Docker instalado (versão 20.10.0 ou superior)
- Docker Compose instalado (versão 2.0.0 ou superior)
- Acesso ao Supabase (URL e chave anônima)

## Estrutura de Arquivos

- `Dockerfile`: Configuração para construir a imagem Docker
- `docker-compose.yml`: Orquestração dos serviços
- `nginx/default.conf`: Configuração do Nginx para subdomínios
- `docker-setup.sh`: Script para facilitar a configuração inicial no Linux/macOS
- `docker-setup.ps1`: Script para facilitar a configuração inicial no Windows

## Configuração Inicial

### No Linux/macOS

Execute o script de configuração:

```bash
chmod +x docker-setup.sh
./docker-setup.sh
```

### No Windows (PowerShell)

Execute o script de configuração como administrador:

```powershell
# Primeiro, permita a execução de scripts se necessário
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process

# Execute o script
.\docker-setup.ps1
```

> **Nota**: Para configurar corretamente os subdomínios no Windows, execute o PowerShell como administrador.

## Configuração do Ambiente

Após executar o script de configuração, edite o arquivo `.env` com suas informações do Supabase:

```
NEXT_PUBLIC_SUPABASE_URL=https://seu-projeto.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=sua-chave-anonima-do-supabase
NEXT_PUBLIC_BASE_DOMAIN=localhost:3000
```

## Acessando a Aplicação

Você pode acessar a aplicação de duas formas:

- **Acesso direto**: http://localhost:3000 (via porta exposta do container Next.js)
- **Acesso via Nginx**: http://localhost (via proxy Nginx)
- **Subdomínios de tenant**: http://[nome-da-academia].localhost (via proxy Nginx)

> **Nota**: Para desenvolvimento, recomendamos usar http://localhost:3000 diretamente, pois isso corresponde à configuração da aplicação Next.js e facilita o acesso a recursos como hot-reload.

## Configuração de Subdomínios Locais

Para que os subdomínios funcionem corretamente no ambiente de desenvolvimento local:

### No Windows

1. Execute o PowerShell como administrador
2. O script `docker-setup.ps1` tentará adicionar automaticamente os subdomínios ao arquivo hosts
3. Caso não consiga, você precisará editar manualmente o arquivo `C:\Windows\System32\drivers\etc\hosts`:
```
127.0.0.1 localhost
127.0.0.1 academia1.localhost
127.0.0.1 academia2.localhost
# Adicione mais subdomínios conforme necessário
```

### No macOS/Linux

1. Edite o arquivo `/etc/hosts` como superusuário
2. Adicione as seguintes linhas:
```
127.0.0.1 localhost
127.0.0.1 academia1.localhost
127.0.0.1 academia2.localhost
# Adicione mais subdomínios conforme necessário
```

## Solução de Problemas

### Erro de permissão no Windows

Se você encontrar erros de permissão ao executar os scripts ou containers:

1. Execute o PowerShell como administrador
2. Verifique se o Docker Desktop está em execução
3. Certifique-se de que a virtualização está habilitada no BIOS

### Não é possível acessar os subdomínios

Certifique-se de:
1. Ter configurado corretamente o arquivo hosts
2. O Nginx está rodando (`docker compose ps`)
3. As variáveis de ambiente estão configuradas corretamente

### Erro de conexão com o Supabase

Verifique:
1. Se as variáveis `NEXT_PUBLIC_SUPABASE_URL` e `NEXT_PUBLIC_SUPABASE_ANON_KEY` estão corretas
2. Se o projeto do Supabase está ativo e acessível

## Considerações de Segurança para Produção

Ao implantar em produção, considere:
1. Utilizar HTTPS com certificados SSL
2. Ajustar as configurações do Nginx para seu domínio de produção
3. Implementar rate limiting e outras proteções de segurança 