'use client'

import { useEffect, useState } from 'react'
import { TenantExtractorClient } from '@/services/tenant/tenant-extractor-client'
import { slides as defaultSlides } from '@/components/auth/carousel/data'
import { Slide } from '@/components/auth/carousel/types'
import { useTenantTheme } from './use-tenant-theme'

export function useTenantCarousel() {
  const { description } = useTenantTheme()
  const [carouselData, setCarouselData] = useState<Slide[]>(defaultSlides)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function fetchCarouselData() {
      try {
        // Primeiro, tenta usar os dados do contexto se disponíveis
        if (description) {
          try {
            const parsedData = JSON.parse(description)
            if (Array.isArray(parsedData) && parsedData.length > 0) {
              setCarouselData(parsedData)
              setIsLoading(false)
              return
            }
          } catch (error) {
            console.error('Erro ao processar dados do carrossel do contexto:', error)
          }
        }

        // Fallback para requisição HTTP se não tiver dados no contexto
        const extractor = new TenantExtractorClient()
        const slug = extractor.extractTenantSlug()
        
        if (!slug) {
          setCarouselData(defaultSlides)
          return
        }

        const response = await fetch(
          `/api/tenant/${slug}/carousel`,
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )

        if (!response.ok) {
          throw new Error('Falha ao carregar dados do carrossel')
        }

        const data = await response.json()
        if (Array.isArray(data) && data.length > 0) {
          setCarouselData(data)
        } else {
          setCarouselData(defaultSlides)
        }
      } catch (error) {
        console.error('Erro ao obter dados do carrossel:', error)
        setCarouselData(defaultSlides)
      } finally {
        setIsLoading(false)
      }
    }

    fetchCarouselData()
  }, [description])

  return { carouselData, isLoading }
} 