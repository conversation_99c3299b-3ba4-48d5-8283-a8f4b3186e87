import { CreateClassFormData } from '../schema';



export const calculateDuration = (startTime: string, endTime: string) => {
  if (startTime && endTime) {
    // Usar parsing mais robusto de datas
    const startDate = new Date(startTime);
    const endDate = new Date(endTime);
    
    // Verificar se as datas são válidas
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return null;
    }
    
    const diffInMinutes = (endDate.getTime() - startDate.getTime()) / (1000 * 60);
    
    if (diffInMinutes > 0) {
      const hours = Math.floor(diffInMinutes / 60);
      const minutes = Math.round(diffInMinutes % 60);
      
      if (hours > 0) {
        return {
          text: `${hours}h${minutes > 0 ? ` ${minutes}min` : ''}`,
          minutes: diffInMinutes
        };
      }
      return {
        text: `${minutes}min`,
        minutes: diffInMinutes
      };
    }
  }
  return null;
};

export const calculateFormProgress = (formValues: CreateClassFormData) => {
  const requiredFields = ['name', 'instructor_id', 'branch_id', 'start_time', 'end_time'];
  const filledFields = requiredFields.filter(field => formValues[field as keyof CreateClassFormData]);
  return (filledFields.length / requiredFields.length) * 100;
};

export const isMobile = () => {
  return typeof window !== 'undefined' && window.innerWidth < 768;
};

export const handleNetworkError = (error: any, setSubmitError: (error: string | null) => void) => {
  console.error('Erro de rede:', error);
  
  if (!navigator.onLine) {
    setSubmitError('Sem conexão com a internet. Verifique sua conexão e tente novamente.');
    return;
  }
  
  if (error?.message?.includes('fetch')) {
    setSubmitError('Erro de comunicação com o servidor. Tente novamente em alguns segundos.');
    return;
  }
  
  setSubmitError('Erro inesperado. Tente novamente ou contate o suporte se o problema persistir.');
}; 