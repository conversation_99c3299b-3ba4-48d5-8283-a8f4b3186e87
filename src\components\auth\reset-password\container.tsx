'use client'

import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import { ReactNode } from 'react'
import { useTenantTheme } from '@/hooks/tenant/use-tenant-theme'

interface ResetPasswordContainerProps {
  children: ReactNode
}

export const ResetPasswordContainer = ({ children }: ResetPasswordContainerProps) => {
  const { logoUrl, tenantName } = useTenantTheme()

  return (
    <AnimatePresence mode="wait">
      <motion.div
        className="w-full max-w-md space-y-8 rounded-2xl bg-white p-8 shadow-xl shadow-gray-200/20 ring-1 ring-gray-200 dark:bg-gray-900 dark:shadow-none dark:ring-gray-800"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="text-center">
          <motion.div
            initial={{ scale: 0.5, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Image
              src={logoUrl || "https://i.imgur.com/GAzKBJ1.png"}
              alt={`Logo ${tenantName || 'Academia'}`}
              width={112}
              height={112}
              priority
              className="mx-auto object-contain"
            />
          </motion.div>

          <motion.h2
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mt-6 bg-gradient-to-r tenant-primary bg-clip-text text-3xl font-bold tracking-tight text-transparent dark:from-white dark:to-gray-200"
          >
            Redefinir minha senha
          </motion.h2>
          <motion.p
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mt-2 text-sm text-gray-600 dark:text-gray-400"
          >
            Você receberá um e-mail com um link para redefinição de sua senha.
            Confira sua caixa de mensagens, spam ou lixeira.
          </motion.p>
        </div>

        {children}
      </motion.div>
    </AnimatePresence>
  )
} 