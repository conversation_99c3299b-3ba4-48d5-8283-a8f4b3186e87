'use client';

import { createContext, useContext, ReactNode, useState, useCallback, useEffect } from 'react';
import { useDebounce } from '@/hooks/ui/use-debounce';
import { ClassFilters, ClassFiltersContextValue } from '../types';

const ClassFiltersContext = createContext<ClassFiltersContextValue | undefined>(undefined);

const defaultFilters: ClassFilters = {
  status: null,
  searchInput: '',
  dateFrom: undefined,
  dateTo: undefined,
  instructorId: undefined,
  page: 1,
  limit: 10,
};

interface ClassFiltersProviderProps {
  children: ReactNode;
  initialFilters?: Partial<ClassFilters>;
}

export function ClassFiltersProvider({ children, initialFilters = {} }: ClassFiltersProviderProps) {
  const [filters, setFilters] = useState<ClassFilters>({
    ...defaultFilters,
    ...initialFilters,
  });

  // Estado separado para o input de pesquisa (valor imediato)
  const [searchInputValue, setSearchInputValue] = useState<string>(filters.searchInput || '');

  // Debounce do valor de pesquisa (500ms)
  const debouncedSearchInput = useDebounce(searchInputValue, 500);

  // Atualizar o filtro quando o valor debounced mudar
  useEffect(() => {
    if (debouncedSearchInput !== filters.searchInput) {
      setFilters(prev => ({
        ...prev,
        searchInput: debouncedSearchInput,
        page: 1, // Reset page when search changes
      }));
    }
  }, [debouncedSearchInput, filters.searchInput]);

  const updateFilters = useCallback((newFilters: Partial<ClassFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      // Reset page when filters change (except when explicitly setting page)
      page: newFilters.page !== undefined ? newFilters.page : 1,
    }));
  }, []);

  const resetFilters = useCallback(() => {
    setFilters(defaultFilters);
    setSearchInputValue('');
  }, []);

  // Funções específicas para pesquisa
  const setSearchInput = useCallback((searchInput: string) => {
    setSearchInputValue(searchInput);
  }, []);

  const clearSearch = useCallback(() => {
    setSearchInputValue('');
  }, []);

  const setStatus = useCallback((status: ClassFilters['status']) => {
    updateFilters({ status });
  }, [updateFilters]);

  // Função para verificar se há filtros ativos
  const hasActiveFilters = useCallback(() => {
    return Boolean(
      filters.searchInput ||
      filters.status ||
      filters.dateFrom ||
      filters.dateTo ||
      filters.instructorId
    );
  }, [filters]);

  // Função para obter lista de filtros ativos
  const getActiveFilters = useCallback(() => {
    const activeFilters: Array<{ key: string; label: string; value: string }> = [];

    if (filters.searchInput) {
      activeFilters.push({
        key: 'search',
        label: 'Busca',
        value: filters.searchInput
      });
    }

    if (filters.status) {
      const statusLabels = {
        scheduled: 'Agendadas',
        ongoing: 'Em andamento',
        completed: 'Concluídas',
        cancelled: 'Canceladas'
      };
      activeFilters.push({
        key: 'status',
        label: 'Status',
        value: statusLabels[filters.status]
      });
    }

    if (filters.instructorId) {
      activeFilters.push({
        key: 'instructor',
        label: 'Instrutor',
        value: 'Selecionado'
      });
    }

    if (filters.dateFrom && filters.dateTo) {
      const fromDate = new Date(filters.dateFrom).toLocaleDateString('pt-BR');
      const toDate = new Date(filters.dateTo).toLocaleDateString('pt-BR');
      activeFilters.push({
        key: 'date',
        label: 'Período',
        value: `${fromDate} - ${toDate}`
      });
    }

    return activeFilters;
  }, [filters]);

  const value: ClassFiltersContextValue = {
    filters: {
      ...filters,
      // Retornar o valor imediato do input para o componente
      searchInput: searchInputValue,
    },
    // Filtros para query (com valor debounced)
    queryFilters: filters,
    updateFilters,
    resetFilters,
    setSearchInput,
    clearSearch,
    setStatus,
    hasActiveFilters,
    getActiveFilters,
  };

  return (
    <ClassFiltersContext.Provider value={value}>
      {children}
    </ClassFiltersContext.Provider>
  );
}

export function useClassFilters() {
  const context = useContext(ClassFiltersContext);
  if (!context) {
    throw new Error('useClassFilters must be used within a ClassFiltersProvider');
  }
  return context;
} 