import { ArrowUp, Calendar, CreditCard } from "lucide-react"
import { format } from "date-fns"
import { PaymentStatsProps } from "../types/types"
import { StatCard } from "./StatCard"
import { LoadingState } from "./LoadingState"
import { getPaymentMethodIconBySlug } from "@/utils/payment-method-formatter"

export function PaymentStats({ pagamentoInfo, historicoPagamentos, loading }: PaymentStatsProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <LoadingState height="h-20" />
        <LoadingState height="h-20" />
        <LoadingState height="h-20" />
      </div>
    )
  }

  const formatarProximoVencimento = () => {
    if (!pagamentoInfo.proximoVencimento) return 'Não definido'
    return format(pagamentoInfo.proximoVencimento, "dd/MM/yyyy")
  }

  const getSubtitleTotalPago = () => {
    const count = historicoPagamentos.length
    return `Últimos ${count} ${count === 1 ? 'mês' : 'meses'}`
  }

  const getSubtitleProximoVencimento = () => {
    const dias = pagamentoInfo.diasParaVencimento

    if (dias < 0) {
      return 'Vencido'
    }

    if (dias === 0) {
      return 'Vence hoje'
    }

    return `Em ${dias} ${dias === 1 ? 'dia' : 'dias'}`
  }

  // Obter o ícone específico da forma de pagamento
  const PaymentMethodIcon = getPaymentMethodIconBySlug(pagamentoInfo.metodoPreferidoSlug)
  const paymentIcon = PaymentMethodIcon ? (
    <PaymentMethodIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
  ) : (
    <CreditCard className="w-6 h-6 text-blue-600 dark:text-blue-400" />
  )

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <StatCard
        icon={<ArrowUp className="w-6 h-6 text-green-600 dark:text-green-400" />}
        title="Total Pago"
        value={`R$ ${pagamentoInfo.totalPago.toFixed(2)}`}
        subtitle={getSubtitleTotalPago()}
        iconBgColor="bg-green-100 dark:bg-green-900/20"
      />

      <StatCard
        icon={<Calendar className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />}
        title="Próximo Vencimento"
        value={formatarProximoVencimento()}
        subtitle={getSubtitleProximoVencimento()}
        iconBgColor="bg-yellow-100 dark:bg-yellow-900/20"
      />

      <StatCard
        icon={paymentIcon}
        title="Método Preferido"
        value={pagamentoInfo.metodoPreferido}
        subtitle="Últimos pagamentos"
        iconBgColor="bg-blue-100 dark:bg-blue-900/20"
      />
    </div>
  )
}
