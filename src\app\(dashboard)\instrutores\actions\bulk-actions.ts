'use server';

import { createClient } from "@/services/supabase/server";
import { createAdminClient } from "@/services/supabase/server";
import { getCurrentUser } from "@/services/auth/actions/auth-actions";
import { BulkActionResult } from "../types/types";
import { revalidatePath } from "next/cache";

// Interface para logs de auditoria
interface AuditLogData {
  instructorId: string;
  userId: string;
  instructorEmail?: string;
  instructorName?: string;
  action: 'delete_initiated' | 'delete_completed' | 'delete_failed';
  adminEmail: string;
  timestamp: string;
  error?: string;
}

// Função para criar logs de auditoria
function createAuditLog(data: AuditLogData) {
  console.log(`[INSTRUCTOR-AUDIT] ${data.action.toUpperCase()}:`, {
    ...data,
    type: 'instructor_deletion'
  });
}

// Função para criar backup dos dados do instrutor antes da exclusão
async function createInstructorBackup(instructorId: string, supabase: any) {
  try {
    // Buscar dados do instrutor
    const { data: instructor, error: instructorError } = await supabase
      .from("instructors")
      .select("*")
      .eq("id", instructorId)
      .single();

    if (instructorError || !instructor) {
      console.warn("[INSTRUCTOR-BACKUP] Erro ao buscar dados do instrutor:", instructorError);
      return null;
    }

    // Buscar dados do usuário separadamente
    const { data: user, error: userError } = await supabase
      .from("users")
      .select("*")
      .eq("id", instructor.user_id)
      .single();

    if (userError) {
      console.warn("[INSTRUCTOR-BACKUP] Erro ao buscar dados do usuário:", userError);
    }

    const backupData = {
      instructor,
      user,
      timestamp: new Date().toISOString()
    };

    // Aqui você pode implementar a lógica de backup se necessário
    // Por exemplo, salvar em uma tabela de backup ou arquivo
    console.log("[INSTRUCTOR-BACKUP] Backup criado para instrutor:", {
      instructorId,
      instructorName: user ? `${user.first_name} ${user.last_name}` : 'Desconhecido',
      timestamp: new Date().toISOString()
    });

    return backupData;
  } catch (error) {
    console.error("[INSTRUCTOR-BACKUP] Erro no processo de backup:", error);
    return null;
  }
}

/**
 * Exclui múltiplos instrutores
 */
export async function deleteMultipleInstructors(ids: string[]): Promise<BulkActionResult> {
  try {
    if (!ids.length) {
      return {
        success: false,
        message: 'Nenhum instrutor selecionado para exclusão'
      };
    }
    
    const supabase = await createClient();
    
    // Primeiro, verificamos se os instrutores existem
    const { data, error: checkError } = await supabase
      .from('instructors')
      .select('id')
      .in('id', ids);
    
    if (checkError) {
      console.error('Erro ao verificar instrutores:', checkError);
      return {
        success: false,
        message: 'Erro ao verificar os instrutores selecionados'
      };
    }
    
    const existingIds = data?.map(item => item.id) || [];
    
    if (!existingIds.length) {
      return {
        success: false,
        message: 'Nenhum dos instrutores selecionados foi encontrado'
      };
    }
    
    // Em seguida, excluímos os instrutores
    const { error: deleteError } = await supabase
      .from('instructors')
      .delete()
      .in('id', existingIds);
    
    if (deleteError) {
      console.error('Erro ao excluir instrutores:', deleteError);
      return {
        success: false,
        message: 'Erro ao excluir os instrutores selecionados'
      };
    }
    
    revalidatePath('/instrutores');
    
    return {
      success: true,
      message: `${existingIds.length} instrutores foram excluídos com sucesso`,
      affectedIds: existingIds
    };
  } catch (error) {
    console.error('Erro ao excluir instrutores:', error);
    return {
      success: false,
      message: 'Não foi possível excluir os instrutores selecionados'
    };
  }
}

/**
 * Atualiza o status de múltiplos instrutores
 */
export async function updateInstructorsStatus(
  ids: string[], 
  isActive: boolean
): Promise<BulkActionResult> {
  try {
    if (!ids.length) {
      return {
        success: false,
        message: 'Nenhum instrutor selecionado para atualização'
      };
    }
    
    const supabase = await createClient();
    
    // Primeiro, verificamos se os instrutores existem
    const { data, error: checkError } = await supabase
      .from('instructors')
      .select('id')
      .in('id', ids);
    
    if (checkError) {
      console.error('Erro ao verificar instrutores:', checkError);
      return {
        success: false,
        message: 'Erro ao verificar os instrutores selecionados'
      };
    }
    
    const existingIds = data?.map(item => item.id) || [];
    
    if (!existingIds.length) {
      return {
        success: false,
        message: 'Nenhum dos instrutores selecionados foi encontrado'
      };
    }
    
    // Em seguida, atualizamos o status dos instrutores
    const { error: updateError } = await supabase
      .from('instructors')
      .update({ is_active: isActive })
      .in('id', existingIds);
    
    if (updateError) {
      console.error('Erro ao atualizar status dos instrutores:', updateError);
      return {
        success: false,
        message: 'Erro ao atualizar o status dos instrutores selecionados'
      };
    }
    
    revalidatePath('/instrutores');
    
    const statusText = isActive ? 'ativados' : 'desativados';
    
    return {
      success: true,
      message: `${existingIds.length} instrutores foram ${statusText} com sucesso`,
      affectedIds: existingIds
    };
  } catch (error) {
    console.error('Erro ao atualizar status dos instrutores:', error);
    return {
      success: false,
      message: 'Não foi possível atualizar o status dos instrutores selecionados'
    };
  }
}

/**
 * Deleta completamente um instrutor do sistema (APENAS ADMINS)
 * Remove todos os registros relacionados em cascata
 */
export async function deleteInstructorCompletely(userId: string, adminClient?: any) {

  const supabase = await createClient();
  const adminSupabase = adminClient || await createAdminClient();
  const currentUser = await getCurrentUser();
  
  try {
    
    if (!currentUser) {
      return { success: false, message: "Usuário não autenticado" };
    }

    // Verificar se o usuário atual é admin
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("role")
      .eq("id", currentUser.id)
      .single();

    if (userError || userData?.role !== "admin") {
      return { success: false, message: "Acesso negado. Apenas administradores podem realizar esta ação." };
    }

    // Buscar o instrutor pelo user_id
    const { data: instructor, error: instructorError } = await supabase
      .from("instructors")
      .select("id, user_id")
      .eq("user_id", userId)
      .single();

    if (instructorError || !instructor) {
      return { success: false, message: "Instrutor não encontrado" };
    }

    const instructorId = instructor.id;
    
    // Buscar dados do usuário para auditoria
    const { data: userInfo, error: userFetchError } = await supabase
      .from("users")
      .select("first_name, last_name, email, created_at")
      .eq("id", userId)
      .single();

    if (userFetchError || !userInfo) {
      return { success: false, message: "Dados do usuário não encontrados" };
    }

    // Log de auditoria ANTES da exclusão
    createAuditLog({
      instructorId,
      userId,
      instructorEmail: userInfo?.email,
      instructorName: `${userInfo?.first_name} ${userInfo?.last_name}`,
      action: 'delete_initiated',
      adminEmail: currentUser.email || 'unknown',
      timestamp: new Date().toISOString()
    });

    // Verificar se existem dados críticos que impedem a exclusão
    const { data: activeClasses, error: classError } = await supabase
      .from("class_groups")
      .select("id, name")
      .eq("instructor_id", instructorId)
      .eq("is_active", true)
      .limit(1);

    if (classError) {
      console.error("[INSTRUCTOR-AUDIT] Erro ao verificar turmas ativas:", classError);
    }

    if (activeClasses && activeClasses.length > 0) {
      createAuditLog({
        instructorId,
        userId,
        instructorEmail: userInfo?.email,
        instructorName: `${userInfo?.first_name} ${userInfo?.last_name}`,
        action: 'delete_failed',
        adminEmail: currentUser.email || 'unknown',
        timestamp: new Date().toISOString(),
        error: 'Turmas ativas encontradas'
      });
      
      return { 
        success: false, 
        message: "Não é possível deletar instrutor com turmas ativas. Transfira ou desative as turmas primeiro." 
      };
    }

    // Criar backup dos dados antes da exclusão
    const backup = await createInstructorBackup(instructorId, supabase);

    // INICIAR OPERAÇÕES DE EXCLUSÃO - Deletar registros em ordem específica para evitar violações de FK
    
    // 1. PRIMEIRO: Deletar instructor_belts (foreign key para instructors)
    const { error: deleteBeltsError } = await supabase
      .from("instructor_belts")
      .delete()
      .eq("instructor_id", instructorId);

    if (deleteBeltsError) {
      throw new Error(`Erro ao deletar instructor_belts: ${deleteBeltsError.message}`);
    }

    // 2. Deletar outras tabelas relacionadas (se existirem)
    const deletionSteps = [
      { table: "class_groups", field: "instructor_id" }, // Turmas inativas
    ];

    // Executar exclusões relacionadas ao instructor_id (exceto tabelas que não existem)
    for (const step of deletionSteps) {
      const { error: deleteError } = await supabase
        .from(step.table)
        .delete()
        .eq(step.field, instructorId);

      if (deleteError) {
        console.error(`[INSTRUCTOR-AUDIT] Erro ao deletar ${step.table}:`, deleteError);
        // Para class_groups, bloquear se der erro
        if (step.table === "class_groups") {
          throw new Error(`Erro ao deletar ${step.table}: ${deleteError.message}`);
        }
      }
    }

    // Deletar o registro do instrutor
    const { error: deleteInstructorError } = await supabase
      .from("instructors")
      .delete()
      .eq("id", instructorId);

    if (deleteInstructorError) {
      throw new Error(`Erro ao deletar instrutor: ${deleteInstructorError.message}`);
    }

    // Deletar registros relacionados ao user_id
    const userDeletionSteps = [
      { table: "ai_chat_usage", field: "user_id" },
      { table: "sessions", field: "user_id" },
      { table: "one_time_tokens", field: "user_id" },
      { table: "mfa_factors", field: "user_id" },
    ];

    for (const step of userDeletionSteps) {
      const { error: deleteError } = await supabase
        .from(step.table)
        .delete()
        .eq(step.field, userId);

      if (deleteError) {
        console.warn(`[INSTRUCTOR-AUDIT] Aviso ao deletar ${step.table}:`, deleteError);
        // Não bloquear por estes erros, são menos críticos
      }
    }

    // Deletar o usuário (public.users)
    const { error: deleteUserError } = await supabase
      .from("users")
      .delete()
      .eq("id", userId);

    if (deleteUserError) {
      throw new Error(`Erro ao deletar usuário: ${deleteUserError.message}`);
    }

    // Deletar também do auth.users usando o cliente admin
    try {
      console.log("[INSTRUCTOR-AUDIT] Tentando deletar usuário do auth.users:", userId);
      const { error: authDeleteError } = await adminSupabase.auth.admin.deleteUser(userId);
      if (authDeleteError) {
        console.error("[INSTRUCTOR-AUDIT] Falha ao deletar do auth.users:", {
          userId,
          error: authDeleteError.message,
          code: authDeleteError.code || 'unknown'
        });
        // Se falhar, não bloquear o processo, apenas logar
      } else {
        console.log("[INSTRUCTOR-AUDIT] ✅ Usuário deletado com sucesso do auth.users:", userId);
      }
    } catch (authError) {
      console.error("[INSTRUCTOR-AUDIT] Exceção ao deletar do auth.users:", {
        userId,
        error: authError instanceof Error ? authError.message : String(authError),
        stack: authError instanceof Error ? authError.stack : undefined
      });
    }

    // Log de auditoria APÓS a exclusão bem-sucedida
    createAuditLog({
      instructorId,
      userId,
      instructorEmail: userInfo?.email,
      instructorName: `${userInfo?.first_name} ${userInfo?.last_name}`,
      action: 'delete_completed',
      adminEmail: currentUser.email || 'unknown',
      timestamp: new Date().toISOString()
    });

    // Revalidar o cache da página de instrutores
    revalidatePath("/instrutores");
    
    return { 
      success: true, 
      message: "Instrutor deletado permanentemente do sistema com sucesso" 
    };
  } catch (error) {
    // Log de auditoria para falha
    createAuditLog({
      instructorId: 'unknown',
      userId: userId || 'unknown',
      action: 'delete_failed',
      adminEmail: currentUser?.email || 'unknown',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
    
    return { 
      success: false, 
      message: error instanceof Error ? error.message : "Erro desconhecido ao deletar instrutor" 
    };
  }
}

/**
 * Deleta múltiplos instrutores permanentemente com performance otimizada
 * Para uso em operações bulk com grandes volumes
 */
export async function deleteMultipleInstructorsPermanently(userIds: string[]) {
  const supabase = await createClient();
  const adminSupabase = await createAdminClient();
  const currentUser = await getCurrentUser();
  
  if (!currentUser) {
    return { success: false, message: "Usuário não autenticado" };
  }

  if (userIds.length === 0) {
    return { success: false, message: "Nenhum instrutor selecionado" };
  }

  // Verificar se o usuário atual é admin
  const { data: userData, error: userError } = await supabase
    .from("users")
    .select("role")
    .eq("id", currentUser.id)
    .single();

  if (userError || userData?.role !== "admin") {
    return { success: false, message: "Acesso negado. Apenas administradores podem realizar esta ação." };
  }

  try {
    // Buscar informações dos instrutores para auditoria (buscar por user_id)
    const { data: instructors, error: instructorsError } = await supabase
      .from("instructors")
      .select(`
        id,
        user_id,
        user:users!instructors_user_id_fkey!inner (
          first_name,
          last_name,
          email
        )
      `)
      .in("user_id", userIds);

    if (instructorsError || !instructors) {
      return { success: false, message: "Erro ao buscar informações dos instrutores" };
    }

    // Extrair instructor_ids para verificar turmas ativas
    const instructorIds = instructors.map(i => i.id);

    // Verificar se existem turmas ativas para qualquer instrutor
    const { data: activeClasses, error: classesError } = await supabase
      .from("class_groups")
      .select("instructor_id, name")
      .in("instructor_id", instructorIds)
      .eq("is_active", true);

    if (classesError) {
      console.error("[BULK-DELETE-INSTRUCTOR] Erro ao verificar turmas ativas:", classesError);
    }

    if (activeClasses && activeClasses.length > 0) {
      const instructorsWithActiveClasses = activeClasses.map(c => c.instructor_id);
      return { 
        success: false, 
        message: `Não é possível deletar instrutores com turmas ativas: ${instructorsWithActiveClasses.join(", ")}` 
      };
    }

    // Log de início da operação bulk
    console.log(`[BULK-DELETE-INSTRUCTOR] Iniciando exclusão permanente de ${userIds.length} instrutores pelo admin ${currentUser.email}`);

    // Executar exclusões em lotes para melhor performance
    const batchSize = 50; // Processar em lotes de 50
    const results = [];

    for (let i = 0; i < userIds.length; i += batchSize) {
      const batch = userIds.slice(i, i + batchSize);
      const batchResults = await Promise.allSettled(
        batch.map(userId => deleteInstructorCompletely(userId, adminSupabase))
      );
      results.push(...batchResults);
    }

    // Processar resultados
    const successful = results.filter(r => 
      r.status === 'fulfilled' && r.value.success
    ).length;
    
    const failed = results.length - successful;

    console.log(`[BULK-DELETE-INSTRUCTOR] Operação concluída: ${successful} sucessos, ${failed} falhas`);

    if (failed === 0) {
      return { 
        success: true, 
        message: `${successful} instrutores deletados permanentemente com sucesso` 
      };
    } else if (successful > 0) {
      return { 
        success: true, 
        message: `${successful} instrutores deletados, ${failed} falharam` 
      };
    } else {
      return { 
        success: false, 
        message: `Falha ao deletar todos os instrutores` 
      };
    }

  } catch (error) {
    console.error("[BULK-DELETE-INSTRUCTOR] Erro na operação bulk:", error);
    return { 
      success: false, 
      message: error instanceof Error ? error.message : "Erro desconhecido na operação bulk" 
    };
  }
} 