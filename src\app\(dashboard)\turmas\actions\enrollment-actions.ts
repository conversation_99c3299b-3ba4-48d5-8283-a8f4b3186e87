'use server'

import { createClient } from '@/services/supabase/server'
import { unstable_noStore as noStore } from 'next/cache'

export async function getStudentEnrollmentDetails(userId: string) {
  noStore()
  const supabase = await createClient()

  try {
    // 1. Buscar o ID do estudante a partir do ID de usuário
    const { data: student, error: studentError } = await supabase
      .from('students')
      .select('id')
      .eq('user_id', userId)
      .single()

    if (studentError || !student) {
      console.error('Erro ao buscar estudante:', studentError?.message)
      if (studentError?.code === 'PGRST116') {
        return { data: null, error: 'Este usuário não é um estudante.' }
      }
      return { data: null, error: 'Estudante não encontrado.' }
    }

    const studentId = student.id

    // 2. Buscar a matrícula mais recente do estudante (ativa primeiro, depois a mais recente inativa)
    const { data: enrollment, error: enrollmentError } = await supabase
      .from('class_group_enrollments')
      .select(
        `
        id,
        enrollment_date,
        status,
        enrollment_pauses!enrollment_pauses_enrollment_id_fkey(
          id,
          paused_at,
          resumed_at,
          reason
        ),
        class_groups (
          id,
          name,
          description,
          recurrence_pattern,
          instructor:users!class_groups_instructor_id_fkey (
            id,
            full_name,
            avatar_url
          )
        )
      `
      )
      .eq('student_id', studentId)
      .order('status', { ascending: true }) // 'active' vem primeiro
      .order('enrollment_date', { ascending: false })
      .limit(1)
      .single()

    if (enrollmentError) {
      if (enrollmentError.code === 'PGRST116') {
        return { data: null, error: 'Nenhuma matrícula encontrada para este aluno.' }
      }
      console.error('Erro ao buscar matrícula:', enrollmentError)
      return { data: null, error: 'Erro ao buscar dados da matrícula.' }
    }
    
    const classGroup = Array.isArray(enrollment.class_groups) ? enrollment.class_groups[0] : enrollment.class_groups;

    if (!enrollment || !classGroup) {
        return { data: null, error: 'Detalhes da turma não encontrados para esta matrícula.' };
    }

    const classGroupId = classGroup.id;

    // 3. Buscar as aulas associadas a essa turma
    const { data: classesInGroup, error: classesError } = await supabase
      .from('classes')
      .select('id')
      .eq('class_group_id', classGroupId)

    if (classesError) {
        console.error('Erro ao buscar aulas da turma:', classesError);
        return { data: null, error: 'Erro ao buscar aulas da turma.' };
    }

    const classIds = classesInGroup.map(c => c.id);

    // 4. Buscar o histórico de presença para essas aulas, se houver alguma
    type AttendanceRecord = {
      checked_in_at: string;
      classes: { id: number; name: string; start_time: string; } | null;
    };
    let attendance: any[] = [];
    if (classIds.length > 0) {
        const { data: attendanceData, error: attendanceError } = await supabase
          .from('attendance')
          .select(
            `
            checked_in_at,
            classes (
              id,
              name,
              start_time
            )
          `
          )
          .eq('student_id', studentId)
          .in('class_id', classIds)
          .order('checked_in_at', { ascending: false })
          .limit(10)
        
        if (attendanceError) {
          console.error('Erro ao buscar histórico de presença:', attendanceError)
          // Não bloqueia o retorno dos outros dados
        } else {
          attendance = (attendanceData || []).map(att => ({
            ...att,
            classes: Array.isArray(att.classes) ? att.classes[0] : att.classes
          }));
        }
    }

    const instructor = Array.isArray(classGroup.instructor) ? classGroup.instructor[0] : classGroup.instructor;

    // Determinar o status correto da matrícula considerando pausas ativas
    const getEnrollmentStatus = () => {
      // Verificar se há pausa ativa (resumed_at é null)
      if (Array.isArray(enrollment.enrollment_pauses) && enrollment.enrollment_pauses.length > 0) {
        const activePause = enrollment.enrollment_pauses.find((pause: any) => pause.resumed_at === null);
        if (activePause) {
          return 'paused'; // Retornar 'paused' se há uma pausa ativa
        }
      }

      // Se não há pausa ativa, retornar o status normal da matrícula
      return enrollment.status;
    };

    const enrollmentStatus = getEnrollmentStatus();

    const result = {
      studentId,
      enrollment: {
        enrollmentDate: enrollment.enrollment_date,
        status: enrollmentStatus,
      },
      classGroup: {
        id: classGroup.id,
        name: classGroup.name,
        description: classGroup.description,
        schedule: classGroup.recurrence_pattern,
        instructor: instructor ? {
            id: instructor.id,
            fullName: instructor.full_name,
            avatarUrl: instructor.avatar_url
        } : null
      },
      attendance,
    }

    return { data: result, error: null }
  } catch (error) {
    console.error('Erro inesperado em getStudentEnrollmentDetails:', error)
    return { data: null, error: 'Ocorreu um erro inesperado ao buscar os dados da matrícula.' }
  }
} 