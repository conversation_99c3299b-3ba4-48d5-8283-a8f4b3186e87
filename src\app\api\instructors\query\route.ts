import { NextRequest, NextResponse } from 'next/server';
import { FetchInstructorsParams } from '@/app/(dashboard)/instrutores/types/types';
import { fetchInstructors } from '@/app/(dashboard)/instrutores/actions';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;

    // Converter query params para o formato esperado pela função fetchInstructors
    const params: FetchInstructorsParams = {
      skip: Number(searchParams.get('skip')) || 0,
      take: Number(searchParams.get('take')) || 10,
      search: searchParams.get('search') || '',
      status: searchParams.getAll('status') || ['active'],
      specialties: searchParams.getAll('specialties') || [],
      branch: searchParams.getAll('branch') || [],
      contractType: searchParams.getAll('contractType') || [],
      experienceLevel: searchParams.getAll('experienceLevel') || [],
      belts: searchParams.getAll('belts') || [],
    };

    // Usar a função fetchInstructors
    const result = await fetchInstructors(params);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Erro na rota de consulta de instrutores:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const params = await request.json() as FetchInstructorsParams;
    
    // Usar a função fetchInstructors do módulo de ações
    const result = await fetchInstructors(params);
    
    // Retornar o resultado diretamente
    return NextResponse.json(result);
  } catch (error) {
    console.error('Erro na rota de consulta de instrutores:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 