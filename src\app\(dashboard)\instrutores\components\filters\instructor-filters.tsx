'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { Search, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SpecialtiesFilter } from './specialties-filter';
import { BranchesFilter } from './branches-filter';
import { Branch } from '../../types/types';

interface LegacyFilterState {
  name: string;
  branch_id: string;
  specialties: string[];
  status: string;
  contract_type: string;
}

interface InstructorFiltersProps {
  branches: Branch[];
  specialties: string[];
}

export function InstructorFilters({ branches, specialties }: InstructorFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [filters, setFilters] = useState<LegacyFilterState>({
    name: searchParams.get('name') || '',
    branch_id: searchParams.get('branch_id') || '',
    specialties: searchParams.getAll('specialties') || [],
    status: searchParams.get('status') || '',
    contract_type: searchParams.get('contract_type') || '',
  });

  const applyFilters = useCallback(() => {
    const params = new URLSearchParams();
    
    if (filters.name) params.set('name', filters.name);
    if (filters.branch_id && filters.branch_id !== 'all') params.set('branch_id', filters.branch_id);
    if (filters.status && filters.status !== 'all') params.set('status', filters.status);
    if (filters.contract_type && filters.contract_type !== 'all') params.set('contract_type', filters.contract_type);
    
    filters.specialties.forEach((specialty: string) => {
      params.append('specialties', specialty);
    });
    
    // Reset para página 1 ao filtrar
    params.set('page', '1');
    
    router.push(`/instrutores?${params.toString()}`);
  }, [filters, router]);

  const clearFilters = () => {
    setFilters({
      name: '',
      branch_id: '',
      specialties: [],
      status: '',
      contract_type: '',
    });
    
    router.push('/instrutores');
  };

  const hasActiveFilters = () => {
    return (
      !!filters.name ||
      (!!filters.branch_id && filters.branch_id !== 'all') ||
      filters.specialties.length > 0 ||
      (!!filters.status && filters.status !== 'all') ||
      (!!filters.contract_type && filters.contract_type !== 'all')
    );
  };

  // Aplicar filtros ao pressionar Enter no campo de busca
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      applyFilters();
    }
  };

  useEffect(() => {
    // Re-aplicar filtros ao navegar com botões do navegador
    const name = searchParams.get('name') || '';
    const branch_id = searchParams.get('branch_id') || '';
    const status = searchParams.get('status') || '';
    const contract_type = searchParams.get('contract_type') || '';
    const specialties = searchParams.getAll('specialties') || [];
    
    setFilters({
      name,
      branch_id,
      specialties,
      status,
      contract_type,
    });
  }, [searchParams]);

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="space-y-2">
          <Label htmlFor="name">Nome</Label>
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              id="name"
              placeholder="Buscar instrutores..."
              className="pl-8"
              value={filters.name}
              onChange={(e) => setFilters({ ...filters, name: e.target.value })}
              onKeyDown={handleKeyDown}
            />
          </div>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="branch">Filial</Label>
          <BranchesFilter
            branches={branches}
            value={filters.branch_id}
            onChange={(value) => setFilters({ ...filters, branch_id: value })}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select
            value={filters.status}
            onValueChange={(value) => setFilters({ ...filters, status: value })}
          >
            <SelectTrigger id="status">
              <SelectValue placeholder="Todos" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos</SelectItem>
              <SelectItem value="active">Ativos</SelectItem>
              <SelectItem value="inactive">Inativos</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="contract_type">Tipo de Contrato</Label>
          <Select
            value={filters.contract_type}
            onValueChange={(value) => setFilters({ ...filters, contract_type: value })}
          >
            <SelectTrigger id="contract_type">
              <SelectValue placeholder="Todos" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos</SelectItem>
              <SelectItem value="CLT">CLT</SelectItem>
              <SelectItem value="PJ">PJ</SelectItem>
              <SelectItem value="Autônomo">Autônomo</SelectItem>
              <SelectItem value="Freelancer">Freelancer</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="space-y-2">
        <Label>Especialidades</Label>
        <SpecialtiesFilter
          allSpecialties={specialties}
          selectedSpecialties={filters.specialties}
          onChange={(specialties) => setFilters({ ...filters, specialties })}
        />
      </div>
      
      <div className="flex items-center justify-end space-x-2">
        {hasActiveFilters() && (
          <Button
            variant="outline"
            size="sm"
            onClick={clearFilters}
            className="h-8 px-2 lg:px-3"
          >
            <X className="mr-2 h-4 w-4" />
            Limpar
          </Button>
        )}
        
        <Button
          size="sm"
          onClick={applyFilters}
          className="h-8 px-2 lg:px-3"
        >
          <Search className="mr-2 h-4 w-4" />
          Filtrar
        </Button>
      </div>
    </div>
  );
} 