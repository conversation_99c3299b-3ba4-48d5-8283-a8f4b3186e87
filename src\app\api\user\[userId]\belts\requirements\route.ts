import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/services/supabase/server";

interface BeltRequirement {
  id: string;
  nome: string;
  descricao: string;
  concluido: boolean;
  dataConlusao?: string;
  categoria: string;
  progresso: number;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const supabase = await createClient();
    
    const resolvedParams = await Promise.resolve(params);
    const { userId } = resolvedParams;

    if (!userId) {
      return NextResponse.json(
        { error: "ID de usuário inválido" },
        { status: 400 }
      );
    }

    const { data: sessionData } = await supabase.auth.getSession();
    
    if (!sessionData?.session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    // Buscar tenant_id do usuário logado
    const { data: currentUser } = await supabase
      .from("users")
      .select("tenant_id")
      .eq("id", sessionData.session.user.id)
      .single();

    const tenantId = currentUser?.tenant_id;

    if (!tenantId) {
      return NextResponse.json({ error: "Tenant não encontrado" }, { status: 400 });
    }

    const { data: student, error: studentError } = await supabase
      .from("students")
      .select("id")
      .eq("user_id", userId)
      .eq("tenant_id", tenantId)
      .single();

    if (studentError || !student) {
      return NextResponse.json(
        { error: "Estudante não encontrado" },
        { status: 404 }
      );
    }

    // Chamar a função RPC para buscar requisitos
    const { data: requirements, error: rpcError } = await supabase
      .rpc("get_student_belt_requirements", {
        student_id_param: student.id,
        tenant_id_param: tenantId,
      });

    if (rpcError) {
      console.error("Erro na RPC get_student_belt_requirements:", rpcError);
      return NextResponse.json(
        { error: "Erro ao buscar requisitos" },
        { status: 500 }
      );
    }

    if (!requirements || requirements.length === 0) {
      return NextResponse.json([]);
    }

    // Converter os dados da RPC para o formato esperado pelo frontend
    const formattedRequirements: BeltRequirement[] = requirements.map((req: any) => ({
      id: req.requirement_id,
      nome: req.requirement_name,
      descricao: req.requirement_description,
      concluido: req.is_completed,
      dataConlusao: req.completion_date,
      categoria: req.requirement_category,
      progresso: req.progress_percentage
    }));

    return NextResponse.json(formattedRequirements);

  } catch (error) {
    console.error("Erro inesperado na API belt requirements:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
} 