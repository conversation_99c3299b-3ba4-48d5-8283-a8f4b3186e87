'use client';

import { FileText } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { FieldErrors, UseFormRegister } from 'react-hook-form';
import { CreateClassFormData } from '../schema';

interface NotesSectionProps {
  register: UseFormRegister<CreateClassFormData>;
  errors: FieldErrors<CreateClassFormData>;
  formValues: CreateClassFormData;
  getErrorMessage: (error: any) => string;
}

export function NotesSection({ 
  register, 
  errors, 
  formValues, 
  getErrorMessage 
}: NotesSectionProps) {
  return (
    <Card>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-lg">
          <FileText className="h-5 w-5" />
          Observações
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <Label htmlFor="notes">Observações Adicionais</Label>
          <Textarea
            id="notes"
            placeholder="Observações internas, materiais necessários, preparação especial..."
            rows={4}
            {...register('notes')}
            className={errors.notes ? 'border-destructive' : ''}
          />
          {errors.notes && (
            <p className="text-sm text-destructive">{getErrorMessage(errors.notes)}</p>
          )}
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Observações internas para organização da aula (máximo 1000 caracteres)</span>
            <span>{formValues.notes?.length || 0}/1000</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 