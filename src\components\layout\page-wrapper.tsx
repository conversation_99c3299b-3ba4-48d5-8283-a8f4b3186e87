'use client';

import { ReactNode } from 'react';
import { usePageTitle } from '@/hooks/ui/use-page-title';

interface PageWrapperProps {
  title?: string;
  subtitle?: string;
  icon?: ReactNode;
  children: ReactNode;
  actions?: ReactNode;
  className?: string;
}

export function PageWrapper({ 
  title, 
  subtitle, 
  icon, 
  children, 
  actions,
  className = "space-y-6" 
}: PageWrapperProps) {
  // Define o título da página no header global
  usePageTitle({ title, subtitle, icon });

  return (
    <div className={className}>
      {actions && (
        <div className="flex items-center justify-end">
          {actions}
        </div>
      )}
      {children}
    </div>
  );
} 