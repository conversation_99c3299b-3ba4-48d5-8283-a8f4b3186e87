'use client';

import { useEditMode } from '@/app/(dashboard)/perfil/components/edit-context';
import { AddressComponentProps } from './types';
import { useAddress } from './hooks';
import { AddressDisplay } from './AddressDisplay';
import { AddressForm } from './AddressForm';

export function AddressComponent({
  address,
  userId,
  className,
  useDirectFields = true,
  onAddressUpdated
}: AddressComponentProps) {
  const { setFieldValue, getCurrentValue } = useEditMode();
  
  const {
    isEditing,
    isSaving,
    wasRecentlyEdited,
    error,
    addressFields,
    isAllowed,
    handleEdit,
    handleCancel,
    handleConfirm,
    handleFieldChange,
    handlePostalCodeChange,
    formatAddressForDisplay
  } = useAddress({
    address,
    userId,
    useDirectFields,
    onAddressUpdated,
    getCurrentValue,
    setFieldValue
  });

  return isEditing ? (
    <AddressForm
      address={address}
      userId={userId}
      className={className}
      useDirectFields={useDirectFields}
      onAddressUpdated={onAddressUpdated}
      addressFields={addressFields}
      error={error}
      isSaving={isSaving}
      onCancel={handleCancel}
      onConfirm={handleConfirm}
      onFieldChange={handleFieldChange}
      handlePostalCodeChange={handlePostalCodeChange}
    />
  ) : (
    <AddressDisplay
      address={address}
      userId={userId}
      className={className}
      useDirectFields={useDirectFields}
      onAddressUpdated={onAddressUpdated}
      onEdit={handleEdit}
      wasRecentlyEdited={wasRecentlyEdited}
      formatAddressForDisplay={formatAddressForDisplay}
      isAllowed={isAllowed}
    />
  );
} 