"use client";

import { ChevronDownIcon } from "@heroicons/react/24/solid";
import clsx from "clsx";
import { SidebarLink } from "./SidebarLink";
import { NavigationItem } from "./types";
import { useTenantTheme } from "@/hooks/tenant/use-tenant-theme";
import { useRolePermissions } from "@/hooks/user/Permissions/use-role-permissions";
import { useMemo, useState, useEffect } from "react";

interface UserRoleState {
  isAdmin: boolean;
  isInstructor: boolean;
  isLoading: boolean;
}

interface SidebarSectionProps {
  title: string;
  items: NavigationItem[];
  isExpanded: boolean;
  onToggle: () => void;
  isCollapsible?: boolean;
  adminOnly?: boolean;
  isHovered?: boolean;
  userRoleState: UserRoleState;
  showFavoriteButton?: boolean;
}

export function SidebarSection({ 
  title, 
  items, 
  isExpanded, 
  onToggle, 
  isCollapsible = true,
  adminOnly = false,
  isHovered = true,
  userRoleState,
  showFavoriteButton = true
}: SidebarSectionProps) {
  const { primaryColor } = useTenantTheme();
  const { 
    canAccessTurmas, 
    canAccessAulasLivres, 
    canAccessPresenca, 
    userRole 
  } = useRolePermissions();
  const [mounted, setMounted] = useState(false);
  const [hovered, setHovered] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // Usar um valor padrão seguro durante o carregamento
  const { isAdmin, isInstructor, isLoading } = userRoleState;
  const safeIsAdmin = isLoading ? false : isAdmin;
  const safeIsInstructor = isLoading ? false : isInstructor;
  
  // Filtrar itens baseado nas novas permissões de roles
  const visibleItems = useMemo(() => {
    return items.filter(item => {
      // Legacy: Se é adminOnly, só admins podem ver
      if (item.adminOnly && !safeIsAdmin) {
        return false;
      }
      
      // Legacy: Se é instructorOnly, só instrutores ou admins podem ver
      if (item.instructorOnly && !safeIsInstructor && !safeIsAdmin) {
        return false;
      }

      // Nova lógica baseada em role-specific access
      if (item.instructorAccess !== undefined || item.studentAccess !== undefined) {
        switch (userRole) {
          case 'admin':
            return true; // Admins sempre têm acesso (a menos que explicitamente negado)
          case 'instructor':
            return item.instructorAccess === true;
          case 'student':
            return item.studentAccess === true;
          default:
            return false;
        }
      }

      // Verificações específicas para certas rotas
      if (item.href === '/turmas') {
        return canAccessTurmas;
      }
      
      if (item.href === '/aulas/livres') {
        return canAccessAulasLivres;
      }
      
      if (item.href === '/presenca') {
        return canAccessPresenca;
      }
      
      // Lógica especial: se for admin, não mostrar Home; se não for admin, não mostrar Dashboard
      if (item.name === "Dashboard" && !safeIsAdmin) {
        return false;
      }
      
      if (item.name === "Home" && safeIsAdmin) {
        return false;
      }
      
      return true;
    });
  }, [items, safeIsAdmin, safeIsInstructor, userRole, canAccessTurmas, canAccessAulasLivres, canAccessPresenca]);
  
  // Se a seção for apenas para admin e o usuário não for admin, não mostrar
  if (adminOnly && !safeIsAdmin) {
    return null;
  }
  
  // Se não houver itens visíveis, não mostrar a seção
  if (visibleItems.length === 0) {
    return null;
  }

  const themeColor = primaryColor || 'var(--primary)';

  return (
    <li className={clsx(
      "relative",
      isHovered && "group"
    )}>
      {isHovered && isCollapsible && (
        <div 
          className={clsx(
            "flex items-center justify-between px-3 py-2 mb-1",
            "cursor-pointer rounded-xl",
            "transition-colors duration-200 ease-in-out",
            "hover:bg-gray-100/50 dark:hover:bg-gray-800/30",
            "group-hover:shadow-sm"
          )} 
          onClick={onToggle}
          onMouseEnter={() => setHovered(true)}
          onMouseLeave={() => setHovered(false)}
        >
          <h2 
            className={clsx(
              "text-xs font-semibold tracking-wide",
              "transition-opacity duration-200",
            )}
            style={{ 
              color: hovered ? themeColor : `${themeColor}80` 
            }}
          >
            {title}
          </h2>
          <ChevronDownIcon
            className={clsx(
              "h-3.5 w-3.5 shrink-0",
              "transition-all duration-200 ease-in-out",
              isExpanded ? "rotate-180" : "rotate-0",
            )}
            style={{ 
              color: hovered ? themeColor : `${themeColor}80`
            }}
            aria-hidden="true"
          />
        </div>
      )}
      
      {!isCollapsible && isHovered && (
        <div className={clsx(
          "px-3 py-2 mb-1",
          "rounded-xl",
          "transition-colors duration-200 ease-in-out"
        )}>
          <h2 
            className="text-xs font-semibold tracking-wide"
            style={{ color: `${themeColor}90` }}
          >
            {title}
          </h2>
        </div>
      )}

      <div className={clsx(
        "overflow-hidden will-change-[max-height]",
        "transition-all duration-200 ease-in-out",
        isHovered && isExpanded 
          ? "max-h-96 opacity-100" 
          : isHovered && isCollapsible
            ? "max-h-0 opacity-0" 
            : "max-h-none opacity-100"
      )}>
        <ul role="list" className={clsx(
          "space-y-1 px-2",
          isHovered && isExpanded && "animate-fadeIn"
        )}>
          {visibleItems.map((item) => (
            <SidebarLink 
              key={item.name} 
              item={item} 
              isAdmin={safeIsAdmin} 
              isHovered={isHovered}
              showFavoriteButton={showFavoriteButton}
            />
          ))}
        </ul>
      </div>
    </li>
  );
} 