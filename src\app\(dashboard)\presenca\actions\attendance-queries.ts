"use server";

import { createClient } from "@/services/supabase/server";
import { getCurrentUser } from "@/services/auth/actions/auth-actions";
import {
  AttendanceFilterSchema,
  StudentAttendanceFilterSchema,
  AttendanceListFilterSchema,
  RemoveAttendanceSchema,
} from "../../aulas/actions/schemas/index";
import type { PaginatedResult, ClassWithDetails } from "../../aulas/types";
import { revalidatePath } from "next/cache";
import type { AttendanceRecord, ClassAttendanceRecord } from './attendance-utils';
import { getClassStatus } from './attendance-utils';

/**
 * Busca lista de presença de uma aula
 */
export async function getAttendanceByClass(filters: unknown): Promise<{
  success: boolean;
  data?: PaginatedResult<ClassAttendanceRecord>;
  errors?: any;
}> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const validationResult = AttendanceFilterSchema.safeParse(filters);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const validatedFilters = validationResult.data;
    const supabase = await createClient();

    if (!validatedFilters.class_id) {
      return { success: false, errors: { class_id: "ID da aula é obrigatório" } };
    }

    const { data: classData, error: classError } = await supabase
      .from("classes")
      .select("id, name, instructor_id, class_group_id")
      .eq("id", validatedFilters.class_id)
      .eq("tenant_id", tenantId)
      .single();

    if (classError || !classData) {
      return { success: false, errors: { class_id: "Aula não encontrada" } };
    }

    // Verificar permissão (admin, instrutor da aula ou instrutor responsável pela turma)
    if (user.app_metadata?.role !== "admin" && classData.instructor_id !== user.id) {
      // Se não é instrutor direto da aula, verificar se a aula pertence a uma turma que ele instrui
      if (classData.class_group_id) {
        const { data: turmaData, error: turmaError } = await supabase
          .from('class_groups')
          .select('instructor_id')
          .eq('id', classData.class_group_id)
          .single();

        if (turmaError || !turmaData || turmaData.instructor_id !== user.id) {
          return { success: false, errors: { _form: "Sem permissão para visualizar esta lista de presença" } };
        }
      } else {
        return { success: false, errors: { _form: "Sem permissão para visualizar esta lista de presença" } };
      }
    }

    let query = supabase
      .from("attendance")
      .select(`
        id,
        student_id,
        checked_in_at,
        notes,
        student:students (
          id,
          check_in_code,
          user:users!students_user_id_fkey (
            first_name,
            last_name,
            full_name,
            email,
            avatar_url
          ),
          current_belt_id
        )
      `, { count: 'exact' })
      .eq("tenant_id", tenantId)
      .eq("class_id", validatedFilters.class_id);

    if (validatedFilters.student_id) {
      query = query.eq("student_id", validatedFilters.student_id);
    }

    if (validatedFilters.date_from) {
      query = query.gte("checked_in_at", validatedFilters.date_from);
    }

    if (validatedFilters.date_to) {
      query = query.lte("checked_in_at", validatedFilters.date_to);
    }

    if (validatedFilters.checked_in_by) {
      query = query.eq("checked_in_by", validatedFilters.checked_in_by);
    }

    query = query.order(validatedFilters.sort_by, { ascending: validatedFilters.sort_order === 'asc' });

    const start = (validatedFilters.page - 1) * validatedFilters.limit;
    const end = start + validatedFilters.limit - 1;
    query = query.range(start, end);

    const { data: attendanceRecords, error, count } = await query;

    if (error) {
      console.error("Erro ao buscar lista de presença:", error);
      return { success: false, errors: { _form: "Erro ao buscar lista de presença" } };
    }

    const totalPages = Math.ceil((count || 0) / validatedFilters.limit);

    const transformedData: ClassAttendanceRecord[] = await Promise.all(
      (attendanceRecords || []).map(async (record) => {
        const studentData = record.student as any;
        const userData = studentData?.user as any;

        // buscar detalhes da faixa via RPC
        let beltData: any = null;
        if (studentData) {
          const { data: beltDetails } = await supabase.rpc(
            'get_student_current_belt_details',
            { student_id_param: studentData.id }
          );
          if (beltDetails && beltDetails.length > 0) beltData = beltDetails[0];
        }

        return {
          id: record.id,
          student_id: record.student_id,
          checked_in_at: record.checked_in_at,
          notes: record.notes,
          student: {
            id: studentData?.id || '',
            user: {
              first_name: userData?.first_name || '',
              last_name: userData?.last_name || null,
              full_name: userData?.full_name || null,
              email: userData?.email || '',
              avatar_url: userData?.avatar_url || '',
            },
            check_in_code: studentData?.check_in_code || null,
            current_belt: beltData ? {
              belt_color: beltData.belt_color,
              degree: beltData.degree,
              label: beltData.label,
              stripe_color: beltData.stripe_color,
              show_center_line: beltData.show_center_line,
              center_line_color: beltData.center_line_color,
            } : null,
          },
        };
      })
    );

    return {
      success: true,
      data: {
        data: transformedData,
        pagination: {
          page: validatedFilters.page,
          limit: validatedFilters.limit,
          total: count || 0,
          totalPages,
          hasNext: validatedFilters.page < totalPages,
          hasPrev: validatedFilters.page > 1,
        },
      },
    };
  } catch (error) {
    console.error("Erro ao buscar lista de presença:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Busca histórico de presença de um aluno
 */
export async function getAttendanceByStudent(filters: unknown): Promise<{
  success: boolean;
  data?: PaginatedResult<AttendanceRecord>;
  errors?: any;
}> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const validationResult = StudentAttendanceFilterSchema.safeParse(filters);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const validatedFilters = validationResult.data;
    const supabase = await createClient();

    const { data: student, error: studentError } = await supabase
      .from("students")
      .select("id, user_id")
      .eq("id", validatedFilters.student_id)
      .eq("tenant_id", tenantId)
      .single();

    if (studentError || !student) {
      return { success: false, errors: { student_id: "Aluno não encontrado" } };
    }

    const isOwnRecord = student.user_id === user.id;
    const isAdmin = user.app_metadata?.role === "admin";
    const isInstructor = user.app_metadata?.role === "instructor";

    if (!isOwnRecord && !isAdmin && !isInstructor) {
      return { success: false, errors: { _form: "Sem permissão para visualizar este histórico" } };
    }

    let query = supabase
      .from("attendance")
      .select(`
        *,
        student:students (
          id,
          check_in_code,
          user:users!students_user_id_fkey (
            first_name,
            last_name,
            full_name,
            avatar_url
          )
        ),
        class:classes (
          id,
          name,
          start_time,
          end_time
        ),
        checked_in_by_user:users!attendance_checked_in_by_fkey (
          first_name,
          last_name,
          full_name
        )
      `, { count: 'exact' })
      .eq("tenant_id", tenantId)
      .eq("student_id", validatedFilters.student_id);

    if (validatedFilters.date_from) {
      query = query.gte("checked_in_at", validatedFilters.date_from);
    }

    if (validatedFilters.date_to) {
      query = query.lte("checked_in_at", validatedFilters.date_to);
    }

    query = query.order(validatedFilters.sort_by, { ascending: validatedFilters.sort_order === 'asc' });

    const start = (validatedFilters.page - 1) * validatedFilters.limit;
    const end = start + validatedFilters.limit - 1;
    query = query.range(start, end);

    const { data: attendanceRecords, error, count } = await query;

    if (error) {
      console.error("Erro ao buscar histórico de presença:", error);
      return { success: false, errors: { _form: "Erro ao buscar histórico de presença" } };
    }

    const totalPages = Math.ceil((count || 0) / validatedFilters.limit);

    const transformedData: AttendanceRecord[] = (attendanceRecords || []).map(record => {
      const studentData = record.student as any;
      const userData = studentData?.user as any;
      const classData = record.class as any;
      const checkedInByData = record.checked_in_by_user as any;
      
      return {
        ...record,
        student: {
          id: studentData?.id || '',
          user: {
            first_name: userData?.first_name || '',
            last_name: userData?.last_name || null,
            full_name: userData?.full_name || null,
            avatar_url: userData?.avatar_url || null,
          },
          check_in_code: studentData?.check_in_code || null,
        },
        class: {
          id: classData?.id || '',
          name: classData?.name || '',
          start_time: classData?.start_time || '',
          end_time: classData?.end_time || '',
        },
        checked_in_by_user: {
          first_name: checkedInByData?.first_name || '',
          last_name: checkedInByData?.last_name || null,
          full_name: checkedInByData?.full_name || null,
        },
      };
    });

    return {
      success: true,
      data: {
        data: transformedData,
        pagination: {
          page: validatedFilters.page,
          limit: validatedFilters.limit,
          total: count || 0,
          totalPages,
          hasNext: validatedFilters.page < totalPages,
          hasPrev: validatedFilters.page > 1,
        },
      },
    };
  } catch (error) {
    console.error("Erro ao buscar histórico de presença:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Remove presença de um aluno de uma aula
 */
export async function removeAttendance(data: unknown) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const userRole = user.app_metadata?.role;
    if (!['admin', 'instructor'].includes(userRole)) {
      return { success: false, errors: { _form: "Sem permissão para remover presença" } };
    }

    const validationResult = RemoveAttendanceSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const validatedData = validationResult.data;
    const supabase = await createClient();

    const { data: attendanceRecord, error: attendanceError } = await supabase
      .from("attendance")
      .select(`
        id,
        student_id,
        class_id,
        checked_in_at,
        checked_in_by,
        notes,
        student:students (
          id,
          user:users!students_user_id_fkey (
            first_name,
            last_name,
            full_name
          )
        ),
        class:classes (
          id,
          name,
          start_time,
          end_time,
          status
        )
      `)
      .eq("id", validatedData.attendance_id)
      .eq("tenant_id", tenantId)
      .single();

    if (attendanceError || !attendanceRecord) {
      return { success: false, errors: { attendance_id: "Registro de presença não encontrado" } };
    }

    const classData = attendanceRecord.class as any;
    const classEndTime = new Date(classData.end_time);
    const now = new Date();
    const hoursAfterClass = (now.getTime() - classEndTime.getTime()) / (1000 * 60 * 60);

    if (hoursAfterClass > 24) {
      return { 
        success: false, 
        errors: { 
          _form: "Não é possível remover presença de aulas que terminaram há mais de 24 horas" 
        } 
      };
    }

    const { error: deleteError } = await supabase
      .from("attendance")
      .delete()
      .eq("id", validatedData.attendance_id)
      .eq("tenant_id", tenantId);

    if (deleteError) {
      console.error("Erro ao remover presença:", deleteError);
      return { success: false, errors: { _form: "Erro ao remover presença" } };
    }

    const studentData = attendanceRecord.student as any;
    const studentName = studentData?.user?.full_name || studentData?.user?.first_name || 'Aluno';
    
    console.log(`Presença removida: ${studentName} da aula ${classData.name} por ${user.email}. Motivo: ${validatedData.reason || 'Não informado'}`);

    revalidatePath("/aulas");
    
    return { 
      success: true, 
      data: {
        removed_attendance: {
          id: attendanceRecord.id,
          student_name: studentName,
          class_name: classData.name,
          checked_in_at: attendanceRecord.checked_in_at,
          removed_by: user.email,
          reason: validatedData.reason
        }
      }
    };
  } catch (error) {
    console.error("Erro ao remover presença:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}
/**
 * Exporta a lista de presença de uma aula em formato CSV
 */
export async function exportAttendanceList(classId: string): Promise<{
  success: boolean;
  data?: {
    csvContent: string;
    filename: string;
  };
  errors?: any;
}> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const supabase = await createClient();

    // Verificar se a aula existe e se o usuário tem permissão
    const { data: classData, error: classError } = await supabase
      .from("classes")
      .select(`
        id,
        name,
        start_time,
        instructor_id,
        class_group_id,
        instructor:users!classes_instructor_id_fkey(
          full_name
        ),
        branch:branches!classes_branch_id_fkey(
          name
        ),
        class_group:class_groups!classes_class_group_id_fkey(
          name
        )
      `)
      .eq("id", classId)
      .eq("tenant_id", tenantId)
      .single();

    if (classError || !classData) {
      return { success: false, errors: { _form: "Aula não encontrada" } };
    }

    // Verificar permissão (admin, instrutor da aula ou instrutor responsável pela turma)
    if (user.app_metadata?.role !== "admin" && classData.instructor_id !== user.id) {
      // Se não é instrutor direto da aula, verificar se a aula pertence a uma turma que ele instrui
      if (classData.class_group_id) {
        const { data: turmaData, error: turmaError } = await supabase
          .from('class_groups')
          .select('instructor_id')
          .eq('id', classData.class_group_id)
          .single();

        if (turmaError || !turmaData || turmaData.instructor_id !== user.id) {
          return { success: false, errors: { _form: "Sem permissão para exportar esta lista de presença" } };
        }
      } else {
        return { success: false, errors: { _form: "Sem permissão para exportar esta lista de presença" } };
      }
    }

    // Buscar todos os registros de presença da aula
    const { data: attendanceRecords, error: attendanceError } = await supabase
      .from("attendance")
      .select(`
        id,
        student_id,
        checked_in_at,
        notes,
        student:students (
          id,
          check_in_code,
          user:users!students_user_id_fkey (
            first_name,
            last_name,
            full_name,
            email
          )
        )
      `)
      .eq("tenant_id", tenantId)
      .eq("class_id", classId)
      .order("checked_in_at", { ascending: true });

    if (attendanceError) {
      console.error("Erro ao buscar lista de presença:", attendanceError);
      return { success: false, errors: { _form: "Erro ao buscar lista de presença" } };
    }

    // Preparar dados para CSV
    const csvHeaders = [
      "Nome do Aluno",
      "Email",
      "Faixa",
      "Grau",
      "Código Check-in",
      "Data/Hora Check-in",
      "Observações"
    ];

    const csvRows = await Promise.all(
      (attendanceRecords || []).map(async (record) => {
        const studentData = record.student as any;
        const userData = studentData?.user as any;

        // Buscar faixa atual via RPC
        let beltColorLabel = '';
        let beltDegree = '';
        if (studentData?.id) {
          const { data: beltDetails } = await supabase.rpc('get_student_current_belt_details', { student_id_param: studentData.id });
          if (beltDetails && beltDetails.length > 0) {
            const belt = beltDetails[0] as any;
            const beltColorMap: Record<string, string> = {
              white: 'Branca',
              blue: 'Azul',
              purple: 'Roxa',
              brown: 'Marrom',
              black: 'Preta',
            };
            beltColorLabel = beltColorMap[belt.belt_color] || belt.belt_color;
            beltDegree = belt.degree?.toString() || '';
          }
        }

        const checkedInDate = record.checked_in_at
          ? new Date(record.checked_in_at).toLocaleString('pt-BR', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
              hour: '2-digit',
              minute: '2-digit',
              timeZone: 'America/Sao_Paulo',
            })
          : '';

        return [
          userData?.full_name || `${userData?.first_name || ''} ${userData?.last_name || ''}`.trim() || '',
          userData?.email || '',
          beltColorLabel,
          beltDegree,
          studentData?.check_in_code || '',
          checkedInDate,
          record.notes || '',
        ];
      })
    );

    // Construir CSV
    const escapeCsvField = (field: string): string => {
      if (!field) return '';
      // Se contém vírgula, aspas ou quebra de linha, deve ser escapado
      if (field.includes(',') || field.includes('"') || field.includes('\n')) {
        return `"${field.replace(/"/g, '""')}"`;
      }
      return field;
    };

    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => row.map(escapeCsvField).join(','))
    ].join('\n');

    // Gerar nome do arquivo
    const classDate = new Date(classData.start_time).toLocaleDateString('pt-BR');
    const className = classData.name.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
    const filename = `presenca_${className}_${classDate.replace(/\//g, '-')}.csv`;

    return {
      success: true,
      data: {
        csvContent,
        filename
      }
    };

  } catch (error) {
    console.error("Erro ao exportar lista de presença:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 