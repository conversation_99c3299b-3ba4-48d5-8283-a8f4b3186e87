'use server';

import { z } from 'zod';

import { createTenantServerClient } from '@/services/supabase/server';
import { getTenantSlug } from '@/services/tenant/server';

interface SearchStudentRequest {
  searchTerm: string;
}

export interface SearchStudentResult {
  id: string;
  name: string;
  avatar?: string;
  membership?: string;
}

const requestSchema = z.object({
  searchTerm: z.string().trim().min(1, 'Termo de busca obrigatório'),
});

/**
 * Busca alunos pelo nome (ou parte) de forma performática.
 * Retorna até 10 resultados ordenados alfabeticamente.
 *
 * Obs.: a busca é limitada ao tenant atual.
 */
export async function searchStudentsByName(
  data: unknown,
): Promise<
  | { success: true; data: SearchStudentResult[] }
  | { success: false; error: string }
> {
  const parsed = requestSchema.safeParse(data);
  if (!parsed.success) {
    return { success: false, error: 'Parâmetros inválidos' };
  }

  const { searchTerm } = parsed.data as SearchStudentRequest;

  try {
    console.log('Iniciando busca de alunos por nome:', searchTerm);
    
    const supabase = await createTenantServerClient();
    console.log('Cliente Supabase criado com sucesso');
    
    const tenantSlug = await getTenantSlug();
    console.log('Tenant slug obtido:', tenantSlug);

    if (!tenantSlug) {
      console.error('Tenant slug não encontrado');
      return { success: false, error: 'Tenant não encontrado' };
    }

    // Obter o tenant_id correspondente ao slug
    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .select('id')
      .eq('slug', tenantSlug)
      .single();

    if (tenantError) {
      console.error('Erro ao buscar tenant:', tenantError);
      return { success: false, error: 'Erro ao buscar configurações do tenant' };
    }

    if (!tenant) {
      console.error('Tenant não encontrado para slug:', tenantSlug);
      return { success: false, error: 'Tenant não encontrado' };
    }

    console.log('Tenant encontrado:', tenant.id);

    // Realizar consulta performática usando ILIKE no search_vector de students/users
    // Limitar resultados e selecionar apenas os campos necessários
    const { data: students, error } = await supabase
      .from('students')
      .select(
        `id,
         users:users!students_user_id_fkey!inner(id, full_name, avatar_url),
         subscriptions(status, current_period_end)`
      )
      .eq('tenant_id', tenant.id)
      .ilike('users.full_name', `%${searchTerm}%`)
      .order('full_name', { ascending: true, foreignTable: 'users' })
      .limit(10);

    if (error) {
      console.error('Erro ao buscar alunos:', error);
      return { success: false, error: 'Erro ao buscar alunos' };
    }

    console.log(`Encontrados ${students?.length || 0} alunos`);

    const formatted: SearchStudentResult[] = (students || []).map((student: any) => {
      const user = student.users || {};
      const subs = (student.subscriptions || []) as Array<{ status: string; current_period_end: string }>;
      const activeSub = subs.find((s) => s.status === 'active');

      return {
        id: student.id,
        name: user.full_name || 'Aluno',
        avatar: user.avatar_url || undefined,
        membership: activeSub ? `Ativo até ${new Date(activeSub.current_period_end).toLocaleDateString('pt-BR')}` : 'Sem plano ativo',
      };
    });

    console.log('Busca concluída com sucesso');
    return { success: true, data: formatted };
  } catch (err) {
    console.error('Erro inesperado ao buscar alunos:', err);
    
    // Verificar se é um erro de configuração do Supabase
    if (err instanceof Error && err.message.includes('Configuração do Supabase não encontrada')) {
      return { 
        success: false, 
        error: 'Erro de configuração do sistema. Entre em contato com o suporte.' 
      };
    }
    
    // Verificar se é um erro de rede ou conectividade
    if (err instanceof Error && (err.message.includes('fetch') || err.message.includes('network'))) {
      return { 
        success: false, 
        error: 'Erro de conectividade. Verifique sua conexão e tente novamente.' 
      };
    }
    
    return { success: false, error: 'Erro inesperado' };
  }
} 