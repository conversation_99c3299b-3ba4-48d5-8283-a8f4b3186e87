'use client'

import { ChevronLeftIcon, ChevronRightIcon } from 'lucide-react'
import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import { slides as defaultSlides } from './data'
import { useTenantTheme } from '@/hooks/tenant/use-tenant-theme'
import { useTenantCarousel } from '@/hooks/tenant/use-tenant-carousel'
import { Slide } from './types'

export function AuthCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const { logoUrl, tenantName, description } = useTenantTheme()
  const { carouselData } = useTenantCarousel()
  
  const slides = carouselData.length > 0 ? carouselData : defaultSlides

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % slides.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [slides.length])

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev - 1 + slides.length) % slides.length)
  }

  const handleNext = () => {
    setCurrentIndex((prev) => (prev + 1) % slides.length)
  }

  const currentSlide = slides[currentIndex] || defaultSlides[0]
  const hasImage = !!currentSlide.image && currentSlide.image !== ''

  return (
    <div className="relative h-full w-full overflow-hidden rounded-lg">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentIndex}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
          className="h-full w-full"
        >
          {hasImage ? (
            <div className="relative h-full w-full">
              <Image
                src={currentSlide.image}
                alt={currentSlide.title}
                className="h-full w-full object-cover"
                fill
                sizes="100vw"
              />
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-6 text-white">
                <h3 className="text-xl font-bold">{currentSlide.title}</h3>
                {currentSlide.subtitle && (
                  <h4 className="text-lg font-semibold">{currentSlide.subtitle}</h4>
                )}
                <p className="mt-2">{currentSlide.description}</p>
              </div>
            </div>
          ) : (
            <div className={`h-full w-full bg-gradient-to-br ${currentSlide.gradient || 'from-gray-900 to-black'}`}>
              <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
                <motion.div
                  initial={{ scale: 0.5, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  <Image
                    src={logoUrl || "/logo.svg"}
                    alt={`Logo ${tenantName || 'Academia'}`}
                    width={96}
                    height={96}
                    priority
                    className="mb-8 object-contain"
                  />
                </motion.div>
                <motion.h2
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.3 }}
                  className="text-4xl font-bold"
                >
                  {currentSlide.title}
                </motion.h2>
                {currentSlide.subtitle && (
                  <motion.h3
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.4 }}
                    className="text-3xl font-semibold"
                  >
                    {currentSlide.subtitle}
                  </motion.h3>
                )}
                <motion.p
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="mt-2 text-lg"
                >
                  {currentSlide.description}
                </motion.p>
              </div>
            </div>
          )}
        </motion.div>
      </AnimatePresence>

      <button
        onClick={handlePrevious}
        className="absolute left-4 top-1/2 -translate-y-1/2 rounded-full bg-black/30 p-2 text-white transition-colors hover:bg-black/50"
        aria-label="Slide anterior"
      >
        <ChevronLeftIcon className="h-6 w-6" />
      </button>

      <button
        onClick={handleNext}
        className="absolute right-4 top-1/2 -translate-y-1/2 rounded-full bg-black/30 p-2 text-white transition-colors hover:bg-black/50"
        aria-label="Próximo slide"
      >
        <ChevronRightIcon className="h-6 w-6" />
      </button>
      
      <div className="absolute bottom-4 left-1/2 flex -translate-x-1/2 gap-2">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            className={`h-2 rounded-full transition-all ${
              index === currentIndex
                ? 'w-4 bg-white'
                : 'w-2 bg-white/50 hover:bg-white/75'
            }`}
            aria-label={`Ir para slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  )
} 