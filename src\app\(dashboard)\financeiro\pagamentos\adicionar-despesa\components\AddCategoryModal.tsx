'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Tag, Palette } from 'lucide-react';
import { ExpenseCategory } from '../../components/types';
import { createExpenseCategory } from '../../actions/expense-categories-actions';
import { toast } from 'sonner';

// Schema de validação para nova categoria
const addCategorySchema = z.object({
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  description: z.string().optional(),
  color: z.string().min(1, 'Selecione uma cor'),
});

type AddCategoryFormValues = z.infer<typeof addCategorySchema>;

// Cores predefinidas para as categorias
const CATEGORY_COLORS = [
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // Yellow
  '#EF4444', // Red
  '#8B5CF6', // Purple
  '#F97316', // Orange
  '#06B6D4', // Cyan
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6B7280', // Gray
  '#14B8A6', // Teal
  '#A855F7', // Violet
];

interface AddCategoryModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCategoryAdd: (category: ExpenseCategory) => void;
}

export function AddCategoryModal({
  open,
  onOpenChange,
  onCategoryAdd,
}: AddCategoryModalProps) {
  const [selectedColor, setSelectedColor] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<AddCategoryFormValues>({
    resolver: zodResolver(addCategorySchema),
    defaultValues: {
      name: '',
      description: '',
      color: '',
    },
  });

  const onSubmit = async (data: AddCategoryFormValues) => {
    setIsSubmitting(true);

    try {
      // Criar categoria usando a action do Supabase
      const result = await createExpenseCategory({
        name: data.name,
        description: data.description,
        color: data.color,
      });

      if (result.success && result.data) {
        // Notificar sucesso
        toast.success('Categoria criada com sucesso!');

        // Adicionar categoria à lista
        onCategoryAdd(result.data);

        // Reset form e fechar modal
        form.reset();
        setSelectedColor('');
        onOpenChange(false);
      } else {
        // Mostrar erro
        toast.error(result.error || 'Erro ao criar categoria');
      }
    } catch (error) {
      console.error('Erro ao criar categoria:', error);
      toast.error('Erro interno. Tente novamente.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleColorSelect = (color: string) => {
    setSelectedColor(color);
    form.setValue('color', color);
    form.clearErrors('color');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <Tag className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
            <span>Adicionar Nova Categoria</span>
          </DialogTitle>
          <DialogDescription>
            Crie uma nova categoria para organizar suas despesas.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form className="space-y-4">
            {/* Nome da Categoria */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome da Categoria</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Ex: Equipamentos, Salários, Aluguel"
                      className="bg-gray-50 dark:bg-gray-800"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Descrição (Opcional) */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição (Opcional)</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Descreva o tipo de despesas desta categoria"
                      className="bg-gray-50 dark:bg-gray-800 resize-none"
                      rows={2}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Seletor de Cor */}
            <FormField
              control={form.control}
              name="color"
              render={() => (
                <FormItem>
                  <FormLabel className="flex items-center space-x-2">
                    <Palette className="h-4 w-4" />
                    <span>Cor da Categoria</span>
                  </FormLabel>
                  <FormControl>
                    <div className="grid grid-cols-6 gap-2">
                      {CATEGORY_COLORS.map((color) => (
                        <button
                          key={color}
                          type="button"
                          onClick={() => handleColorSelect(color)}
                          className={`w-8 h-8 rounded-full border-2 transition-all duration-200 hover:scale-110 ${
                            selectedColor === color
                              ? 'border-gray-900 dark:border-gray-100 shadow-lg'
                              : 'border-gray-300 dark:border-gray-600'
                          }`}
                          style={{ backgroundColor: color }}
                          title={`Selecionar cor ${color}`}
                        />
                      ))}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancelar
              </Button>
              <Button
                type="button"
                disabled={isSubmitting}
                className="bg-blue-600 hover:bg-blue-700"
                onClick={form.handleSubmit(onSubmit)}
              >
                {isSubmitting ? 'Criando...' : 'Criar Categoria'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
