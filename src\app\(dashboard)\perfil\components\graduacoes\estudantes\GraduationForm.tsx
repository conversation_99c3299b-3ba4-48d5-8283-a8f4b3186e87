'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Calendar, Clock, Award, User, AlertTriangle, CheckCircle, GraduationCap, X } from 'lucide-react';
import { BeltWithDetails } from '@/components/belt';
import {
  getModalitiesForGraduation,
  getBeltLevelsForModality,
  getInstructorsForGraduation,
  graduateStudent,
  type GraduationFormData
} from '../../../actions/graduation-actions';
import { formatToDatetimeLocal, datetimeLocalToBrasiliaISO, nowInBrasilia } from '@/utils/timezone-utils';

interface GraduationFormProps {
  userId: string;
  isOpen: boolean;
  currentRequirements?: Array<{
    id: string;
    nome: string;
    concluido: boolean;
    progresso: number;
  }>;
  onSuccess?: () => void;
  onCancel?: () => void;
}

interface Modality {
  id: string;
  slug: string;
  name: string;
  enabled: boolean;
}

interface BeltLevel {
  id: string;
  belt_color: string;
  degree: number;
  label: string;
  sort_order: number;
  stripe_color?: string | null;
  show_center_line?: boolean | null;
  center_line_color?: string | null;
}

interface Instructor {
  id: string;
  first_name: string;
  last_name: string | null;
  full_name: string | null;
}

export function GraduationForm({ 
  userId, 
  isOpen,
  currentRequirements = [], 
  onSuccess, 
  onCancel 
}: GraduationFormProps) {
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // Dados do formulário
  const [modalities, setModalities] = useState<Modality[]>([]);
  const [beltLevels, setBeltLevels] = useState<BeltLevel[]>([]);
  const [instructors, setInstructors] = useState<Instructor[]>([]);
  
  // Estado do formulário
  const [selectedModality, setSelectedModality] = useState<string>('');
  const [selectedBeltLevel, setSelectedBeltLevel] = useState<string>('');
  const [selectedInstructor, setSelectedInstructor] = useState<string>('');
  const [graduationDateTime, setGraduationDateTime] = useState<string>('');

  // Função para definir data/hora atual do Brasil
  const setCurrentDateTime = () => {
    const brasilDateTime = formatToDatetimeLocal(nowInBrasilia());
    setGraduationDateTime(brasilDateTime);
  };

  // Carregar dados iniciais
  useEffect(() => {
    const loadInitialData = async () => {
      setLoading(true);
      try {
        // Carregar modalidades e instrutores em paralelo
        const [modalitiesResult, instructorsResult] = await Promise.all([
          getModalitiesForGraduation(),
          getInstructorsForGraduation()
        ]);

        if (modalitiesResult.success && modalitiesResult.data) {
          setModalities(modalitiesResult.data);
        } else {
          setErrors(prev => ({ ...prev, _form: modalitiesResult.errors?._form || 'Erro ao carregar modalidades' }));
        }

        if (instructorsResult.success && instructorsResult.data) {
          setInstructors(instructorsResult.data);
        } else {
          setErrors(prev => ({ ...prev, _form: 'Erro ao carregar instrutores' }));
        }

        // Definir data/hora atual por padrão
        setCurrentDateTime();
      } catch (error) {
        console.error('Erro ao carregar dados iniciais:', error);
        setErrors({ _form: 'Erro ao carregar dados do formulário' });
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Carregar níveis de faixa quando modalidade for selecionada
  useEffect(() => {
    const loadBeltLevels = async () => {
      if (!selectedModality) {
        setBeltLevels([]);
        setSelectedBeltLevel('');
        return;
      }

      const selectedModalityData = modalities.find(m => m.id === selectedModality);
      if (!selectedModalityData) return;

      try {
        const result = await getBeltLevelsForModality(selectedModalityData.slug);
        if (result.success && result.data) {
          setBeltLevels(result.data);
        } else {
          setErrors(prev => ({ ...prev, modality: 'Erro ao buscar níveis de faixa' }));
        }
      } catch (error) {
        console.error('Erro ao carregar níveis de faixa:', error);
        setErrors(prev => ({ ...prev, modality: 'Erro ao buscar níveis de faixa' }));
      }
    };

    loadBeltLevels();
  }, [selectedModality, modalities]);

  // Status dos requisitos
  const requirementsStatus = currentRequirements.length > 0 ? {
    total: currentRequirements.length,
    completed: currentRequirements.filter(r => r.concluido).length,
    percentage: Math.round((currentRequirements.filter(r => r.concluido).length / currentRequirements.length) * 100)
  } : null;

  // Submissão do formulário
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setErrors({});

    try {
      const formData: GraduationFormData = {
        studentId: userId, // Será convertido para student_id na action
        modalityId: selectedModality,
        beltLevelId: selectedBeltLevel,
        instructorId: selectedInstructor,
        awardedAt: datetimeLocalToBrasiliaISO(graduationDateTime)
      };

      const result = await graduateStudent(userId, formData);

      if (result.success) {
        onSuccess?.();
      } else {
        // Convert Zod errors to simple string record
        const formattedErrors: Record<string, string> = {};
        if (result.errors) {
          Object.entries(result.errors).forEach(([key, value]) => {
            if (typeof value === 'string') {
              formattedErrors[key] = value;
            } else if (value && typeof value === 'object' && 'message' in value) {
              formattedErrors[key] = value.message as string;
            }
          });
        }
        setErrors(Object.keys(formattedErrors).length > 0 ? formattedErrors : { _form: 'Erro desconhecido' });
      }
    } catch (error) {
      console.error('Erro ao graduar estudante:', error);
      setErrors({ _form: 'Erro interno do servidor' });
    } finally {
      setSubmitting(false);
    }
  };

  const selectedBeltLevelData = beltLevels.find(bl => bl.id === selectedBeltLevel);

  const getRequirementsVariant = () => {
    if (!requirementsStatus) return 'secondary';
    if (requirementsStatus.percentage >= 80) return 'statusActive';
    if (requirementsStatus.percentage >= 50) return 'statusCompleted';
    return 'destructive';
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onCancel?.()}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-2 pb-4">
          <div className="flex items-center gap-2">
            <div className="p-2 bg-primary/10 rounded-lg">
              <GraduationCap className="w-5 h-5 text-primary" />
            </div>
            <div className="flex-1">
              <DialogTitle className="text-lg">Graduar Estudante</DialogTitle>
              <DialogDescription>
                Promover o estudante para uma nova graduação
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
        {/* Status dos Requisitos */}
        {requirementsStatus && (
          <Card className="border border-muted bg-muted/30">
            <CardContent className="pt-4 pb-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Award className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Status dos Requisitos</span>
                </div>
                <Badge variant={getRequirementsVariant()}>
                  {requirementsStatus.percentage}%
                </Badge>
              </div>
              
              <div className="flex items-center justify-between text-sm text-muted-foreground mb-2">
                <span>
                  {requirementsStatus.completed} de {requirementsStatus.total} requisitos concluídos
                </span>
              </div>

              {requirementsStatus.percentage < 100 && (
                <div className="flex items-start gap-2 mt-3 p-2 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800">
                  <AlertTriangle className="w-4 h-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                  <p className="text-xs text-amber-700 dark:text-amber-300">
                    Nem todos os requisitos foram concluídos. A graduação ainda pode ser realizada.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Erros Gerais */}
        {errors._form && (
          <Alert variant="destructive" className="rounded-lg">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{errors._form}</AlertDescription>
          </Alert>
        )}

        {loading ? (
          <div className="space-y-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-10 bg-muted rounded-lg animate-pulse" />
            ))}
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Seleção de Modalidade */}
            <div className="space-y-3">
              <Label htmlFor="modality" className="text-sm font-medium">
                Modalidade
              </Label>
              <Select value={selectedModality} onValueChange={setSelectedModality}>
                <SelectTrigger className="rounded-lg">
                  <SelectValue placeholder="Selecione a modalidade" />
                </SelectTrigger>
                <SelectContent>
                  {modalities.map((modality) => (
                    <SelectItem key={modality.id} value={modality.id}>
                      {modality.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.modalityId && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <AlertTriangle className="w-3 h-3" />
                  {errors.modalityId}
                </p>
              )}
            </div>

            <Separator />

            {/* Seleção de Nível de Faixa */}
            <div className="space-y-3">
              <Label htmlFor="beltLevel" className="text-sm font-medium">
                Novo Nível de Graduação
              </Label>
              <Select 
                value={selectedBeltLevel} 
                onValueChange={setSelectedBeltLevel}
                disabled={!selectedModality || beltLevels.length === 0}
              >
                <SelectTrigger className="rounded-lg">
                  <SelectValue placeholder="Selecione o novo nível" />
                </SelectTrigger>
                <SelectContent>
                  {beltLevels.map((level) => (
                    <SelectItem key={level.id} value={level.id}>
                      <div className="flex items-center gap-3">
                        <BeltWithDetails
                          color={level.belt_color}
                          degree={level.degree}
                          stripeColor={level.stripe_color}
                          showCenterLine={level.show_center_line}
                          centerLineColor={level.center_line_color}
                          size="sm"
                          className="w-8 h-2"
                        />
                        <span>{level.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.beltLevelId && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <AlertTriangle className="w-3 h-3" />
                  {errors.beltLevelId}
                </p>
              )}
            </div>

            {/* Preview da Nova Faixa */}
            {selectedBeltLevelData && (
              <Card className="border-primary/20 bg-primary/5">
                <CardContent className="pt-4 pb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <CheckCircle className="w-4 h-4 text-primary" />
                    </div>
                    <BeltWithDetails
                      color={selectedBeltLevelData.belt_color}
                      degree={selectedBeltLevelData.degree}
                      stripeColor={selectedBeltLevelData.stripe_color}
                      showCenterLine={selectedBeltLevelData.show_center_line}
                      centerLineColor={selectedBeltLevelData.center_line_color}
                      size="md"
                      className="w-16 h-4"
                    />
                    <div>
                      <p className="text-sm font-medium text-primary">
                        Nova Graduação: {selectedBeltLevelData.label}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            <Separator />

            {/* Seleção de Instrutor */}
            <div className="space-y-3">
              <Label htmlFor="instructor" className="text-sm font-medium">
                Instrutor Responsável
              </Label>
              <Select value={selectedInstructor} onValueChange={setSelectedInstructor}>
                <SelectTrigger className="rounded-lg">
                  <SelectValue placeholder="Selecione o instrutor" />
                </SelectTrigger>
                <SelectContent>
                  {instructors.map((instructor) => (
                    <SelectItem key={instructor.id} value={instructor.id}>
                      <div className="flex items-center gap-2">
                        <User className="w-4 h-4" />
                        <span>{instructor.full_name || `${instructor.first_name} ${instructor.last_name || ''}`.trim()}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.instructorId && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <AlertTriangle className="w-3 h-3" />
                  {errors.instructorId}
                </p>
              )}
            </div>

            {/* Data e Hora da Graduação */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="dateTime" className="text-sm font-medium">
                  Data e Hora da Graduação
                </Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={setCurrentDateTime}
                  className="h-7 px-3 text-xs rounded-lg"
                >
                  <Clock className="w-3 h-3 mr-1" />
                  Agora
                </Button>
              </div>
              <Input
                id="dateTime"
                type="datetime-local"
                value={graduationDateTime}
                onChange={(e) => setGraduationDateTime(e.target.value)}
                required
                className="rounded-lg"
              />
              {errors.awardedAt && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <AlertTriangle className="w-3 h-3" />
                  {errors.awardedAt}
                </p>
              )}
            </div>

            <Separator />

            {/* Botões de Ação */}
            <div className="flex gap-3 pt-2">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={submitting}
                className="flex-1 rounded-lg"
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={submitting || !selectedModality || !selectedBeltLevel || !selectedInstructor || !graduationDateTime}
                className="flex-1 rounded-lg"
              >
                {submitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    Graduando...
                  </>
                ) : (
                  <>
                    <Award className="w-4 h-4 mr-2" />
                    Graduar
                  </>
                )}
              </Button>
            </div>
          </form>
        )}
        </div>
      </DialogContent>
    </Dialog>
  );
} 