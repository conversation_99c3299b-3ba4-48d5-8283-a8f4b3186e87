'use server';

import { createClient, createAdminClient } from '@/services/supabase/server'

/**
 * Tipo representando uma sessão ativa retornada da tabela auth.sessions
 */
export interface SessionInfo {
  id: string
  createdAt: string
  ip?: string | null
  userAgent?: string | null
  isCurrent: boolean
}

/**
 * Obtém todas as sessões ativas do usuário autenticado lendo direto da
 * tabela `auth.sessions` do Supabase.
 * 
 * Requer a Service Role Key ─ por isso utilizamos `createAdminClient`.
 * 
 * Retorna:
 *  { success: true, sessions: SessionInfo[] }
 *  { success: false, error: string }
 */
export async function getActiveSessions() {
  const supabase = await createClient()

  // 1. Autenticar o usuário de forma segura
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser()

  if (userError || !user) {
    return {
      success: false,
      error: 'Usuário não autenticado',
    } as const
  }

  // 1.1  Recuperar a sessão apenas para obter o refresh_token da sessão atual
  const {
    data: { session: currentSession },
  } = await supabase.auth.getSession()

  // Extrair session_id do access_token para comparar com a coluna id da tabela auth.sessions
  let currentSessionId: string | null = null
  if (currentSession?.access_token) {
    try {
      const payloadPart = currentSession.access_token.split('.')[1]
      const decoded = Buffer.from(payloadPart, 'base64').toString('utf-8')
      const payloadJson = JSON.parse(decoded)
      currentSessionId = payloadJson['session_id'] ?? null
    } catch {}
  }

  // 2. Chamar RPC que retorna as sessões do usuário
  type RawSessionRow = {
    id: string
    created_at: string
    ip: string | null
    user_agent: string | null
  }

  const { data: rows, error } = await supabase.rpc('get_active_sessions')

  if (error) {
    console.error('Erro ao obter sessões ativas via RPC:', error.message)
    return { success: false, error: error.message } as const
  }

  // 3. Mapear para estrutura amigável
  const typedRows: RawSessionRow[] = (rows ?? []) as RawSessionRow[]

  const sessions: SessionInfo[] = typedRows.map((row) => ({
    id: row.id,
    createdAt: row.created_at,
    ip: row.ip ?? null,
    userAgent: row.user_agent ?? null,
    isCurrent: currentSessionId ? row.id === currentSessionId : false,
  }))

  return { success: true, sessions } as const
}

/**
 * Encerra **todas** as sessões do usuário, exceto a sessão atual.
 * 
 * Estratégia:
 *  1. Identifica o `refresh_token` da sessão atual
 *  2. Deleta da tabela `auth.sessions` todas as sessões do usuário
 *     cujo `id` seja diferente do token atual.
 */
export async function endOtherSessions() {
  const supabase = await createClient()

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser()

  if (userError || !user) {
    return {
      success: false,
      error: 'Usuário não autenticado',
    } as const
  }

  // Utiliza escopo "others" para encerrar todas as sessões exceto a atual
  const { error } = await supabase.auth.signOut({ scope: 'others' as any })
  if (error) {
    console.error('Erro ao encerrar outras sessões:', error.message)
    return { success: false, error: error.message } as const
  }

  return { success: true } as const
}

/**
 * Encerra uma sessão específica (identificada por sessionId).
 */
export async function endSession(sessionId: string) {
  const supabase = await createClient()

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser()

  if (userError || !user) {
    return {
      success: false,
      error: 'Usuário não autenticado',
    } as const
  }

  const { error } = await supabase.rpc('end_session', {
    p_session_id: sessionId,
  })

  if (error) {
    console.error('Erro ao encerrar sessão via RPC:', error.message)
    return { success: false, error: error.message } as const
  }

  return { success: true } as const
} 