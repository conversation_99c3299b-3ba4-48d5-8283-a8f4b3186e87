import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Clock, Info } from 'lucide-react';
import { RECURRENCE_FREQUENCIES } from '../constants/class-group-constants';
import { DaysOfWeekSelector } from './DaysOfWeekSelector';
import type { RecurrencePatternSectionProps } from '../types/class-group-form-types';

export const RecurrencePatternSection = ({
  form,
  primaryColor,
  showRecurrenceDetails,
  setShowRecurrenceDetails,
}: RecurrencePatternSectionProps) => {
  const handleRecurrenceToggle = (checked: boolean) => {
    setShowRecurrenceDetails(checked);
    if (!checked) {
      form.setValue('recurrence_pattern', undefined);
    } else {
      form.setValue('recurrence_pattern', {
        frequency: 'weekly',
        interval: 1,
        daysOfWeek: [],
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="w-5 h-5" />
          Padrão de Recorrência
          <Badge variant="secondary">Opcional</Badge>
        </CardTitle>
        <CardDescription>
          Configure como as aulas serão geradas automaticamente
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center space-x-2">
          <Switch
            checked={showRecurrenceDetails}
            onCheckedChange={handleRecurrenceToggle}
          />
          <FormLabel>Configurar recorrência automática</FormLabel>
        </div>

        {showRecurrenceDetails && (
          <div className="space-y-4 p-4 border rounded-lg bg-slate-50 dark:bg-slate-800/50">
            <div className="flex items-start gap-2 text-sm text-blue-600 dark:text-blue-400">
              <Info className="w-4 h-4 mt-0.5 flex-shrink-0" />
              <p>
                Configure como as aulas individuais serão criadas automaticamente
                com base neste grupo.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="recurrence_pattern.frequency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Frequência</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value as string}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione a frequência" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {RECURRENCE_FREQUENCIES.map((freq) => (
                          <SelectItem key={freq.value} value={freq.value}>
                            {freq.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="recurrence_pattern.interval"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Intervalo</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="1"
                        min={1}
                        max={52}
                        value={field.value as number || 1}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                      />
                    </FormControl>
                    <FormDescription>
                      A cada quantas semanas/meses repetir
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {form.watch('recurrence_pattern.frequency') === 'weekly' && (
              <DaysOfWeekSelector form={form} primaryColor={primaryColor} />
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="recurrence_pattern.endDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data Final da Recorrência</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        value={field.value ? new Date(field.value as string).toISOString().split('T')[0] : ''}
                        onChange={(e) => {
                          const date = e.target.value;
                          field.onChange(date ? new Date(date + 'T00:00:00.000Z').toISOString() : '');
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Quando parar de gerar aulas (opcional)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="recurrence_pattern.maxOccurrences"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Máximo de Aulas</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Ex: 50"
                        min={1}
                        max={1000}
                        value={field.value as number || ''}
                        onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                      />
                    </FormControl>
                    <FormDescription>
                      Número máximo de aulas a gerar (opcional)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 