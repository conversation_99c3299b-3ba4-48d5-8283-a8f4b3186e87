'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  User, 
  Calendar, 
  Clock, 
  MapPin, 
  CheckCircle, 
  ArrowLeft,
  Users,
  AlertCircle,
  ChevronDown
} from 'lucide-react';
import { LottieAnimation } from './lottie-animation';
import { CheckInData, Class } from '../types/checkin-types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import useTenantSupabase from '@/hooks/tenant/use-tenant-supabase';
import { type SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/services/supabase/types/database.types';
import { toast } from 'sonner';
import { checkInStudent } from '@/app/(dashboard)/presenca/actions/check-in-actions';

interface ConfirmationModalProps {
  data: CheckInData;
  onGoBack: () => void;
}

export function ConfirmationModal({ data, onGoBack }: ConfirmationModalProps) {
  const [selectedClass, setSelectedClass] = useState<string | null>(null);
  const [isConfirming, setIsConfirming] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [availableClasses, setAvailableClasses] = useState<Class[]>([]);

  // Controle de check-ins já realizados
  const [checkedInClassIds, setCheckedInClassIds] = useState<Set<string>>(new Set());

  // Cliente Supabase do tenant
  const supabase = useTenantSupabase() as SupabaseClient<Database> | null;

  useEffect(() => {
    // Carrega as aulas disponíveis vindas do backend (sem mocks)
    setAvailableClasses(data.availableClasses ?? []);
  }, [data.availableClasses]);

  // Após obter as aulas e o client, verifica check-ins existentes
  useEffect(() => {
    const fetchCheckedInClasses = async () => {
      if (!supabase || availableClasses.length === 0) return;

      try {
        const classIds = availableClasses.map((cls) => cls.id);
        const { data: attendanceData, error } = await supabase
          .from('attendance')
          .select('class_id')
          .eq('student_id', data.student.id)
          .in('class_id', classIds);

        if (error) {
          console.error('[Check-in] Erro ao consultar presença:', error);
          return;
        }

        const ids = new Set<string>(attendanceData?.map((a: { class_id: string }) => a.class_id) ?? []);
        setCheckedInClassIds(ids);
      } catch (err) {
        console.error('[Check-in] Falha inesperada ao buscar presenças:', err);
      }
    };

    fetchCheckedInClasses();
  }, [supabase, availableClasses, data.student.id]);

  const handleConfirmCheckIn = async () => {
    if (!selectedClass) return;

    setIsConfirming(true);
    
    try {
      const result = await checkInStudent({
        student_id: data.student.id,
        class_id: selectedClass,
      });

      if (result.success) {
        toast.success('Check-in realizado com sucesso!');
        setShowSuccess(true);

        setTimeout(() => {
          onGoBack();
        }, 3000);
      } else {
        const errorMessage = (result.errors as any)?._form || (result.errors as any)?.student_id || (result.errors as any)?.qr_code || 'Erro ao fazer check-in';
        toast.error(errorMessage);
        setIsConfirming(false);
      }
    } catch (error) {
      console.error('Erro ao fazer check-in:', error);
      toast.error('Erro inesperado ao fazer check-in');
      setIsConfirming(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400';
      case 'inactive':
        return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300';
      case 'suspended':
        return 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  const getClassStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400';
      case 'in-progress':
        return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400';
      case 'ended':
        return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  if (showSuccess) {
    return (
      <div className="bg-white dark:bg-gray-900 rounded-3xl shadow-xl p-8 text-center">
        <div className="mb-6">
          <div className="mx-auto flex h-24 w-24 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30">
            <LottieAnimation
              animationPath="/check.json"
              className="h-20 w-20"
            />
          </div>
        </div>

        <div className="text-gray-800 dark:text-gray-200">
          <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-gray-100">
            Check-in Realizado!
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Seu check-in foi registrado com sucesso.
          </p>

          <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-4 mb-6">
            <h3 className="font-semibold mb-2 dark:text-gray-100">{data.student.name}</h3>
            <p className="text-sm text-gray-700 dark:text-gray-300">
              {availableClasses.find(c => c.id === selectedClass)?.name}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {new Date().toLocaleTimeString('pt-BR', {
                hour: '2-digit',
                minute: '2-digit',
              })}
            </p>
          </div>

          <p className="text-sm text-gray-500 dark:text-gray-400">
            Redirecionando automaticamente...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-900 rounded-3xl shadow-xl">
      {/* Header */}
      <div className="bg-gradient-to-r from-teal-600 to-teal-700 dark:from-gray-800 dark:to-gray-900 rounded-t-3xl p-6">
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={onGoBack}
            className="text-white dark:text-gray-200 hover:bg-white/10 dark:hover:bg-gray-700/50"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Voltar
          </Button>
          
          <h2 className="text-xl font-bold text-white dark:text-gray-100">
            Confirmar Check-in
          </h2>
          
          <div className="w-20" /> {/* Spacer */}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2">
        {/* Coluna da Esquerda: Informações do Aluno */}
        <div className="p-6 border-r border-gray-100 dark:border-gray-700">
          <Card className="dark:bg-gray-800 dark:border-gray-700">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                {/* Avatar */}
                <Avatar className="h-16 w-16 rounded-xl">
                  {data.student.avatar && (
                    <AvatarImage src={data.student.avatar} alt={data.student.name} />
                  )}
                  <AvatarFallback className="rounded-xl bg-gradient-to-br from-teal-400 to-teal-600 text-lg font-bold text-white">
                    {data.student.name
                      .split(' ')
                      .map((n) => n[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>

                {/* Informações */}
                <div className="flex-1">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">
                        {data.student.name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {data.student.email}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-500">
                        Código: {data.student.code}
                      </p>
                    </div>

                    {/* Status da Matrícula */}
                    <Badge className={getStatusColor(data.student.membership.status)}>
                      {data.student.membership.status === 'active' ? 'Ativo' : 
                       data.student.membership.status === 'inactive' ? 'Inativo' : 'Suspenso'}
                    </Badge>
                  </div>

                  {/* Informações da Matrícula */}
                  <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className={`grid gap-4 text-sm ${data.student.membership.expiresAt ? 'grid-cols-2' : 'grid-cols-1'}`}>
                      <div>
                        <p className="text-gray-500 dark:text-gray-400">Plano</p>
                        <p className="font-medium dark:text-gray-200">{data.student.membership.type}</p>
                      </div>
                      {data.student.membership.expiresAt && (
                        <div>
                          <p className="text-gray-500 dark:text-gray-400">Vence em</p>
                          <p className="font-medium dark:text-gray-200">{formatDate(data.student.membership.expiresAt)}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Detalhes do Acesso */}
          <Card className="mt-4 dark:bg-gray-800 dark:border-gray-700">
            <CardContent className="p-4">
              <div className="bg-gradient-to-r from-teal-50 to-teal-100 dark:from-gray-700 dark:to-gray-600 border border-teal-200 dark:border-gray-600 rounded-lg p-3">
                  <div className="flex items-center justify-between text-teal-800 dark:text-gray-200">
                   <span className="text-sm font-medium">Detalhes do Acesso</span>
                   <ChevronDown className="w-4 h-4" />
                  </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Coluna da Direita: Seleção de Aula e Ações */}
        <div className="p-6 flex flex-col">
          <div className="flex-grow">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Selecionar Aula
            </h3>

            {availableClasses.length === 0 ? (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <AlertCircle className="w-12 h-12 mx-auto mb-3 text-gray-300 dark:text-gray-600" />
                <p>Nenhuma aula disponível no momento</p>
              </div>
            ) : (
              <div className="space-y-3">
                {availableClasses.map((classItem) => (
                  <Card
                    key={classItem.id}
                    className={`relative transition-all dark:bg-gray-800 dark:border-gray-700 ${
                      selectedClass === classItem.id
                        ? 'ring-2 ring-teal-500 border-teal-200 dark:border-teal-600'
                        : 'hover:border-gray-300 dark:hover:border-gray-600'
                    } ${checkedInClassIds.has(classItem.id) ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}`}
                    onClick={() => {
                      if (checkedInClassIds.has(classItem.id)) return;
                      setSelectedClass(classItem.id);
                    }}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                              {classItem.name}
                            </h4>
                            <Badge 
                              className={getClassStatusColor(classItem.status)}
                              variant="secondary"
                            >
                              {classItem.status === 'upcoming' ? 'Próxima' :
                               classItem.status === 'in-progress' ? 'Em andamento' : 'Finalizada'}
                            </Badge>
                          </div>

                          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400">
                            <div className="flex items-center gap-1">
                              <User className="w-3 h-3" />
                              {classItem.instructor}
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              {classItem.time}
                            </div>
                            <div className="flex items-center gap-1">
                              <MapPin className="w-3 h-3" />
                              {classItem.location}
                            </div>
                            <div className="flex items-center gap-1">
                              <Users className="w-3 h-3" />
                              {classItem.enrolled}/{classItem.capacity} alunos
                            </div>
                          </div>
                        </div>

                        {/* Selo de check-in realizado */}
                        {checkedInClassIds.has(classItem.id) && (
                          <Badge className="bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 ml-2">
                            Check-in realizado
                          </Badge>
                        )}

                        {/* Radio Button Visual */}
                        <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                          selectedClass === classItem.id && !checkedInClassIds.has(classItem.id)
                            ? 'border-teal-500 bg-teal-500'
                            : 'border-gray-300 dark:border-gray-600'
                        }`}>
                          {selectedClass === classItem.id && !checkedInClassIds.has(classItem.id) && (
                            <CheckCircle className="w-3 h-3 text-white" />
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          <div className="mt-auto pt-6 space-y-4">
            {/* Aviso de Status */}
            {data.student.membership.status !== 'active' && (
              <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-600 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium text-red-800 dark:text-red-300 mb-1">
                      Matrícula {data.student.membership.status === 'inactive' ? 'Inativa' : 'Suspensa'}
                    </p>
                    <p className="text-red-700 dark:text-red-400">
                      {data.student.membership.status === 'inactive' 
                        ? 'A matrícula está inativa.'
                        : 'A matrícula está suspensa. Entre em contato com a administração.'
                      }
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Ações */}
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={onGoBack}
                className="flex-1 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
                disabled={isConfirming}
              >
                Cancelar
              </Button>
              <Button
                onClick={handleConfirmCheckIn}
                disabled={
                  !selectedClass ||
                  isConfirming ||
                  // Permitir check-in mesmo se o plano não estiver ativo (para fins de teste)
                  // data.student.membership.status !== 'active' ||
                  (selectedClass ? checkedInClassIds.has(selectedClass) : false)
                }
                className="flex-1 bg-teal-600 hover:bg-teal-700 dark:bg-teal-600 dark:hover:bg-teal-700"
              >
                {isConfirming ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Confirmando...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4" />
                    Confirmar Check-in
                  </div>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 