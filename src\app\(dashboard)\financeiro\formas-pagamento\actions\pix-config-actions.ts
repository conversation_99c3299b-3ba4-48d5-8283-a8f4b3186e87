'use server';

import { z } from 'zod';
import { createTenantServerClient } from '@/services/supabase/server';

/**
 * Schema de validação para salvar configurações PIX
 */
const savePixSettingsSchema = z.object({
  pixKey: z.string().min(1, 'Chave PIX é obrigatória').trim(),
});

export type SavePixSettingsInput = z.infer<typeof savePixSettingsSchema>;

/**
 * savePixSettings – Salva a chave PIX nas configurações do tenant
 *
 * @param input {SavePixSettingsInput}
 * @returns {Promise<{ success: boolean; error?: string }>} – Status da operação
 */
export async function savePixSettings(input: SavePixSettingsInput) {
  const parsed = savePixSettingsSchema.safeParse(input);
  if (!parsed.success) {
    return { success: false, error: 'Chave PIX inválida' } as const;
  }

  const { pixKey } = parsed.data;

  try {
    // Importar next/headers dinamicamente
    const { headers, cookies } = await import('next/headers');
    const [headersList, cookieStore] = await Promise.all([
      headers(),
      cookies()
    ]);

    // Resolvemos o tenant no próprio servidor para não expor o ID para o cliente
    const extractor = new (await import('@/services/tenant/tenant-extractor-server')).TenantExtractorServer();
    const tenant = await extractor.getTenantIdentifier(headersList, cookieStore);

    if (!tenant.id) {
      console.error('[savePixSettings] Tenant ID não encontrado');
      return { success: false, error: 'Tenant não identificado' } as const;
    }

    const supabase = await createTenantServerClient();

    // Primeiro, busca o ID do método de pagamento PIX
    const { data: pixMethod, error: pixMethodError } = await supabase
      .from('payment_methods')
      .select('id')
      .eq('slug', 'pix')
      .single();

    if (pixMethodError || !pixMethod) {
      console.error('[savePixSettings] Método PIX não encontrado', pixMethodError);
      return { success: false, error: 'Método de pagamento PIX não encontrado' } as const;
    }

    // Prepara as configurações PIX
    const pixSettings = {
      pixKey,
      updatedAt: new Date().toISOString(),
    };

    // Atualiza ou insere as configurações PIX
    const { error: upsertError } = await supabase
      .from('tenant_payment_settings')
      .upsert({
        tenant_id: tenant.id,
        payment_method_id: pixMethod.id,
        enabled: true, // Mantém o status atual ou ativa se não existir
        settings: pixSettings,
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'tenant_id,payment_method_id',
      });

    if (upsertError) {
      console.error('[savePixSettings] Erro ao salvar configurações PIX', upsertError);
      return { success: false, error: 'Erro ao salvar configurações PIX' } as const;
    }

    return { success: true } as const;
  } catch (err) {
    console.error('[savePixSettings] Erro inesperado', err);
    return { success: false, error: 'Erro inesperado' } as const;
  }
}

/**
 * getPixSettings – Busca as configurações PIX do tenant
 *
 * @returns {Promise<{ pixKey?: string; error?: string }>} – Configurações PIX ou erro
 */
export async function getPixSettings() {
  try {
    // Importar next/headers dinamicamente
    const { headers, cookies } = await import('next/headers');
    const [headersList, cookieStore] = await Promise.all([
      headers(),
      cookies()
    ]);

    const extractor = new (await import('@/services/tenant/tenant-extractor-server')).TenantExtractorServer();
    const tenant = await extractor.getTenantIdentifier(headersList, cookieStore);

    if (!tenant.id) {
      console.error('[getPixSettings] Tenant ID não encontrado');
      return { error: 'Tenant não identificado' } as const;
    }

    const supabase = await createTenantServerClient();

    // Primeiro, busca o ID do método de pagamento PIX
    const { data: pixMethod, error: pixMethodError } = await supabase
      .from('payment_methods')
      .select('id')
      .eq('slug', 'pix')
      .single();

    if (pixMethodError || !pixMethod) {
      console.error('[getPixSettings] Método PIX não encontrado', pixMethodError);
      return { error: 'Método de pagamento PIX não encontrado' } as const;
    }

    // Busca as configurações PIX do tenant
    const { data: pixSettings, error: pixSettingsError } = await supabase
      .from('tenant_payment_settings')
      .select('settings')
      .eq('tenant_id', tenant.id)
      .eq('payment_method_id', pixMethod.id)
      .single();

    if (pixSettingsError) {
      // Se não encontrar configurações, retorna vazio (não é erro)
      if (pixSettingsError.code === 'PGRST116') {
        return { pixKey: undefined };
      }
      console.error('[getPixSettings] Erro ao buscar configurações PIX', pixSettingsError);
      return { error: 'Erro ao buscar configurações PIX' } as const;
    }

    const settings = pixSettings?.settings as { pixKey?: string } | null;
    return { pixKey: settings?.pixKey };
  } catch (err) {
    console.error('[getPixSettings] Erro inesperado', err);
    return { error: 'Erro inesperado' } as const;
  }
}
