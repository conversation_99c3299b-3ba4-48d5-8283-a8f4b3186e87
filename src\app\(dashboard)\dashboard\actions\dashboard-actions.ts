'use server';
import { DashboardStat, DashboardData } from "../types";
import { 
  getStudentsMetrics, 
  getRevenueMetrics, 
  getRetentionMetrics,
  getStudentsChartData, 
  getMonthlyRevenue,
  getDailyActiveStudents,
  getDailyNewStudents,
  getDailyRetentionRate,
  getInstructorsBeltDistribution,
  getInstructorsContractDistribution
} from "./metrics-actions";
import { getRecentActivity } from "./activity-actions";
import { formatCurrency } from "@/utils/format-utils";

function calculatePercentageChange(current: number | null, previous: number | null): number {
  if (current === null) {
    return 0;
  }
  
  if (previous === null || previous === 0) {
    // Se não havia valor anterior mas agora há, considerar como 100% de crescimento
    return current > 0 ? 100 : 0;
  }
  
  return ((current - previous) / previous) * 100;
}

export async function getDashboardData(tenantId: string): Promise<DashboardData> {
  const [
    studentsMetrics,
    revenueMetrics,
    retentionRateMetrics,
    recentActivity,
    studentsChartData,
    monthlyRevenueChartData,
    dailyActiveStudentsChartData,
    dailyNewStudentsChartData,
    dailyRetentionRateChartData,
    instructorsBeltChart,
    instructorsContractChart
  ] = await Promise.all([
    getStudentsMetrics(tenantId),
    getRevenueMetrics(tenantId),
    getRetentionMetrics(tenantId),
    getRecentActivity(tenantId),
    getStudentsChartData(tenantId),
    getMonthlyRevenue(tenantId),
    getDailyActiveStudents(tenantId),
    getDailyNewStudents(tenantId),
    getDailyRetentionRate(tenantId),
    getInstructorsBeltDistribution(tenantId),
    getInstructorsContractDistribution(tenantId)
  ]);

  const stats: DashboardStat[] = [
    {
      name: "Alunos Ativos",
      value: studentsMetrics.activeStudentsNow?.toString() || '0',
      change: calculatePercentageChange(studentsMetrics.activeStudentsNow, studentsMetrics.activeStudentsLast),
      iconName: "users",
      chartData: dailyActiveStudentsChartData, 
    },
    {
      name: "Novos Alunos",
      value: studentsMetrics.newStudentsNow?.toString() || '0',
      change: calculatePercentageChange(studentsMetrics.newStudentsNow, studentsMetrics.newStudentsLast),
      iconName: "user-group",
      chartData: dailyNewStudentsChartData,
    },
    {
      name: "Receita Mensal",
      value: formatCurrency(revenueMetrics.revenueNow),
      change: calculatePercentageChange(revenueMetrics.revenueNow, revenueMetrics.revenueLast),
      iconName: "banknotes",
      chartData: monthlyRevenueChartData,
    },
    {
      name: "Taxa de Retenção",
      value: `${retentionRateMetrics.retentionRate.toFixed(1)}%`,
      change: calculatePercentageChange(retentionRateMetrics.retentionRate, retentionRateMetrics.retentionRateLast),
      iconName: "arrow-trending-up",
      chartData: dailyRetentionRateChartData,
    },
  ];

  return {
    stats,
    recentActivity,
    studentsChart: studentsChartData,
    instructorsBeltChart,
    instructorsContractChart,
  };
} 