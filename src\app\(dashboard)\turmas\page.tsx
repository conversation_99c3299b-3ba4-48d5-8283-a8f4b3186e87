import { Metadata } from 'next';
import { ClassGroupActions } from '../aulas/components/actions/class-group-actions';
import { PageWrapper } from '@/components/layout';
import { Users } from 'lucide-react';
import { ClassGroupsPageContent } from './components/class-groups-page-content';

export const metadata: Metadata = {
  title: 'Turmas | Apex SaaS',
  description: 'Gerencie grupos de aulas, matrículas e horários da sua academia',
};

export default function ClassGroupsPage() {
  return (
    <PageWrapper
      title="Turmas"
      subtitle="Gere<PERSON><PERSON> turmas, matrículas e horários da sua academia"
      icon={<Users className="h-6 w-6 text-primary" />}
      actions={<ClassGroupActions />}
    >
      <ClassGroupsPageContent />
    </PageWrapper>
  );
} 