'use client';

import { motion } from 'framer-motion';
import {
  TooltipProvider,
  TooltipRoot,
  TooltipTrigger,
  TooltipContent,
  TooltipPortal
} from '@/components/ui/tooltip';

interface MiniChartProps {
  data: number[];
  height?: number;
  width?: number;
  color?: string;
  className?: string;
  /** Data inicial que representa o primeiro valor do array. Se informado, cada barra receberá um tooltip com a data correspondente. */
  startDate?: Date;
}

export function MiniChart({
  data,
  height = 40,
  width = 60,
  color = '#3b82f6',
  className = '',
  startDate,
}: MiniChartProps) {
  if (!data || data.length === 0) return null;

  const maxValue = Math.max(...data);
  const minValue = Math.min(...data);
  const range = maxValue - minValue || 1;

  const barWidth = width / data.length;
  const spacing = 1;

  return (
    <TooltipProvider delayDuration={150}>
      <div className={`flex items-end gap-px ${className}`} style={{ height, width }}>
        {data.map((value, index) => {
          const normalizedHeight = ((value - minValue) / range) * height;
          const barHeight = Math.max(normalizedHeight, 2); // Minimum height for visibility
          
          // Tooltip label baseado na data
          let tooltipLabel: string | undefined;
          if (startDate) {
            const tooltipDate = new Date(startDate);
            tooltipDate.setDate(startDate.getDate() + index);
            tooltipLabel = tooltipDate.toLocaleDateString('pt-BR', {
              weekday: 'short',
              day: '2-digit',
              month: '2-digit',
            });
          }

          const content = tooltipLabel ? `${tooltipLabel}: ${value}` : `${value}`;

          return (
            <TooltipRoot key={index}>
              <TooltipTrigger asChild>
                <motion.div
                  className="bg-current opacity-70 rounded-sm"
                  style={{ 
                    width: barWidth - spacing,
                    color: color,
                    height: barHeight
                  }}
                  initial={{ height: 0 }}
                  animate={{ height: barHeight }}
                  transition={{ 
                    duration: 0.5, 
                    delay: index * 0.05,
                    ease: 'easeOut'
                  }}
                />
              </TooltipTrigger>
              <TooltipPortal>
                <TooltipContent side="top" className="text-xs font-medium">
                  {content}
                </TooltipContent>
              </TooltipPortal>
            </TooltipRoot>
          );
        })}
      </div>
    </TooltipProvider>
  );
}

interface TrendLineProps {
  data: number[];
  height?: number;
  width?: number;
  color?: string;
  strokeWidth?: number;
  className?: string;
  fill?: boolean;
}

export function TrendLine({
  data,
  height = 40,
  width = 60,
  color = '#3b82f6',
  strokeWidth = 2,
  className = '',
  fill = false
}: TrendLineProps) {
  if (!data || data.length === 0) return null;

  const maxValue = Math.max(...data);
  const minValue = Math.min(...data);
  const range = maxValue - minValue || 1;

  const stepX = width / (data.length - 1);
  
  const points = data.map((value, index) => {
    const x = index * stepX;
    const normalizedY = (value - minValue) / range;
    const y = height - (normalizedY * height);
    return { x, y };
  });

  // Create path for line
  const pathData = points.reduce((acc, point, index) => {
    if (index === 0) {
      return `M ${point.x} ${point.y}`;
    }
    return `${acc} L ${point.x} ${point.y}`;
  }, '');

  // Create path for fill area
  const fillPathData = fill ? `${pathData} L ${width} ${height} L 0 ${height} Z` : '';

  return (
    <div className={className} style={{ width, height }}>
      <svg width={width} height={height} className="overflow-visible">
        {/* Fill area */}
        {fill && fillPathData && (
          <motion.path
            d={fillPathData}
            fill={color}
            fillOpacity={0.1}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          />
        )}
        
        {/* Line */}
        <motion.path
          d={pathData}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 1, ease: 'easeInOut' }}
        />
        
        {/* Points */}
        {points.map((point, index) => (
          <motion.circle
            key={index}
            cx={point.x}
            cy={point.y}
            r={strokeWidth}
            fill={color}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ 
              duration: 0.3, 
              delay: index * 0.1 + 0.5,
              ease: 'easeOut'
            }}
          />
        ))}
      </svg>
    </div>
  );
}

interface CircularProgressProps {
  value: number;
  maxValue: number;
  size?: number;
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
  className?: string;
}

export function CircularProgress({
  value,
  maxValue,
  size = 40,
  strokeWidth = 3,
  color = '#3b82f6',
  backgroundColor = '#e5e7eb',
  className = ''
}: CircularProgressProps) {
  const progress = Math.min((value / maxValue) * 100, 100);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (progress / 100) * circumference;
  const center = size / 2;

  return (
    <div className={className} style={{ width: size, height: size }}>
      <svg width={size} height={size} className="transform -rotate-90">
        {/* Background circle */}
        <circle
          cx={center}
          cy={center}
          r={radius}
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          fill="none"
        />
        
        {/* Progress circle */}
        <motion.circle
          cx={center}
          cy={center}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="none"
          strokeLinecap="round"
          strokeDasharray={strokeDasharray}
          initial={{ strokeDashoffset: circumference }}
          animate={{ strokeDashoffset }}
          transition={{ duration: 1, ease: 'easeInOut' }}
        />
      </svg>
      
      {/* Center text */}
      <div className="absolute inset-0 flex items-center justify-center">
        <span className="text-xs font-semibold" style={{ color }}>
          {Math.round(progress)}%
        </span>
      </div>
    </div>
  );
} 