'use client';

import { useState } from 'react';
import { updateUserAvatar } from '../actions';
import { useQueryClient } from '@tanstack/react-query'; 
import { CACHE_KEYS } from '@/constants/cache-keys';

interface UseAvatarUpdateOptions {
  userId: string;
  onSuccess?: (avatarUrl: string) => void;
  onError?: (error: string) => void;
}

export function useAvatarUpdate({ userId, onSuccess, onError }: UseAvatarUpdateOptions) {
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const queryClient = useQueryClient();
  
  const uploadAvatar = async (file: File) => {
    if (!file) return;
    
    try {
      setIsUploading(true);
      setProgress(0);
      setError(null);
      
      // Simular progresso para feedback visual
      let currentProgress = 0;
      const progressInterval = setInterval(() => {
        currentProgress = Math.min(90, currentProgress + 10);
        setProgress(currentProgress);
        
        if (currentProgress >= 90) {
          clearInterval(progressInterval);
        }
      }, 100);
      
      const formData = new FormData();
      formData.append('file', file);
      
      const result = await updateUserAvatar(userId, formData);
      
      clearInterval(progressInterval);
      
      if (result.success && result.avatarUrl) {
        setProgress(100);
        
        // Invalidar cache para forçar recarregamento de dados do perfil
        queryClient.removeQueries({ queryKey: [CACHE_KEYS.USER_PROFILE[0], userId] });
        queryClient.removeQueries({ queryKey: CACHE_KEYS.VIEWED_USER_PROFILE(userId) });
        
        // Forçar recarregamento dos dados
        queryClient.refetchQueries({ 
          queryKey: [CACHE_KEYS.USER_PROFILE[0], userId],
          type: 'all'
        });
        queryClient.refetchQueries({
          queryKey: CACHE_KEYS.VIEWED_USER_PROFILE(userId),
          type: 'all'
        });
        
        // Invalidar cache da lista de alunos para refletir a alteração do avatar
        queryClient.invalidateQueries({
          queryKey: ['students'],
        });
        
        // Chamar callback de sucesso com a nova URL do avatar
        onSuccess?.(result.avatarUrl);
        
        setTimeout(() => {
          setIsUploading(false);
          setProgress(0);
        }, 500);
      } else {
        setError(result.error || 'Erro ao fazer upload');
        onError?.(result.error || 'Erro ao fazer upload');
        setIsUploading(false);
      }
    } catch (err) {
      setError('Ocorreu um erro inesperado');
      onError?.('Ocorreu um erro inesperado');
      setIsUploading(false);
    }
  };
  
  return {
    uploadAvatar,
    isUploading,
    progress,
    error,
    reset: () => {
      setIsUploading(false);
      setProgress(0);
      setError(null);
    }
  };
}