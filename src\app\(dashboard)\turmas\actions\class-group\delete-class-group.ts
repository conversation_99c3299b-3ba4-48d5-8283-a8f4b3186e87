"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/services/supabase/server";
import { validateUserAuthentication } from "./shared/validation-helpers";

/**
 * Remove uma turma completamente do banco de dados (hard delete)
 * Remove também todas as aulas associadas
 */
export async function deleteClassGroup(id: string) {
  console.log('🗑️ Iniciando exclusão COMPLETA da turma:', id);
  
  try {
    // Validar ID
    if (!id || typeof id !== 'string') {
      console.error('❌ ID inválido:', id);
      return { success: false, errors: { _form: "ID da turma é obrigatório" } };
    }

    console.log('🔐 Validando autenticação...');
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      console.error('❌ Falha na autenticação:', authResult);
      return authResult;
    }

    const { tenantId } = authResult;
    console.log('✅ Autenticação válida, tenantId:', tenantId);
    
    const supabase = await createAdminClient();
    console.log('🔌 Cliente Supabase criado');

    // Verificar se a turma tem alunos matriculados
    console.log('👥 Verificando alunos matriculados...');
    const { data: enrollments, error: enrollmentError } = await supabase
      .from("class_group_enrollments")
      .select("id")
      .eq("class_group_id", id)
      .eq("status", "active");

    if (enrollmentError) {
      console.error("❌ Erro ao verificar matrículas:", enrollmentError);
      return { success: false, errors: { _form: "Erro ao verificar matrículas" } };
    }

    console.log('📊 Matrículas encontradas:', enrollments?.length || 0);

    if (enrollments && enrollments.length > 0) {
      console.log('⚠️ Turma tem alunos matriculados, bloqueando exclusão');
      return { 
        success: false, 
        errors: { _form: "Não é possível excluir uma turma com alunos matriculados" } 
      };
    }

    // Primeiro, remover todas as aulas associadas
    console.log('🏃‍♀️ Removendo aulas associadas...');
    const { error: classesError } = await supabase
      .from("classes")
      .delete()
      .eq("class_group_id", id);

    if (classesError) {
      console.error("❌ Erro ao remover aulas:", classesError);
      return { success: false, errors: { _form: "Erro ao remover aulas associadas" } };
    }

    console.log('✅ Aulas removidas com sucesso');

    // Em seguida, remover a turma completamente
    console.log('🗑️ Executando remoção COMPLETA da turma...');
    const { error } = await supabase
      .from("class_groups")
      .delete()
      .eq("id", id)
      .eq("tenant_id", tenantId);

    if (error) {
      console.error("❌ Erro ao excluir turma:", error);
      return { success: false, errors: { _form: "Erro ao excluir turma" } };
    }

    console.log('🔄 Revalidando cache...');
    // Revalidar todas as páginas relacionadas de forma mais abrangente
    revalidatePath("/turmas", 'layout');
    revalidatePath("/turmas", 'page');
    revalidatePath(`/turmas/${id}`, 'layout');
    revalidatePath(`/turmas/${id}`, 'page');
    revalidatePath(`/turmas/${id}/alunos`, 'page');
    revalidatePath(`/turmas/${id}/aulas`, 'page');
    revalidatePath(`/turmas/${id}/editar`, 'page');
    revalidatePath("/aulas", 'page');
    revalidatePath("/dashboard", 'page');
    
    console.log('✅ Turma e aulas removidas COMPLETAMENTE com sucesso');
    return { success: true, message: "Turma excluída completamente com sucesso" };
  } catch (error) {
    console.error("💥 Erro inesperado ao excluir turma:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 