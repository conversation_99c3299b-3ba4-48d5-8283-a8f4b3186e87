/**
 * Converte uma string datetime-local para ISO string considerando o timezone de Brasília
 * @param datetimeLocal String no formato YYYY-MM-DDTHH:MM (datetime-local)
 * @returns ISO string em UTC considerando que a entrada estava em timezone de Brasília
 */
export function datetimeLocalToBrasiliaISO(datetimeLocal: string): string {
  if (!datetimeLocal) return datetimeLocal;
  
  if (datetimeLocal.includes('Z') || datetimeLocal.includes('+') || datetimeLocal.includes('-', 19)) {
    return datetimeLocal;
  }
  
  if (datetimeLocal.includes('T')) {
    try {
      let withSeconds = datetimeLocal;
      if (datetimeLocal.length === 16) {
        withSeconds = `${datetimeLocal}:00`;
      }

      const tempDate = new Date(withSeconds + 'Z');
      
      if (isNaN(tempDate.getTime())) {
        return datetimeLocal;
      }

      const utcDate = new Date(tempDate.getTime() + (3 * 60 * 60 * 1000));
      
      return utcDate.toISOString();
    } catch (error) {
      console.error('Erro na conversão de timezone:', error);
      return datetimeLocal;
    }
  }
  
  return datetimeLocal;
}

/**
 * Converte uma data UTC para o timezone de Brasília
 * @param utcDate Data em UTC
 * @returns Data no timezone de Brasília
 */
export function utcToBrasilia(utcDate: Date): Date {
  try {
    const brasiliaString = utcDate.toLocaleString('sv-SE', { 
      timeZone: 'America/Sao_Paulo'
    });
    
    return new Date(brasiliaString.replace(' ', 'T'));
  } catch (error) {
    return new Date(utcDate.getTime() - (3 * 60 * 60 * 1000));
  }
}

/**
 * Obtém a data/hora atual no timezone de Brasília
 * @returns Data atual no timezone de Brasília
 */
export function nowInBrasilia(): Date {
  const now = new Date();
  
  const brasiliaString = now.toLocaleString('sv-SE', { 
    timeZone: 'America/Sao_Paulo'
  });
  
  const brasiliaDate = new Date(brasiliaString.replace(' ', 'T'));
  
  return brasiliaDate;
}

/**
 * Formata uma data para datetime-local considerando timezone de Brasília
 * @param date Data para formatar
 * @returns String no formato YYYY-MM-DDTHH:MM
 */
export function formatToDatetimeLocal(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  
  // Converter para timezone de Brasília
  const brasiliaString = dateObj.toLocaleString('sv-SE', { 
    timeZone: 'America/Sao_Paulo'
  });
  
  // Remover os segundos para ficar no formato datetime-local
  return brasiliaString.substring(0, 16);
} 