'use client';

import { useQuery } from '@tanstack/react-query';
import { getBranchesForForm } from '@/app/(dashboard)/aulas/actions/form-data-actions';

export interface Branch {
  id: string;
  name: string;
}

/**
 * Hook para buscar filiais disponíveis para o tenant atual
 */
export function useBranches() {
  return useQuery({
    queryKey: ['branches'],
    queryFn: async () => {
      const result = await getBranchesForForm();
      
      if (!result.success) {
        throw new Error(result.errors?._form || 'Erro ao buscar filiais');
      }
      
      return result.data || [];
    },
    staleTime: 5 * 60 * 1000, // Cache por 5 minutos
    retry: 1,
  });
} 