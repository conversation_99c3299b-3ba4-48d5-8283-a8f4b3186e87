import { ActionType, PermissionCheckResult, ResourceType } from './types/permission-types';
import { PermissionRepository } from './repository/permission-repository';
import { PermissionEvaluator } from './actions/permission-evaluator';
import { getPermissionContext } from './contexts/permission-context';
import { redirect } from 'next/navigation';

/**
 * Serviço principal de permissões, ponto de entrada para verificações de permissão
 */
export class PermissionService {
  private repository: PermissionRepository;
  private evaluator: PermissionEvaluator;
  
  constructor() {
    this.repository = new PermissionRepository();
    this.evaluator = new PermissionEvaluator(this.repository);
  }
  
  /**
   * Verifica se o usuário atual pode realizar uma ação em um recurso
   * 
   * @param currentUserId ID do usuário atual
   * @param resource Tipo de recurso
   * @param action Tipo de ação
   * @param targetUserId ID do usuário alvo (opcional)
   * @returns Resultado da verificação
   */
  async hasPermission(
    currentUserId: string,
    resource: ResourceType,
    action: ActionType,
    targetUserId?: string
  ): Promise<PermissionCheckResult> {
    try {
      const context = await getPermissionContext(currentUserId, targetUserId);
      
      if (!context) {
        return { 
          granted: false, 
          reason: 'Não foi possível obter o contexto de permissão' 
        };
      }
      
      return this.evaluator.evaluate(context, resource, action);
    } catch (error) {
      console.error('Erro ao verificar permissão:', error);
      return { 
        granted: false, 
        reason: 'Erro interno ao verificar permissão' 
      };
    }
  }
  
  /**
   * Verifica se o usuário atual pode realizar uma ação em um usuário alvo
   * 
   * @param currentUserId ID do usuário atual 
   * @param targetUserId ID do usuário alvo
   * @param action Ação a ser verificada
   * @returns Resultado da verificação
   */
  async canManageUser(
    currentUserId: string,
    targetUserId: string | undefined,
    action: ActionType
  ): Promise<PermissionCheckResult> {
    try {
      const context = await getPermissionContext(currentUserId, targetUserId);
      
      if (!context) {
        return { 
          granted: false, 
          reason: 'Não foi possível obter o contexto de permissão' 
        };
      }
      
      return this.evaluator.canPerformUserAction(
        currentUserId, 
        targetUserId, 
        'user',
        action,
        context
      );
    } catch (error) {
      console.error('Erro ao verificar permissão de usuário:', error);
      return { 
        granted: false, 
        reason: 'Erro interno ao verificar permissão' 
      };
    }
  }
  
  /**
   * Verifica especificamente se o usuário tem permissão para acessar um perfil
   * Usa o sistema de políticas padrão para verificar permissões
   * 
   * @param currentUserId ID do usuário atual 
   * @param profileUserId ID do usuário do perfil a ser acessado
   * @returns Resultado da verificação
   */
  async canAccessProfile(
    currentUserId: string,
    profileUserId: string
  ): Promise<PermissionCheckResult> {
    try {
      console.log(`[PermissionService] Verificando acesso ao perfil: currentUser=${currentUserId}, profileUser=${profileUserId}`);
      
      // Usar o sistema de políticas padrão ao invés de lógica hardcoded
      return await this.hasPermission(
        currentUserId,
        'profile',
        'view',
        profileUserId
      );
    } catch (error) {
      console.error('Erro ao verificar permissão de acesso ao perfil:', error);
      return {
        granted: false,
        reason: 'Erro interno ao verificar permissão'
      };
    }
  }
  
  /**
   * Protege o acesso a uma rota verificando permissão
   * Redireciona para uma página de erro se a permissão for negada
   * 
   * @param currentUserId ID do usuário atual
   * @param resource Tipo de recurso
   * @param action Tipo de ação
   * @param targetUserId ID do usuário alvo (opcional)
   * @param redirectUrl URL para redirecionar em caso de erro (opcional)
   * @returns null se permitido, ou um objeto de redirecionamento
   */
  async protectRoute(
    currentUserId: string,
    resource: ResourceType,
    action: ActionType,
    targetUserId?: string,
    redirectUrl: string = '/home?erro=acesso-negado'
  ): Promise<null | Response> {
    const result = await this.hasPermission(
      currentUserId,
      resource,
      action,
      targetUserId
    );
    
    if (!result.granted) {
      console.warn('Acesso negado:', {
        userId: currentUserId,
        resource,
        action,
        targetId: targetUserId,
        reason: result.reason
      });
      
      return redirect(redirectUrl);
    }
    
    return null;
  }
  
  /**
   * Protege o acesso a uma página de perfil
   * Permite acesso próprio perfil e administradores podem ver qualquer perfil
   * 
   * @param currentUserId ID do usuário atual
   * @param profileUserId ID do usuário do perfil a ser acessado
   * @param redirectUrl URL para redirecionar em caso de erro (opcional)
   * @returns null se permitido, ou um objeto de redirecionamento
   */
  async protectProfileRoute(
    currentUserId: string,
    profileUserId: string,
    redirectUrl: string = '/home?erro=acesso-negado'
  ): Promise<null | Response> {
    const result = await this.canAccessProfile(
      currentUserId,
      profileUserId
    );
    
    if (!result.granted) {
      console.warn('Acesso negado ao perfil:', {
        currentUserId,
        profileUserId,
        reason: result.reason
      });
      
      return redirect(redirectUrl);
    }
    
    return null;
  }
}

let permissionServiceInstance: PermissionService | null = null;

/**
 * Obtém uma instância do serviço de permissões
 */
export function getPermissionService(): PermissionService {
  if (!permissionServiceInstance) {
    permissionServiceInstance = new PermissionService();
  }
  
  return permissionServiceInstance;
} 