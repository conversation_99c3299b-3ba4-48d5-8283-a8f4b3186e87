'use client';

import { useQueries, useQuery } from '@tanstack/react-query';
import { CACHE_KEYS } from '@/constants/cache-keys';
import { GroupClassInfo, ClassesResponse, ClassFilters, ClassStats } from '../types';
import { getGroupInfo, getGroupClassStats, getGroupClasses, getGroupInstructors } from '../actions/group-actions';
import { useClassFilters } from '../contexts/ClassFiltersContext';

export interface UseGroupClassDataResult {
  groupInfo: GroupClassInfo | null;
  classStats: ClassStats | null;
  classesData: ClassesResponse | null;
  instructors: any[] | null;
  isLoading: boolean;
  isClassesLoading: boolean; // Loading específico para aulas
  error: string | null;
  refetch: () => void;
  refetchClasses: () => void;
}

export function useGroupClassData(groupId: string): UseGroupClassDataResult {
  const { queryFilters } = useClassFilters(); // Usar queryFilters com debounce

  // Usar useQueries para carregar dados em paralelo
  const queries = useQueries({
    queries: [
      {
        queryKey: CACHE_KEYS.CLASS_GROUPS.INFO(groupId),
        queryFn: () => getGroupInfo(groupId),
        enabled: !!groupId,
        staleTime: 5 * 60 * 1000, // 5 minutos
        gcTime: 10 * 60 * 1000, // 10 minutos
      },
      {
        queryKey: CACHE_KEYS.CLASS_GROUPS.CLASS_STATS(groupId),
        queryFn: () => getGroupClassStats(groupId),
        enabled: !!groupId,
        staleTime: 1 * 60 * 1000, // 1 minuto
        gcTime: 5 * 60 * 1000, // 5 minutos
      },
      {
        queryKey: CACHE_KEYS.CLASS_GROUPS.INSTRUCTORS(groupId),
        queryFn: () => getGroupInstructors(groupId),
        enabled: !!groupId,
        staleTime: 10 * 60 * 1000, // 10 minutos
        gcTime: 30 * 60 * 1000, // 30 minutos
      },
    ],
  });

  // Query separada para aulas com filtros reativos (usando queryFilters com debounce)
  const {
    data: classesData,
    isLoading: isClassesLoading,
    error: classesError,
    refetch: refetchClasses,
  } = useQuery({
    queryKey: CACHE_KEYS.CLASS_GROUPS.CLASSES(groupId, queryFilters),
    queryFn: () => getGroupClasses(groupId, queryFilters),
    enabled: !!groupId,
    staleTime: 30 * 1000, // 30 segundos
    gcTime: 2 * 60 * 1000, // 2 minutos
    select: (data: any) => data.success ? data.data : null,
  });

  // Extrair dados das queries
  const [groupInfoQuery, classStatsQuery, instructorsQuery] = queries;

  // Loading geral (sem incluir o loading das aulas)
  const isLoading = 
    groupInfoQuery.isLoading || 
    classStatsQuery.isLoading || 
    instructorsQuery.isLoading;

  // Verificar erros
  const errors = [
    groupInfoQuery.error,
    classStatsQuery.error,
    instructorsQuery.error,
    classesError,
  ].filter(Boolean);

  const error = errors.length > 0 
    ? errors.map(err => 
        err instanceof Error ? err.message : 'Erro desconhecido'
      ).join('; ')
    : null;

  // Função para refazer todas as queries
  const refetch = () => {
    groupInfoQuery.refetch();
    classStatsQuery.refetch();
    instructorsQuery.refetch();
    refetchClasses();
  };

  return {
    groupInfo: (groupInfoQuery.data?.success ? groupInfoQuery.data.data : null) as GroupClassInfo | null,
    classStats: (classStatsQuery.data?.success ? classStatsQuery.data.data : null) as ClassStats | null,
    classesData: (classesData?.success ? classesData.data : null) as ClassesResponse | null,
    instructors: (instructorsQuery.data?.success ? instructorsQuery.data.data : null) as any[] | null,
    isLoading,
    isClassesLoading, // Loading específico para aulas
    error,
    refetch,
    refetchClasses,
  };
} 