'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateUserStatus, getUserStatus } from '@/services/user/status-service';
import { CACHE_KEYS } from '@/constants/cache-keys';

export type UserStatus = 'active' | 'inactive' | 'suspended';

interface UpdateStatusParams {
  userId: string;
  status: UserStatus;
}

export function useUserStatus() {
  const [isUpdating, setIsUpdating] = useState(false);
  const queryClient = useQueryClient();

  // Mutation para atualizar status
  const updateStatusMutation = useMutation({
    mutationFn: async ({ userId, status }: UpdateStatusParams) => {
      const result = await updateUserStatus({ userId, status });
      if (!result.success) {
        const msg =
          result.errors && typeof result.errors === 'object' && '_form' in result.errors
            ? (result.errors as { _form: string })._form
            : 'Erro ao atualizar status';
        throw new Error(msg);
      }
      return result;
    },
    onSuccess: (_, { userId }) => {
      // Invalidar caches relacionados ao usuário
      queryClient.invalidateQueries({
        queryKey: ['user', userId]
      });
      queryClient.invalidateQueries({
        queryKey: ['users']
      });
      queryClient.invalidateQueries({
        queryKey: ['students']
      });
      queryClient.invalidateQueries({
        queryKey: ['instructors']
      });
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.USER_METADATA
      });
      
      // Invalidar estatísticas
      queryClient.invalidateQueries({
        queryKey: ['student-stats']
      });
      queryClient.invalidateQueries({
        queryKey: ['instructor-stats']
      });
    },
    onError: (error) => {
      console.error('Erro ao atualizar status:', error);
    }
  });

  // Função para atualizar status (compatível com versão anterior)
  const updateStatus = async (userId: string, status: UserStatus) => {
    setIsUpdating(true);
    try {
      const result = await updateStatusMutation.mutateAsync({ userId, status });
      return result;
    } finally {
      setIsUpdating(false);
    }
  };

  // Função para buscar status de um usuário específico
  const getStatus = async (userId: string) => {
    try {
      return await getUserStatus(userId);
    } catch (error) {
      console.error('Erro ao buscar status do usuário:', error);
      return null;
    }
  };

  return {
    updateStatus,
    getStatus,
    isUpdating: isUpdating || updateStatusMutation.isPending,
    error: updateStatusMutation.error,
    isSuccess: updateStatusMutation.isSuccess,
    mutate: updateStatusMutation.mutate,
    mutateAsync: updateStatusMutation.mutateAsync,
    reset: updateStatusMutation.reset
  };
}

// Hook específico para toggle de status (ativo/inativo)
export function useToggleUserStatus() {
  const { updateStatus, isUpdating, error } = useUserStatus();

  const toggleStatus = async (userId: string, currentStatus: UserStatus) => {
    const newStatus: UserStatus = currentStatus === 'active' ? 'inactive' : 'active';
    return await updateStatus(userId, newStatus);
  };

  return {
    toggleStatus,
    isToggling: isUpdating,
    error
  };
}

// Hook para gerenciar status em lote (bulk operations)
export function useBulkUserStatus() {
  const [isUpdating, setIsUpdating] = useState(false);
  const queryClient = useQueryClient();

  const updateBulkStatus = async (userIds: string[], status: UserStatus) => {
    setIsUpdating(true);
    try {
      const promises = userIds.map(userId => 
        updateUserStatus({ userId, status })
      );
      
      const results = await Promise.allSettled(promises);
      
      // Verificar se todas as operações foram bem-sucedidas
      const failed = results.filter(result => 
        result.status === 'rejected' || 
        (result.status === 'fulfilled' && !result.value.success)
      );

      if (failed.length > 0) {
        console.warn(`${failed.length} operações falharam de ${userIds.length}`);
      }

      // Invalidar caches
      queryClient.invalidateQueries({
        queryKey: ['users']
      });
      queryClient.invalidateQueries({
        queryKey: ['students']
      });
      queryClient.invalidateQueries({
        queryKey: ['instructors']
      });
      queryClient.invalidateQueries({
        queryKey: ['student-stats']
      });
      queryClient.invalidateQueries({
        queryKey: ['instructor-stats']
      });

      return {
        success: true,
        total: userIds.length,
        failed: failed.length,
        successful: userIds.length - failed.length
      };
    } catch (error) {
      console.error('Erro na operação em lote:', error);
      throw error;
    } finally {
      setIsUpdating(false);
    }
  };

  return {
    updateBulkStatus,
    isUpdating
  };
} 