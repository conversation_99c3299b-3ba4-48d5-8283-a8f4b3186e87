'use client';

import { useFormContext } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { NovoInstrutorFormValues } from "../../../actions/schemas/instrutor-schema";
import { Award, Users, FileText, DollarSign } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { useCurrencyFormat } from '@/hooks/form/useCurrencyFormat';

const contractTypeOptions = [
  { value: "clt", label: "CLT - Carteira Assinada" },
  { value: "pj", label: "PJ - Pessoa Jurídica" },
  { value: "autonomo", label: "Autônomo" },
  { value: "parceria", label: "Parceria/Associação" },
  { value: "associacao", label: "Sócio da Academia" },
];

const paymentModelOptions = [
  { value: "hora_aula", label: "Por Hora/Aula" },
  { value: "salario_mensal", label: "Salário Mensal" },
  { value: "comissao", label: "Comissão por Resultado" },
  { value: "participacao_lucros", label: "Participação nos Lucros" },
];

const commonSpecialties = [
  "Jiu-Jitsu Gi",
  "No-Gi",
  "Jiu-Jitsu Infantil",
  "Jiu-Jitsu Feminino",
  "MMA",
  "Wrestling",
  "Grappling",
  "Self Defense",
  "Competição",
  "Preparação Física",
];

export default function ProfessionalInfoSection() {
  const { control, watch, setValue } = useFormContext<NovoInstrutorFormValues>();
  const contractType = watch('contract_type');
  const paymentModel = watch('payment_model');
  const specialties = watch('specialties') || [];
  
  const [newSpecialty, setNewSpecialty] = useState('');


  useEffect(() => {
    switch (paymentModel) {
      case 'hora_aula':
        setValue('monthly_salary', undefined, { shouldValidate: false });
        setValue('commission_percentage', undefined, { shouldValidate: false });
        break;
      case 'salario_mensal':
        setValue('hourly_rate', undefined, { shouldValidate: false });
        setValue('commission_percentage', undefined, { shouldValidate: false });
        break;
      case 'comissao':
      case 'participacao_lucros':
        setValue('hourly_rate', undefined, { shouldValidate: false });
        setValue('monthly_salary', undefined, { shouldValidate: false });
        break;
      default:
        // Caso nenhum esteja selecionado, limpamos todos por segurança
        setValue('hourly_rate', undefined, { shouldValidate: false });
        setValue('monthly_salary', undefined, { shouldValidate: false });
        setValue('commission_percentage', undefined, { shouldValidate: false });
    }
  }, [paymentModel, setValue]);

  const addSpecialty = (specialty: string) => {
    if (specialty && !specialties.includes(specialty)) {
      setValue('specialties', [...specialties, specialty], { shouldValidate: true });
    }
  };

  const removeSpecialty = (specialty: string) => {
    setValue('specialties', specialties.filter(s => s !== specialty), { shouldValidate: true });
  };

  const addNewSpecialty = () => {
    if (newSpecialty.trim()) {
      addSpecialty(newSpecialty.trim());
      setNewSpecialty('');
    }
  };

  return (
    <div className="grid grid-cols-1 gap-6">
      {/* Certificações */}
      <Card className="border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <Award className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Certificações e Qualificações
          </h2>
        </div>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Certificações federativas */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300">Certificações Federativas</h3>
              
              <FormField
                control={control}
                name="cbjj_certified"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="text-sm font-normal cursor-pointer">
                        Certificado CBJJ (Confederação Brasileira de Jiu-Jitsu)
                      </FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="ibjjf_certified"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="text-sm font-normal cursor-pointer">
                        Certificado IBJJF (International Brazilian Jiu-Jitsu Federation)
                      </FormLabel>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            {/* Certificações de segurança */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300">Certificações de Segurança</h3>
              
              <FormField
                control={control}
                name="first_aid_certified"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="text-sm font-normal cursor-pointer">
                        Certificado de Primeiros Socorros
                      </FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="cpr_certified"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="text-sm font-normal cursor-pointer">
                        Certificado CPR (Reanimação Cardiopulmonar)
                      </FormLabel>
                    </div>
                  </FormItem>
                )}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Especialidades */}
      <Card className="border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <Users className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Especialidades
          </h2>
        </div>
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Especialidades selecionadas */}
            {specialties.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">Especialidades Selecionadas</h3>
                <div className="flex flex-wrap gap-2">
                  {specialties.map((specialty) => (
                    <Badge 
                      key={specialty} 
                      variant="secondary" 
                      className="cursor-pointer hover:bg-red-100 hover:text-red-700 transition-colors"
                      onClick={() => removeSpecialty(specialty)}
                    >
                      {specialty}
                      <span className="ml-1 text-xs">×</span>
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Especialidades comuns */}
            <div>
              <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">Especialidades Comuns</h3>
              <div className="flex flex-wrap gap-2">
                {commonSpecialties
                  .filter(specialty => !specialties.includes(specialty))
                  .map((specialty) => (
                    <Badge 
                      key={specialty} 
                      variant="outline" 
                      className="cursor-pointer hover:bg-primary hover:text-white transition-colors"
                      onClick={() => addSpecialty(specialty)}
                    >
                      {specialty}
                      <span className="ml-1 text-xs">+</span>
                    </Badge>
                  ))}
              </div>
            </div>

            {/* Adicionar especialidade personalizada */}
            <div>
              <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">Adicionar Especialidade Personalizada</h3>
              <div className="flex gap-2">
                <Input
                  placeholder="Digite uma especialidade..."
                  value={newSpecialty}
                  onChange={(e) => setNewSpecialty(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addNewSpecialty())}
                  className="border-slate-300 dark:border-slate-600"
                />
                <Button 
                  type="button" 
                  onClick={addNewSpecialty}
                  variant="outline"
                  size="sm"
                >
                  Adicionar
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tipo de Contrato e Remuneração */}
      <Card className="border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <DollarSign className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Contrato e Remuneração
          </h2>
        </div>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Tipo de contrato */}
            <FormField
              control={control}
              name="contract_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-700 dark:text-slate-300">Tipo de Contrato</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className="border-slate-300 dark:border-slate-600">
                        <SelectValue placeholder="Selecione o tipo de contrato" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {contractTypeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Modelo de pagamento */}
            <FormField
              control={control}
              name="payment_model"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-700 dark:text-slate-300">Modelo de Pagamento</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className="border-slate-300 dark:border-slate-600">
                        <SelectValue placeholder="Selecione o modelo de pagamento" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {paymentModelOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Campos condicionais baseados no modelo de pagamento */}
            {paymentModel === 'hora_aula' && (
              <FormField
                control={control}
                name="hourly_rate"
                render={({ field }) => {
                  const { displayValue, handleChange, handleBlur } = useCurrencyFormat({
                    value: field.value ?? 0,
                    onChange: (v) => field.onChange(v),
                  });
                  return (
                    <FormItem>
                      <FormLabel className="text-slate-700 dark:text-slate-300">Valor por Hora/Aula (R$)</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          inputMode="decimal"
                          min="0"
                          placeholder="R$ 0,00"
                          value={displayValue}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          className="border-slate-300 dark:border-slate-600"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            )}

            {paymentModel === 'salario_mensal' && (
              <FormField
                control={control}
                name="monthly_salary"
                render={({ field }) => {
                  const { displayValue, handleChange, handleBlur } = useCurrencyFormat({
                    value: field.value ?? 0,
                    onChange: (v) => field.onChange(v),
                  });
                  return (
                    <FormItem>
                      <FormLabel className="text-slate-700 dark:text-slate-300">Salário Mensal (R$)</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          inputMode="decimal"
                          min="0"
                          placeholder="R$ 0,00"
                          value={displayValue}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          className="border-slate-300 dark:border-slate-600"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            )}

            {(paymentModel === 'comissao' || paymentModel === 'participacao_lucros') && (
              <FormField
                control={control}
                name="commission_percentage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-700 dark:text-slate-300">
                      {paymentModel === 'comissao' ? 'Percentual de Comissão (%)' : 'Percentual de Participação (%)'}
                    </FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min="0" 
                        max="100"
                        step="0.1"
                        placeholder="0.0"
                        value={field.value ?? ''}
                        onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                        className="border-slate-300 dark:border-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>
        </CardContent>
      </Card>

      {/* Observações e Notas */}
      <Card className="border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <FileText className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Observações e Notas
          </h2>
        </div>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 gap-6">
            {/* Notas sobre ensino */}
            <FormField
              control={control}
              name="teaching_notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-700 dark:text-slate-300">Notas sobre Ensino/Metodologia</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Informações sobre metodologia de ensino, abordagem pedagógica, especializações técnicas..."
                      {...field}
                      className="border-slate-300 dark:border-slate-600 min-h-20"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Observações gerais */}
            <FormField
              control={control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-slate-700 dark:text-slate-300">Observações Gerais</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Observações adicionais, informações relevantes sobre o instrutor..."
                      {...field}
                      className="border-slate-300 dark:border-slate-600 min-h-20"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 