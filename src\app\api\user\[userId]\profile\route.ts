import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/services/supabase/server'
import { getPermissionService } from '@/services/permissions/service'
import { checkApiAuthAndPermission } from '@/services/auth/actions/api-auth-actions'
import { z } from 'zod'

// Schema para validação do parâmetro userId
const userIdSchema = z.string().uuid({
  message: 'ID de usuário inválido'
})

// Cache para respostas de perfil (edge cache)
const profileResponseCache = new Map<
  string,
  {
    data: any
    timestamp: number
    etag: string
    gzipped?: boolean
  }
>()
const PROFILE_CACHE_TTL = 3 * 60 * 1000 // 3 minutos
const MAX_CACHE_SIZE = 100 // Máximo de perfis em cache

/**
 * Gera ETag para cache baseado nos dados do perfil
 */
function generateETag(data: any): string {
  const content = JSON.stringify(data)
  const hash = require('crypto').createHash('md5').update(content).digest('hex')
  return `"${hash}"`
}

/**
 * Comprime dados se necessário
 */
function compressResponse(data: any): { compressed: string; isCompressed: boolean } {
  const jsonString = JSON.stringify(data)

  // Só comprimir se o payload for maior que 1KB
  if (jsonString.length > 1024) {
    try {
      const zlib = require('zlib')
      const compressed = zlib.gzipSync(jsonString).toString('base64')
      return { compressed, isCompressed: true }
    } catch (error) {
      console.warn('Erro ao comprimir resposta:', error)
    }
  }

  return { compressed: jsonString, isCompressed: false }
}

/**
 * Descomprime dados se necessário
 */
function decompressResponse(data: string, isCompressed: boolean): any {
  if (!isCompressed) {
    return JSON.parse(data)
  }

  try {
    const zlib = require('zlib')
    const decompressed = zlib.gunzipSync(Buffer.from(data, 'base64')).toString()
    return JSON.parse(decompressed)
  } catch (error) {
    console.error('Erro ao descomprimir resposta:', error)
    throw error
  }
}

/**
 * Limpa cache expirado e mantém apenas os mais acessados
 */
function cleanProfileCache() {
  const now = Date.now()
  const entries = Array.from(profileResponseCache.entries())

  // Remover entradas expiradas
  const validEntries = entries.filter(([key, value]) => now - value.timestamp < PROFILE_CACHE_TTL)

  // Se ainda estiver acima do limite, manter apenas os mais recentes
  if (validEntries.length > MAX_CACHE_SIZE) {
    validEntries.sort((a, b) => b[1].timestamp - a[1].timestamp)
    validEntries.splice(MAX_CACHE_SIZE)
  }

  // Recriar cache com entradas válidas
  profileResponseCache.clear()
  validEntries.forEach(([key, value]) => {
    profileResponseCache.set(key, value)
  })
}

interface StudentBelt {
  id?: string
  belt_color: string
  degree: number
  label?: string | null
  stripe_color?: string | null
  show_center_line?: boolean | null
  center_line_color?: string | null
  awardedAt?: string | null
  instructor?: string | null
  modality_name?: string | null
  belt_label?: string | null
}

interface InstructorBelt {
  id: string
  belt_color: string
  degree: number
  awarded_at: string
  notes?: string | null
  modality_name?: string | null
  awarded_by_user?: {
    first_name: string
    last_name: string
    full_name: string
  }
}

interface UserProfileData {
  id: string
  email: string
  first_name: string | null
  last_name: string | null
  full_name: string | null
  role: string
  fullName: string
  created_at: string
  avatar_url: string | null
  phone: string | null
  joinDate: string | null
  address: {
    street?: string
    number?: string
    complement?: string
    neighborhood?: string
    city?: string
    state?: string
    postalCode?: string
  } | null
  emergency_contact: string | null
  emergency_phone: string | null

  // Campos de responsável/menor de idade
  is_minor?: boolean
  is_guardian_account?: boolean
  managed_student_id?: string | null
  guardian_name?: string | null
  guardian_email?: string | null
  guardian_phone?: string | null
  guardian_relationship?: string | null
  guardian_document?: string | null

  // Campos específicos de estudante
  registration_number?: string
  status?: string
  financial_status?: string
  birth_date?: string
  emergency_contact_name?: string
  emergency_contact_phone?: string
  emergency_contact_relationship?: string
  health_notes?: string
  allergies?: string
  medical_conditions?: string
  medications?: string
  gender?: string
  street?: string
  street_number?: string
  complement?: string
  neighborhood?: string
  city?: string
  state?: string
  postal_code?: string
  check_in_code?: string
  current_belt?: StudentBelt
  currentBelt?: StudentBelt // Compatibilidade camelCase
  payment_status?: string
  last_payment_date?: string
  next_payment_due?: string
  attendance_rate?: number
  last_attendance_date?: string

  // Campos específicos de instrutor
  specialties?: string[]
  certification_level?: string | null
  federation_registration?: string | null
  experience_years?: number | null
  bio?: string | null
  contract_type?: string
  payment_model?: string | null
  payment_value?: string | null
  payment_percentage?: string | null
  has_first_aid_certification?: boolean
  has_cpr_certification?: boolean
  has_rules_course?: boolean
  has_ibjjf_certification?: boolean
  hire_date?: string | null
  teaching_notes?: string | null
  instructor_id?: string
  current_instructor_belt?: InstructorBelt
}

/**
 * Sanitiza os dados do perfil para evitar XSS e outros ataques
 */
function sanitizeProfileData(data: any, authUserData: any | null = null): UserProfileData {
  // Função auxiliar para sanitizar strings
  const sanitizeString = (value: any): string | null => {
    if (typeof value !== 'string') return null
    return value.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/&/g, '&amp;').replace(/"/g, '&quot;').replace(/'/g, '&#x27;').trim()
  }

  // Sanitizar campos básicos
  const sanitized: any = {
    id: data.id,
    email: sanitizeString(data.email),
    first_name: sanitizeString(data.first_name),
    last_name: sanitizeString(data.last_name),
    role: sanitizeString(data.role),
    created_at: data.created_at,
    avatar_url: sanitizeString(data.avatar_url),
    phone: sanitizeString(data.phone),
    status: sanitizeString(data.status) || 'active' // Status centralizado da tabela users
  }

  // Processar campos calculados - priorizar dados do auth.users se disponíveis e mais completos
  let fullNameFromAuth = null
  if (authUserData?.full_name) {
    fullNameFromAuth = sanitizeString(authUserData.full_name)
  }

  // Usar full_name do auth se for mais completo que o da tabela users
  const currentFullName = sanitizeString(data.full_name)
  const fallbackFullName = `${sanitized.first_name || ''}${sanitized.last_name ? ' ' + sanitized.last_name : ''}`

  // Priorizar: auth.users > users.full_name > first_name + last_name
  if (fullNameFromAuth && fullNameFromAuth.trim().split(' ').length >= 2) {
    sanitized.fullName = fullNameFromAuth
  } else if (currentFullName && currentFullName.trim().split(' ').length >= 2) {
    sanitized.fullName = currentFullName
  } else {
    sanitized.fullName = fallbackFullName
  }

  sanitized.joinDate = data.created_at ? new Date(data.created_at).toLocaleDateString('pt-BR') : null

  // Sanitizar metadados aninhados
  const metadata = data.metadata || {}

  if (metadata.address) {
    sanitized.address = {
      street: sanitizeString(metadata.address.street),
      number: sanitizeString(metadata.address.number),
      complement: sanitizeString(metadata.address.complement),
      neighborhood: sanitizeString(metadata.address.neighborhood),
      city: sanitizeString(metadata.address.city),
      state: sanitizeString(metadata.address.state),
      postalCode: sanitizeString(metadata.address.postalCode)
    }
  } else {
    sanitized.address = null
  }

  sanitized.emergency_contact = sanitizeString(metadata.emergency_contact)
  sanitized.emergency_phone = sanitizeString(metadata.emergency_phone)

  return sanitized
}

export async function GET(request: NextRequest, { params }: { params: Promise<{ userId: string }> }) {
  try {
    const { userId } = await params

    const validatedParams = userIdSchema.safeParse(userId)
    if (!validatedParams.success) {
      return NextResponse.json({ error: 'ID de usuário inválido' }, { status: 400 })
    }

    const validatedUserId = validatedParams.data

    // Verificar cache primeiro
    const cacheKey = `profile:${validatedUserId}`
    const cached = profileResponseCache.get(cacheKey)

    // Verificar se temos cache válido
    if (cached && Date.now() - cached.timestamp < PROFILE_CACHE_TTL) {
      const ifNoneMatch = request.headers.get('if-none-match')

      // Se o cliente tem o mesmo ETag, retornar 304 Not Modified
      if (ifNoneMatch === cached.etag) {
        return new NextResponse(null, {
          status: 304,
          headers: {
            'ETag': cached.etag,
            'Cache-Control': 'private, max-age=180' // 3 minutos
          }
        })
      }

      // Retornar dados do cache
      const responseData = cached.gzipped ? decompressResponse(cached.data, true) : cached.data

      return NextResponse.json(responseData, {
        headers: {
          'ETag': cached.etag,
          'Cache-Control': 'private, max-age=180',
          'X-Cache': 'HIT',
          'X-Content-Type-Options': 'nosniff',
          'X-Frame-Options': 'DENY',
          'X-XSS-Protection': '1; mode=block'
        }
      })
    }

    // Verificar o limite de requisições por IP
    const ip = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1'

    // Adicionar controle de acesso mais restrito para esta rota
    const MAX_REQUESTS = 30 // Máximo de requisições em 1 minuto
    const WINDOW = 60 * 1000 // 1 minuto em milissegundos
    const ipCache = new Map<string, { count: number; timestamp: number }>()

    const key = `${ip}:profile:${validatedUserId}`
    const now = Date.now()
    const cached_ip = ipCache.get(key) || { count: 0, timestamp: now }

    if (now - cached_ip.timestamp > WINDOW) {
      cached_ip.count = 0
      cached_ip.timestamp = now
    }

    cached_ip.count++
    ipCache.set(key, cached_ip)

    // if (cached_ip.count > MAX_REQUESTS) {
    //   return NextResponse.json(
    //     { error: 'Muitas requisições. Tente novamente mais tarde.' },
    //     {
    //       status: 429,
    //       headers: {
    //         'Retry-After': '60',
    //         'X-RateLimit-Limit': MAX_REQUESTS.toString(),
    //         'X-RateLimit-Remaining': '0',
    //         'X-RateLimit-Reset': Math.ceil((cached_ip.timestamp + WINDOW) / 1000).toString()
    //       }
    //     }
    //   );
    // }

    // Usar server action para autenticação e permissões (otimizada)
    const permissionService = getPermissionService()
    const authResult = await checkApiAuthAndPermission(validatedUserId, 'profile', 'view', permissionService)

    // Se não estiver autenticado ou não tiver permissão, retorna a resposta de erro
    if (authResult.response) {
      return authResult.response
    }

    const supabase = await createClient()

    // Query única otimizada que busca todos os dados necessários com joins
    const { data, error } = await supabase
      .from('users')
      .select(
        `
        id,
        email,
        first_name,
        last_name,
        full_name,
        role,
        created_at,
        avatar_url,
        phone,
        metadata,
        status,
        is_minor,
        is_guardian_account,
        managed_student_id,
        guardian_name,
        guardian_email,
        guardian_phone,
        guardian_relationship,
        guardian_document
      `
      )
      .eq('id', validatedUserId)
      .single()

    if (error) {
      console.error('Erro ao carregar perfil do usuário:', error)
      return NextResponse.json({ error: 'Erro ao carregar perfil' }, { status: 500 })
    }

    // Buscar dados do auth.users para obter o full_name correto se necessário
    let authUserData = null
    const shouldFetchAuthData = !data.full_name || data.full_name.trim().split(' ').length < 3 // Se tem menos de 3 palavras, provavelmente está incompleto

    if (shouldFetchAuthData) {
      try {
        const { data: authData, error: authError } = await supabase.from('auth.users').select('raw_user_meta_data').eq('id', validatedUserId).single()

        if (!authError && authData?.raw_user_meta_data) {
          authUserData = authData.raw_user_meta_data
        }
      } catch (error) {
        console.warn('Não foi possível buscar dados do auth.users:', error)
      }
    }

    // Não é mais necessário sanitizar aqui, a sanitização completa será feita no final
    // let userData = sanitizeProfileData(data, authUserData);

    if (data.role === 'student') {
      const { data: studentData, error: studentError } = await supabase
        .from('students')
        .select(
          `
          id,
          registration_number,
          financial_status,
          birth_date,
          emergency_contact_name,
          emergency_contact_phone,
          emergency_contact_relationship,
          health_notes,
          allergies,
          medical_conditions,
          medications,
          gender,
          street,
          street_number,
          complement,
          neighborhood,
          city,
          state,
          postal_code,
          check_in_code,
          payment_status,
          last_payment_date,
          next_payment_due,
          attendance_rate,
          last_attendance_date,
          current_belt_id
        `
        )
        .eq('user_id', validatedUserId)
        .single()

      if (studentError) {
        console.error('Erro ao carregar dados do estudante:', studentError)
        // Não retorna erro fatal, pois o usuário pode existir sem perfil de estudante
      } else if (studentData) {
        // Buscar detalhes da faixa via RPC
        let currentBeltDetails: any = null
        if (studentData.id) {
          const { data: beltDetails } = await supabase.rpc('get_student_current_belt_details', { student_id_param: studentData.id })
          if (beltDetails && beltDetails.length > 0) {
            const belt = beltDetails[0]
            currentBeltDetails = {
              id: belt.id,
              belt_level_id: belt.belt_level_id,
              color: belt.belt_color,
              degree: belt.degree,
              stripeColor: belt.stripe_color,
              showCenterLine: belt.show_center_line,
              centerLineColor: belt.center_line_color,
              label: belt.label,
              sortOrder: belt.sort_order,
              awardedAt: belt.awarded_at || null,
              awarded_at: belt.awarded_at || null, // ISO timestamp
              belt_label: belt.label,
              modality_name: null,
              instructor: null
            }

            // Buscar instrutor que graduou e data usando current_belt_id do estudante
            const studentBeltId = studentData.current_belt_id
            if (studentBeltId) {
              try {
                const { data: beltJoin } = await supabase
                  .from('student_belts')
                  .select(`awarded_at, users:awarded_by(first_name,last_name)`) // inclui data e instrutor
                  .eq('id', studentBeltId)
                  .single()

                console.log('[BELT_JOIN]', beltJoin)
                console.log('[BELT_JOIN] awarded_at', beltJoin?.awarded_at)
                console.log('[BELT_JOIN] currentBeltDetails before', currentBeltDetails)
                if (beltJoin && (beltJoin as any).users) {
                  const instr = (beltJoin as any).users
                  currentBeltDetails.instructor = `${instr.first_name || ''}${instr.last_name ? ' ' + instr.last_name : ''}`.trim()
                }
                if (beltJoin && beltJoin.awarded_at) {
                  currentBeltDetails.awarded_at = beltJoin.awarded_at
                  currentBeltDetails.awardedAt = beltJoin.awarded_at ? new Date(beltJoin.awarded_at).toLocaleDateString('pt-BR') : null
                }
              } catch (beltJoinErr) {
                console.warn('Não foi possível buscar dados adicionais da faixa:', beltJoinErr)
              }
            }

            // Buscar o nome da modalidade se não estiver presente
            if (belt.belt_level_id) {
              try {
                const { data: levelData, error: levelError } = await supabase
                  .from('belt_levels')
                  .select(`modalities!inner(name)`) // join implícito
                  .eq('id', belt.belt_level_id)
                  .single()

                if (!levelError && levelData && levelData.modalities) {
                  currentBeltDetails.modality_name = (levelData as any).modalities.name
                }
              } catch (levelFetchError) {
                console.warn('Não foi possível buscar modalidade da faixa:', levelFetchError)
              }
            }
          }
        }
        ;(data as any).students = [{ ...studentData, current_belt: currentBeltDetails }]
      }
    }

    // INÍCIO: Busca adicional de dados de instrutor
    let instructorData: any | null = null
    try {
      const { data: instrData, error: instrError } = await supabase
        .from('instructors')
        .select(
          `
          id,
          specialties,
          certification_level,
          federation_registration,
          experience_years,
          bio,
          contract_type,
          payment_model,
          payment_value,
          payment_percentage,
          has_first_aid_certification,
          has_cpr_certification,
          has_rules_course,
          has_ibjjf_certification,
          hire_date,
          teaching_notes,
          birth_date,
          gender,
          street,
          street_number,
          complement,
          neighborhood,
          city,
          state,
          postal_code,
          emergency_contact_name,
          emergency_contact_phone,
          emergency_contact_relationship,
          current_belt_id
        `
        )
        .eq('user_id', validatedUserId)
        .single()

      if (!instrError && instrData) {
        // Buscar faixa atual via RPC
        let currentInstructorBelt: any = null
        const { data: beltDetails } = await supabase.rpc('get_instructor_current_belt_details', { instructor_id_param: instrData.id })
        if (beltDetails && beltDetails.length > 0) {
          const beltDetail = beltDetails[0]
          currentInstructorBelt = {
            belt_level_id: beltDetail.belt_level_id,
            belt_color: beltDetail.belt_color,
            color: beltDetail.belt_color, // Para compatibilidade
            degree: beltDetail.degree,
            stripe_color: beltDetail.stripe_color,
            show_center_line: beltDetail.show_center_line,
            center_line_color: beltDetail.center_line_color,
            label: beltDetail.label,
            sort_order: beltDetail.sort_order,
            awarded_at: beltDetail.awarded_at,
            awarded_by: beltDetail.awarded_by,
            modality_name: beltDetail.modality_name,
            notes: beltDetail.notes,
            // Dados do usuário que concedeu a faixa
            awarded_by_user:
              beltDetail.awarded_by_first_name || beltDetail.awarded_by_last_name || beltDetail.awarded_by_full_name
                ? {
                    first_name: beltDetail.awarded_by_first_name,
                    last_name: beltDetail.awarded_by_last_name,
                    full_name: beltDetail.awarded_by_full_name
                  }
                : null,
            // Campos adicionais para compatibilidade
            awarded_by_first_name: beltDetail.awarded_by_first_name,
            awarded_by_last_name: beltDetail.awarded_by_last_name,
            awarded_by_full_name: beltDetail.awarded_by_full_name
          }
        }
        instructorData = { ...instrData, current_instructor_belt: currentInstructorBelt }
      }
    } catch (instrFetchError) {
      console.warn('Não foi possível carregar dados de instrutor ou não existem para este usuário.')
    }

    // Caso haja dados de instrutor, adiciona ao objeto principal, mantendo compatibilidade
    if (instructorData) {
      ;(data as any).instructors = [instructorData]
    }

    // Processar e sanitizar dados de forma otimizada
    const processedUserData = processAndSanitizeProfileData(data)

    // Gerar ETag para cache
    const etag = generateETag(processedUserData)

    // Armazenar no cache com compressão
    const { compressed, isCompressed } = compressResponse(processedUserData)

    // Limpar cache se necessário
    if (profileResponseCache.size >= MAX_CACHE_SIZE) {
      cleanProfileCache()
    }

    profileResponseCache.set(cacheKey, {
      data: isCompressed ? compressed : processedUserData,
      timestamp: Date.now(),
      etag,
      gzipped: isCompressed
    })

    // Configurar resposta com cabeçalhos de segurança e cache
    const response = NextResponse.json(processedUserData)

    // Adicionar cabeçalhos de segurança e cache
    response.headers.set('ETag', etag)
    response.headers.set('Cache-Control', 'private, max-age=180') // 3 minutos
    response.headers.set('X-Cache', 'MISS')
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('X-XSS-Protection', '1; mode=block')
    response.headers.set('Content-Security-Policy', "default-src 'self'")

    return response
  } catch (error) {
    console.error('Erro ao processar requisição de perfil:', error)
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 })
  }
}

/**
 * Processa e sanitiza os dados do perfil de forma otimizada
 * Consolida a lógica de sanitização em uma função mais eficiente
 */
function processAndSanitizeProfileData(data: any): UserProfileData {
  // Função auxiliar para sanitizar strings de forma mais eficiente
  const sanitizeString = (value: any): string | null => {
    if (typeof value !== 'string') return null
    return value
      .replace(/[<>&"']/g, (match) => {
        const entities: { [key: string]: string } = {
          '<': '&lt;',
          '>': '&gt;',
          '&': '&amp;',
          '"': '&quot;',
          "'": '&#x27;'
        }
        return entities[match]
      })
      .trim()
  }

  // Sanitizar campos básicos
  const sanitized: any = {
    id: data.id,
    email: sanitizeString(data.email),
    first_name: sanitizeString(data.first_name),
    last_name: sanitizeString(data.last_name),
    role: sanitizeString(data.role),
    created_at: data.created_at,
    avatar_url: sanitizeString(data.avatar_url),
    phone: sanitizeString(data.phone),
    status: sanitizeString(data.status) || 'active',
    // Campos de responsável/menor de idade
    is_minor: data.is_minor || false,
    is_guardian_account: data.is_guardian_account || false,
    managed_student_id: sanitizeString(data.managed_student_id),
    guardian_name: sanitizeString(data.guardian_name),
    guardian_email: sanitizeString(data.guardian_email),
    guardian_phone: sanitizeString(data.guardian_phone),
    guardian_relationship: sanitizeString(data.guardian_relationship),
    guardian_document: sanitizeString(data.guardian_document)
  }

  // Processar nome completo de forma otimizada
  const currentFullName = sanitizeString(data.full_name)
  const fallbackFullName = `${sanitized.first_name || ''}${sanitized.last_name ? ' ' + sanitized.last_name : ''}`

  sanitized.fullName = currentFullName && currentFullName.trim().split(' ').length >= 2 ? currentFullName : fallbackFullName

  sanitized.joinDate = data.created_at ? new Date(data.created_at).toLocaleDateString('pt-BR') : null

  // Processar metadados de endereço
  const metadata = data.metadata || {}

  if (metadata.address) {
    sanitized.address = {
      street: sanitizeString(metadata.address.street),
      number: sanitizeString(metadata.address.number),
      complement: sanitizeString(metadata.address.complement),
      neighborhood: sanitizeString(metadata.address.neighborhood),
      city: sanitizeString(metadata.address.city),
      state: sanitizeString(metadata.address.state),
      postalCode: sanitizeString(metadata.address.postalCode)
    }
  } else {
    sanitized.address = null
  }

  sanitized.emergency_contact = sanitizeString(metadata.emergency_contact)
  sanitized.emergency_phone = sanitizeString(metadata.emergency_phone)

  // Processar dados específicos de estudante
  if (data.students && data.students[0]) {
    const student = data.students[0]
    Object.assign(sanitized, {
      registration_number: sanitizeString(student.registration_number),
      financial_status: sanitizeString(student.financial_status),
      birth_date: student.birth_date,
      emergency_contact_name: sanitizeString(student.emergency_contact_name),
      emergency_contact_phone: sanitizeString(student.emergency_contact_phone),
      emergency_contact_relationship: sanitizeString(student.emergency_contact_relationship),
      health_notes: sanitizeString(student.health_notes),
      allergies: sanitizeString(student.allergies),
      medical_conditions: sanitizeString(student.medical_conditions),
      medications: sanitizeString(student.medications),
      gender: sanitizeString(student.gender),
      street: sanitizeString(student.street),
      street_number: sanitizeString(student.street_number),
      complement: sanitizeString(student.complement),
      neighborhood: sanitizeString(student.neighborhood),
      city: sanitizeString(student.city),
      state: sanitizeString(student.state),
      postal_code: sanitizeString(student.postal_code),
      check_in_code: sanitizeString(student.check_in_code),
      payment_status: sanitizeString(student.payment_status),
      last_payment_date: student.last_payment_date,
      next_payment_due: student.next_payment_due,
      attendance_rate: student.attendance_rate,
      last_attendance_date: student.last_attendance_date
    })

    // Processar faixa atual
    if (student.current_belt) {
      const beltData = {
        id: student.current_belt.id,
        belt_color: sanitizeString(student.current_belt.belt_color),
        color: sanitizeString(student.current_belt.color || student.current_belt.belt_color),
        degree: student.current_belt.degree,
        awardedAt: student.current_belt.awarded_at ? new Date(student.current_belt.awarded_at).toLocaleDateString('pt-BR') : null,
        instructor: student.current_belt.instructor ? sanitizeString(student.current_belt.instructor) : null,
        label: student.current_belt.label ? sanitizeString(student.current_belt.label) : null,
        stripe_color: student.current_belt.stripe_color ? sanitizeString(student.current_belt.stripe_color) : null,
        stripeColor: sanitizeString(student.current_belt.stripeColor || student.current_belt.stripe_color),
        show_center_line: typeof student.current_belt.show_center_line === 'boolean' ? student.current_belt.show_center_line : null,
        showCenterLine: typeof student.current_belt.show_center_line === 'boolean' ? student.current_belt.show_center_line : typeof student.current_belt.showCenterLine === 'boolean' ? student.current_belt.showCenterLine : null,
        center_line_color: student.current_belt.center_line_color ? sanitizeString(student.current_belt.center_line_color) : null,
        centerLineColor: sanitizeString(student.current_belt.centerLineColor || student.current_belt.center_line_color),
        modality_name: student.current_belt.modality_name ? sanitizeString(student.current_belt.modality_name) : null,
        belt_label: student.current_belt.belt_label ? sanitizeString(student.current_belt.belt_label) : null
      }

      // Disponibilizar em ambos os formatos para compatibilidade
      // sanitized.current_belt = beltData;
      sanitized.currentBelt = beltData
    }
  }

  // Processar dados específicos de instrutor
  if (data.instructors && data.instructors[0]) {
    const instructor = data.instructors[0]
    Object.assign(sanitized, {
      instructor_id: instructor.id,
      specialties: Array.isArray(instructor.specialties) ? instructor.specialties.map((s: any) => sanitizeString(s)).filter(Boolean) : [],
      certification_level: sanitizeString(instructor.certification_level),
      federation_registration: sanitizeString(instructor.federation_registration),
      experience_years: instructor.experience_years,
      bio: sanitizeString(instructor.bio),
      contract_type: sanitizeString(instructor.contract_type),
      payment_model: sanitizeString(instructor.payment_model),
      payment_value: sanitizeString(instructor.payment_value),
      payment_percentage: sanitizeString(instructor.payment_percentage),
      has_first_aid_certification: instructor.has_first_aid_certification,
      has_cpr_certification: instructor.has_cpr_certification,
      has_rules_course: instructor.has_rules_course,
      has_ibjjf_certification: instructor.has_ibjjf_certification,
      hire_date: instructor.hire_date,
      teaching_notes: sanitizeString(instructor.teaching_notes),
      birth_date: instructor.birth_date,
      gender: sanitizeString(instructor.gender),
      street: sanitizeString(instructor.street),
      street_number: sanitizeString(instructor.street_number),
      complement: sanitizeString(instructor.complement),
      neighborhood: sanitizeString(instructor.neighborhood),
      city: sanitizeString(instructor.city),
      state: sanitizeString(instructor.state),
      postal_code: sanitizeString(instructor.postal_code),
      emergency_contact_name: sanitizeString(instructor.emergency_contact_name),
      emergency_contact_phone: sanitizeString(instructor.emergency_contact_phone),
      emergency_contact_relationship: sanitizeString(instructor.emergency_contact_relationship)
    })

    // Processar faixa atual do instrutor
    if (instructor.current_instructor_belt) {
      const belt = instructor.current_instructor_belt
      sanitized.current_instructor_belt = {
        id: belt.id,
        belt_color: sanitizeString(belt.belt_color),
        degree: belt.degree,
        awarded_at: belt.awarded_at,
        notes: sanitizeString(belt.notes),
        modality_name: sanitizeString(belt.modality_name),
        awarded_by_user: belt.awarded_by_user
          ? {
              first_name: sanitizeString(belt.awarded_by_user.first_name),
              last_name: sanitizeString(belt.awarded_by_user.last_name),
              full_name: sanitizeString(belt.awarded_by_user.full_name)
            }
          : undefined
      }
    }
  }

  return sanitized
}
