'use client';

import { useCallback, useMemo } from 'react';
import { Calendar } from 'lucide-react';
import { FieldErrors, UseFormSetValue } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import DatePicker from '@/components/ui/calendar-01';
import FormFieldWrapper from '../FormFieldWrapper';
import { FormValues } from '../validation';
import { normalizeDateString, getTodayDateString } from '../utils';

interface ScheduleSectionProps {
  errors: FieldErrors<FormValues>;
  setValue: UseFormSetValue<FormValues>;
  watchedValues: FormValues;
  initialData?: { start_date?: string };
}

export default function ScheduleSection({
  errors,
  setValue,
  watchedValues,
  initialData
}: ScheduleSectionProps) {
  const today = useMemo(() => new Date(), []);

  // Verificar se a data original já passou (memoizado)
  const isStartDateInPast = useMemo(() => {
    if (!initialData?.start_date) return false;
    
    try {
      const todayString = getTodayDateString();
      const originalDateString = initialData.start_date.split('T')[0]; // Pegar apenas a parte da data
      
      return originalDateString < todayString;
    } catch (error) {
      console.error('Erro ao verificar data original:', error);
      return false;
    }
  }, [initialData?.start_date]);

  // Converter datas string para Date objects preservando timezone local
  const getStartDate = (): Date | undefined => {
    if (!watchedValues.start_date) return undefined;
    // Para datas no formato YYYY-MM-DD, adicionar horário local para evitar problemas de timezone
    const dateString = watchedValues.start_date;
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      return new Date(dateString + 'T00:00:00');
    }
    return new Date(watchedValues.start_date);
  };

  const getEndDate = (): Date | undefined => {
    if (!watchedValues.end_date) return undefined;
    // Para datas no formato YYYY-MM-DD, adicionar horário local para evitar problemas de timezone
    const dateString = watchedValues.end_date;
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      return new Date(dateString + 'T00:00:00');
    }
    return new Date(watchedValues.end_date);
  };

  // Handlers para mudança de datas - corrigido para evitar problemas de timezone (memoizados)
  const handleStartDateChange = useCallback((date: Date | undefined) => {
    if (date) {
      const dateString = normalizeDateString(date);
      console.log('Debug - Data selecionada:', {
        originalDate: date,
        normalizedString: dateString,
        dateDisplay: date.toLocaleDateString('pt-BR')
      });
      setValue('start_date', dateString, { shouldValidate: true });
    } else {
      setValue('start_date', '', { shouldValidate: true });
    }
  }, [setValue]);

  const handleEndDateChange = useCallback((date: Date | undefined) => {
    if (date) {
      const dateString = normalizeDateString(date);
      setValue('end_date', dateString, { shouldValidate: true });
    } else {
      setValue('end_date', '', { shouldValidate: true });
    }
  }, [setValue]);

  // Handler para mudança do switch (memoizado)
  const handleActiveChange = useCallback((checked: boolean) => {
    setValue('is_active', checked);
  }, [setValue]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Cronograma da Turma
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label className="text-sm font-medium">Status da Turma</Label>
          <div className="flex items-center gap-2">
            <Switch
              checked={watchedValues.is_active}
              onCheckedChange={handleActiveChange}
            />
            <span className="text-sm text-muted-foreground">
              {watchedValues.is_active ? 'Turma ativa' : 'Turma inativa'}
            </span>
            <Badge variant={watchedValues.is_active ? 'default' : 'secondary'}>
              {watchedValues.is_active ? 'Ativa' : 'Inativa'}
            </Badge>
          </div>
        </div>

        <Separator />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormFieldWrapper 
            label="Data de Início" 
            required 
            error={errors.start_date?.message}
            hint={isStartDateInPast 
              ? "⚠️ Data de início não pode ser alterada pois a turma já começou" 
              : "A turma pode iniciar hoje ou em uma data futura"
            }
          >
            <DatePicker
              date={getStartDate()}
              onDateChange={isStartDateInPast ? undefined : handleStartDateChange}
              placeholder="Selecione a data de início"
              minDate={isStartDateInPast ? undefined : today}
              disabled={isStartDateInPast}
              className={errors.start_date ? 'border-red-500' : (isStartDateInPast ? 'bg-gray-100 cursor-not-allowed' : '')}
            />
            {isStartDateInPast && (
              <div className="mt-1 text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200">
                <strong>Turma já iniciada:</strong> A data de início não pode ser alterada pois a turma já começou.
              </div>
            )}
          </FormFieldWrapper>

          <FormFieldWrapper 
            label="Data de Término (Opcional)" 
            error={errors.end_date?.message}
            hint="Deixe em branco para turmas sem data de término definida"
          >
            <DatePicker
              date={getEndDate()}
              onDateChange={handleEndDateChange}
              placeholder="Selecione a data de término"
              minDate={getStartDate() || today}
              className={errors.end_date ? 'border-red-500' : ''}
            />
          </FormFieldWrapper>
        </div>
      </CardContent>
    </Card>
  );
} 