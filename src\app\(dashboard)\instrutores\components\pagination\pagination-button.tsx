"use client";

import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import type { PaginationButtonProps } from "./types";

export function PaginationButton({
  page,
  isActive,
  isDisabled,
  onClick,
  children,
}: PaginationButtonProps) {
  return (
    <Button
      variant={isActive ? "default" : "outline"}
      size="icon"
      className={cn(
        "h-8 w-8 text-sm transition-all",
        isActive && "hover:bg-primary/90",
        !isActive && "hover:bg-muted",
        isDisabled && "pointer-events-none opacity-50"
      )}
      onClick={onClick}
      disabled={isDisabled}
      aria-current={isActive ? "page" : undefined}
      aria-label={`Go to page ${page}`}
    >
      {children}
    </Button>
  );
} 