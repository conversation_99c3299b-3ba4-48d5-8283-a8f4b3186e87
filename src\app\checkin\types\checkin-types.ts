export interface Student {
  id: string;
  name: string;
  email: string;
  code: string;
  avatar?: string;
  membership: {
    type: string;
    status: 'active' | 'inactive' | 'suspended';
    expiresAt: string;
  };
}

export interface Class {
  id: string;
  name: string;
  instructor: string;
  time: string;
  location: string;
  capacity: number;
  enrolled: number;
  status: 'upcoming' | 'in-progress' | 'ended';
}

export interface CheckInData {
  student: Student;
  availableClasses: Class[];
}

export interface RecentCheckIn {
  id: string;
  studentName: string;
  className: string;
  time: string;
  status: 'checked-in' | 'checked-out';
  avatar?: string;
}

export interface TodayClass {
  id: string;
  time: string;
  name: string;
  duration: string;
  studentsCount: number;
  status: 'active' | 'upcoming';
}

export interface StudentInClass {
  id: string;
  name: string;
  checkInTime?: string;
  status: 'present' | 'absent' | 'late';
  avatar?: string;
}

export interface ClassInfo {
  id: string;
  name: string;
  time: string;
  instructor: string;
  location: string;
  capacity: number;
} 