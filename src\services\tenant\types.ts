export interface TenantIdentifier {
  id: string | null;
  slug: string | null;
}

export interface TenantInfo extends TenantIdentifier {
  name: string | null;
  primaryColor: string | null;
  secondaryColor: string | null;
  logoUrl: string | null;
}

export interface TenantConfig {
  baseDomain: string;
  reservedPaths: string[];
  defaultTenant: string;
  mainDomains: string[];
  cacheEnabled: boolean;
  cacheTTL: number;
}

export interface TenantCacheEntry {
  slug: string;
  timestamp: number;
  ttl: number;
}

export interface TenantExtractionContext {
  hostname?: string;
  pathname?: string;
  headers?: Record<string, string>;
  cookies?: Record<string, string>;
}

export type TenantExtractionStrategy = 'subdomain' | 'path' | 'header' | 'cookie'; 