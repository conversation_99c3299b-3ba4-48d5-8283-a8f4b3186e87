'use server'

// import { updateSingleClassStatus } from './auto-status-update';

/**
 * Interface para dados básicos de uma aula necessários para verificação de status
 */
export interface ClassStatusData {
  id: string
  status: string
  start_time: string
  end_time: string
  tenant_id: string
}

/**
 * Obtém o status efetivo de uma aula, usando o banco como fonte primária
 * e fazendo fallback para cálculo baseado em tempo se necessário
 */
export async function getEffectiveClassStatus(classData: ClassStatusData): Promise<string> {
  // Para status finais/especiais, sempre confiar no banco de dados
  if (['cancelled', 'rescheduled', 'completed'].includes(classData.status)) {
    return classData.status
  }

  // Para status que podem estar desatualizados, fazer uma verificação básica
  const now = new Date()
  const startTime = new Date(classData.start_time)
  const endTime = new Date(classData.end_time)

  // Se a aula já terminou e ainda está como 'scheduled' ou 'ongoing',
  // confiar no banco mas registrar para investigação
  if (endTime < now && ['scheduled', 'ongoing'].includes(classData.status)) {
    console.warn(`⚠️ Aula ${classData.id} terminou mas status no banco é '${classData.status}' - cron job pode estar desabilitado`)
  }

  // Retornar status do banco de dados (confiamos no sistema automático)
  return classData.status
}

/**
 * Força atualização manual do status de uma aula específica
 * Útil para casos onde o cron job pode estar atrasado ou falhou
 */
export async function forceClassStatusUpdate(
  classId: string,
  tenantId: string
): Promise<{
  success: boolean
  message: string
  oldStatus?: string
  newStatus?: string
}> {
  try {
    // Dynamic import to avoid circular dependency issues
    const { updateSingleClassStatus } = await import('./auto-status-update')
    const result = await updateSingleClassStatus(classId, tenantId)

    if (result.success) {
      console.log(`✅ Status da aula ${classId} atualizado: ${result.oldStatus} → ${result.newStatus}`)
    } else {
      console.warn(`⚠️ Falha ao atualizar status da aula ${classId}: ${result.message}`)
    }

    return result
  } catch (error) {
    console.error(`❌ Erro ao forçar atualização do status da aula ${classId}:`, error)
    return {
      success: false,
      message: 'Erro interno ao forçar atualização'
    }
  }
}

/**
 * Verifica se uma aula precisa ter seu status atualizado
 * baseado na diferença entre o status atual e o esperado
 */
export async function needsStatusUpdate(classData: ClassStatusData): Promise<{
  needsUpdate: boolean
  expectedStatus: string
  reason?: string
}> {
  const now = new Date()
  const startTime = new Date(classData.start_time)
  const endTime = new Date(classData.end_time)

  // Determinar qual deveria ser o status baseado no tempo
  let expectedStatus: string
  if (endTime <= now) {
    expectedStatus = 'completed'
  } else if (startTime <= now && now <= endTime) {
    expectedStatus = 'ongoing'
  } else {
    expectedStatus = 'scheduled'
  }

  // Apenas aulas 'scheduled' e 'ongoing' podem ser atualizadas automaticamente
  if (!['scheduled', 'ongoing'].includes(classData.status)) {
    return {
      needsUpdate: false,
      expectedStatus,
      reason: `Status '${classData.status}' não pode ser atualizado automaticamente`
    }
  }

  // Verificar se há diferença
  if (classData.status === expectedStatus) {
    return {
      needsUpdate: false,
      expectedStatus,
      reason: 'Status já está correto'
    }
  }

  return {
    needsUpdate: true,
    expectedStatus,
    reason: `Status deve mudar de '${classData.status}' para '${expectedStatus}'`
  }
}

/**
 * Helper para transformar dados de aulas com status efetivo
 * Útil para queries que precisam do status mais atualizado
 */
export async function transformClassesWithEffectiveStatus<T extends ClassStatusData>(classes: T[]): Promise<(T & { effectiveStatus: string })[]> {
  return Promise.all(
    classes.map(async (classItem) => ({
      ...classItem,
      effectiveStatus: await getEffectiveClassStatus(classItem)
    }))
  )
}

/**
 * Helper para filtrar aulas por status efetivo
 */
export async function filterClassesByEffectiveStatus<T extends ClassStatusData>(classes: T[], targetStatus: string): Promise<T[]> {
  const results: T[] = []

  for (const classItem of classes) {
    const effectiveStatus = await getEffectiveClassStatus(classItem)
    if (effectiveStatus === targetStatus) {
      results.push(classItem)
    }
  }

  return results
}

/**
 * Helper para agrupar aulas por status efetivo
 */
export async function groupClassesByEffectiveStatus<T extends ClassStatusData>(classes: T[]): Promise<Record<string, T[]>> {
  const acc: Record<string, T[]> = {}

  for (const classItem of classes) {
    const effectiveStatus = await getEffectiveClassStatus(classItem)
    if (!acc[effectiveStatus]) {
      acc[effectiveStatus] = []
    }
    acc[effectiveStatus].push(classItem)
  }

  return acc
}
