'use client';

import { useFavorites } from '@/contexts/FavoritesContext';
import { NavigationItem } from '@/components/layout/sidebar/types';
import { useCallback } from 'react';

export function useSidebarFavorites() {
  const { 
    favorites, 
    addToFavorites, 
    removeFromFavorites, 
    isFavorite, 
    toggleFavorite,
    // Novas funcionalidades de histórico recente
    recentlyVisited,
    getRecentlyVisitedItems
  } = useFavorites();

  const addFavorite = (item: NavigationItem) => {
    addToFavorites(item);
  };

  const removeFavorite = (href: string) => {
    removeFromFavorites(href);
  };

  const checkIsFavorite = (href: string) => {
    return isFavorite(href);
  };

  const handleToggleFavorite = (item: NavigationItem) => {
    toggleFavorite(item);
  };

  const getRecentItems = useCallback((maxItems?: number) => {
    return getRecentlyVisitedItems(maxItems);
  }, [getRecentlyVisitedItems]);

  return {
    favorites,
    addFavorite,
    removeFavorite,
    isFavorite: checkIsFavorite,
    toggleFavorite: handleToggleFavorite,
    favoritesCount: favorites.length,
    // Funcionalidades de histórico recente
    recentlyVisited,
    getRecentItems,
    recentCount: recentlyVisited.length,
  };
}