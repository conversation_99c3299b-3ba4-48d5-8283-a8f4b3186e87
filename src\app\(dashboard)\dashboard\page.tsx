import { protectDashboardRoute } from "@/app/middleware";
import { getCurrentUser } from "@/services/auth/actions/auth-actions";
import { getDashboardData } from "./actions/dashboard-actions";
import { DashboardRealtimeWrapper } from "./components";

export const metadata = {
  title: 'Dashboard Administrativo',
  description: 'Painel de controle para administradores do sistema',
};

async function getTenantId(): Promise<string | null> {
  const user = await getCurrentUser();
  return (user?.app_metadata as any)?.tenant_id || null;
}

export default async function DashboardPage() {
  const redirectResponse = await protectDashboardRoute();
  if (redirectResponse) {
    return redirectResponse;
  }

  const tenantId = await getTenantId();
  if (!tenantId) {
    return <div>Erro: Tenant não encontrado</div>;
  }

  const data = await getDashboardData(tenantId);

  return (
    <DashboardRealtimeWrapper
      tenantId={tenantId}
      initialData={data}
    />
  );
} 