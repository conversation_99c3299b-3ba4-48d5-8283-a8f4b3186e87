'use client'

import { useQuery, useQueryClient } from '@tanstack/react-query'
import { CACHE_KEYS } from '@/constants/cache-keys'
import { useCallback, useEffect } from 'react'
import { cacheService } from '@/services/cache'
import type { ClassGroupData, EnsinoStats } from '@/app/(dashboard)/perfil/components/ensino/types'

/**
 * Hook para buscar turmas que um instrutor leciona
 */
export function useInstructorClassGroups(instructorId: string) {
  return useQuery({
    queryKey: CACHE_KEYS.INSTRUCTOR_TEACHING.CLASS_GROUPS(instructorId),
    queryFn: async (): Promise<ClassGroupData[]> => {
      const response = await fetch(`/api/instructors/${instructorId}/class-groups`)
      if (!response.ok) {
        throw new Error('Erro ao buscar turmas do instrutor')
      }
      const data = await response.json()
      return data.data as ClassGroupData[]
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    enabled: !!instructorId,
    retry: 2,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000)
  })
}

/**
 * Hook para calcular estatísticas de ensino
 */
export function useInstructorTeachingStats(classGroups?: ClassGroupData[]): EnsinoStats {
  if (!classGroups) {
    return {
      totalClassGroups: 0,
      totalStudents: 0,
      averageCapacityUsage: 0,
      activeClassGroups: 0
    }
  }

  const totalClassGroups = classGroups.length
  const activeClassGroups = classGroups.filter(group => group.is_active).length
  const totalStudents = classGroups.reduce((sum, group) => sum + group.totalStudents, 0)
  
  const groupsWithCapacity = classGroups.filter(group => group.capacityUsage !== null && group.capacityUsage !== undefined)
  const averageCapacityUsage = groupsWithCapacity.length > 0
    ? Math.round(groupsWithCapacity.reduce((sum, group) => sum + (group.capacityUsage || 0), 0) / groupsWithCapacity.length)
    : 0

  return {
    totalClassGroups,
    totalStudents,
    averageCapacityUsage,
    activeClassGroups
  }
}

/**
 * Hook para gerenciar operações de cache e refresh dos dados de ensino
 */
export function useInstructorTeachingCache(instructorId: string) {
  const queryClient = useQueryClient()

  // Inicializar o cache service
  useEffect(() => {
    cacheService.initialize(queryClient)
  }, [queryClient])

  const refreshClassGroups = useCallback(async () => {
    // Invalidar cache usando o serviço centralizado
    cacheService.invalidateQueries(CACHE_KEYS.INSTRUCTOR_TEACHING.CLASS_GROUPS(instructorId), true)
    
    // Refetch usando React Query diretamente
    return queryClient.refetchQueries({
      queryKey: CACHE_KEYS.INSTRUCTOR_TEACHING.CLASS_GROUPS(instructorId)
    })
  }, [queryClient, instructorId])

  const invalidateCache = useCallback(() => {
    cacheService.invalidateQueries(CACHE_KEYS.INSTRUCTOR_TEACHING.CLASS_GROUPS(instructorId), true)
  }, [instructorId])

  const prefetchClassGroups = useCallback(async () => {
    return queryClient.prefetchQuery({
      queryKey: CACHE_KEYS.INSTRUCTOR_TEACHING.CLASS_GROUPS(instructorId),
      queryFn: async (): Promise<ClassGroupData[]> => {
        const response = await fetch(`/api/instructors/${instructorId}/class-groups`)
        if (!response.ok) {
          throw new Error('Erro ao buscar turmas do instrutor')
        }
        const data = await response.json()
        return data.data as ClassGroupData[]
      },
      staleTime: 5 * 60 * 1000
    })
  }, [queryClient, instructorId])

  const getCachedData = useCallback((): ClassGroupData[] | undefined => {
    return cacheService.getData<ClassGroupData[]>(CACHE_KEYS.INSTRUCTOR_TEACHING.CLASS_GROUPS(instructorId))
  }, [instructorId])

  return {
    refreshClassGroups,
    invalidateCache,
    prefetchClassGroups,
    getCachedData
  }
} 