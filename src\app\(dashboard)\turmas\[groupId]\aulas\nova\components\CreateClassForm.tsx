'use client';

import { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

// Imports dos componentes e hooks separados
import { createClassSchema, CreateClassFormData } from './schema';
import { CreateClassFormProps } from './types';
import { 
  useAutoSave, 
  useKeyboardShortcuts, 
  useFormValidation,
  useFormSubmission,
  useFormState,
  useFormHandlers
} from './hooks';


// Componentes
import { FormStatusBars } from './FormStatusBars';
import { DraftCard } from './DraftCard';
import { SuggestionsCard } from './SuggestionsCard';
import { ValidationHelp } from './ValidationHelp';
import { SuccessView } from './SuccessView';
import { BasicInfoSection } from './sections/BasicInfoSection';
import { ConfigSection } from './sections/ConfigSection';
import { ScheduleSection } from './sections/ScheduleSection';
import { NotesSection } from './sections/NotesSection';
import { FormActions } from './FormActions';
import { DebugPanel } from './DebugPanel';

export function CreateClassForm({ classGroup, instructors, branches }: CreateClassFormProps) {
  // Configuração do formulário
  const form = useForm<CreateClassFormData>({
    resolver: zodResolver(createClassSchema),
    defaultValues: {
      name: '',
      description: '',
      instructor_id: classGroup.instructor_id || '',
      branch_id: classGroup.branch_id || '',
      start_time: '',
      end_time: '',
      max_capacity: classGroup.max_capacity || undefined,
      notes: '',
    },
    mode: 'onChange',
  });

  const { handleSubmit, register, watch, setValue, setError, formState: { errors, isValid, isDirty } } = form;
  const formValues = watch();

  // Hooks personalizados
  const { isCheckingConflicts, hasTimeConflict, checkTimeConflicts, getErrorMessage } = useFormValidation();
  
  const { lastSaved, isAutoSaving, clearDraft, getDraft } = useAutoSave(formValues, isValid, false);
  
  const { 
    formProgress, 
    showValidationHelp, 
    setShowValidationHelp, 
    hasDraftLoaded, 
    handleClearDraft 
  } = useFormState({ form, getDraft, clearDraft });
  
  const { handleStartTimeChange, duration } = useFormHandlers({ form, checkTimeConflicts });
  
  const { 
    isPending, 
    submitError, 
    showSuccess, 
    onSubmit, 
    handleCancel 
  } = useFormSubmission({ classGroup, setError, clearDraft });

  // Keyboard shortcuts
  const handleSaveShortcut = useCallback(() => {
    if (isValid && isDirty && !isPending) {
      handleSubmit(onSubmit)();
    }
  }, [isValid, isDirty, isPending, handleSubmit, onSubmit]);

  useKeyboardShortcuts(handleSaveShortcut, handleCancel, !showSuccess);

  if (showSuccess) {
    return <SuccessView />;
  }

  return (
    <div className="space-y-6">
      <FormStatusBars 
        formProgress={formProgress}
        isAutoSaving={isAutoSaving}
        lastSaved={lastSaved}
      />

      <DraftCard 
        hasDraftLoaded={hasDraftLoaded}
        onClearDraft={handleClearDraft}
      />

      <SuggestionsCard 
        formValues={formValues}
        classGroup={classGroup}
        instructors={instructors}
      />

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {submitError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{submitError}</AlertDescription>
          </Alert>
        )}

        <ValidationHelp 
          errors={errors}
          showValidationHelp={showValidationHelp}
          setShowValidationHelp={setShowValidationHelp}
          getErrorMessage={getErrorMessage}
        />

        <BasicInfoSection 
          register={register}
          errors={errors}
          formValues={formValues}
          classGroup={classGroup}
          getErrorMessage={getErrorMessage}
        />

        <ConfigSection 
          watch={watch}
          setValue={setValue}
          errors={errors}
          classGroup={classGroup}
          instructors={instructors}
          branches={branches}
          getErrorMessage={getErrorMessage}
        />

        <ScheduleSection 
          watch={watch}
          setValue={setValue}
          errors={errors}
          isCheckingConflicts={isCheckingConflicts}
          hasTimeConflict={hasTimeConflict}
          duration={duration}
          onStartTimeChange={handleStartTimeChange}
          getErrorMessage={getErrorMessage}
        />

        <NotesSection 
          register={register}
          errors={errors}
          formValues={formValues}
          getErrorMessage={getErrorMessage}
        />

        <FormActions 
          isPending={isPending}
          isValid={isValid}
          isDirty={isDirty}
          hasTimeConflict={hasTimeConflict}
          errors={errors}
          getErrorMessage={getErrorMessage}
          onCancel={handleCancel}
        />
      </form>

      {/* Debug Panel - só aparece em desenvolvimento */}
      {process.env.NODE_ENV === 'development' && (
        <DebugPanel
          form={form}
          errors={errors}
          isCheckingConflicts={isCheckingConflicts}
          hasTimeConflict={hasTimeConflict}
          duration={duration}
          formProgress={formProgress}
          isAutoSaving={isAutoSaving}
          lastSaved={lastSaved}
          isPending={isPending}
          hasDraftLoaded={hasDraftLoaded}
        />
      )}
    </div>
  );
} 