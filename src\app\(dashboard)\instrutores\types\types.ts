import { BeltColor } from "@/components/belt";

export interface Instructor {
  id: string;
  user_id: string;
  name: string;
  email: string;
  phone: string | null;
  avatar: string | null;
  branch_id: string;
  branch_name: string | null;
  specialties: string[];
  experience_years: number;
  contract_type: string | null | undefined;
  payment_model: string | null | undefined;
  created_at: string;
  updated_at: string;
  current_belt?: BeltColor;
  current_belt_degree?: number;
  // Campos de compatibilidade e status centralizado
  is_active: boolean; // Para compatibilidade com código existente
  status: 'active' | 'inactive' | 'suspended'; // Status centralizado da tabela users
  // Status agora vem da tabela users através do join
  user?: {
    status: 'active' | 'inactive' | 'suspended';
  };
  // Dados completos da faixa para o componente BeltWithDetails
  beltDetails?: {
    label?: string | null;
    stripe_color?: string | null;
    show_center_line?: boolean | null;
    center_line_color?: string | null;
  };
}

export interface InstructorUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  specialties?: string[];
  contract_type?: string;
  payment_model?: string;
  experience_years?: number;
  // Status agora vem da tabela users através do join
  user?: {
    status: 'active' | 'inactive' | 'suspended';
  };
}

export interface PaginationInfo {
  total: number;
  total_pages: number;
  current_page: number;
  page_size: number;
}

export interface FetchInstructorsResult {
  data: Instructor[];
  pagination: PaginationInfo;
}

export interface InstructorFilterState {
  search?: string;
  status?: string[];
  specialties?: string[];
  branch?: string[];
  contractType?: string[];
  experienceLevel?: string[];
  belts?: string[];
  startDate?: Date;
  endDate?: Date;
  page?: number;
  limit?: number;
}

export interface Branch {
  id: string;
  name: string;
  address?: string;
  active?: boolean;
}

export interface FilterParams {
  skip?: number;
  take?: number;
  search?: string;
  status?: string;
  specialties?: string[];
  branch?: string[];
  contractType?: string[];
  experienceLevel?: string[];
  belts?: string[];
  page?: number;
}

export interface FetchInstructorsParams {
  skip?: number;
  take?: number;
  search?: string;
  status?: string[];
  specialties?: string[];
  branch?: string[];
  contractType?: string[];
  experienceLevel?: string[];
  belts?: string[];
  startDate?: Date;
  endDate?: Date;
}

export interface BulkActionResult {
  success: boolean;
  message: string;
  affectedIds?: string[];
}

export interface InstructorStats {
  total: number;
  active: number;
  inactive: number;
  specialistCount: Record<string, number>;
  beltCount: Record<string, number>;
}

export interface BeltOption {
  value: string;
  label: string;
  count?: number;
} 