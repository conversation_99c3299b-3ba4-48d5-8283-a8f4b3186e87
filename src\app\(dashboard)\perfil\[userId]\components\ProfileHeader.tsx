'use client';

import { CalendarIcon as Calendar, LogOut, Shield, Edit } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { AvatarUpload } from './AvatarUpload';
import { EditableField } from '@/app/(dashboard)/perfil/components/editable-field';
import { EditButton } from '@/app/(dashboard)/perfil/components/edit-button';
import { PermissionButton } from '@/services/permissions';
import { formatDateBrazil } from '@/utils/format';
import { useGuardianInfo } from '@/hooks/user/guardian';
import { useAdminStatus } from '@/hooks/user/Permissions/useAdminStatus';
import { InlineGuardianInfo } from './GuardianInfoCard';

interface ProfileHeaderInfoProps {
  userId: string;
  userData: any;
}

interface ProfileHeaderAvatarProps {
  avatar?: string;
  name: string;
  userId: string;
}

// Helpers
const getInitials = (name: string): string => {
  if (!name) return "";
  return name
    .split(" ")
    .map((n: string) => n?.[0])
    .slice(0, 2)
    .join("")
    .toUpperCase();
};

const translateStatus = (status: string | undefined): string => {
  if (!status) return '';

  const lowerStatus = status.toLowerCase();

  switch (lowerStatus) {
    case 'active':
    case 'ativo':
      return 'Ativo';
    case 'inactive':
    case 'inativo':
      return 'Inativo';
    case 'pending':
    case 'pendente':
      return 'Pendente';
    case 'suspended':
    case 'suspenso':
      return 'Suspenso';
    default:
      return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
  }
};

const getUserRoleDisplay = (role: string): string => {
  switch (role) {
    case 'student': return 'Aluno';
    case 'instructor': return 'Instrutor';
    case 'admin': return 'Administrador';
    case 'teacher': return 'Professor';
    default: return '';
  }
};

const getStatusBadgeClass = (status: string | undefined, isAdmin: boolean): string => {
  if (isAdmin) {
    return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/40 dark:text-emerald-300 border-emerald-200 dark:border-emerald-900';
  }
  
  if (!status) return 'bg-sky-100 text-sky-800 dark:bg-sky-900/40 dark:text-sky-300 border-sky-200 dark:border-sky-900';
  
  switch (status.toLowerCase()) {
    case 'active':
    case 'ativo':
      return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/40 dark:text-emerald-300 border-emerald-200 dark:border-emerald-900';
    case 'inactive':
    case 'inativo':
      return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300 border-slate-200 dark:border-slate-700';
    case 'pending':
    case 'pendente':
      return 'bg-amber-100 text-amber-800 dark:bg-amber-900/40 dark:text-amber-300 border-amber-200 dark:border-amber-900';
    case 'suspended':
    case 'suspenso':
      return 'bg-rose-100 text-rose-800 dark:bg-rose-900/40 dark:text-rose-300 border-rose-200 dark:border-rose-900';
    default:
      return 'bg-sky-100 text-sky-800 dark:bg-sky-900/40 dark:text-sky-300 border-sky-200 dark:border-sky-900';
  }
};

const validateName = (value: string) => {
  if (!value || value.length < 3) return 'Nome deve ter pelo menos 3 caracteres';
  return undefined;
};

export const ProfileHeaderActions = ({ userId }: { userId: string }) => {
  return (
    <div className="flex items-center gap-2">
      {/* <ProfileSyncButton userId={userId} /> */}
      <EditButton userId={userId} className="mr-2" />
      <PermissionButton
        variant="ghost" 
        className="gap-2 text-rose-600 hover:text-rose-700 hover:bg-rose-50 dark:text-rose-400 dark:hover:bg-slate-700"
        resource="user"
        action="deactivate"
        targetId={userId}
        showDisabled={false}
      >
        <LogOut className="w-4 h-4" />
        Desativar
      </PermissionButton>
    </div>
  );
};

export const ProfileHeaderAvatar = ({ avatar, name, userId }: ProfileHeaderAvatarProps) => {
  return (
    <div className="flex-shrink-0">
      <AvatarUpload 
        userId={userId}
        name={name}
        avatarUrl={avatar}
        size="lg"
      />
    </div>
  );
};

export const ProfileHeaderInfo = ({ userId, userData }: ProfileHeaderInfoProps) => {
  const isAdmin = userData.role === 'admin';
  const isInstructor = userData.role === 'instructor';
  // Verifica se há informação válida sobre anos de experiência (> 0)
  const experienceYears = Number(userData?.experience_years);
  const hasExperienceYears = isInstructor && !isNaN(experienceYears) && experienceYears > 0;

  return (
    <div className="mb-4">
      <div className="flex flex-wrap items-center gap-2 mb-1.5">
        {!isAdmin && (
          <span className={cn(
            "text-xs font-medium px-2.5 py-0.5 rounded-full border",
            getStatusBadgeClass(userData.status, false)
          )}>
            {translateStatus(userData.status)}
          </span>
        )}
        <span className="px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 dark:bg-slate-700 text-primary dark:text-slate-200 border border-primary/20 dark:border-slate-600">
          {getUserRoleDisplay(userData.role)}
        </span>
        
        {/* Badge adicional para instrutores mostrando especialidades */}
        {isInstructor && userData.specialties && userData.specialties.length > 0 && (
          <span className="px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300 border border-amber-200 dark:border-amber-800">
            {Array.isArray(userData.specialties) ? userData.specialties[0] : userData.specialties}
            {Array.isArray(userData.specialties) && userData.specialties.length > 1 && ` +${userData.specialties.length - 1}`}
          </span>
        )}
      </div>
      
      <div className="relative group">
        <h2 className="text-2xl sm:text-3xl font-bold text-slate-900 dark:text-slate-100">
          <EditableField
            inline
            label="Nome"
            value={userData.fullName}
            fieldName="fullName"
            userId={userId}
            validate={validateName}
            className="text-2xl sm:text-3xl font-bold text-slate-900 dark:text-slate-100"
          />
        </h2>
      </div>
      
      <div className="flex flex-wrap items-center gap-3 mt-2">
        {/* Para instrutores, mostrar data de contratação ao invés de data de entrada */}
        {isInstructor && userData.hire_date ? (
          <div className="flex items-center text-sm text-slate-500 dark:text-slate-400">
            <Calendar className="w-3.5 h-3.5 mr-1" />
            <span>Contratado em {formatDateBrazil(userData.hire_date)}</span>
          </div>
        ) : userData.joinDate && !isInstructor && (
          <div className="flex items-center text-sm text-slate-500 dark:text-slate-400">
            <Calendar className="w-3.5 h-3.5 mr-1" />
            <span>Desde {userData.joinDate}</span>
          </div>
        )}
        
        {/* Informações específicas do instrutor */}
        {hasExperienceYears && (
          <div className="flex items-center text-sm text-slate-500 dark:text-slate-400">
            <Shield className="w-3.5 h-3.5 mr-1" />
            <span>{experienceYears} anos de experiência</span>
          </div>
        )}
        
        {isInstructor && userData.certification_level && (
          <div className="flex items-center text-sm text-slate-500 dark:text-slate-400">
            <Shield className="w-3.5 h-3.5 mr-1" />
            <span>{userData.certification_level}</span>
          </div>
        )}
        
        {isAdmin && (
          <div className="flex items-center text-sm text-slate-500 dark:text-slate-400">
            <Shield className="w-3.5 h-3.5 mr-1" />
            <span>Acesso completo ao sistema</span>
          </div>
        )}
      </div>
    </div>
  );
};

export const ProfileHeaderGuardianInfo = ({ userId }: { userId: string }) => {
  const { displayInfo, loading, error } = useGuardianInfo(userId);
  const { isAdmin } = useAdminStatus();
  
  if (loading || error || !displayInfo?.isMinor) {
    return null;
  }

  return (
    <div className="mt-3">
      <InlineGuardianInfo displayInfo={displayInfo} isAdmin={isAdmin} />
    </div>
  );
};

export const ProfileHeader = {
  Actions: ProfileHeaderActions,
  Avatar: ProfileHeaderAvatar,
  Info: ProfileHeaderInfo,
  GuardianInfo: ProfileHeaderGuardianInfo,
}; 