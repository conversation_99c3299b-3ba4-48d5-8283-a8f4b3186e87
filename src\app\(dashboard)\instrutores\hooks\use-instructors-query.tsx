'use client';

import { useQuery, keepPreviousData } from '@tanstack/react-query';
import { Instructor, FetchInstructorsParams, FetchInstructorsResult } from '../types/types';
import { useInstructorsFilter } from './use-instructors-filter';

/**
 * Hook para buscar dados dos instrutores com React Query
 */
export function useInstructorsQuery() {
  const { filters } = useInstructorsFilter();
  
  // Converter os filtros do estado para o formato esperado pela API
  const queryParams: FetchInstructorsParams = {
    skip: ((filters.page || 1) - 1) * (filters.limit || 10),
    take: filters.limit || 10,
    search: filters.search || '',
    status: filters.status || ['active'],
    specialties: filters.specialties || [],
    branch: filters.branch || [],
    contractType: filters.contractType || [],
    experienceLevel: filters.experienceLevel || [],
    belts: filters.belts || [],
    startDate: filters.startDate,
    endDate: filters.endDate
  };
  
  // Criar uma chave de consulta baseada nos filtros atuais
  const queryKey = [
    'instructors',
    queryParams.skip,
    queryParams.take,
    queryParams.search,
    queryParams.status,
    queryParams.specialties,
    queryParams.branch,
    queryParams.contractType,
    queryParams.experienceLevel,
    queryParams.belts,
    queryParams.startDate,
    queryParams.endDate
  ];
  
  // Função que fará a requisição dos dados
  const fetchInstructors = async (): Promise<FetchInstructorsResult> => {
    try {
      // Fazer a requisição para a API
      const response = await fetch('/api/instructors/query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(queryParams),
      });
      
      if (!response.ok) {
        throw new Error(`Erro ao buscar instrutores: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Erro ao buscar instrutores:', error);
      throw error;
    }
  };
  
  // Usar React Query para buscar e cachear os dados
  const query = useQuery({
    queryKey,
    queryFn: fetchInstructors,
    staleTime: 1000 * 60 * 5, // 5 minutos
    // Manter dados anteriores durante refetch para melhor UX (React Query v5)
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false // Evitar refetches ao focar na janela durante buscas
  });

  // Retornar explicitamente todos os estados e métodos necessários
  return {
    data: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    isFetching: query.isFetching,
    refetch: query.refetch
  };
} 