import { Suspense } from 'react';
import { Metadata } from 'next';
import { getClassGroupEnrollments, getClassGroupEnrollmentsStats, getClassGroupById } from '../../actions/class-group';
import { 
  StudentFilters,
  StudentLoadingSkeleton,
  StudentListHeader
} from '@/components/students';
import { ClassGroupStudentsClient } from './components/ClassGroupStudentsClient';
import type { StudentStats } from '@/components/students/types';

export const metadata: Metadata = {
  title: 'Alunos da Turma',
  description: 'Visualize e gerencie os alunos matriculados em uma turma específica',
};

interface PageProps {
  params: Promise<{
    groupId: string;
  }>;
  searchParams: Promise<{
    status?: string;
    search?: string;
    sort?: string;
    order?: string;
    page?: string;
    limit?: string;
  }>;
}

async function ClassGroupStudents({ 
  groupId, 
  searchParams 
}: { 
  groupId: string; 
  searchParams: any;
}) {
  const [enrollmentsResult, statsResult, classGroupResult] = await Promise.all([
    getClassGroupEnrollments(groupId, searchParams),
    getClassGroupEnrollmentsStats(groupId),
    getClassGroupById(groupId)
  ]);

  const enrollments = enrollmentsResult.success && 'data' in enrollmentsResult && enrollmentsResult.data 
    ? enrollmentsResult.data 
    : [];

  // Get real statistics (without search filters)
  const stats: StudentStats = statsResult.success && 'data' in statsResult && statsResult.data
    ? statsResult.data
    : {
        total: 0,
        active: 0,
        inactive: 0,
        suspended: 0,
        completed: 0,
        paused: 0,
      };

  // Get class group data
  const classGroup = classGroupResult.success && 'data' in classGroupResult && classGroupResult.data
    ? classGroupResult.data
    : null;

  // Check if we have any filters applied
  const hasFilters = searchParams.status || searchParams.search;
  const hasStudents = enrollments.length > 0;

  // Map enrollments to the expected format
  const mappedStudents = enrollments.map((enrollment: any) => {
    const student = enrollment.students;
    const user = student?.users;
    const currentBelt = student?.current_belt;
    
    return {
      id: enrollment.id,
      user_id: user?.id || '',
      user: {
        id: user?.id || '',
        full_name: user?.full_name || user?.first_name || 'Nome não disponível',
        email: user?.email || '',
        phone: user?.phone,
        avatar_url: user?.avatar_url,
      },
      enrollment_date: enrollment.enrollment_date,
      status: enrollment.status,
      belt: enrollment.belt ? enrollment.belt.toLowerCase() : null, // Para compatibilidade
      current_belt: currentBelt, // Detalhes completos da faixa
      notes: enrollment.notes,
      class_group_id: enrollment.class_group_id || groupId,
      is_paused: enrollment.is_paused || false,
      pause_info: enrollment.pause_info || null,
    };
  });

  return (
    <main className="min-h-screen bg-background" role="main">
      <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 max-w-7xl">
        <div className="space-y-6 sm:space-y-8">
          {/* Header with class group info and enroll button */}
          {classGroup && (
            <StudentListHeader
              classGroup={{
                id: classGroup.id,
                name: classGroup.name,
                max_capacity: classGroup.max_capacity ?? undefined,
                description: classGroup.description ?? undefined
              }}
              stats={{
                total: stats.total,
                active: stats.active,
                inactive: stats.inactive,
                suspended: stats.suspended
              }}
            />
          )}

          {/* Show filters if we have students OR if there are active filters */}
          {(hasStudents || hasFilters) && (
            <aside aria-label="Controles de filtro e busca">
              <StudentFilters stats={{
                total: stats.total,
                active: stats.active,
                inactive: stats.inactive,
                suspended: stats.suspended,
                paused: stats.paused
              }} />
            </aside>
          )}

          {/* Main content area - delegated to Client Component */}
          <ClassGroupStudentsClient
            students={mappedStudents}
            classGroupId={groupId}
            classGroupName={classGroup?.name || 'Turma'}
            classGroupMaxCapacity={classGroup?.max_capacity ?? undefined}
            hasFilters={hasFilters}
            hasStudents={hasStudents}
          />
        </div>
      </div>
    </main>
  );
}

function LoadingSkeleton() {
  return <StudentLoadingSkeleton count={6} showFilters={true} showHeader={true} />;
}

export default async function ClassGroupStudentsPage({ params, searchParams }: PageProps) {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;

  return (
    <Suspense fallback={<LoadingSkeleton />}>
      <ClassGroupStudents groupId={resolvedParams.groupId} searchParams={resolvedSearchParams} />
    </Suspense>
  );
} 