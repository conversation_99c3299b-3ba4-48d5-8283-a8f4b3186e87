import { createAdminClient } from '@/services/supabase/server';
import { createClient } from '@/services/supabase/server';

export interface BeltLevelRequirements {
  tenant_id: string;
  belt_level_id: string;
  sessions?: number;
  hours?: number;
  days_in_rank?: number;
  days_attended?: number;
  skill_requirements: boolean;
  minimum_age?: number;
  promotion_fee?: number;
  created_at: string;
  updated_at: string;
}

export interface BeltLevelRequirementsInput {
  sessions?: number;
  hours?: number;
  days_in_rank?: number;
  days_attended?: number;
  skill_requirements?: boolean;
  minimum_age?: number;
  promotion_fee?: number;
}

export interface BeltLevelRequirementsWithBeltInfo extends BeltLevelRequirements {
  belt_label: string;
  belt_color: string;
  degree: number;
  sort_order: number;
}

/**
 * Busca os requisitos de múltiplos níveis de faixa para um tenant
 */
export async function getRankRequirements(
  tenantId: string,
  beltLevelIds: string[]
): Promise<BeltLevelRequirements[]> {
  if (beltLevelIds.length === 0) return [];

  // Filtrar e validar IDs antes de fazer a query
  const validBeltLevelIds = beltLevelIds.filter(id => {
    if (!id || typeof id !== 'string' || id.trim() === '') {
      console.warn('⚠️ ID de belt level inválido filtrado:', id);
      return false;
    }
    // Validação básica de UUID (36 caracteres com hífens)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      console.warn('⚠️ ID de belt level com formato inválido filtrado:', id);
      return false;
    }
    return true;
  });

  if (validBeltLevelIds.length === 0) {
    console.log('📝 Nenhum ID de belt level válido encontrado, retornando array vazio');
    return [];
  }

  // Usar adminClient para contornar RLS
  const supabase = await createAdminClient();

  const { data, error } = await supabase
    .from('tenant_belt_level_requirements')
    .select('*')
    .eq('tenant_id', tenantId)
    .in('belt_level_id', validBeltLevelIds);

  if (error) {
    console.error('❌ Erro ao buscar requisitos dos níveis:', error);
    throw new Error('Falha ao buscar requisitos dos níveis');
  }

  return data || [];
}

/**
 * Busca os requisitos de um nível específico
 */
export async function getSingleRankRequirements(
  tenantId: string,
  beltLevelId: string
): Promise<BeltLevelRequirements | null> {
  const supabase = await createAdminClient();

  const { data, error } = await supabase
    .from('tenant_belt_level_requirements')
    .select('*')
    .eq('tenant_id', tenantId)
    .eq('belt_level_id', beltLevelId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // Não encontrado - retorna null
      return null;
    }
    console.error('Erro ao buscar requisitos do nível:', error);
    throw new Error('Falha ao buscar requisitos do nível');
  }

  return data;
}

/**
 * Atualiza ou cria os requisitos de um nível de faixa
 */
export async function upsertRankRequirements(
  tenantId: string,
  beltLevelId: string,
  requirements: BeltLevelRequirementsInput
): Promise<{ success: boolean; error?: any }> {
  const supabase = await createAdminClient();

  const { error } = await supabase.rpc('upsert_tenant_belt_level_requirement', {
    p_tenant_id: tenantId,
    p_belt_level_id: beltLevelId,
    p_sessions: requirements.sessions ?? null,
    p_hours: requirements.hours ?? null,
    p_days_in_rank: requirements.days_in_rank ?? null,
    p_days_attended: requirements.days_attended ?? null,
    p_skill_requirements: requirements.skill_requirements ?? null,
    p_minimum_age: requirements.minimum_age ?? null,
    p_promotion_fee: requirements.promotion_fee ?? null,
  });

  if (error) {
    console.error('Erro ao atualizar requisitos do nível (RPC):', error);
    return { success: false, error };
  }

  return { success: true };
}

/**
 * Remove os requisitos de um nível de faixa
 */
export async function deleteRankRequirements(
  tenantId: string,
  beltLevelId: string
): Promise<{ success: boolean; error?: any }> {
  const supabase = await createAdminClient();

  const { error } = await supabase
    .from('tenant_belt_level_requirements')
    .delete()
    .eq('tenant_id', tenantId)
    .eq('belt_level_id', beltLevelId);

  if (error) {
    console.error('Erro ao deletar requisitos do nível:', error);
    return { success: false, error };
  }

  return { success: true };
} 

/**
 * Busca todos os requisitos de uma modalidade usando RPC
 */
export async function getModalityRequirements(
  tenantId: string,
  modalityId: string
): Promise<BeltLevelRequirementsWithBeltInfo[]> {
  const supabase = await createAdminClient();

  const { data, error } = await supabase.rpc('get_modality_requirements', {
    p_tenant_id: tenantId,
    p_modality_id: modalityId
  });

  if (error) {
    console.error('Erro ao buscar requisitos da modalidade via RPC:', error);
    throw new Error('Falha ao buscar requisitos da modalidade');
  }

  return data || [];
} 