import { forgotPasswordAction } from '@/services/auth/actions/index';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const result = await forgotPasswordAction(formData);
    
    if ('redirect' in result) {
      return NextResponse.json({ 
        success: true, 
        redirect: result.redirect 
      });
    }
    
    return NextResponse.json({ success: true });
    
  } catch (error) {
    console.error('Erro ao processar redefinição de senha:', error);
    return NextResponse.json(
      { 
        error: 'Falha ao processar a solicitação de redefinição de senha',
        details: error instanceof Error ? error.message : String(error)
      }, 
      { status: 500 }
    );
  }
} 