/**
 * Tipos relacionados a responsáveis e administração de contas de menores de idade
 */

export interface GuardianInfo {
  /** Nome completo do responsável legal */
  guardian_name: string | null;
  /** E-mail do responsável legal */
  guardian_email: string | null;
  /** Telefone do responsável legal */
  guardian_phone: string | null;
  /** Relação do responsável com o menor (pai, mãe, avô, etc.) */
  guardian_relationship: string | null;
  /** Documento de identificação do responsável (CPF/RG) */
  guardian_document: string | null;
}

export interface MinorAccountInfo {
  /** Indica se o usuário é menor de idade (< 18 anos) */
  is_minor: boolean;
  /** Indica se esta conta é de um responsável administrando conta de menor */
  is_guardian_account: boolean;
  /** ID do estudante que este responsável administra */
  managed_student_id: string | null;
}

export interface StudentMinorInfo {
  /** Indica se o estudante é menor de idade (< 18 anos) */
  is_minor: boolean;
  /** Indica se o estudante requer consentimento do responsável */
  requires_guardian_consent: boolean;
}

export interface UserWithGuardianInfo extends MinorAccountInfo, GuardianInfo {
  id: string;
  first_name: string | null;
  last_name: string | null;
  email: string;
  birth_date?: string | null;
}

export interface GuardianFormData {
  guardian_name: string;
  guardian_email: string;
  guardian_phone: string;
  guardian_relationship: string;
  guardian_document: string;
}

export interface GuardianValidationErrors {
  guardian_name?: string[];
  guardian_email?: string[];
  guardian_phone?: string[];
  guardian_relationship?: string[];
  guardian_document?: string[];
  _form?: string;
}

export type GuardianRelationship = 
  | 'pai'
  | 'mae'
  | 'avo'
  | 'avo_materna'
  | 'avo_paterna'
  | 'tio'
  | 'tia'
  | 'responsavel_legal'
  | 'outro';

export const GUARDIAN_RELATIONSHIPS: Record<GuardianRelationship, string> = {
  pai: 'Pai',
  mae: 'Mãe',
  avo: 'Avô/Avó',
  avo_materna: 'Avô/Avó Materna',
  avo_paterna: 'Avô/Avó Paterna',
  tio: 'Tio',
  tia: 'Tia',
  responsavel_legal: 'Responsável Legal',
  outro: 'Outro'
};

export interface MinorAccountDisplayInfo {
  isMinor: boolean;
  hasGuardian: boolean;
  guardianName?: string;
  guardianRelationship?: string;
  requiresGuardianConsent: boolean;
}

/**
 * Calcula se uma pessoa é menor de idade com base na data de nascimento
 */
export function calculateIsMinor(birthDate: string | Date | null): boolean {
  if (!birthDate) return false;
  
  const birth = typeof birthDate === 'string' ? new Date(birthDate) : birthDate;
  const today = new Date();
  const age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    return age - 1 < 18;
  }
  
  return age < 18;
}

/**
 * Valida se os dados do responsável estão completos
 */
export function validateGuardianData(data: Partial<GuardianFormData>): boolean {
  return !!(
    data.guardian_name?.trim() &&
    data.guardian_email?.trim() &&
    data.guardian_phone?.trim() &&
    data.guardian_relationship?.trim() &&
    data.guardian_document?.trim()
  );
} 