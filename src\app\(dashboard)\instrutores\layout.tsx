import { Metadata } from 'next';
import { requireAuth } from "@/services/auth/server";

export const metadata: Metadata = {
  title: 'Instrutores | ApexSAAS',
  description: 'Gerenciamento de instrutores no sistema ApexSAAS',
};

export default async function InstrutoresLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Verificar se o usuário está autenticado (middleware já verifica permissões)
  await requireAuth();

  return (
    <div className="py-6">
      {children}
    </div>
  );
} 