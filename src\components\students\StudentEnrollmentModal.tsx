'use client';

import { useState, useC<PERSON>back, useMemo } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { UserAvatar } from '@/components/ui/user-avatar';
import { BeltDisplay } from '@/components/belt';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { 
  Users, 
  Loader2, 
  UserPlus, 
  AlertCircle, 
  RefreshCw,
  CheckCircle,
  UserX,
} from 'lucide-react';
import { useStudentsForEnrollment } from '@/hooks/alunos/use-students-for-enrollment';
import type { StudentForEnrollment } from '@/app/(dashboard)/turmas/[groupId]/alunos/actions/student-enrollment-actions';
import { StudentEnrollmentFilters } from './StudentEnrollmentFilters';
import { bulkEnrollStudents } from '@/src/app/(dashboard)/turmas/actions/class-group'; 
import { useQueryClient } from '@tanstack/react-query';
import { CACHE_KEYS } from '@/constants/cache-keys';
import { toast } from 'sonner';

interface StudentEnrollmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  classGroup: {
    id: string;
    name: string;
    max_capacity?: number;
  };
  onEnrollmentSuccess: () => void;
}

type EnrollmentStep = 'selecting' | 'enrolling' | 'success' | 'error';

export function StudentEnrollmentModal({
  isOpen,
  onClose,
  classGroup,
  onEnrollmentSuccess
}: StudentEnrollmentModalProps) {
  const [selectedStudents, setSelectedStudents] = useState<Set<string>>(new Set());
  const [enrollmentStep, setEnrollmentStep] = useState<EnrollmentStep>('selecting');
  const [enrollmentError, setEnrollmentError] = useState<string | null>(null);
  const [enrollmentResult, setEnrollmentResult] = useState<{
    successCount: number;
    totalCount: number;
  } | null>(null);

  const queryClient = useQueryClient();

  const {
    students,
    availableStudents,
    stats,
    filters,
    isLoading,
    isFetching,
    error,
    setFilters: updateFilters,
    resetFilters: clearFilters,
    hasMore: hasNextPage,
    nextPage,
    refetch: refetchStudents,
    resetData,
    capacityInfo
  } = useStudentsForEnrollment(classGroup.id, {});

  // Usar diretamente capacityInfo do hook que já vem calculado corretamente
  const remainingSpots = capacityInfo?.available_spots;

  // Calcular se pode selecionar mais estudantes
  const canSelectMore = useMemo(() => {
    if (!capacityInfo) return true;
    
    const currentSelections = selectedStudents.size;
    const maxSelectionsAllowed = remainingSpots || Infinity;
    
    return currentSelections < maxSelectionsAllowed;
  }, [selectedStudents.size, remainingSpots, capacityInfo]);

  // Resetar estado quando modal fecha
  const handleClose = useCallback(() => {
    setSelectedStudents(new Set());
    setEnrollmentStep('selecting');
    setEnrollmentError(null);
    setEnrollmentResult(null);
    
    // Limpar apenas os filtros, mas manter os dados em cache para reabertura rápida
    clearFilters();
    
    // Não resetar os dados automaticamente - deixar o cache do React Query gerenciar
    // resetData(); // Removido para manter dados em cache
    
    onClose();
  }, [clearFilters, onClose]);

  // Lidar com seleção individual de estudante
  const handleStudentToggle = useCallback((studentId: string) => {
    setSelectedStudents(prev => {
      const newSet = new Set(prev);
      if (newSet.has(studentId)) {
        newSet.delete(studentId);
      } else if (canSelectMore) {
        newSet.add(studentId);
      } else {
        toast.warning(`Você pode selecionar no máximo ${remainingSpots} estudante${remainingSpots !== 1 ? 's' : ''}`);
        return prev;
      }
      return newSet;
    });
  }, [canSelectMore, remainingSpots]);

  // Selecionar todos os estudantes disponíveis (limitado pela capacidade)
  const handleSelectAll = useCallback(() => {
    const availableIds = availableStudents.map(s => s.id);
    const maxSelections = remainingSpots || availableIds.length;
    const idsToSelect = availableIds.slice(0, maxSelections);
    
    setSelectedStudents(new Set(idsToSelect));
    
    if (remainingSpots && availableIds.length > remainingSpots) {
      toast.info(`Selecionados ${idsToSelect.length} de ${availableIds.length} estudantes (limite da capacidade da turma)`);
    }
  }, [availableStudents, remainingSpots]);

  // Desselecionar todos
  const handleDeselectAll = useCallback(() => {
    setSelectedStudents(new Set());
  }, []);

  // Realizar matrícula
  const handleEnroll = useCallback(async () => {
    if (selectedStudents.size === 0) return;

    setEnrollmentStep('enrolling');
    setEnrollmentError(null);

    try {
      const result = await bulkEnrollStudents({
        class_group_id: classGroup.id,
        student_ids: Array.from(selectedStudents)
      });

      if (result.success) {
        setEnrollmentResult({
          successCount: selectedStudents.size,
          totalCount: selectedStudents.size
        });
        setEnrollmentStep('success');
        
        // Invalidar caches relacionados
        await queryClient.invalidateQueries({
          queryKey: CACHE_KEYS.ENROLLMENT.CLASS_GROUP_ENROLLMENTS(classGroup.id)
        });
        await queryClient.invalidateQueries({
          queryKey: CACHE_KEYS.ENROLLMENT.CAPACITY_CHECK(classGroup.id)
        });
        await queryClient.invalidateQueries({
          queryKey: CACHE_KEYS.ENROLLMENT.AVAILABLE_STUDENTS(classGroup.id, {})
        });

        // Notificar sucesso
        onEnrollmentSuccess();
        
      } else {
        const errorMessage = (result.errors && typeof result.errors === 'object' && '_form' in result.errors) 
          ? result.errors._form 
          : 'Erro ao matricular estudantes';
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('Erro na matrícula:', error);
      setEnrollmentError(error instanceof Error ? error.message : 'Erro inesperado');
      setEnrollmentStep('error');
    }
  }, [selectedStudents, classGroup.id, queryClient, onEnrollmentSuccess]);

  // Renderizar card de estudante
  const renderStudentCard = useCallback((student: StudentForEnrollment) => {
    const isSelected = selectedStudents.has(student.id);
    const isDisabled = student.is_already_enrolled || (!canSelectMore && !isSelected);

    return (
      <div
        key={student.id}
        className={`
          relative p-4 border rounded-lg transition-all duration-200
          ${student.is_already_enrolled 
            ? 'border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/20' 
            : isSelected
              ? 'border-primary bg-primary/5 ring-2 ring-primary/20'
              : isDisabled
                ? 'border-border bg-muted/50 opacity-60'
                : 'border-border bg-card hover:bg-accent/50 cursor-pointer'
          }
        `}
        onClick={() => !isDisabled && !student.is_already_enrolled && handleStudentToggle(student.id)}
      >
        <div className="flex items-center gap-4">
          {/* Checkbox para seleção */}
          {!student.is_already_enrolled && (
            <Checkbox
              checked={isSelected}
              disabled={isDisabled}
              onChange={() => handleStudentToggle(student.id)}
              className="flex-shrink-0"
            />
          )}
          
          {/* Avatar e informações */}
          <UserAvatar 
            src={student.user.avatar_url} 
            name={student.user.full_name}
            size="md"
            className="flex-shrink-0"
          />
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-medium text-foreground truncate">
                {student.user.full_name}
              </h4>
              {student.is_already_enrolled && (
                <Badge variant="secondary" className="text-xs bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200">
                  Já matriculado
                </Badge>
              )}
            </div>
            
            <p className="text-sm text-muted-foreground truncate mb-2">
              {student.user.email}
            </p>
            
            {student.current_belt && (
              <div className="flex items-center">
                <BeltDisplay 
                  belt={student.current_belt.belt_color as any}
                  stripes={Math.max(0, (student.current_belt.degree || 1) - 1)}
                  size="sm"
                  showTranslation
                />
              </div>
            )}
          </div>
          
          {/* Ícone de status */}
          {student.is_already_enrolled ? (
            <UserX className="h-5 w-5 text-amber-500 flex-shrink-0" />
          ) : isSelected ? (
            <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
          ) : (
            <div className="h-5 w-5 flex-shrink-0" />
          )}
        </div>
      </div>
    );
  }, [selectedStudents, canSelectMore, handleStudentToggle]);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-4xl h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            <DialogTitle>Matricular Estudantes - {classGroup.name}</DialogTitle>
          </div>
          {enrollmentStep === 'selecting' && (
            <p className="text-sm text-muted-foreground">
              Selecione os estudantes que deseja matricular na turma
            </p>
          )}
        </DialogHeader>

        <div className="flex-1 flex flex-col overflow-hidden">
          {enrollmentStep === 'enrolling' && (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
              <h3 className="font-medium text-foreground mb-2">Matriculando estudantes...</h3>
              <p className="text-muted-foreground">
                Processando {selectedStudents.size} matrícula{selectedStudents.size !== 1 ? 's' : ''}
              </p>
            </div>
          )}

          {enrollmentStep === 'success' && (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
              <h3 className="font-medium text-foreground mb-2">Matrícula realizada com sucesso!</h3>
              <p className="text-muted-foreground mb-6">
                {enrollmentResult?.successCount} estudante{enrollmentResult?.successCount !== 1 ? 's' : ''} matriculado{enrollmentResult?.successCount !== 1 ? 's' : ''} na turma
              </p>
              <Button onClick={handleClose}>
                Fechar
              </Button>
            </div>
          )}

          {enrollmentStep === 'error' && (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <AlertCircle className="h-12 w-12 text-destructive mb-4" />
              <h3 className="font-medium text-foreground mb-2">Erro na matrícula</h3>
              <p className="text-muted-foreground mb-6">{enrollmentError}</p>
              <div className="flex gap-2">
                <Button onClick={() => setEnrollmentStep('selecting')} variant="outline">
                  Voltar
                </Button>
                <Button onClick={handleEnroll}>
                  Tentar novamente
                </Button>
              </div>
            </div>
          )}

          {enrollmentStep === 'selecting' && (
            <>
              {error ? (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <AlertCircle className="h-8 w-8 text-destructive mb-4" />
                  <p className="text-destructive mb-4">{error instanceof Error ? error.message : 'Erro inesperado'}</p>
                  <Button onClick={() => window.location.reload()} variant="outline" size="sm">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Tentar novamente
                  </Button>
                </div>
              ) : (
                <>
                  {/* Seção fixa: Filtros e informações */}
                  <div className="flex-shrink-0 space-y-4 pb-4">
                    {/* Filtros */}
                    <div className="relative">
                      <StudentEnrollmentFilters
                        filters={filters}
                        onFiltersChange={updateFilters}
                        onClearFilters={clearFilters}
                        stats={stats}
                        isLoading={isFetching}
                      />
                      {isFetching && !isLoading && (
                        <div className="absolute top-2 right-2">
                          <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                        </div>
                      )}
                    </div>

                    <Separator />

                    {/* Informações de capacidade */}
                    {capacityInfo && (
                      <div className="bg-muted/50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium">Capacidade da turma</span>
                          <span className="text-sm text-muted-foreground">
                            {capacityInfo.current_enrollment_count} / {capacityInfo.max_capacity || '∞'}
                          </span>
                        </div>
                        {capacityInfo.max_capacity && (
                          <Progress 
                            value={(capacityInfo.current_enrollment_count / capacityInfo.max_capacity) * 100} 
                            className="h-2"
                          />
                        )}
                        {selectedStudents.size > 0 && (
                          <p className="text-xs text-muted-foreground mt-2">
                            Selecionados: {selectedStudents.size} 
                            {remainingSpots && ` (restam ${remainingSpots - selectedStudents.size} vaga${(remainingSpots - selectedStudents.size) !== 1 ? 's' : ''})`}
                          </p>
                        )}
                      </div>
                    )}

                    {/* Ações de seleção */}
                    {!isLoading && availableStudents.length > 0 && (
                      <div className="flex items-center justify-between">
                        <div className="flex gap-2">
                          <Button
                            onClick={handleSelectAll}
                            variant="outline"
                            size="sm"
                            disabled={!canSelectMore && selectedStudents.size === 0}
                          >
                            Selecionar {remainingSpots && remainingSpots < availableStudents.length 
                              ? `${remainingSpots}` 
                              : 'todos'}
                          </Button>
                          {selectedStudents.size > 0 && (
                            <Button
                              onClick={handleDeselectAll}
                              variant="outline"
                              size="sm"
                            >
                              Desselecionar todos
                            </Button>
                          )}
                        </div>
                        
                        {selectedStudents.size > 0 && (
                          <Badge variant="secondary">
                            {selectedStudents.size} selecionado{selectedStudents.size !== 1 ? 's' : ''}
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Seção rolável: Lista de estudantes */}
                  <div className="flex-1 overflow-hidden">
                    {isLoading ? (
                      <div className="flex justify-center py-12">
                        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                      </div>
                    ) : availableStudents.length === 0 ? (
                      <div className="flex flex-col items-center justify-center py-12 text-center">
                        <Users className="h-8 w-8 text-muted-foreground mb-4" />
                        <h3 className="font-medium text-foreground mb-2">
                          Nenhum estudante encontrado
                        </h3>
                        <p className="text-muted-foreground mb-4">
                          {(filters.search || filters.belt_color || filters.status !== 'active' || filters.branch_id) 
                            ? 'Nenhum estudante corresponde aos filtros aplicados.' 
                            : 'Todos os estudantes já estão matriculados nesta turma.'
                          }
                        </p>
                        {(filters.search || filters.belt_color || filters.status !== 'active' || filters.branch_id) && (
                          <Button onClick={clearFilters} variant="outline" size="sm">
                            Limpar filtros
                          </Button>
                        )}
                      </div>
                    ) : (
                      <ScrollArea className="h-full">
                        <div className="space-y-2 p-1">
                          {availableStudents.map(renderStudentCard)}
                          
                          {hasNextPage && !isLoading && (
                            <div className="pt-4">
                              <Button
                                onClick={() => nextPage()}
                                variant="outline"
                                className="w-full"
                                disabled={isFetching}
                              >
                                {isFetching ? (
                                  <>
                                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                    Carregando...
                                  </>
                                ) : (
                                  'Carregar mais estudantes'
                                )}
                              </Button>
                            </div>
                          )}
                        </div>
                      </ScrollArea>
                    )}
                  </div>
                </>
              )}
            </>
          )}
        </div>

        {/* Footer com ações */}
        {enrollmentStep === 'selecting' && selectedStudents.size > 0 && (
          <div className="flex items-center justify-between pt-4 border-t flex-shrink-0">
            <div className="text-sm text-muted-foreground">
              {selectedStudents.size} estudante{selectedStudents.size !== 1 ? 's' : ''} selecionado{selectedStudents.size !== 1 ? 's' : ''}
            </div>
            <div className="flex gap-2">
              <Button onClick={handleClose} variant="outline">
                Cancelar
              </Button>
              <Button 
                onClick={handleEnroll}
                disabled={selectedStudents.size === 0}
                variant="secondary"
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Matricular {selectedStudents.size > 1 ? `${selectedStudents.size} estudantes` : 'estudante'}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
} 