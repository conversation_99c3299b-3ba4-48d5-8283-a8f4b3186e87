import { Suspense } from 'react';
import { getClasses } from '../aulas/actions';
import { getAttendanceStatsWithTrends } from './actions/attendance-actions';
import { AttendanceTabNavigation } from './components/AttendanceTabNavigation';
import { QuickActionBar } from './components/QuickActionBar';
import { StatsSection } from './components/StatsSection';
import { ClassesSection } from './components/ClassesSection';
import { AttendanceFiltersProvider } from './components/AttendanceFiltersContext';
import { AttendanceListSection } from './components/AttendanceListSection';
import { checkAttendancePermission, applyRoleBasedFilters } from '@/services/permissions/utils/role-verification';

// Função utilitária para converter erros em strings legíveis
function formatErrorMessage(errors: any): string {
  if (typeof errors === 'string') {
    return errors;
  }
  
  if (errors?._form) {
    return errors._form;
  }
  
  // Se for um objeto de erros do Zod, pegar a primeira mensagem de erro
  if (typeof errors === 'object' && errors !== null) {
    const firstErrorKey = Object.keys(errors)[0];
    if (firstErrorKey && errors[firstErrorKey]) {
      const errorValue = errors[firstErrorKey];
      if (typeof errorValue === 'string') {
        return errorValue;
      }
      if (errorValue?._errors && Array.isArray(errorValue._errors) && errorValue._errors.length > 0) {
        return errorValue._errors[0];
      }
      if (errorValue?.message) {
        return errorValue.message;
      }
    }
  }
  
  // Tentar converter o objeto para JSON como fallback
  try {
    return `Erro: ${JSON.stringify(errors, null, 2)}`;
  } catch (jsonError) {
    return `Erro desconhecido - não foi possível formatar detalhes: ${String(errors)}`;
  }
}

// Componente de loading para Suspense
function AttendanceLoading() {
  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 rounded-lg p-4 shadow-sm animate-pulse">
        <div className="h-10 bg-slate-200 dark:bg-gray-700 rounded w-full"></div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 rounded-lg p-6 shadow-sm animate-pulse">
            <div className="h-20 bg-slate-200 dark:bg-gray-700 rounded"></div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Componente principal de conteúdo
async function AttendanceContent() {
  // Verificar permissões antes de carregar dados
  const userContext = await checkAttendancePermission();
  
  // Buscar aulas do dia atual
  const today = new Date();
  const startOfDay = new Date(today);
  startOfDay.setHours(0, 0, 0, 0);
  
  const endOfDay = new Date(today);
  endOfDay.setHours(23, 59, 59, 999);

  // Aplicar filtros baseados na role do usuário
  const baseFilters = {
    startDate: startOfDay.toISOString(),
    endDate: endOfDay.toISOString(),
    page: 1,
    pageSize: 50
  };

  const filteredClassesParams = await applyRoleBasedFilters(userContext, baseFilters, 'classes');
  
  const classesResult = await getClasses(filteredClassesParams);
  const statsResult = await getAttendanceStatsWithTrends();

  if (!classesResult.success) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 dark:text-red-400">Erro ao carregar aulas: {formatErrorMessage(classesResult.errors)}</p>
      </div>
    );
  }

  if (!statsResult.success) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 dark:text-red-400">Erro ao carregar estatísticas: {formatErrorMessage(statsResult.errors)}</p>
      </div>
    );
  }

  const classes = classesResult.data?.data || [];
  const stats = statsResult.data || {
    totalClasses: 0,
    ongoingClasses: 0,
    upcomingClasses: 0,
    completedClasses: 0,
    totalAttendances: 0,
    uniqueStudents: 0,
    trends: {
      total: { value: 0, isPositive: true, data: [] },
      ongoing: { value: 0, isPositive: true, data: [] },
      upcoming: { value: 0, isPositive: true, data: [] },
      completed: { value: 0, isPositive: true, data: [] }
    },
    dailyData: []
  };

  const now = new Date();
  
  // Organizar aulas por status baseado na nova lógica unificada
  const transformClass = (classItem: any) => {
    const isOpenClass = !classItem.class_group_id;
    const effectiveStatus = determineClassStatus(classItem, now);
    
    return {
      id: classItem.id,
      title: classItem.name,
      instructor: {
        name: classItem.instructor?.full_name || classItem.instructor?.first_name || 'Instrutor',
        avatar: classItem.instructor?.avatar_url,
      },
      time: new Date(classItem.start_time).toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
      }),
      duration: Math.round((new Date(classItem.end_time).getTime() - new Date(classItem.start_time).getTime()) / (1000 * 60)),
      enrolledCount: classItem._count?.attendance || 0,
      maxCapacity: isOpenClass ? null : classItem.max_capacity,
      location: classItem.branch?.name || 'Academia',
      status: effectiveStatus,
      startDate: new Date(classItem.start_time).toLocaleDateString('pt-BR'),
      description: classItem.description,
      isOpenClass,
      // Propriedades adicionais para compatibilidade
      start_time: classItem.start_time,
      end_time: classItem.end_time,
      attendance_count: classItem._count?.attendance || 0,
    };
  };

  // Função para determinar o status real da aula
  const determineClassStatus = (classItem: any, currentTime: Date) => {
    const start = new Date(classItem.start_time);
    const end = new Date(classItem.end_time);
    
    if (['cancelled', 'rescheduled'].includes(classItem.status)) {
      return classItem.status;
    }
    
    if (classItem.status === 'completed') {
      return 'completed';
    }
    
    if (currentTime < start) {
      return 'scheduled';
    }
    
    if (currentTime >= start && currentTime <= end) {
      return 'ongoing';
    }
    
    if (currentTime > end) {
      return 'completed';
    }
    
    return classItem.status || 'scheduled';
  };

  const transformedClasses = classes.map(transformClass);
  
  const classesGrouped = {
    ongoing: transformedClasses.filter(c => c.status === 'ongoing'),
    scheduled: transformedClasses.filter(c => c.status === 'scheduled'),
    completed: transformedClasses.filter(c => c.status === 'completed'),
  };

  return (
    <AttendanceTabNavigation>
      <div className="space-y-6">
        {/* Barra de Ações Rápidas */}
        <QuickActionBar />
        
        {/* Seção de Estatísticas */}
        <StatsSection 
          totalClasses={stats.totalClasses}
          ongoingClasses={stats.ongoingClasses}
          upcomingClasses={stats.upcomingClasses}
          completedClasses={stats.completedClasses}
          trends={stats.trends}
        />
        
        {/* Nova Listagem de Aulas */}
        <AttendanceListSection />
        
        {/* Seção de Aulas (Cards por Status) */}
        {/* <ClassesSection
          ongoingClasses={classesGrouped.ongoing}
          upcomingClasses={classesGrouped.scheduled}
          completedClasses={classesGrouped.completed}
        /> */}
      </div>
    </AttendanceTabNavigation>
  );
}

export default function AttendancePage() {
  return (
    <AttendanceFiltersProvider>
      <div className="space-y-6">
        <Suspense fallback={<AttendanceLoading />}>
          <AttendanceContent />
        </Suspense>
      </div>
    </AttendanceFiltersProvider>
  );
}
