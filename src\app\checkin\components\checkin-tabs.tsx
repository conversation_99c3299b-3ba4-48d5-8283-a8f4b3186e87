'use client';

interface CheckInTabsProps {
  activeTab: 'code' | 'name' | 'qr';
  onTabChange: (tab: 'code' | 'name' | 'qr') => void;
}

interface TabConfig {
  key: 'code' | 'name' | 'qr';
  label: string;
}

export function CheckInTabs({ activeTab, onTabChange }: CheckInTabsProps) {
  const tabs: TabConfig[] = [
    { key: 'code', label: 'Código' },
    { key: 'name', label: 'Nome' },
    // { key: 'qr', label: 'QR Code' },
  ];

  return (
    <div className="flex justify-center mb-8">
      <div className="flex space-x-1 bg-black/10 dark:bg-white/10 rounded-full p-1">
        {tabs.map(tab => (
          <button
            key={tab.key}
            onClick={() => onTabChange(tab.key)}
            className={`
              px-6 py-2 rounded-full text-sm font-medium transition-all relative
              ${activeTab === tab.key 
                ? 'text-white/90 dark:text-gray-100' 
                : 'text-white/60 dark:text-gray-400 hover:text-white/80 dark:hover:text-gray-200'
              }
            `}
          >
            {tab.label}
            <div 
              className={`
                absolute bottom-0 left-4 right-4 h-0.5 bg-emerald-400 dark:bg-emerald-300 rounded-full transition-opacity
                ${activeTab === tab.key ? 'opacity-100' : 'opacity-0'}
              `} 
            />
          </button>
        ))}
      </div>
    </div>
  );
} 