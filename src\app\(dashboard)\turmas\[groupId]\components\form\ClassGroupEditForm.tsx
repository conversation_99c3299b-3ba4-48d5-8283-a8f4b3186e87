'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Save, 
  Loader2, 
  Alert<PERSON>ircle, 
  Settings,
  Info
} from 'lucide-react';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { useClassGroup } from '../ClassGroupContext';
import { useRouter } from 'next/navigation';

// Importar componentes modulares
import BasicInfoSection from './sections/BasicInfoSection';
import CapacitySection from './sections/CapacitySection';
import LocationSection from './sections/LocationSection';
import RequirementsSection from './sections/RequirementsSection';
import ScheduleSection from './sections/ScheduleSection';
// import RecurrenceConfig from './sections/RecurrenceConfig';
import PreviewSection from './sections/PreviewSection';

// Importar tipos, validação e constantes
import { ClassGroupEditFormProps } from './types';
import { tabs } from './constants';

// Importar o novo hook
import { useEditClassGroupForm } from './hooks/useEditClassGroupForm';

export default function ClassGroupEditForm({ 
  initialData, 
  instructors, 
  branches, 
  onSuccess 
}: ClassGroupEditFormProps) {
  const { state, refreshAllData, getLastRefreshText, markAsRecentlyUpdated } = useClassGroup();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('basic');

  // Usar o novo hook para gerenciar o formulário
  const {
    form,
    isSubmitting,
    previewMode,
    setPreviewMode,
    hasChanges,
    userInteracted,
    checkUserInteraction,
    manualDirtyFields,
    flattenDirtyFields,
    watchedValues,
    onSubmit,
    reset
  } = useEditClassGroupForm({
    initialData,
    onSuccess: () => {
      refreshAllData().then(() => {
        markAsRecentlyUpdated();
        if (onSuccess) {
          onSuccess();
        }
      });
    }
  });

  const {
    register,
    formState: { errors, isValid, isValidating },
    setValue,
    setError,
    clearErrors,
    trigger,
    getValues
  } = form;

  // Hook personalizado já cuida da inicialização dos dados
  // Removido useEffect conflitante que sobrescrevia a lógica correta

  // Monitorar mudanças no formulário para atualizar userInteracted
  useEffect(() => {
    checkUserInteraction();
  }, [checkUserInteraction]);

  // Função para debug de validação (apenas em desenvolvimento)
  const handleForceValidation = async () => {
    const currentValues = getValues();
    console.log('=== DEBUG VALIDAÇÃO ===');
    console.log('Valores atuais:', currentValues);
    console.log('Erros antes da validação:', errors);
    
    const result = await trigger();
    console.log('Resultado da validação:', result);
    console.log('Erros após validação:', errors);
    console.log('isValid:', isValid);
    console.log('hasChanges:', hasChanges);
    console.log('======================');
    
    if (!result) {
      toast.error('Formulário inválido - verifique os campos');
    } else {
      toast.success('Formulário válido!');
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* Header com ações */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Configurações da Turma
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Atualize as informações da turma "{initialData.name}"
              </p>
            </div>
            <div className="flex items-center gap-2">
              {process.env.NODE_ENV === 'development' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleForceValidation}
                >
                  Debug Validação
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPreviewMode(!previewMode)}
                disabled={!hasChanges}
              >
                {previewMode ? 'Editar' : 'Preview'}
              </Button>
              <Button
                type="submit"
                form="class-group-edit-form"
                disabled={!hasChanges || (!isValid && Object.keys(errors).length > 0) || isSubmitting || isValidating}
                size="sm"
                className="min-w-[100px]"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Salvando...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Salvar
                  </>
                )}
              </Button>
            </div>
          </div>
          
          {hasChanges && (
            <Alert className="mt-4">
              <Info className="h-4 w-4" />
              <AlertDescription>
                Você tem alterações não salvas. Clique em "Salvar" para confirmar as mudanças.
              </AlertDescription>
            </Alert>
          )}

          {/* Debug info em desenvolvimento */}
          {process.env.NODE_ENV === 'development' && (
            <Alert className="mt-4 bg-amber-50 border-amber-200 dark:bg-amber-950/50 dark:border-amber-800 dark:text-amber-200">
              <Info className="h-4 w-4" />
              <AlertDescription className="text-xs">
                <strong>Debug:</strong> isValid={String(isValid)}, hasChanges={String(hasChanges)}, 
                errors={Object.keys(errors).length}, dirtyFields={Object.keys(form.formState.dirtyFields).length}, 
                isValidating={String(isValidating)}, userInteracted={String(userInteracted)}
                <br />
                <strong>Campos alterados (RHF):</strong> {Object.keys(form.formState.dirtyFields).join(', ') || 'nenhum'}
                <br />
                <strong>Campos alterados (manual):</strong> {Object.keys(manualDirtyFields).join(', ') || 'nenhum'}
                <br />
                <strong>Erros:</strong> {Object.entries(errors).map(([k, v]) => `${k}: ${v?.message || 'unknown'}`).join('; ') || 'nenhum'}
                <br />
                <strong>Valores atuais:</strong> min_belt={watchedValues.min_belt_level}, max_belt={watchedValues.max_belt_level}, 
                min_age={watchedValues.min_age}, max_age={watchedValues.max_age}, 
                unlimited_capacity={String(watchedValues.unlimited_capacity)}, 
                max_capacity={watchedValues.max_capacity === null ? 'null' : watchedValues.max_capacity}
                <br />
                <strong>Valores iniciais:</strong> max_capacity_inicial={initialData.max_capacity === null ? 'null' : initialData.max_capacity},
                unlimited_capacity_inicial={initialData.max_capacity === null ? 'true' : 'false'}
              </AlertDescription>
            </Alert>
          )}
          
          {state.error && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {state.error}
              </AlertDescription>
            </Alert>
          )}
          
          {state.lastRefresh && (
            <div className="mt-4 text-xs text-muted-foreground">
              Última atualização: {getLastRefreshText()}
            </div>
          )}
        </CardHeader>
      </Card>

      {/* Formulário Principal com Abas */}
      <AnimatePresence mode="wait">
        {!previewMode && (
          <motion.div
            key="form"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="grid w-full grid-cols-3 md:grid-cols-6 bg-muted/50 dark:bg-muted/30 border dark:border-border/50">
                {tabs.map((tab) => (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    className="flex flex-col gap-1 p-3 h-auto text-muted-foreground hover:text-foreground transition-colors data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm dark:data-[state=active]:bg-card dark:data-[state=active]:text-foreground dark:text-muted-foreground dark:hover:text-foreground"
                  >
                    <tab.icon className="h-4 w-4" />
                    <span className="text-xs hidden md:block">{tab.shortLabel}</span>
                  </TabsTrigger>
                ))}
              </TabsList>

              <form id="class-group-edit-form" onSubmit={onSubmit} className="space-y-6">
                <TabsContent value="basic">
                  <BasicInfoSection
                    register={register}
                    errors={errors}
                    setValue={setValue}
                    watchedValues={watchedValues}
                  />
                </TabsContent>

                <TabsContent value="capacity">
                  <CapacitySection
                    register={register}
                    errors={errors}
                    setValue={setValue}
                    watchedValues={watchedValues}
                    checkUserInteraction={checkUserInteraction}
                  />
                </TabsContent>

                <TabsContent value="location">
                  <LocationSection
                    register={register}
                    errors={errors}
                    setValue={setValue}
                    watchedValues={watchedValues}
                    instructors={instructors}
                    branches={branches}
                  />
                </TabsContent>

                <TabsContent value="requirements">
                  <RequirementsSection
                    register={register}
                    errors={errors}
                    setValue={setValue}
                    setError={setError}
                    clearErrors={clearErrors}
                    watchedValues={watchedValues}
                  />
                </TabsContent>

                <TabsContent value="schedule">
                  <ScheduleSection
                    errors={errors}
                    setValue={setValue}
                    watchedValues={watchedValues}
                    initialData={initialData}
                  />
                </TabsContent>

                {/* <TabsContent value="recurrence">
                  <RecurrenceConfig
                    errors={errors}
                    setValue={setValue}
                    setError={setError}
                    clearErrors={clearErrors}
                    watchedValues={watchedValues}
                  />
                </TabsContent> */}
              </form>
            </Tabs>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Modo Preview */}
      <AnimatePresence>
        {previewMode && hasChanges && (
          <PreviewSection
            initialData={initialData}
            watchedValues={watchedValues}
            dirtyFields={flattenDirtyFields(manualDirtyFields)}
            instructors={instructors}
            branches={branches}
          />
        )}
      </AnimatePresence>
    </motion.div>
  );
}