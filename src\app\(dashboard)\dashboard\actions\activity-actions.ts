'use server';

import { createAdminClient } from "@/services/supabase/server";
import { ActivityItem } from "../types";

interface PaymentActivity {
  id: string;
  paid_at: string;
  amount: number;
  student_id: string;
}

interface GraduationActivity {
  id: string;
  awarded_at: string;
  student_id: string;
  belt_level: {
    id: string;
    belt_color: string;
    degree: number;
    label: string | null;
    stripe_color: string | null;
    show_center_line: boolean | null;
    center_line_color: string | null;
    sort_order: number | null;
  } | null;
}

interface AttendanceActivity {
  id: string;
  checked_in_at: string;
  student_id: string;
}

/**
 * Formata uma data para o formato brasileiro com horário
 * Abordagem mais robusta que não depende exclusivamente de timezone libraries
 */
function formatDateBR(dateString: string): string {
  try {
    // Parse da data assumindo que é UTC ou local
    const date = new Date(dateString);
    
    // Verificar se a data é válida
    if (isNaN(date.getTime())) {
      console.error(`Data inválida: ${dateString}`);
      return 'Data inválida';
    }

    // Usar toLocaleDateString com configurações específicas do Brasil
    const dateOptions: Intl.DateTimeFormatOptions = {
      day: '2-digit',
      month: '2-digit', 
      year: 'numeric',
      timeZone: 'America/Sao_Paulo'
    };

    const timeOptions: Intl.DateTimeFormatOptions = {
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'America/Sao_Paulo',
      hour12: false
    };

    const formattedDate = date.toLocaleDateString('pt-BR', dateOptions);
    const formattedTime = date.toLocaleTimeString('pt-BR', timeOptions);

    return `${formattedDate} ${formattedTime}`;
  } catch (error) {
    console.error(`Erro ao formatar data ${dateString}:`, error);
    return 'Erro na data';
  }
}

type StudentInfoMap = Record<
  string,
  {
    name: string;
    userId?: string;
  }
>;

async function getStudentInfoMap(
  supabase: any,
  studentIds: string[]
): Promise<StudentInfoMap> {
  if (!studentIds.length) return {};

  // First get students
  const { data: students, error: studentsError } = await supabase
    .from("students")
    .select("id, user_id")
    .in("id", studentIds);

  if (studentsError) {
    console.error("Error fetching students:", studentsError);
    return {};
  }

  if (!students || students.length === 0) {
    return {};
  }

  // Get user IDs
  const userIds = students.map((s: any) => s.user_id).filter(Boolean);
  
  if (userIds.length === 0) {
    return {};
  }

  // Then get users
  const { data: users, error: usersError } = await supabase
    .from("users")
    .select("id, first_name, last_name, full_name")
    .in("id", userIds);

  if (usersError) {
    console.error("Error fetching users:", usersError);
    return {};
  }

  // Create user lookup map
  const userMap = new Map();
  (users || []).forEach((user: any) => {
    userMap.set(user.id, user);
  });

  const infoMap: StudentInfoMap = {};
  
  students.forEach((student: any) => {
    const user = userMap.get(student.user_id);
    if (user) {
      const firstName = user.first_name || "";
      const lastName = user.last_name || "";
      const fullName = user.full_name || `${firstName} ${lastName}`.trim();

      infoMap[student.id] = {
        name: fullName || `Aluno ${student.id?.slice(0, 4)}`,
        userId: student.user_id,
      };
    } else {
      infoMap[student.id] = {
        name: `Aluno ${student.id?.slice(0, 4)}`,
        userId: student.user_id || undefined,
      };
    }
  });

  return infoMap;
}

async function getRecentPayments(
  supabase: any,
  tenantId: string
): Promise<PaymentActivity[]> {
  const { data: recentPayments } = await supabase
    .from("payments")
    .select("id, paid_at, amount, student_id")
    .eq("tenant_id", tenantId)
    .in("status", ["succeeded", "paid"])
    .order("paid_at", { ascending: false })
    .limit(5);

  return recentPayments || [];
}

async function getRecentGraduations(
  supabase: any,
  tenantId: string
): Promise<GraduationActivity[]> {
  const { data: recentBelts } = await supabase
    .from("student_belts")
    .select(`
      id,
      awarded_at,
      student_id,
      belt_level:belt_levels (
        id,
        belt_color,
        degree,
        label,
        stripe_color,
        show_center_line,
        center_line_color,
        sort_order
      )
    `)
    .eq("tenant_id", tenantId)
    .order("awarded_at", { ascending: false })
    .limit(5);

  return recentBelts || [];
}

async function getRecentAttendance(
  supabase: any,
  tenantId: string
): Promise<AttendanceActivity[]> {
  const { data: recentAttendance } = await supabase
    .from("attendance")
    .select("id, checked_in_at, student_id")
    .eq("tenant_id", tenantId)
    .order("checked_in_at", { ascending: false })
    .limit(5);

  return recentAttendance || [];
}

function transformPaymentsToActivityItems(
  payments: PaymentActivity[],
  studentInfoMap: StudentInfoMap
): ActivityItem[] {
  return payments.map((payment) => {
    const info = studentInfoMap[payment.student_id] || {};
    return {
      id: payment.id,
      type: "payment" as const,
      person: {
        name: info.name || `Aluno ${payment.student_id?.slice(0, 4)}`,
        href: info.userId ? `/perfil/${info.userId}` : undefined,
      },
      date: formatDateBR(payment.paid_at),
      timestamp: payment.paid_at,
      description: `Pagamento recebido: R$ ${Number(payment.amount).toFixed(2)}`,
    };
  });
}

function transformGraduationsToActivityItems(
  graduations: GraduationActivity[],
  studentInfoMap: StudentInfoMap
): ActivityItem[] {
  return graduations.map((graduation) => {
    const info = studentInfoMap[graduation.student_id] || {};
    const beltLabel = graduation.belt_level?.label || `Faixa (${graduation.belt_level?.degree ?? ''})`;
    return {
      id: graduation.id,
      type: "graduation" as const,
      person: {
        name: info.name || `Aluno ${graduation.student_id?.slice(0, 4)}`,
        href: info.userId ? `/perfil/${info.userId}` : undefined,
      },
      date: formatDateBR(graduation.awarded_at),
      timestamp: graduation.awarded_at,
      description: `Graduação: ${beltLabel}`,
    };
  });
}

function transformAttendanceToActivityItems(
  attendances: AttendanceActivity[],
  studentInfoMap: StudentInfoMap
): ActivityItem[] {
  return attendances.map((attendance) => {
    const info = studentInfoMap[attendance.student_id] || {};
    return {
      id: attendance.id,
      type: "attendance" as const,
      person: {
        name: info.name || `Aluno ${attendance.student_id?.slice(0, 4)}`,
        href: info.userId ? `/perfil/${info.userId}` : undefined,
      },
      date: formatDateBR(attendance.checked_in_at),
      timestamp: attendance.checked_in_at,
      description: "Check-in realizado",
    };
  });
}

export async function getRecentActivity(tenantId: string): Promise<ActivityItem[]> {
  const supabase = await createAdminClient();

  const [
    paymentsActivityRaw,
    graduationActivityRaw,
    attendanceActivityRaw
  ] = await Promise.all([
    getRecentPayments(supabase, tenantId),
    getRecentGraduations(supabase, tenantId),
    getRecentAttendance(supabase, tenantId)
  ]);

  const allStudentIds = Array.from(
    new Set([
      ...paymentsActivityRaw.map((p) => p.student_id),
      ...graduationActivityRaw.map((g) => g.student_id),
      ...attendanceActivityRaw.map((a) => a.student_id),
    ])
  ).filter(Boolean);

  const studentInfoMap = await getStudentInfoMap(supabase, allStudentIds);

  const paymentsActivity = transformPaymentsToActivityItems(paymentsActivityRaw, studentInfoMap);
  const graduationActivity = transformGraduationsToActivityItems(graduationActivityRaw, studentInfoMap);
  const attendanceActivity = transformAttendanceToActivityItems(attendanceActivityRaw, studentInfoMap);

  const combined = [...paymentsActivity, ...graduationActivity, ...attendanceActivity];
  combined.sort((a, b) => (a.timestamp < b.timestamp ? 1 : -1));
  
  return combined.slice(0, 9);
} 