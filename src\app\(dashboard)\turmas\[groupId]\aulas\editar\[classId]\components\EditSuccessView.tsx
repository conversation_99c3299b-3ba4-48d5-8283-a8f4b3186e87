'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, ArrowLeft, Calendar, Edit } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import type { ClassData } from './types';

interface EditSuccessViewProps {
  classData: ClassData;
  groupId: string;
}

export function EditSuccessView({ classData, groupId }: EditSuccessViewProps) {
  const router = useRouter();

  // Auto-redirect após alguns segundos (opcional)
  useEffect(() => {
    const timer = setTimeout(() => {
      router.push(`/turmas/${groupId}/aulas`);
    }, 5000); // 5 segundos

    return () => clearTimeout(timer);
  }, [router, groupId]);

  return (
    <div className="max-w-2xl mx-auto">
      <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/30">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mb-4">
            <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
          <CardTitle className="text-green-900 dark:text-green-100">
            Aula Atualizada com Sucesso!
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Informações da aula atualizada */}
          <div className="bg-white dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
              Detalhes da Aula Atualizada
            </h3>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Nome:</span>
                <p className="font-medium text-gray-900 dark:text-gray-100">{classData.name}</p>
              </div>
              
              {classData.description && (
                <div>
                  <span className="text-muted-foreground">Descrição:</span>
                  <p className="font-medium text-gray-900 dark:text-gray-100">{classData.description}</p>
                </div>
              )}
              
              <div>
                <span className="text-muted-foreground">Instrutor:</span>
                <p className="font-medium text-gray-900 dark:text-gray-100">
                  {classData.instructor?.full_name || 
                   `${classData.instructor?.first_name} ${classData.instructor?.last_name}` ||
                   'Não informado'}
                </p>
              </div>
              
              <div>
                <span className="text-muted-foreground">Filial:</span>
                <p className="font-medium text-gray-900 dark:text-gray-100">{classData.branch?.name || 'Não informada'}</p>
              </div>
              
              <div>
                <span className="text-muted-foreground">Data/Hora:</span>
                <p className="font-medium text-gray-900 dark:text-gray-100">
                  {new Date(classData.start_time).toLocaleString('pt-BR')}
                </p>
              </div>
              
              {classData.max_capacity && (
                <div>
                  <span className="text-muted-foreground">Capacidade:</span>
                  <p className="font-medium text-gray-900 dark:text-gray-100">{classData.max_capacity} alunos</p>
                </div>
              )}
            </div>
          </div>

          {/* Próximas ações */}
          <div className="bg-blue-50 dark:bg-blue-950/30 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
              O que você pode fazer agora:
            </h4>
            <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>• Visualizar a aula na lista de aulas da turma</li>
              <li>• Editar novamente se necessário</li>
              <li>• Gerenciar presenças quando a aula acontecer</li>
              <li>• Verificar conflitos de horário com outras aulas</li>
            </ul>
          </div>

          {/* Ações */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Link 
              href={`/turmas/${groupId}/aulas`}
              className="flex-1"
            >
              <Button className="w-full">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar para Aulas
              </Button>
            </Link>
            
            <Link 
              href={`/turmas/${groupId}/aulas/editar/${classData.id}`}
              className="flex-1"
            >
              <Button variant="outline" className="w-full">
                <Edit className="h-4 w-4 mr-2" />
                Editar Novamente
              </Button>
            </Link>
            
            <Link 
              href={`/turmas/${groupId}`}
              className="flex-1"
            >
              <Button variant="ghost" className="w-full">
                <Calendar className="h-4 w-4 mr-2" />
                Ver Turma
              </Button>
            </Link>
          </div>

          {/* Auto-redirect info */}
          <div className="text-center text-sm text-muted-foreground">
            Redirecionando automaticamente em alguns segundos...
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 