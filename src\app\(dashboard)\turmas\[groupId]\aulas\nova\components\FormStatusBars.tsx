'use client';

import { Check<PERSON><PERSON>cle2, Loader2, Zap } from 'lucide-react';
import { Progress } from '@/components/ui/progress';

interface FormStatusBarsProps {
  formProgress: number;
  isAutoSaving: boolean;
  lastSaved: Date | null;
}

export function FormStatusBars({ formProgress, isAutoSaving, lastSaved }: FormStatusBarsProps) {
  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between text-sm">
        <div className="flex items-center gap-2">
          <span className="font-medium">Progresso do formulário</span>
          <span className="text-muted-foreground">({Math.round(formProgress)}%)</span>
        </div>
        <div className="flex items-center gap-4 text-xs text-muted-foreground">
          {isAutoSaving && (
            <div className="flex items-center gap-1">
              <Loader2 className="h-3 w-3 animate-spin" />
              <span><PERSON><PERSON><PERSON> rascunho...</span>
            </div>
          )}
          {lastSaved && !isAutoSaving && (
            <div className="flex items-center gap-1">
              <CheckCircle2 className="h-3 w-3 text-green-600" />
              <span>Rascunho salvo às {lastSaved.toLocaleTimeString()}</span>
            </div>
          )}
          <div className="flex items-center gap-1">
            <Zap className="h-3 w-3" />
            <span>Ctrl+S para salvar</span>
          </div>
        </div>
      </div>
      <Progress value={formProgress} className="h-2" />
    </div>
  );
} 