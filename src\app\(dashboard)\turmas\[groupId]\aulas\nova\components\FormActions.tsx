'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Plus, Loader2, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FieldErrors } from 'react-hook-form';
import { isMobile } from './utils/formUtils';
import { CreateClassFormData } from './schema';

interface FormActionsProps {
  isPending: boolean;
  isValid: boolean;
  isDirty: boolean;
  hasTimeConflict: boolean;
  errors: FieldErrors<CreateClassFormData>;
  getErrorMessage: (error: any) => string;
  onCancel: () => void;
}

export function FormActions({ 
  isPending, 
  isValid, 
  isDirty, 
  hasTimeConflict, 
  errors,
  getErrorMessage,
  onCancel 
}: FormActionsProps) {
  const errorCount = Object.keys(errors).length;
  const hasErrors = errorCount > 0;
  return (
    <>
      {/* Botões de Ação */}
      <div className="flex flex-col sm:flex-row gap-3 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isPending}
          className="w-full sm:w-auto"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Cancelar
        </Button>

        <Button
          type="submit"
          disabled={isPending || !isValid || !isDirty || hasTimeConflict}
          className="w-full sm:flex-1"
          aria-describedby="submit-button-help"
        >
          {isPending ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Criando aula...
            </>
          ) : (
            <>
              <Plus className="h-4 w-4 mr-2" />
              Criar Aula
            </>
          )}
        </Button>
      </div>

      {/* Exibição de erros */}
      {hasErrors && isDirty && (
        <Alert variant="destructive" className="mt-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">
                {errorCount === 1 ? '1 erro encontrado:' : `${errorCount} erros encontrados:`}
              </p>
              <ul className="text-sm space-y-1">
                {Object.entries(errors).map(([field, error]) => {
                  const fieldNames: Record<string, string> = {
                    name: 'Nome',
                    description: 'Descrição',
                    instructor_id: 'Instrutor',
                    branch_id: 'Filial',
                    start_time: 'Horário de início',
                    end_time: 'Horário de fim',
                    max_capacity: 'Capacidade máxima',
                    notes: 'Observações'
                  };
                  
                  return (
                    <li key={field}>
                      <strong>{fieldNames[field] || field}:</strong> {getErrorMessage(error)}
                    </li>
                  );
                })}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Status do formulário */}
      {hasTimeConflict && (
        <Alert variant="destructive" className="mt-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Conflito de horário detectado. Verifique se não há outra aula agendada no mesmo período.
          </AlertDescription>
        </Alert>
      )}

      {/* Mensagem quando formulário não está completo, mas sem erros */}
      {!hasErrors && !isValid && isDirty && !hasTimeConflict && (
        <div className="text-center mt-4">
          <p className="text-sm text-muted-foreground">
            Preencha todos os campos obrigatórios para continuar
          </p>
        </div>
      )}

      {/* Informações de help no mobile */}
      {isMobile() && (
        <div className="p-3 bg-muted/50 rounded-lg border text-center">
          <p className="text-xs text-muted-foreground">
            💡 Dica: Use Ctrl+S para salvar rascunho ou ESC para cancelar
          </p>
        </div>
      )}
    </>
  );
} 