import { z } from "zod";
import { userFilterStatusSchema } from "@/services/user/schemas/user-schema";

export const instrutorFilterSchema = z.object({
  search: z.string().optional(),
  specialties: z.array(z.string()).optional(),
  status: userFilterStatusSchema,
  branch: z.array(z.string()).optional(),
  contract_type: z.array(z.string()).optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  page: z.number().optional().default(1),
  limit: z.number().optional().default(10),
});

export type InstrutorFilterValues = z.infer<typeof instrutorFilterSchema>;

export const instrutorFormSchema = z.object({
  name: z.string().min(2, { message: "O nome deve ter pelo menos 2 caracteres" }),
  email: z.string().email({ message: "Email inválido" }),
  phone: z.string().min(8, { message: "Telefone inválido" }),
  specialties: z.array(z.string()).min(1, { message: "Selecione pelo menos uma especialidade" }),
  branch: z.string({
    message: "Selecione uma filial"
  }),
  contract_type: z.enum(["full_time", "part_time", "contractor"], {
    message: "Selecione um tipo de contrato válido"
  }).optional(),
  experience_years: z.number().min(0).optional(),

});

export type InstrutorFormValues = z.infer<typeof instrutorFormSchema>; 