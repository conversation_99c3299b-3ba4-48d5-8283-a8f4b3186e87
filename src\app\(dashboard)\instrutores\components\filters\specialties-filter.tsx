'use client';

import { X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useState } from 'react';

interface SpecialtiesFilterProps {
  allSpecialties: string[];
  selectedSpecialties: string[];
  onChange: (specialties: string[]) => void;
}

export function SpecialtiesFilter({
  allSpecialties,
  selectedSpecialties,
  onChange
}: SpecialtiesFilterProps) {
  const [open, setOpen] = useState(false);

  const toggleSpecialty = (specialty: string) => {
    if (selectedSpecialties.includes(specialty)) {
      onChange(selectedSpecialties.filter(s => s !== specialty));
    } else {
      onChange([...selectedSpecialties, specialty]);
    }
  };

  const removeSpecialty = (specialty: string) => {
    onChange(selectedSpecialties.filter(s => s !== specialty));
  };

  const clearAll = () => {
    onChange([]);
  };

  return (
    <div className="space-y-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button 
            variant="outline" 
            className="w-full justify-between h-10"
            role="combobox"
            aria-expanded={open}
          >
            Selecionar especialidades
            <span className="ml-2 rounded-full bg-primary text-primary-foreground px-2 py-0.5 text-xs">
              {selectedSpecialties.length}
            </span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput placeholder="Buscar especialidade..." />
            <CommandEmpty>Nenhuma especialidade encontrada.</CommandEmpty>
            <CommandGroup className="max-h-64 overflow-auto">
              {allSpecialties.map((specialty) => {
                const isSelected = selectedSpecialties.includes(specialty);
                return (
                  <CommandItem
                    key={specialty}
                    value={specialty}
                    onSelect={() => toggleSpecialty(specialty)}
                    className={cn(
                      'flex items-center gap-2',
                      isSelected ? 'bg-muted' : ''
                    )}
                  >
                    <div
                      className={cn(
                        'flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                        isSelected
                          ? 'bg-primary text-primary-foreground'
                          : 'opacity-50'
                      )}
                    >
                      {isSelected && (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          width="14"
                          height="14"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="3"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      )}
                    </div>
                    <span>{specialty}</span>
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      {selectedSpecialties.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {selectedSpecialties.map((specialty) => (
            <Badge key={specialty} variant="secondary" className="px-2 py-1">
              {specialty}
              <button
                type="button"
                onClick={() => removeSpecialty(specialty)}
                className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              >
                <X className="h-3 w-3" />
                <span className="sr-only">Remover {specialty}</span>
              </button>
            </Badge>
          ))}
          
          {selectedSpecialties.length > 1 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAll}
              className="h-7 px-2 text-xs"
            >
              Limpar todos
            </Button>
          )}
        </div>
      )}
    </div>
  );
} 