'use client';

import { LogOut, Monitor, Shield } from 'lucide-react';
import { ThemeSwitcher } from '@/components/layout/header/components';
import { DotLottieReact } from '@lottiefiles/dotlottie-react';
import { useReceptionMode } from '@/contexts/ReceptionModeContext';
import { Badge } from '@/components/ui/badge';

interface CheckInHeaderProps {
  onExitClick: () => void;
}

export function CheckInHeader({ onExitClick }: CheckInHeaderProps) {
  const { isReceptionModeActive, state } = useReceptionMode();

  return (
    <header className="bg-white dark:bg-gray-900 shadow-sm border-b border-slate-200 dark:border-gray-700">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 rounded-full flex items-center justify-center dark:bg-white/10">
              <DotLottieReact
                src="/checkin.lottie"
                loop
                autoplay
                style={{ width: '100%', height: '100%' }}
              />
            </div>
            <div>
              <div className="flex items-center gap-3">
                <h1 className="text-xl font-semibold text-slate-800 dark:text-gray-100">Check-In</h1>
                {isReceptionModeActive && (
                  <Badge variant="secondary" className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 border-orange-200 dark:border-orange-700">
                    <Monitor className="w-3 h-3 mr-1" />
                    Modo de Recepção
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2">
                <p className="text-sm text-slate-500 dark:text-gray-400">Vilhena • Matriz</p>
                {/* {isReceptionModeActive && (
                  <div className="flex items-center gap-1 text-xs text-orange-600">
                    <Shield className="w-3 h-3" />
                    <span>Ativado por {state.activatedBy?.name || state.activatedBy?.email}</span>
                  </div>
                )} */}
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <ThemeSwitcher />

            <button 
              onClick={onExitClick}
              className="flex items-center space-x-2 text-slate-600 dark:text-gray-400 hover:text-slate-800 dark:hover:text-gray-200 transition-colors"
            >
              <LogOut className="w-5 h-5" />
              <span className="text-sm font-medium">Sair</span>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
} 