import { ExpenseTransaction, ExpenseCategory, FinancialMetric } from './components/types';

// Categorias de despesas mock
export const mockExpenseCategories: ExpenseCategory[] = [
  {
    id: '1',
    name: 'Recursos Humanos',
    description: '<PERSON><PERSON><PERSON>s, encargos e benefícios',
    color: '#3B82F6',
  },
  {
    id: '2',
    name: 'Infraestrutura',
    description: 'Aluguel, utilities e manutenção',
    color: '#EF4444',
  },
  {
    id: '3',
    name: 'Equipamentos',
    description: 'Tatames, uniformes e materiais',
    color: '#10B981',
  },
  {
    id: '4',
    name: 'Marketing',
    description: 'Publicidade e promoção',
    color: '#F59E0B',
  },
  {
    id: '5',
    name: 'Operacional',
    description: 'Seguros, taxas e outros',
    color: '#8B5CF6',
  },
];

// Despesas mock
export const mockExpenseTransactions: ExpenseTransaction[] = [
  {
    id: 1,
    transactionType: 'expense',
    supplierName: '<PERSON> Instrutor',
    categoryName: 'Recursos Humanos',
    categoryColor: '#3B82F6',
    type: 'Sal<PERSON>rio <PERSON>',
    paymentMethod: 'Transferência Bancária',
    date: '15/01/2024',
    amount: 'R$ 3.500,00',
    status: 'Pago',
    paidAt: '15/01/2024',
    description: 'Salário mensal do instrutor João Silva',
  },
  {
    id: 2,
    transactionType: 'expense',
    supplierName: 'Imobiliária Central',
    categoryName: 'Infraestrutura',
    categoryColor: '#EF4444',
    type: 'Aluguel',
    paymentMethod: 'PIX',
    date: '05/01/2024',
    amount: 'R$ 4.500,00',
    status: 'Pago',
    paidAt: '05/01/2024',
    description: 'Aluguel mensal da academia',
  },
  {
    id: 3,
    transactionType: 'expense',
    supplierName: 'Equipamentos Esportivos Ltda',
    categoryName: 'Equipamentos',
    categoryColor: '#10B981',
    type: 'Tatames',
    paymentMethod: 'Cartão de Crédito',
    date: '20/01/2024',
    amount: 'R$ 2.800,00',
    status: 'Pendente',
    dueDate: '25/01/2024',
    description: 'Compra de novos tatames para a academia',
  },
  {
    id: 4,
    transactionType: 'expense',
    supplierName: 'Agência Digital Pro',
    categoryName: 'Marketing',
    categoryColor: '#F59E0B',
    type: 'Publicidade Online',
    paymentMethod: 'PIX',
    date: '10/01/2024',
    amount: 'R$ 800,00',
    status: 'Vencido',
    dueDate: '12/01/2024',
    description: 'Campanha de marketing digital',
  },
  {
    id: 5,
    transactionType: 'expense',
    supplierName: 'Seguradora Total',
    categoryName: 'Operacional',
    categoryColor: '#8B5CF6',
    type: 'Seguro',
    paymentMethod: 'Boleto',
    date: '01/01/2024',
    amount: 'R$ 450,00',
    status: 'Pago',
    paidAt: '01/01/2024',
    description: 'Seguro anual da academia',
  },
];

// Métricas de despesas mock
export const mockExpenseMetrics: FinancialMetric[] = [
  {
    value: 'R$ 12.050,00',
    label: 'Total Gasto',
    color: 'red',
  },
  {
    value: '3',
    label: 'Despesas Pagas',
    color: 'blue',
  },
  {
    value: '1',
    label: 'Pendentes',
    color: 'orange',
  },
  {
    value: '1',
    label: 'Vencidas',
    color: 'red',
  },
];
