import { Suspense } from 'react';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getClassGroupById } from '../../../actions/class-group'; 
import { getInstructorsForForm, getBranchesForForm } from '../../../../aulas/actions';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronLeft, Calendar, Users, Clock, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { CreateClassForm } from './components/CreateClassForm';

export const metadata: Metadata = {
  title: 'Nova Aula - Turma | Apex SaaS',
  description: 'Criar uma nova aula para uma turma específica',
};

interface PageProps {
  params: Promise<{
    groupId: string;
  }>;
}

async function NewClassFormContent({ groupId }: { groupId: string }) {
  // Buscar dados necessários em paralelo
  const [classGroupResult, instructorsResult, branchesResult] = await Promise.all([
    getClassGroupById(groupId),
    getInstructorsForForm(),
    getBranchesForForm()
  ]);

  // Verificar se a turma existe
  if (!classGroupResult.success || !('data' in classGroupResult) || !classGroupResult.data) {
    notFound();
  }

  const classGroup = classGroupResult.data;
  const instructors = instructorsResult.success 
    ? (instructorsResult.data || []).map(instructor => ({
        id: instructor.id,
        name: instructor.full_name || `${instructor.first_name} ${instructor.last_name || ''}`.trim()
      }))
    : [];
  const branches = branchesResult.success ? branchesResult.data || [] : [];

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Breadcrumb - Responsivo */}
      <div className="flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm text-muted-foreground overflow-x-auto pb-2 sm:pb-0">
        <Link href="/turmas" className="hover:text-foreground transition-colors whitespace-nowrap">Turmas</Link>
        <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
        <Link href={`/turmas/${groupId}`} className="hover:text-foreground transition-colors whitespace-nowrap max-w-[100px] sm:max-w-none truncate">
          {classGroup.name}
        </Link>
        <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
        <Link href={`/turmas/${groupId}/aulas`} className="hover:text-foreground transition-colors whitespace-nowrap">
          Aulas
        </Link>
        <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
        <span className="text-foreground whitespace-nowrap">Nova Aula</span>
      </div>

      {/* Header Section - Responsivo */}
      <div className="flex flex-col space-y-3 sm:space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        <div className="space-y-1 sm:space-y-2">
          <div className="flex items-center gap-2">
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground leading-tight">
              Nova Aula
            </h1>
            {/* Badge de Debug - só aparece em desenvolvimento */}
            {process.env.NODE_ENV === 'development' && (
              <span className="px-2 py-1 text-xs bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300 rounded border border-yellow-200 dark:border-yellow-800">
                DEBUG
              </span>
            )}
          </div>
          <div className="flex flex-col sm:flex-row sm:items-center sm:gap-2">
            <span className="text-sm sm:text-base text-muted-foreground">
              Turma:
            </span>
            <span className="text-sm sm:text-base font-medium text-foreground truncate">
              {classGroup.name}
            </span>
          </div>
          <p className="text-xs sm:text-sm text-muted-foreground">
            Agende uma nova aula para esta turma
          </p>
          {/* Informações de Debug Adicionais - só aparecem em desenvolvimento */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/10 border border-blue-200 dark:border-blue-800 rounded text-xs">
              <div className="font-medium text-blue-800 dark:text-blue-300 mb-1">
                Informações de Debug:
              </div>
                             <div className="space-y-0.5 text-blue-700 dark:text-blue-400">
                 <div>• Instrutores disponíveis: {instructors.length}</div>
                 <div>• Filiais disponíveis: {branches.length}</div>
                 <div>• Turma ativa: {classGroup.is_active ? 'Sim' : 'Não'}</div>
                 <div>• Capacidade máxima: {classGroup.max_capacity || 'Não definida'}</div>
                 <div>• Instrutor ID: {classGroup.instructor_id || 'Não definido'}</div>
                 <div>• Recorrência: {classGroup.recurrence_pattern ? 'Configurada' : 'Nenhuma'}</div>
                 {classGroup.instructor && (
                   <div>• Instrutor: {classGroup.instructor.full_name || `${classGroup.instructor.first_name} ${classGroup.instructor.last_name || ''}`.trim()}</div>
                 )}
                 {classGroup.branch && (
                   <div>• Filial: {classGroup.branch.name}</div>
                 )}
                 <div>• Total matriculados: {classGroup._count?.enrollments || 0}</div>
                 <div>• Lista de espera: {classGroup._count?.waitlist || 0}</div>
                 <div>• Total de aulas: {classGroup._count?.classes || 0}</div>
               </div>
            </div>
          )}
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
          <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
            <Link href={`/turmas/${groupId}/aulas`}>
              <ChevronLeft className="h-4 w-4 mr-2" />
              <span className="sm:hidden">Voltar</span>
              <span className="hidden sm:inline">Voltar às Aulas</span>
            </Link>
          </Button>
        </div>
      </div>

      {/* Informações da Turma - Responsivo */}
      <Card className="bg-gradient-to-r from-background via-muted/30 to-background border-0 shadow-sm">
        <CardHeader className="p-4 sm:p-6 pb-3 sm:pb-4">
          <div className="flex flex-col sm:flex-row items-start gap-3 sm:gap-4">
            <div className="flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-xl bg-primary/10 ring-1 ring-primary/20 flex-shrink-0">
              <Users className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
            </div>
            <div className="flex-1 space-y-1 min-w-0">
              <CardTitle className="text-lg sm:text-xl font-bold tracking-tight truncate">
                {classGroup.name}
              </CardTitle>
              {classGroup.description && (
                <p className="text-sm sm:text-base text-muted-foreground line-clamp-2">
                  {classGroup.description}
                </p>
              )}
            </div>
          </div>
          
          {/* Features Grid - Responsivo */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-3 mt-3 sm:mt-4">
            <div className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg bg-background/50 border border-border/50">
              <div className="flex h-6 w-6 sm:h-8 sm:w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/20 flex-shrink-0">
                <Calendar className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="min-w-0">
                <div className="text-xs sm:text-sm font-medium truncate">Status da Turma</div>
                <div className="text-xs text-muted-foreground">
                  {classGroup.is_active ? 'Ativa' : 'Inativa'}
                </div>
              </div>
            </div>
            
            {classGroup.max_capacity && (
              <div className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg bg-background/50 border border-border/50">
                <div className="flex h-6 w-6 sm:h-8 sm:w-8 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/20 flex-shrink-0">
                  <Users className="h-3 w-3 sm:h-4 sm:w-4 text-green-600 dark:text-green-400" />
                </div>
                <div className="min-w-0">
                  <div className="text-xs sm:text-sm font-medium truncate">Capacidade</div>
                  <div className="text-xs text-muted-foreground">
                    Máx. {classGroup.max_capacity} alunos
                  </div>
                </div>
              </div>
            )}
            
            <div className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg bg-background/50 border border-border/50 sm:col-span-2 lg:col-span-1">
              <div className="flex h-6 w-6 sm:h-8 sm:w-8 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900/20 flex-shrink-0">
                <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="min-w-0">
                <div className="text-xs sm:text-sm font-medium truncate">Tipo de Agendamento</div>
                <div className="text-xs text-muted-foreground">
                  {classGroup.recurrence_pattern ? 'Recorrente' : 'Pontual'}
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Formulário - Responsivo */}
      <Card>
        <CardContent className="p-3 sm:p-4 lg:p-6">
          <CreateClassForm
            classGroup={classGroup}
            instructors={instructors}
            branches={branches}
          />
        </CardContent>
      </Card>
    </div>
  );
}

export default async function NovaAulaPage({ params }: PageProps) {
  const { groupId } = await params;

  return (
    <Suspense fallback={
      <div className="space-y-4 sm:space-y-6">
        {/* Skeleton para mobile e desktop */}
        <div className="animate-pulse">
          <div className="h-3 sm:h-4 bg-muted rounded w-full max-w-sm mb-3 sm:mb-4"></div>
          <div className="h-6 sm:h-8 bg-muted rounded w-48 sm:w-64 mb-1 sm:mb-2"></div>
          <div className="h-4 bg-muted rounded w-32 sm:w-48"></div>
        </div>
        <div className="animate-pulse">
          <div className="h-24 sm:h-32 bg-muted rounded"></div>
        </div>
        <div className="animate-pulse">
          <div className="h-64 sm:h-96 bg-muted rounded"></div>
        </div>
      </div>
    }>
      <NewClassFormContent groupId={groupId} />
    </Suspense>
  );
} 