"use client"

import * as React from "react"
import * as TooltipPrimitive from "@radix-ui/react-tooltip"
import { cn } from "@/lib/utils"

const ErrorTooltipProvider = TooltipPrimitive.Provider
const ErrorTooltipRoot = TooltipPrimitive.Root
const ErrorTooltipTrigger = TooltipPrimitive.Trigger
const ErrorTooltipPortal = TooltipPrimitive.Portal

const ErrorTooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <TooltipPrimitive.Content
    ref={ref}
    sideOffset={sideOffset}
    className={cn(
      "z-50 overflow-hidden rounded-md bg-red-600 px-3 py-1.5 text-xs font-semibold text-white",
      "animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95",
      "data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2",
      "data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
      "shadow-md",
      className
    )}
    {...props}
  />
))
ErrorTooltipContent.displayName = TooltipPrimitive.Content.displayName

export {
  ErrorTooltipProvider,
  ErrorTooltipRoot,
  ErrorTooltipTrigger,
  ErrorTooltipPortal,
  ErrorTooltipContent,
} 