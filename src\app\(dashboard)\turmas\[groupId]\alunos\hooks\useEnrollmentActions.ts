import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { unenrollStudent } from '../../../actions/class-group/unenroll-student';
import { 
  pauseEnrollment, 
  resumeEnrollment 
} from '../actions/student-enrollment-actions';
import { CACHE_KEYS } from '@/constants/cache-keys';

export interface EnrollmentActionState {
  isLoading: boolean;
  error: string | null;
}

export function useEnrollmentActions(classGroupId: string) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [actionState, setActionState] = useState<EnrollmentActionState>({
    isLoading: false,
    error: null
  });

  const invalidateRelatedQueries = useCallback(async () => {
    await Promise.all([
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.ENROLLMENT.CLASS_GROUP_ENROLLMENTS(classGroupId)
      }),
      queryClient.invalidateQueries({
        queryKey: CACHE_KEYS.ENROLLMENT.CAPACITY_CHECK(classGroupId)
      })
    ]);
    router.refresh();
  }, [queryClient, classGroupId, router]);

  const handleUnenrollStudent = useCallback(async (
    enrollmentId: string, 
    studentName: string,
    reason?: string
  ) => {
    setActionState({ isLoading: true, error: null });

    try {
      const result = await unenrollStudent({
        enrollment_id: enrollmentId,
        reason
      });

      if (result.success) {
        const message = (result as any).message || `Matrícula de ${studentName} cancelada com sucesso`;
        toast.success(message);
        await invalidateRelatedQueries();
        return { success: true };
      } else {
        const errorMessage = ((result as any).errors?._form) || 'Erro ao cancelar matrícula';
        setActionState({ isLoading: false, error: errorMessage });
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (error) {
      const errorMessage = 'Erro inesperado ao cancelar matrícula';
      setActionState({ isLoading: false, error: errorMessage });
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setActionState(prev => ({ ...prev, isLoading: false }));
    }
  }, [invalidateRelatedQueries]);

  const handlePauseEnrollment = useCallback(async (
    enrollmentId: string, 
    studentName: string,
    reason?: string
  ) => {
    setActionState({ isLoading: true, error: null });

    try {
      const result = await pauseEnrollment({
        enrollment_id: enrollmentId,
        reason
      });

      if (result.success) {
        const message = (result as any).message || `Matrícula de ${studentName} pausada com sucesso`;
        toast.success(message);
        await invalidateRelatedQueries();
        return { success: true };
      } else {
        const errorMessage = ((result as any).errors?._form) || 'Erro ao pausar matrícula';
        setActionState({ isLoading: false, error: errorMessage });
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (error) {
      const errorMessage = 'Erro inesperado ao pausar matrícula';
      setActionState({ isLoading: false, error: errorMessage });
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setActionState(prev => ({ ...prev, isLoading: false }));
    }
  }, [invalidateRelatedQueries]);

  const handleReactivateEnrollment = useCallback(async (
    enrollmentId: string, 
    studentName: string,
    reason?: string
  ) => {
    setActionState({ isLoading: true, error: null });

    try {
      const result = await resumeEnrollment({
        enrollment_id: enrollmentId,
        reason
      });

      if (result.success) {
        const message = (result as any).message || `Matrícula de ${studentName} reativada com sucesso`;
        toast.success(message);
        await invalidateRelatedQueries();
        return { success: true };
      } else {
        const errorMessage = ((result as any).errors?._form) || 'Erro ao reativar matrícula';
        setActionState({ isLoading: false, error: errorMessage });
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (error) {
      const errorMessage = 'Erro inesperado ao reativar matrícula';
      setActionState({ isLoading: false, error: errorMessage });
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setActionState(prev => ({ ...prev, isLoading: false }));
    }
  }, [invalidateRelatedQueries]);

  const clearError = useCallback(() => {
    setActionState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    actionState,
    handleUnenrollStudent,
    handlePauseEnrollment,
    handleReactivateEnrollment,
    clearError
  };
} 