'use client';

import React from 'react';
import { ActionType, ResourceType } from '../types/permission-types';
import { usePermission } from '../hooks/use-permission';
import { Button, ButtonProps } from '@/components/ui/button';

interface PermissionGateProps {
  resource: ResourceType;
  action: ActionType;
  targetId?: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * Componente que renderiza os filhos apenas se o usuário tiver a permissão especificada
 */
export function PermissionGate({
  resource,
  action,
  targetId,
  children,
  fallback = null
}: PermissionGateProps) {
  const { isAllowed, isLoading } = usePermission(resource, action, targetId);
  
  if (isLoading) {
    return null;
  }
  
  return isAllowed ? <>{children}</> : <>{fallback}</>;
}

/**
 * Component de botão que só é habilitado se o usuário tiver a permissão
 */
interface PermissionButtonProps extends ButtonProps {
  resource: ResourceType;
  action: ActionType;
  targetId?: string;
  showDisabled?: boolean;
  disabledTitle?: string;
  isEditTrigger?: boolean;
  onEditTrigger?: () => void;
}

export function PermissionButton({
  resource,
  action,
  targetId,
  showDisabled = true,
  disabledTitle = 'Você não tem permissão para executar esta ação',
  isEditTrigger = false,
  onEditTrigger,
  children,
  onClick,
  ...props
}: PermissionButtonProps) {
  const { isAllowed, reason, isLoading } = usePermission(resource, action, targetId);
  
  // Suporte para o modo de edição
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (isEditTrigger && onEditTrigger) {
      // Usar a callback fornecida para alterar o modo de edição
      onEditTrigger();
    }
    
    // Chama a função onClick original, se existir
    onClick?.(e);
  };
  
  if (isLoading) {
    return showDisabled ? (
      <Button
        disabled
        title="Verificando permissões..."
        {...props}
      >
        {children}
      </Button>
    ) : null;
  }
  
  if (!isAllowed) {
    if (!showDisabled) return null;
    
    return (
      <Button
        disabled
        title={reason || disabledTitle}
        {...props}
      >
        {children}
      </Button>
    );
  }
  
  return <Button onClick={handleClick} {...props}>{children}</Button>;
} 