import { useEffect } from 'react';
import { UseFormSetError, UseFormClearErrors } from 'react-hook-form';
import { FormValues } from '../validation';

const beltOrder = ['white', 'blue', 'purple', 'brown', 'black'];

interface UseBeltValidationProps {
  minBelt: string;
  maxBelt: string;
  setError: UseFormSetError<FormValues>;
  clearErrors: UseFormClearErrors<FormValues>;
}

export function useBeltValidation({ 
  minBelt, 
  maxBelt, 
  setError, 
  clearErrors 
}: UseBeltValidationProps) {
  useEffect(() => {
    // Limpar erros anteriores
    clearErrors(['min_belt_level', 'max_belt_level']);
    
    // Se algum dos valores estiver vazio, não validar
    if (!minBelt || !maxBelt) {
      return;
    }
    
    const minIndex = beltOrder.indexOf(minBelt);
    const maxIndex = beltOrder.indexOf(maxBelt);
    
    // Se alguma faixa não for encontrada, não validar
    if (minIndex === -1 || maxIndex === -1) {
      return;
    }
    
    // Verificar se a faixa mínima é maior que a máxima
    if (minIndex > maxIndex) {
      setError('max_belt_level', {
        type: 'manual',
        message: 'A faixa máxima deve ser igual ou superior à faixa mínima'
      });
    }
  }, [minBelt, maxBelt, setError, clearErrors]);
} 