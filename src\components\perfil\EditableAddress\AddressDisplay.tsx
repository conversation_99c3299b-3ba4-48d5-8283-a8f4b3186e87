'use client';

import { Button } from '@/components/ui/button';
import { MapPin, Edit2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { AddressDisplayProps } from './types';

export function AddressDisplay({
  className,
  onEdit,
  wasRecentlyEdited,
  formatAddressForDisplay,
  isAllowed
}: AddressDisplayProps & { isAllowed: boolean }) {
  const addressLines = formatAddressForDisplay();
  const isNotInformed = addressLines.length === 1 && addressLines[0] === 'Não informado';
  
  return (
    <div className={cn(
      "flex items-start group relative",
      wasRecentlyEdited ? "bg-green-50 dark:bg-green-900/20 transition-colors duration-500" : "",
      className
    )}>
      <div className="flex-shrink-0 mt-0.5 mr-3">
        <MapPin className="h-4 w-4" />
      </div>
      
      <div className="flex-grow">
        <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Endereço</p>
        
        <div className="mt-1">
          <div className={cn(
            "text-base text-slate-900 dark:text-slate-100 rounded p-1",
            isNotInformed && "text-slate-400 dark:text-slate-500 italic"
          )}>
            {isNotInformed ? (
              <p>Não informado</p>
            ) : (
              <div className="space-y-1.5">
                {Array.isArray(addressLines) && addressLines.map((line, index) => (
                  <p key={index} className={cn(
                    "leading-snug flex items-start",
                    index === 0 && "font-medium"
                  )}>
                    {index === addressLines.length - 1 && line.startsWith('CEP:') ? (
                      <span className="flex items-center gap-1">
                        <span className="font-medium">CEP:</span> 
                        <span>{line.replace('CEP:', '').trim()}</span>
                      </span>
                    ) : (
                      <span>{line}</span>
                    )}
                  </p>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {isAllowed && (
        <Button
          size="sm"
          variant="ghost"
          className="opacity-0 group-hover:opacity-100 transition-opacity"
          onClick={onEdit}
        >
          <Edit2 className="h-4 w-4 text-slate-500" />
        </Button>
      )}
    </div>
  );
} 