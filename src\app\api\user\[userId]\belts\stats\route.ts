import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/services/supabase/server";
import { differenceInMonths } from "date-fns";

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const supabase = await createClient();
    
    const resolvedParams = await Promise.resolve(params);
    const { userId } = resolvedParams;

    if (!userId) {
      return NextResponse.json(
        { error: "ID de usuário inválido" },
        { status: 400 }
      );
    }

    const { data: sessionData } = await supabase.auth.getSession();
    if (!sessionData?.session) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      );
    }

    const { data: student, error: studentError } = await supabase
      .from("students")
      .select(`
        id,
        created_at,
        current_belt_id
      `)
      .eq("user_id", userId)
      .single();

    if (studentError) {
      console.error("Erro ao buscar dados do estudante:", studentError);
      return NextResponse.json(
        { error: "Erro ao buscar dados do estudante" },
        { status: 500 }
      );
    }

    let currentBelt = null;
    let timeInBelt = 0;
    let timeToNextBelt = 12; 
    let degree = 0;

    if (student?.current_belt_id) {
      const { data: beltData, error: beltError } = await supabase
        .from("student_belts")
        .select(`
          id,
          awarded_at,
          belt_level_id,
          belt_levels:belt_levels!inner(
            belt_color,
            label,
            degree,
            stripe_color,
            show_center_line,
            center_line_color,
            sort_order,
            modalities:modalities!inner(
              name
            )
          )
        `)
        .eq("id", student.current_belt_id)
        .single();

      if (!beltError && beltData) {
        currentBelt = beltData;
        degree = (beltData.belt_levels as any)?.degree || 0;

        const awardedAt = new Date(beltData.awarded_at);
        const currentDate = new Date();
        timeInBelt = differenceInMonths(currentDate, awardedAt);

        const beltColor = (beltData.belt_levels as any)?.belt_color || 'white';
        timeToNextBelt = calculateTimeToNextBelt(beltColor, degree);
      }
    }

    let trainingTime = "0 dias";
    if (student?.created_at) {
      const joinDate = new Date(student.created_at);
      const currentDate = new Date();
      const monthsSinceJoin = differenceInMonths(currentDate, joinDate);
      
      if (monthsSinceJoin < 1) {
        const daysSinceJoin = Math.floor((currentDate.getTime() - joinDate.getTime()) / (1000 * 60 * 60 * 24));
        trainingTime = `${daysSinceJoin} dias`;
      } else if (monthsSinceJoin < 12) {
        trainingTime = `${monthsSinceJoin} meses`;
      } else {
        const years = Math.floor(monthsSinceJoin / 12);
        const remainingMonths = monthsSinceJoin % 12;
        trainingTime = `${years} ano${years > 1 ? 's' : ''}${remainingMonths > 0 ? ` e ${remainingMonths} ${remainingMonths === 1 ? 'mês' : 'meses'}` : ''}`;
      }
    }

    const beltLevel = (currentBelt as any)?.belt_levels;
    const beltColor = beltLevel?.belt_color || null;
    const beltLabel = beltLevel?.label || null;
    const modalityName = beltLevel?.modalities?.name || null;
    const nextPromotion = beltColor ? determineNextPromotion(beltColor, degree) : null;

    const statsResponse = {
      current_belt: beltColor,
      current_belt_label: beltLabel,
      modality_name: modalityName,
      current_degree: degree,
      stripe_color: beltLevel?.stripe_color || null,
      show_center_line: beltLevel?.show_center_line || false,
      center_line_color: beltLevel?.center_line_color || null,
      sort_order: beltLevel?.sort_order || 0,
      time_in_belt: timeInBelt,
      time_to_next_belt: timeToNextBelt,
      training_time: trainingTime,
      next_promotion: nextPromotion
    };

    return NextResponse.json(statsResponse);
  } catch (error) {
    console.error("Erro ao processar solicitação:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}

function calculateTimeToNextBelt(beltColor: string, degree: number): number {
  const timeMap: Record<string, number[]> = {
    'white': [2, 2, 3, 3, 6],
    'blue': [3, 3, 4, 4, 8],
    'purple': [4, 4, 5, 5, 12],
    'brown': [6, 6, 6, 6, 12],
    'black': [12, 12, 12, 12, 24]
  };

  if (!timeMap[beltColor]) {
    return 12;
  }

  if (degree >= timeMap[beltColor].length - 1) {
    return timeMap[beltColor][timeMap[beltColor].length - 1];
  }

  return timeMap[beltColor][degree];
}

function determineNextPromotion(beltColor: string, degree: number): string {
  const beltProgression = ['white', 'blue', 'purple', 'brown', 'black'];
  const maxDegree = 4;

  if (degree < maxDegree) {
    return `${degree + 1}º Grau`;
  } else {
    const currentIndex = beltProgression.indexOf(beltColor);
    if (currentIndex >= 0 && currentIndex < beltProgression.length - 1) {
      const nextBelt = beltProgression[currentIndex + 1];
      const beltNames: Record<string, string> = {
        'white': 'Branca',
        'blue': 'Azul',
        'purple': 'Roxa',
        'brown': 'Marrom',
        'black': 'Preta'
      };
      return `Faixa ${beltNames[nextBelt] || nextBelt}`;
    }
  }

  return "Próximo nível";
} 