"use client"

import { FilterPopover } from "./filter-popover"
import { motion, AnimatePresence } from "framer-motion"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { X, ChevronUp, ChevronDown } from "lucide-react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useState, useEffect } from "react"
import { Branch, InstructorFilterState, InstructorStats } from "../../types/types"
import { InstructorStatsSection } from "./instructor-stats-section"
import { Belt, beltColorTranslation, BeltColor } from "@/components/belt"

interface ActiveFilter {
  id: string
  type: 'status' | 'specialties' | 'branch' | 'contractType' | 'experienceLevel' | 'belts' | 'date'
  label: string
  value: string
}

interface InstructorFiltersProps {
  filters: InstructorFilterState;
  onFilterChange: (filters: InstructorFilterState) => void;
  branches: Branch[];
  initialStats?: InstructorStats;
}

const filterLabels = {
  status: {
    active: "Ativo",
    inactive: "Inativo",
  },
  specialties: {
    'jiu-jitsu': 'Jiu-Jitsu',
    'muay-thai': 'Muay Thai',
    'boxe': 'Boxe',
    'wrestling': 'Wrestling',
    'judo': 'Judô',
  } as Record<string, string>,
  contractType: {
    'clt': 'CLT',
    'freelancer': 'Freelancer',
    'partner': 'Sócio',
    'volunteer': 'Voluntário',
  },
  experienceLevel: {
    'beginner': 'Iniciante',
    'intermediate': 'Intermediário',
    'advanced': 'Avançado',
    'expert': 'Especialista',
  },
  belts: {
    'white': 'Branca',
    'blue': 'Azul',
    'purple': 'Roxa',
    'brown': 'Marrom',
    'black': 'Preta',
  },
  branch: {} as Record<string, string>,
}

export function FilterBar({ filters, onFilterChange, branches, initialStats }: InstructorFiltersProps) {
  const [activeFilters, setActiveFilters] = useState<ActiveFilter[]>([])
  const [isFilterBarVisible, setIsFilterBarVisible] = useState(false)
  
  useEffect(() => {
    if (branches.length > 0) {
      const branchLabels = branches.reduce((acc, branch) => {
        acc[branch.id] = branch.name;
        return acc;
      }, {} as Record<string, string>);
      
      filterLabels.branch = branchLabels;
    }
  }, [branches]);

  const handleRemoveFilter = (filterId: string) => {
    const [type, value] = filterId.split('-')
    
    setActiveFilters(prev => prev.filter(f => f.id !== filterId))
    
    // Update filter state
    if (type === 'date') {
      onFilterChange({
        ...filters,
        startDate: undefined,
        endDate: undefined,
      })
    } else {
      onFilterChange({
        ...filters,
        [type]: Array.isArray(filters[type as keyof InstructorFilterState])
          ? (filters[type as keyof InstructorFilterState] as string[])?.filter(v => v !== value)
          : undefined,
      })
    }
  }

  const handleFilterChange = (newFilters: InstructorFilterState) => {
    onFilterChange(newFilters)
    const newActiveFilters: ActiveFilter[] = []

    if (newFilters.status?.length) {
      newFilters.status.forEach(status => {
        newActiveFilters.push({
          id: `status-${status}`,
          type: 'status',
          label: 'Status',
          value: filterLabels.status[status as keyof typeof filterLabels.status],
        })
      })
    }

    if (newFilters.specialties?.length) {
      newFilters.specialties.forEach(specialty => {
        newActiveFilters.push({
          id: `specialties-${specialty}`,
          type: 'specialties',
          label: 'Especialidade',
          value: filterLabels.specialties[specialty] || specialty,
        })
      })
    }

    if (newFilters.contractType?.length) {
      newFilters.contractType.forEach(contractType => {
        newActiveFilters.push({
          id: `contractType-${contractType}`,
          type: 'contractType',
          label: 'Contrato',
          value: filterLabels.contractType[contractType as keyof typeof filterLabels.contractType],
        })
      })
    }

    if (newFilters.experienceLevel?.length) {
      newFilters.experienceLevel.forEach(level => {
        newActiveFilters.push({
          id: `experienceLevel-${level}`,
          type: 'experienceLevel',
          label: 'Experiência',
          value: filterLabels.experienceLevel[level as keyof typeof filterLabels.experienceLevel],
        })
      })
    }

    if (newFilters.belts?.length) {
      newFilters.belts.forEach(belt => {
        newActiveFilters.push({
          id: `belts-${belt}`,
          type: 'belts',
          label: 'Faixa',
          value: filterLabels.belts[belt as keyof typeof filterLabels.belts],
        })
      })
    }

    if (newFilters.branch?.length) {
      newFilters.branch.forEach(branch => {
        newActiveFilters.push({
          id: `branch-${branch}`,
          type: 'branch',
          label: 'Filial',
          value: filterLabels.branch[branch],
        })
      })
    }

    if (newFilters.startDate || newFilters.endDate) {
      const dateLabel = newFilters.startDate && newFilters.endDate
        ? `${newFilters.startDate.toLocaleDateString()} - ${newFilters.endDate.toLocaleDateString()}`
        : newFilters.startDate
          ? `A partir de ${newFilters.startDate.toLocaleDateString()}`
          : `Até ${newFilters.endDate!.toLocaleDateString()}`

      newActiveFilters.push({
        id: 'date-range',
        type: 'date',
        label: 'Período',
        value: dateLabel,
      })
    }

    setActiveFilters(newActiveFilters)
  }

  const handleClearAll = () => {
    setActiveFilters([])
    onFilterChange({})
  }

  // Update active filters when filters prop changes
  useEffect(() => {
    handleFilterChange(filters)
  }, [filters])

  const toggleFilterBar = () => {
    setIsFilterBarVisible(prev => !prev)
  }

  return (
    <div className="rounded-lg border bg-card">
      <div className="p-2 flex justify-between items-center">
        <div className="flex items-center">
          <FilterPopover 
            filters={filters}
            onFilterChange={handleFilterChange}
            branches={branches}
          />
          
          {activeFilters.length > 0 && (
            <div className="ml-2 flex flex-wrap gap-1">
              <AnimatePresence>
                {activeFilters.map((filter) => (
                  <motion.div
                    key={filter.id}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{ duration: 0.15 }}
                  >
                    <Badge 
                      variant="secondary"
                      className={cn(
                        "pl-2 h-7 gap-1 pr-1 transition-colors",
                        filter.type === 'status' && "bg-secondary/10 hover:bg-secondary/20",
                        filter.type === 'specialties' && "bg-primary/10 hover:bg-primary/20 text-primary",
                        filter.type === 'contractType' && "bg-accent/10 hover:bg-accent/20",
                        filter.type === 'experienceLevel' && "bg-muted/10 hover:bg-muted/20",
                        filter.type === 'belts' && "bg-amber-50 hover:bg-amber-100 text-amber-800 border-amber-200",
                        filter.type === 'date' && "bg-muted hover:bg-muted/80",
                        filter.type === 'branch' && "bg-primary/10 hover:bg-primary/20 text-primary"
                      )}
                    >
                      <span className="text-xs font-medium opacity-70">
                        {filter.label}:
                      </span>
                      {filter.type === 'belts' ? (
                        <div className="flex items-center gap-1">
                          <Belt color={filter.id.split('-')[1] as BeltColor} size="xs" />
                          <span className="text-xs font-medium">{filter.value}</span>
                        </div>
                      ) : (
                        <span className="text-xs font-medium">
                          {filter.value}
                        </span>
                      )}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5 hover:bg-secondary/20 rounded-full"
                        onClick={() => handleRemoveFilter(filter.id)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  </motion.div>
                ))}
              </AnimatePresence>
              {activeFilters.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 px-2 text-xs text-muted-foreground hover:text-foreground"
                  onClick={handleClearAll}
                >
                  Limpar todos
                </Button>
              )}
            </div>
          )}
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleFilterBar}
          className="h-8 w-8 p-0"
        >
          {isFilterBarVisible ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </Button>
      </div>

      <AnimatePresence>
        {isFilterBarVisible && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="overflow-hidden"
          >
            <Separator />
            <InstructorStatsSection initialStats={initialStats} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
} 