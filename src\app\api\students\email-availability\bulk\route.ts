import { NextResponse } from 'next/server'
import { z } from 'zod'
import { getCurrentUser } from '@/services/auth/actions/auth-actions'
import { createClient } from '@/services/supabase/server'

const bulkSchema = z.object({
  emails: z.array(z.string()).min(1),
})

export async function POST(req: Request) {
  try {
    const body = await req.json()
    const parsed = bulkSchema.safeParse(body)

    if (!parsed.success) {
      return NextResponse.json({ error: 'Corpo da requisição inválido' }, { status: 400 })
    }

    // Normalizar e remover duplicados
    const normalizedEmails = Array.from(new Set(parsed.data.emails.map(e => e.trim().toLowerCase())))

    // Separar válidos e inválidos segundo verificação de formato
    const emailValidation = z.string().email()
    const validEmails: string[] = []
    const invalidEmails: string[] = []
    normalizedEmails.forEach(email => {
      if (emailValidation.safeParse(email).success) {
        validEmails.push(email)
      } else {
        invalidEmails.push(email)
      }
    })

    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return NextResponse.json({ error: 'Usuário não autenticado' }, { status: 401 })
    }

    const supabase = await createClient()

    // Identificar tenant do usuário
    const { data: userTenant, error: tenantError } = await supabase
      .from('users')
      .select('tenant_id')
      .eq('id', currentUser.id)
      .single()

    if (tenantError || !userTenant?.tenant_id) {
      return NextResponse.json({ error: 'Não foi possível determinar o tenant' }, { status: 500 })
    }

    // Consultar todos os usuários com e-mails na lista dentro desse tenant
    const { data: existingEmails, error } = await supabase
      .from('users')
      .select('email')
      .eq('tenant_id', userTenant.tenant_id)
      .in('email', validEmails.length > 0 ? validEmails : ['__dummy__'])

    if (error) {
      console.error('Erro ao consultar usuários para verificação de e-mail:', error)
      return NextResponse.json({ error: 'Erro ao verificar e-mails' }, { status: 500 })
    }

    const unavailable = existingEmails?.map(u => u.email.toLowerCase()) || []

    // Unavailable também inclui e-mails com formato inválido, pois devem ser marcados como erro
    const combinedUnavailable = Array.from(new Set([...unavailable, ...invalidEmails]))

    return NextResponse.json({ unavailable: combinedUnavailable })
  } catch (err) {
    console.error('Erro inesperado na verificação em lote de e-mails:', err)
    return NextResponse.json({ error: 'Erro inesperado' }, { status: 500 })
  }
} 