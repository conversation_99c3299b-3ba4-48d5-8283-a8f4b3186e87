"use server";

import { createClient } from "@/services/supabase/server";
import { ClassGroupFilterSchema } from "../schemas/class-group";
import { validateUserAuthentication } from "./shared/validation-helpers";
import type { ClassGroupWithDetails, PaginatedResult } from "../../../aulas/types";

/**
 * Lista turmas com filtros e paginação
 */
export async function getClassGroups(filters: unknown): Promise<{ 
  success: boolean; 
  data?: PaginatedResult<ClassGroupWithDetails>; 
  errors?: any 
}> {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;

    const validationResult = ClassGroupFilterSchema.safeParse(filters);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const {
      search,
      instructor_id,
      branch_id,
      category,
      is_active,
      min_belt_level,
      max_belt_level,
      has_availability,
      start_date_from,
      start_date_to,
      page,
      limit,
      sort_by,
      sort_order,
    } = validationResult.data;

    const supabase = await createClient();
    
    // Função para aplicar filtros a qualquer query
    const applyFilters = (query: any) => {
      query = query.eq("tenant_id", tenantId);

      if (search) {
        query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
      }

      if (instructor_id) {
        query = query.eq("instructor_id", instructor_id);
      }

      if (branch_id) {
        query = query.eq("branch_id", branch_id);
      }

      if (category) {
        query = query.eq("category", category);
      }

      if (is_active !== undefined) {
        query = query.eq("is_active", is_active);
      }

      if (min_belt_level) {
        query = query.gte("min_belt_level", min_belt_level);
      }

      if (max_belt_level) {
        query = query.lte("max_belt_level", max_belt_level);
      }

      if (start_date_from) {
        query = query.gte("start_date", start_date_from.split('T')[0]);
      }

      if (start_date_to) {
        query = query.lte("start_date", start_date_to.split('T')[0]);
      }

      return query;
    };

    // Query principal com select completo
    let query = supabase
      .from("class_groups")
      .select(`
        *,
        instructor:users(id, first_name, last_name, full_name, avatar_url),
        branch:branches(id, name),
        class_group_enrollments(id, status),
        class_waitlist(id),
        classes(id)
      `);

    // Aplicar filtros à query principal
    query = applyFilters(query);

    // Aplicar ordenação
    const orderByField = sort_by === "instructor_name" ? "users.first_name" : sort_by;
    query = query.order(orderByField, { ascending: sort_order === "asc" });

    // Query de contagem com os mesmos filtros
    let countQuery = supabase
      .from("class_groups")
      .select("*", { count: "exact", head: true });

    // Aplicar os mesmos filtros à query de contagem
    countQuery = applyFilters(countQuery);

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error("Erro ao contar turmas:", countError);
      return { success: false, errors: { _form: "Erro ao buscar turmas" } };
    }

    // Aplicar paginação
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    const { data: classGroups, error } = await query;

    if (error) {
      console.error("Erro ao buscar turmas:", error);
      return { success: false, errors: { _form: "Erro ao buscar turmas" } };
    }

    // Processar dados para incluir contadores e cálculos
    const processedGroups: ClassGroupWithDetails[] = classGroups.map((group: any) => ({
      ...group,
      _count: {
        enrollments: group.class_group_enrollments?.filter((e: any) => e.status === 'active')?.length || 0,
        waitlist: group.class_waitlist?.length || 0,
        classes: group.classes?.length || 0,
      },
      current_enrollment_count: group.class_group_enrollments?.filter((e: any) => e.status === 'active')?.length || 0,
      capacity_usage_percentage: group.max_capacity 
        ? Math.round(((group.class_group_enrollments?.filter((e: any) => e.status === 'active')?.length || 0) / group.max_capacity) * 100)
        : null,
    }));

    // Filtrar por disponibilidade se solicitado (aplicado no lado do cliente por limitações do Supabase)
    let finalGroups = processedGroups;
    let adjustedCount = count || 0;

    if (has_availability !== undefined) {
      finalGroups = processedGroups.filter(group => {
        const hasSpace = !group.max_capacity || group.current_enrollment_count! < group.max_capacity;
        return has_availability ? hasSpace : !hasSpace;
      });
      // Ajustar a contagem para refletir o filtro de disponibilidade
      adjustedCount = finalGroups.length;
    }

    const totalPages = Math.ceil(adjustedCount / limit);

    return {
      success: true,
      data: {
        data: finalGroups,
        pagination: {
          page,
          limit,
          total: adjustedCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    };
  } catch (error) {
    console.error("Erro ao buscar turmas:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 