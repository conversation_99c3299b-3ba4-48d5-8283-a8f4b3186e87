import { cookies } from 'next/headers';

interface Props {
  children: React.ReactNode;
  params: {
    slug: string;
  };
}

export default async function TenantLayout({ children, params }: Props) {
  const { slug } = params;
  
  // Definir cookie para o host atual
  const cookieStore = await cookies();
  cookieStore.set('host', `${slug}.${process.env.NEXT_PUBLIC_BASE_DOMAIN}`, {
    httpOnly: false,
    path: '/',
    secure: process.env.NODE_ENV === 'production',
    maxAge: 60 * 60 * 24 // 1 dia
  });
  
  console.log(`Definindo cookie de host: ${slug}.${process.env.NEXT_PUBLIC_BASE_DOMAIN}`);
  console.log(`Tentando acessar tenant com slug: ${slug}`);
  
  return (
    <>
      {children}
    </>
  );
} 