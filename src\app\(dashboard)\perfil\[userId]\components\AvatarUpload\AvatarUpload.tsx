'use client';

import { AvatarUploadRoot } from './AvatarUploadRoot';
import { AvatarUploadPreview } from './AvatarUploadPreview';
import { AvatarUploadButton } from './AvatarUploadButton';
import { cn } from '@/lib/utils';

interface AvatarUploadProps {
  userId: string;
  name: string;
  avatarUrl?: string | null;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  onAvatarUpdated?: (url: string) => void;
}

export const AvatarUpload = ({
  userId,
  name,
  avatarUrl,
  className,
  size = 'md',
  onAvatarUpdated
}: AvatarUploadProps) => {
  return (
    <div className={cn('flex flex-col items-center', className)}>
      <AvatarUploadRoot userId={userId} initialAvatarUrl={avatarUrl}>
        <div className="relative">
          <AvatarUploadPreview name={name} size={size} />
          <AvatarUploadButton 
            userId={userId} 
            onAvatarUpdated={onAvatarUpdated}
          />
        </div>
      </AvatarUploadRoot>
    </div>
  );
};

export const AvatarUploadComponents = {
  Root: AvatarUploadRoot,
  Preview: AvatarUploadPreview,
  Button: AvatarUploadButton
}; 