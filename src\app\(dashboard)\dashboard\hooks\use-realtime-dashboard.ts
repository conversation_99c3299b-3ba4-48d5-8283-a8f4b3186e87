'use client';

import { useState, useEffect, useCallback } from 'react';
import { DashboardData } from '../types';
import { getDashboardData } from '../actions/dashboard-actions';
import { useAdaptivePolling } from './use-adaptive-polling';

interface UseRealtimeDashboardProps {
  tenantId: string;
  initialData?: DashboardData;
  pollingInterval?: number; // Intervalo em ms, padrão 30 segundos
  enablePolling?: boolean; // Permitir desabilitar polling
}

interface UseRealtimeDashboardReturn {
  data: DashboardData | null;
  isLoading: boolean;
  error: string | null;
  isConnected: boolean;
  isActive: boolean;
  lastUpdated: Date | null;
  currentInterval: number;
  refreshData: () => Promise<void>;
  togglePolling: () => void;
}

export function useRealtimeDashboard({
  tenantId,
  initialData,
  pollingInterval = 30000, // 30 segundos por padrão
  enablePolling = true
}: UseRealtimeDashboardProps): UseRealtimeDashboardReturn {
  const [data, setData] = useState<DashboardData | null>(initialData || null);
  const [isLoading, setIsLoading] = useState(!initialData);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const refreshData = useCallback(async () => {
    if (!tenantId) return;
    
    try {
      setError(null);
      const newData = await getDashboardData(tenantId);
      setData(newData);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Erro ao atualizar dados do dashboard:', err);
      setError('Falha ao carregar dados do dashboard');
    } finally {
      setIsLoading(false);
    }
  }, [tenantId]);

  // Configurar polling adaptativo
  const polling = useAdaptivePolling({
    onPoll: refreshData,
    baseInterval: pollingInterval,
    activeInterval: pollingInterval * 0.5, // Mais rápido quando ativo
    inactiveInterval: pollingInterval * 2, // Mais lento quando inativo
    enabled: enablePolling
  });

  // Carregar dados iniciais se não fornecidos
  useEffect(() => {
    if (!initialData && tenantId) {
      refreshData();
    }
  }, [initialData, tenantId, refreshData]);

  return {
    data,
    isLoading,
    error,
    isConnected: polling.isPolling,
    isActive: polling.isActive,
    lastUpdated,
    currentInterval: polling.currentInterval,
    refreshData: polling.pollNow,
    togglePolling: polling.togglePolling
  };
} 