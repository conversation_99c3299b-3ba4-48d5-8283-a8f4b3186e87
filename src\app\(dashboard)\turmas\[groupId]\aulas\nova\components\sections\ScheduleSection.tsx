'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Clock, Loader2, Timer } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FieldErrors, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { CreateClassFormData } from '../schema';
import { FormDuration } from '../types';

interface ScheduleSectionProps {
  watch: UseFormWatch<CreateClassFormData>;
  setValue: UseFormSetValue<CreateClassFormData>;
  errors: FieldErrors<CreateClassFormData>;
  isCheckingConflicts: boolean;
  hasTimeConflict: boolean;
  duration: FormDuration | null;
  onStartTimeChange: (value: string) => void;
  getErrorMessage: (error: any) => string;
}

export function ScheduleSection({ 
  watch,
  setValue,
  errors, 
  isCheckingConflicts, 
  hasTimeConflict, 
  duration, 
  onStartTimeChange, 
  getErrorMessage 
}: ScheduleSectionProps) {
  const startTimeValue = watch('start_time');
  const endTimeValue = watch('end_time');

  const handleStartTimeChange = (value: string) => {
    setValue('start_time', value, { shouldDirty: true, shouldTouch: true, shouldValidate: true });
    onStartTimeChange(value);
  };

  const handleEndTimeChange = (value: string) => {
    setValue('end_time', value, { shouldDirty: true, shouldTouch: true, shouldValidate: true });
  };

  return (
    <Card className={`transition-all duration-200 ${(errors.start_time || errors.end_time || hasTimeConflict) ? 'ring-2 ring-destructive/20' : ''}`}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Clock className="h-5 w-5" />
          Horários
          {(errors.start_time || errors.end_time) && <AlertCircle className="h-4 w-4 text-destructive" />}
          {isCheckingConflicts && <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="start_time">Data e Hora de Início *</Label>
            <Input
              id="start_time"
              type="datetime-local"
              value={startTimeValue}
              onChange={(e) => handleStartTimeChange(e.target.value)}
              className={errors.start_time ? 'border-destructive' : ''}
              aria-describedby={errors.start_time ? 'start_time-error' : undefined}
            />
            {errors.start_time && (
              <p id="start_time-error" className="text-sm text-destructive" role="alert">
                {getErrorMessage(errors.start_time)}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="end_time">Data e Hora de Fim *</Label>
            <Input
              id="end_time"
              type="datetime-local"
              value={endTimeValue}
              onChange={(e) => handleEndTimeChange(e.target.value)}
              className={errors.end_time ? 'border-destructive' : ''}
              aria-describedby={errors.end_time ? 'end_time-error' : undefined}
            />
            {errors.end_time && (
              <p id="end_time-error" className="text-sm text-destructive" role="alert">
                {getErrorMessage(errors.end_time)}
              </p>
            )}
          </div>
        </div>

        {/* Verificação de conflitos */}
        {isCheckingConflicts && (
          <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg border">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              Verificando conflitos de horário...
            </span>
          </div>
        )}

        {hasTimeConflict && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Conflito de horário detectado! O instrutor já possui uma aula agendada neste horário.
            </AlertDescription>
          </Alert>
        )}

        {/* Mostrar duração calculada */}
        {duration && (
          <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg border">
            <Timer className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              Duração da aula: <span className="font-medium text-foreground">{duration.text}</span>
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 