'use client';

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle2, User, PlusCircle, GraduationCap, Eye, EyeOff, Co<PERSON>, Check, Key } from "lucide-react";
import { useToast } from "@/hooks/ui/use-toast";

interface FormSuccessProps {
  instructorId: string | null;
  userId: string | null;
  temporaryPassword?: string;
  onViewProfile: () => void;
  onAddAnother: () => void;
}

export default function FormSuccess({ 
  instructorId, 
  userId,
  temporaryPassword,
  onViewProfile, 
  onAddAnother 
}: FormSuccessProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [copiedPassword, setCopiedPassword] = useState(false);
  const { toast } = useToast();

  const handleCopyPassword = async () => {
    if (!temporaryPassword) return;
    
    try {
      await navigator.clipboard.writeText(temporaryPassword);
      setCopiedPassword(true);
      toast({
        title: "Senha copiada!",
        description: "A senha temporária foi copiada para a área de transferência.",
      });
      
      setTimeout(() => setCopiedPassword(false), 2000);
    } catch (error) {
      toast({
        title: "Erro ao copiar",
        description: "Não foi possível copiar a senha.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-full">
            <CheckCircle2 className="h-12 w-12 text-green-600 dark:text-green-400" />
          </div>
        </div>
        <CardTitle className="text-2xl">Instrutor Criado com Sucesso</CardTitle>
        <CardDescription>
          O novo instrutor foi cadastrado com sucesso no sistema e já pode acessar a plataforma.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {temporaryPassword && (
          <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 p-4 rounded-md">
            <div className="flex items-center mb-2">
              <Key className="w-4 h-4 mr-2 text-amber-600 dark:text-amber-400" />
              <h3 className="font-medium text-amber-800 dark:text-amber-200">Senha Temporária</h3>
            </div>
            <p className="text-sm text-amber-700 dark:text-amber-300 mb-3">
              Esta senha deve ser compartilhada com o instrutor para o primeiro acesso. 
              Recomendamos que seja alterada no primeiro login.
            </p>
            
            <div className="flex items-center gap-2 bg-white dark:bg-gray-950 p-3 rounded border border-gray-200 dark:border-gray-700">
              <code className="flex-1 font-mono text-sm text-gray-900 dark:text-gray-100">
                {showPassword ? temporaryPassword : '•'.repeat(temporaryPassword.length)}
              </code>
              
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setShowPassword(!showPassword)}
                className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
              
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleCopyPassword}
                className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
                disabled={copiedPassword}
              >
                {copiedPassword ? <Check className="h-4 w-4 text-green-600 dark:text-green-400" /> : <Copy className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        )}
        
        <div className="bg-muted p-4 rounded-md">
          <h3 className="font-medium mb-2 flex items-center">
            <GraduationCap className="w-4 h-4 mr-2" />
            Próximos passos sugeridos:
          </h3>
          <ul className="list-disc pl-5 space-y-1 text-sm">
            <li>Informar ao instrutor sobre suas credenciais de acesso</li>
            <li>Orientar a alteração da senha no primeiro login</li>
            <li>Configurar horários de aulas do instrutor</li>
            <li>Atribuir turmas e responsabilidades</li>
            <li>Verificar permissões e acessos no sistema</li>
            <li>Configurar dados de pagamento (se aplicável)</li>
          </ul>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col sm:flex-row gap-3 justify-center">
        <Button 
          onClick={onViewProfile} 
          className="w-full sm:w-auto"
          disabled={!userId}
        >
          <User className="mr-2 h-4 w-4" />
          Ver Perfil do Instrutor
        </Button>
        <Button 
          onClick={onAddAnother} 
          variant="outline" 
          className="w-full sm:w-auto"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Adicionar Outro Instrutor
        </Button>
      </CardFooter>
    </Card>
  );
} 