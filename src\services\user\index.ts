export * from './user-client'

// Exporta tipos compartilhados que podem ser usados em qualquer contexto
export * from './user-types'

// Exportar serviços de status de usuário
export { updateUserStatus, getUserStatus, validateUserActiveStatus } from './status-service'

// Exportar queries atualizadas
export { getUsersWithStatus, getStudentsWithUserStatus, getInstructorsWithUserStatus, getUsersByStatus, getActiveUsers, getInactiveUsers, getSuspendedUsers, getUserById, updateUserBasicInfo } from './queries'

// Re-export apenas funções server-side específicas quando necessário
// export { functionName } from './user-service';
// export { anotherFunction } from './role-service';
