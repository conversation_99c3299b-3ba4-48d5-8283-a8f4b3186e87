"use server";

import { revalidatePath } from "next/cache";
import { createAdminClient } from "@/services/supabase/server";
import { z } from "zod";
import { validateUserAuthentication, validateClassGroup } from "./shared/validation-helpers";

const RemoveFromWaitlistSchema = z.object({
  class_group_id: z.string().uuid({ message: 'ID da turma é obrigatório' }),
  student_id: z.string().uuid({ message: 'ID do aluno é obrigatório' }),
});

/**
 * Remove um aluno da lista de espera de uma turma
 */
export async function removeFromWaitlist(data: unknown) {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;

    const validationResult = RemoveFromWaitlistSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const { class_group_id, student_id } = validationResult.data;
    const supabase = await createAdminClient();

    // Verificar se a turma existe
    const classGroupValidation = await validateClassGroup(class_group_id, tenantId);
    if (!classGroupValidation.success) {
      return classGroupValidation;
    }

    // Verificar se o aluno está na lista de espera
    const { data: waitlistEntry, error: checkError } = await supabase
      .from("class_waitlist")
      .select("id, position")
      .eq("class_group_id", class_group_id)
      .eq("student_id", student_id)
      .eq("tenant_id", tenantId)
      .eq("status", "waiting")
      .single();

    if (checkError || !waitlistEntry) {
      return { success: false, errors: { _form: "Aluno não encontrado na lista de espera" } };
    }

    // Remover da lista de espera
    const { error: removeError } = await supabase
      .from("class_waitlist")
      .update({ status: "removed" })
      .eq("id", waitlistEntry.id);

    if (removeError) {
      console.error("Erro ao remover da lista de espera:", removeError);
      return { success: false, errors: { _form: "Erro ao remover aluno da lista de espera" } };
    }

    // Reordenar posições dos alunos restantes
    const { error: reorderError } = await supabase
      .from("class_waitlist")
      .update({ position: supabase.raw('position - 1') })
      .eq("class_group_id", class_group_id)
      .eq("tenant_id", tenantId)
      .eq("status", "waiting")
      .gt("position", waitlistEntry.position);

    if (reorderError) {
      console.error("Erro ao reordenar lista de espera:", reorderError);
      // Não retorna erro pois a remoção foi bem-sucedida
    }

    revalidatePath("/aulas");

    return {
      success: true,
      message: "Aluno removido da lista de espera com sucesso"
    };
  } catch (error) {
    console.error("Erro ao remover da lista de espera:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 