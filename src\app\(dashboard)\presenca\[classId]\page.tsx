import { Suspense, memo } from 'react';
import { notFound } from 'next/navigation';
import { getClassById, getAttendanceByClass } from '../../aulas/actions';
import { DismissibleAlert } from './components/DismissibleAlert';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { UserAvatar } from '@/components/ui/user-avatar';
import { BeltDisplay } from '@/components/belt';
import { checkAttendancePermission, checkResourceAccess } from '@/services/permissions/utils/role-verification';
import { ErrorWrapper } from './components/ErrorWrapper';

// Imports otimizados
import {
  UsersIcon,
  QrCodeIcon,
  FileTextIcon,
  navigationIconProps
} from '@/components/icons/attendance-icons';
import { formatErrorMessage } from '@/lib/error-utils';
import {
  getClassStatus,
  isClassActive
} from '@/lib/date-utils';
import {
  ClassAttendanceHeader,
  ClassInfoCard,
  ClassAttendanceSkeleton
} from './components/OptimizedComponents';
import {
  LazyQRCodeSection,
  LazyAttendanceList,
  ReportSkeleton
} from './components/LazyComponents';

// Funções utilitárias movidas para módulos separados - imports otimizados

// CapacityDisplay movido para OptimizedComponents

interface ClassAttendancePageProps {
  params: Promise<{
    classId: string;
  }>;
  searchParams: Promise<{
    tab?: 'lista' | 'qr' | 'relatorio';
    search?: string;
  }>;
}

export default async function ClassAttendancePage({ params, searchParams }: ClassAttendancePageProps) {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;
  const activeTab = resolvedSearchParams.tab || 'lista';

  return (
    <div className="space-y-6">
      <Suspense fallback={<ClassAttendanceSkeleton />}>
        <ClassAttendanceContent
          classId={resolvedParams.classId}
          activeTab={activeTab}
        />
      </Suspense>
    </div>
  );
}

async function ClassAttendanceContent({
  classId,
  activeTab
}: {
  classId: string;
  activeTab: string;
}) {
  // Verificar permissões antes de carregar dados
  const userContext = await checkAttendancePermission();
  
  // Buscar dados da aula
  const classResult = await getClassById(classId);

  if (!classResult.success || !classResult.data) {
    notFound();
  }

  const classData = classResult.data;
  
  // Verificar se o usuário pode acessar esta aula específica
  const hasAccess = await checkResourceAccess(classId, 'class', userContext);
  if (!hasAccess) {
    notFound();
  }
  
  // Usar funções otimizadas
  const status = getClassStatus(classData.start_time, classData.end_time, classData.status);
  const isActive = isClassActive(classData.start_time, classData.end_time, classData.status);
  // Debug removido para otimização




  return (
    <div className="space-y-6">
      {/* Header otimizado */}
      <ClassAttendanceHeader classId={classId} isActive={isActive} />

      {/* Card de informações otimizado */}
      <ClassInfoCard classData={classData} />

      {/* Tabs otimizadas com lazy loading */}
      <Tabs value={activeTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="lista" asChild>
            <Link href={`/presenca/${classId}?tab=lista`} className="flex items-center gap-2">
              <UsersIcon {...navigationIconProps} />
              Lista de Presença
            </Link>
          </TabsTrigger>
          <TabsTrigger value="qr" asChild disabled={!isActive}>
            <Link href={`/presenca/${classId}?tab=qr`} className="flex items-center gap-2">
              <QrCodeIcon {...navigationIconProps} />
              QR Code
            </Link>
          </TabsTrigger>
          <TabsTrigger value="relatorio" asChild>
            <Link href={`/presenca/${classId}?tab=relatorio`} className="flex items-center gap-2">
              <FileTextIcon {...navigationIconProps} />
              Relatório
            </Link>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="lista" className="space-y-4">
          <Suspense fallback={<div className="text-muted-foreground">Carregando lista de presença...</div>}>
            <AttendanceListWrapper
              classId={classId}
              canCheckIn={isActive}
              classData={classData}
              isVisible={activeTab === 'lista'}
            />
          </Suspense>
        </TabsContent>

        <TabsContent value="qr" className="space-y-4">
          <LazyQRCodeSection
            classId={classId}
            classGroupId={classData.class_group_id}
            tenantId={classData.tenant_id}
            isActive={isActive}
            status={status === 'cancelled' || status === 'rescheduled' ? 'completed' : status}
            isVisible={activeTab === 'qr'}
          />
        </TabsContent>

        <TabsContent value="relatorio" className="space-y-4">
          <Suspense fallback={<ReportSkeleton />}>
            <AttendanceReportContent classId={classId} isVisible={activeTab === 'relatorio'} />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  );
}

const AttendanceReportContent = memo<{ classId: string; isVisible?: boolean }>(async ({ classId, isVisible = true }) => {
  // Só renderizar quando visível
  if (!isVisible) return null;
  // Buscar dados da aula primeiro
  const classResult = await getClassById(classId);
  
  if (!classResult.success || !classResult.data) {
    return (
      <ErrorWrapper
        title="Erro ao carregar dados da aula"
        message="Não foi possível carregar as informações da aula."
        actionType="reload"
        actionLabel="Tentar novamente"
      />
    );
  }

  const classData = classResult.data;

  const attendanceResult = await getAttendanceByClass({
    class_id: classId,
    page: 1,
    limit: 100
  });

  if (!attendanceResult.success) {
    const errorMessage = formatErrorMessage(attendanceResult.errors);
    
    return (
      <ErrorWrapper
        title="Erro ao carregar relatório"
        message={errorMessage}
        actionType="reload"
        actionLabel="Tentar novamente"
      />
    );
  }

  const attendances = attendanceResult.data?.data || [];
  const present = attendances.length;
  
  // Buscar o total correto de alunos matriculados
  let total = 0;
  
  if (classData.class_group_id) {
    try {
      const { getClassGroupEnrollments } = await import('../../turmas/actions/class-group/get-class-group-enrollments');
      const enrollmentsResult = await getClassGroupEnrollments(classData.class_group_id, { status: 'active' });
      
      if (enrollmentsResult.success && 'data' in enrollmentsResult && enrollmentsResult.data) {
        total = enrollmentsResult.data.length;
      }
    } catch (error) {
      console.error('Erro ao buscar matrículas:', error);
      // Fallback para capacidade máxima se disponível
      total = classData.max_capacity || present;
    }
  } else {
    // Para aulas livres, usar o número de presentes como total (não há matrículas)
    total = present;
  }
  
  const rate = total > 0 ? Math.round((present / total) * 100) : 0;

  return (
    <div className="space-y-4">
      {/* Estatísticas com melhor contraste */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-l-4 border-l-primary">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-foreground">Total de Alunos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{total}</div>
            <p className="text-xs text-muted-foreground">Matriculados/Inscritos</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500 dark:border-l-green-400">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-foreground">Presentes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">{present}</div>
            <p className="text-xs text-muted-foreground">Check-in realizado</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-500 dark:border-l-blue-400">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-foreground">Taxa de Presença</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{rate}%</div>
            <p className="text-xs text-muted-foreground">Da capacidade total</p>
          </CardContent>
        </Card>
      </div>

      {/* Lista de presentes */}
      {attendances.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg text-foreground">Alunos Presentes</CardTitle>
            <CardDescription>Lista completa de check-ins realizados</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {attendances.map((attendance) => (
                <div key={attendance.id} className="flex items-center justify-between p-4 border border-border rounded-lg bg-card hover:bg-accent/20 transition-colors">
                  <div className="flex items-center gap-4">
                    <UserAvatar 
                      src={attendance.student?.user?.avatar_url} 
                      name={attendance.student?.user?.full_name || attendance.student?.user?.first_name || 'Aluno'}
                      size="lg"
                      className="h-12 w-12"
                    />
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      <div className="min-w-0 flex-1">
                        <span className="font-medium text-foreground truncate block">
                          {attendance.student?.user?.full_name || attendance.student?.user?.first_name || 'Nome não disponível'}
                        </span>
                        {attendance.student?.current_belt && (
                          <div className="mt-1">
                            <BeltDisplay 
                              belt={attendance.student.current_belt.belt_color as any}
                              stripes={Math.max(0, (attendance.student.current_belt.degree || 1) - 1)}
                              size="md"
                              showTranslation
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground flex-shrink-0">
                    <div className="text-right">
                      <Badge 
                        variant="default" 
                        className="bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50 border-green-200 dark:border-green-800"
                      >
                        Presente
                      </Badge>
                      <p className="text-xs text-muted-foreground mt-1">
                        Check-in: {new Date(attendance.checked_in_at).toLocaleTimeString('pt-BR', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Estado vazio */}
      {attendances.length === 0 && (
        <Card>
          <CardContent className="pt-6 text-center">
            <UsersIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2 text-foreground">Nenhuma presença registrada</h3>
            <p className="text-muted-foreground">
              Ainda não há check-ins para esta aula.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
});
AttendanceReportContent.displayName = 'AttendanceReportContent';

const AttendanceListWrapper = memo<{
  classId: string;
  canCheckIn: boolean;
  classData?: any;
  isVisible?: boolean;
}>(async ({ classId, canCheckIn, classData, isVisible = true }) => {
  // Só renderizar quando visível
  if (!isVisible) return null;
  // NOVA IMPLEMENTAÇÃO: Usar as funções SQL que corrigem o problema de presença
  // Agora apenas alunos matriculados ANTES da aula são considerados elegíveis
  
  // Se não recebeu os dados da aula, buscar
  let actualClassData = classData;
  if (!actualClassData) {
    const classResult = await getClassById(classId);
    
    if (!classResult.success || !classResult.data) {
      return (
        <ErrorWrapper
          title="Erro ao carregar dados da aula"
          message="Não foi possível carregar as informações da aula. Verifique se a aula existe e tente novamente."
          actionType="redirect"
          redirectPath="/presenca"
          actionLabel="Voltar para lista de aulas"
        />
      );
    }
    actualClassData = classResult.data;
  }

  // Usar as novas funções SQL que resolvem o problema de presença corretamente
  try {
    const { createClient } = await import('@/services/supabase/server');
    const supabase = await createClient();

    // Buscar alunos elegíveis (apenas matriculados antes da aula e não pausados)
    const { data: eligibleStudents, error: eligibleError } = await supabase.rpc('get_eligible_students_for_class', {
      p_class_id: classId,
      p_include_paused: false  // Excluir alunos pausados durante a aula
    });

    if (eligibleError) {
      console.error('Erro ao buscar alunos elegíveis:', eligibleError);
      throw new Error('Erro ao buscar alunos elegíveis');
    }

    // Buscar registros de presença
    const attendanceResult = await getAttendanceByClass({
      class_id: classId,
      page: 1,
      limit: 100
    });

    if (!attendanceResult.success) {
      const errorMessage = formatErrorMessage(attendanceResult.errors);
      
      return (
        <ErrorWrapper
          title="Erro ao carregar lista de presença"
          message={errorMessage}
          actionType="reload"
          actionLabel="Tentar novamente"
        />
      );
    }

    const attendanceRecords = attendanceResult.data?.data || [];
    
    // Converter formato dos alunos elegíveis para o formato esperado pelo componente AttendanceList
    const enrolledStudents = (eligibleStudents || []).map((student: any) => ({
      id: student.student_id,
      user: {
        first_name: student.first_name || '',
        last_name: student.last_name || null,
        full_name: student.full_name || null,
        email: student.email || '',
        avatar_url: student.avatar_url || null,
      },
      check_in_code: student.check_in_code || null,
      current_belt: student.current_belt ? {
        belt_color: student.current_belt.belt_color,
        degree: student.current_belt.degree
      } : null,
      enrollment_id: student.enrollment_id,
      is_currently_paused: student.is_currently_paused,
      was_paused_during_class: student.was_paused_during_class,
      pause_reason: student.pause_reason
    }));

    return (
      <div className="space-y-4">
        {/* Alerta quando check-in não está disponível */}
        <DismissibleAlert show={!canCheckIn} />

        {/* Informação sobre a correção implementada (pode ser removida após verificação) */}
        {/* {process.env.NODE_ENV === 'development' && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <p className="text-sm font-medium text-green-800 dark:text-green-200">
                ✅ Correção aplicada: Lista agora mostra apenas alunos matriculados antes da data da aula
              </p>
            </div>
            <p className="text-xs text-green-700 dark:text-green-300 mt-1">
              Total de alunos elegíveis: {enrolledStudents.length} 
              {actualClassData.class_group_id ? '' : ' (aula livre - sem turma)'}
            </p>
          </div>
        )} */}

        <LazyAttendanceList
          classId={classId}
          attendanceRecords={attendanceRecords}
          enrolledStudents={enrolledStudents}
          canCheckIn={canCheckIn}
          isVisible={isVisible}
        />
      </div>
    );

  } catch (error) {
    console.error('Erro ao carregar dados de presença:', error);
    
    return (
      <ErrorWrapper
        title="Erro ao carregar dados de presença"
        message="Ocorreu um erro ao carregar os dados de presença. Tente novamente."
        actionType="reload"
        actionLabel="Tentar novamente"
      />
    );
  }
});
AttendanceListWrapper.displayName = 'AttendanceListWrapper';