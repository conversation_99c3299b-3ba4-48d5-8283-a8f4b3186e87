export const countries = [
  { code: 'BR', name: 'Brasil', phoneCode: '+55', flag: '🇧🇷' },
  // { code: 'US', name: 'Estados Unidos', phoneCode: '+1', flag: '🇺🇸' },
  // { code: 'PT', name: 'Portugal', phoneCode: '+351', flag: '🇵🇹' },
  // { code: 'AR', name: 'Argentina', phoneCode: '+54', flag: '🇦🇷' },
  // { code: 'UY', name: 'Uruguai', phoneCode: '+598', flag: '🇺🇾' },
  // { code: 'CL', name: 'Chile', phoneCode: '+56', flag: '🇨🇱' },
  // { code: 'CO', name: 'Colômbia', phoneCode: '+57', flag: '🇨🇴' },
  // { code: 'PE', name: 'Peru', phoneCode: '+51', flag: '🇵🇪' },
  // { code: 'PY', name: 'Paragua<PERSON>', phoneCode: '+595', flag: '🇵🇾' },
  // { code: 'BO', name: 'Bolívia', phoneCode: '+591', flag: '🇧🇴' },
  // { code: 'VE', name: 'Venezuela', phoneCode: '+58', flag: '🇻🇪' },
  // { code: 'MX', name: 'México', phoneCode: '+52', flag: '🇲🇽' },
  // { code: 'ES', name: 'Espanha', phoneCode: '+34', flag: '🇪🇸' },
  // { code: 'IT', name: 'Itália', phoneCode: '+39', flag: '🇮🇹' },
  // { code: 'FR', name: 'França', phoneCode: '+33', flag: '🇫🇷' },
  // { code: 'DE', name: 'Alemanha', phoneCode: '+49', flag: '🇩🇪' },
  // { code: 'GB', name: 'Reino Unido', phoneCode: '+44', flag: '🇬🇧' },
  // { code: 'JP', name: 'Japão', phoneCode: '+81', flag: '🇯🇵' },
  // { code: 'CN', name: 'China', phoneCode: '+86', flag: '🇨🇳' },
  // { code: 'AU', name: 'Austrália', phoneCode: '+61', flag: '🇦🇺' },
  // { code: 'CA', name: 'Canadá', phoneCode: '+1', flag: '🇨🇦' },
]; 