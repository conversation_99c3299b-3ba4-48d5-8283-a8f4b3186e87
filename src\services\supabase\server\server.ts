'use server';

import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import { getSupabaseConfig, getSupabaseServiceRoleKey } from "@/config/supabase";

export const createClient = async () => {
  try {
    const cookieStore = await cookies();
    const { url, anonKey } = getSupabaseConfig();

    return createServerClient(
      url,
      anonKey,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(() => {
                // Cookies will be managed by middleware
              });
            } catch (error) {
              // Handle cookie processing errors silently
            }
          },
        },
        auth: {
          persistSession: true,
          autoRefreshToken: true,
          detectSessionInUrl: false,
        },
      },
    );
  } catch (error) {
    console.error('Erro ao criar cliente Supabase:', error);
    throw error;
  }
};

export const createAdminClient = async () => {
  try {
    const cookieStore = await cookies();
    const { url } = getSupabaseConfig();
    const serviceKey = getSupabaseServiceRoleKey();

    return createServerClient(
      url,
      serviceKey,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(() => {
                // Cookies will be managed by middleware
              });
            } catch (error) {
              // Handle cookie processing errors silently
            }
          },
        },
        auth: {
          persistSession: true,
          autoRefreshToken: true,
          detectSessionInUrl: false,
        },
      },
    );
  } catch (error) {
    console.error('Erro ao criar cliente admin do Supabase:', error);
    throw error;
  }
}; 