"use server";

import { createClient } from "@/services/supabase/server";
import { ClassGroupStatsRequestSchema } from "../schemas/class-group";
import { validateUserAuthentication, validateClassGroup } from "./shared/validation-helpers";

/**
 * Obtém estatísticas de uma turma
 */
export async function getClassGroupStats(data: unknown) {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;

    const validationResult = ClassGroupStatsRequestSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const { class_group_id, period_start, period_end } = validationResult.data;
    const supabase = await createClient();

    // Verificar se a turma existe
    const classGroupValidation = await validateClassGroup(class_group_id!, tenantId);
    if (!classGroupValidation.success) {
      return classGroupValidation;
    }

    const { classGroup } = classGroupValidation;

    // Estatísticas básicas
    const [
      { count: totalEnrollments },
      { count: activeEnrollments },
      { count: waitlistCount },
      { count: totalClasses },
    ] = await Promise.all([
      supabase
        .from("class_group_enrollments")
        .select("*", { count: "exact", head: true })
        .eq("class_group_id", class_group_id),
      
      supabase
        .from("class_group_enrollments")
        .select("*", { count: "exact", head: true })
        .eq("class_group_id", class_group_id)
        .eq("status", "active"),
      
      supabase
        .from("class_waitlist")
        .select("*", { count: "exact", head: true })
        .eq("class_group_id", class_group_id)
        .eq("status", "waiting"),
      
      supabase
        .from("classes")
        .select("*", { count: "exact", head: true })
        .eq("class_group_id", class_group_id),
    ]);

    // Estatísticas de presença se período especificado
    let attendanceStats = null;
    if (period_start && period_end) {
      // Buscar todas as presenças das aulas da turma no período
      const { data: attendanceData } = await supabase
        .from("attendance")
        .select(`
          id,
          student_id,
          checked_in_at,
          class:classes!inner(class_group_id, start_time)
        `)
        .eq("class.class_group_id", class_group_id)
        .gte("class.start_time", period_start)
        .lte("class.start_time", period_end);

      // Buscar todas as aulas da turma no período para calcular estatísticas
      const { data: classesInPeriod } = await supabase
        .from("classes")
        .select("id, start_time")
        .eq("class_group_id", class_group_id)
        .gte("start_time", period_start)
        .lte("start_time", period_end);

      if (attendanceData && classesInPeriod) {
        const totalAttendances = attendanceData.length;
        const uniqueStudents = new Set(attendanceData.map(a => a.student_id)).size;
        const totalClassesInPeriod = classesInPeriod.length;
        
        // Calcular taxa de frequência baseada em alunos ativos vs presenças esperadas
        const expectedAttendances = (activeEnrollments || 0) * totalClassesInPeriod;
        const attendanceRate = expectedAttendances > 0 ? 
          Math.round((totalAttendances / expectedAttendances) * 100) : 0;

        attendanceStats = {
          total: totalAttendances,
          present: totalAttendances, // Na tabela attendance, todos os registros são presenças
          absent: 0, // Não temos registro de ausências na tabela attendance
          excused: 0, // Não temos registro de faltas justificadas
          attendance_rate: attendanceRate,
          unique_students: uniqueStudents,
          classes_in_period: totalClassesInPeriod,
        };
      }
    }

    const stats = {
      class_group: {
        id: classGroup.id,
        name: classGroup.name,
        max_capacity: classGroup.max_capacity,
      },
      enrollments: {
        total: totalEnrollments || 0,
        active: activeEnrollments || 0,
        capacity_usage: classGroup.max_capacity 
          ? Math.round(((activeEnrollments || 0) / classGroup.max_capacity) * 100)
          : 0,
      },
      waitlist: {
        count: waitlistCount || 0,
      },
      classes: {
        total: totalClasses || 0,
      },
      attendance: attendanceStats,
    };

    return { success: true, data: stats };
  } catch (error) {
    console.error("Erro ao obter estatísticas:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 