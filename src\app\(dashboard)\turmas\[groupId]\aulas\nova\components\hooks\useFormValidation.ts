import { useState, useCallback } from 'react';

const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

export const useFormValidation = () => {
  const [isCheckingConflicts, setIsCheckingConflicts] = useState(false);
  const [hasTimeConflict, setHasTimeConflict] = useState(false);

  // Validação de conflitos em tempo real (debounced)
  const checkTimeConflicts = useCallback(
    debounce(async (startTime: string, endTime: string, instructorId: string) => {
      if (!startTime || !endTime || !instructorId) return;
      
      setIsCheckingConflicts(true);
      try {
        // Aqui você poderia chamar uma API para verificar conflitos
        // Por enquanto, vamos simular uma verificação simples
        await new Promise(resolve => setTimeout(resolve, 500));
        setHasTimeConflict(false);
      } catch (error) {
        console.error('Erro ao verificar conflitos:', error);
      } finally {
        setIsCheckingConflicts(false);
      }
    }, 1000),
    []
  );

  // Função utilitária para extrair mensagens de erro de forma segura
  const getErrorMessage = (error: any): string => {
    if (!error) return '';
    
    // Se for uma string, retorna diretamente
    if (typeof error === 'string') return error;
    
    // Se tem uma propriedade message que é string
    if (error.message && typeof error.message === 'string') return error.message;
    
    // Se tem _errors array (formato Zod)
    if (error._errors && Array.isArray(error._errors) && error._errors.length > 0) {
      return String(error._errors[0]);
    }
    
    // Se tem propriedades adicionais, tenta extrair a primeira mensagem
    if (typeof error === 'object' && error !== null) {
      const values = Object.values(error);
      for (const value of values) {
        if (typeof value === 'string' && value.trim()) {
          return value;
        }
        if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'string') {
          return value[0];
        }
      }
    }
    
    // Fallback seguro
    return 'Erro de validação';
  };

  return {
    isCheckingConflicts,
    hasTimeConflict,
    checkTimeConflicts,
    getErrorMessage
  };
}; 