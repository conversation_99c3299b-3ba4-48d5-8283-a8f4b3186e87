'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { CalendarIcon, Filter, X } from 'lucide-react';
import DatePicker from '@/components/ui/calendar-01';
import { useClassFilters } from '../contexts/ClassFiltersContext';
import { getGroupInstructors } from '../actions/group-actions';

interface Instructor {
  id: string;
  name: string;
  avatar?: string;
}

interface ClassFiltersBarProps {
  groupId: string;
  isLoading?: boolean;
}

export function ClassFiltersBar({ groupId, isLoading = false }: ClassFiltersBarProps) {
  const { filters, updateFilters, resetFilters } = useClassFilters();
  const [instructors, setInstructors] = useState<Instructor[]>([]);
  const [instructorsLoading, setInstructorsLoading] = useState(false);

  // Buscar instrutores ao montar o componente
  useEffect(() => {
    const fetchInstructors = async () => {
      setInstructorsLoading(true);
      try {
        const result = await getGroupInstructors(groupId);
        if (result.success) {
          setInstructors(result.data || []);
        }
      } catch (error) {
        console.error('Erro ao buscar instrutores:', error);
      } finally {
        setInstructorsLoading(false);
      }
    };

    fetchInstructors();
  }, [groupId]);

  const handleStatusChange = (status: string) => {
    const newStatus = status === 'all' ? null : status as any;
    updateFilters({ status: newStatus });
  };

  const handleDateFromChange = (date: Date | undefined) => {
    updateFilters({ dateFrom: date?.toISOString() });
  };

  const handleDateToChange = (date: Date | undefined) => {
    updateFilters({ dateTo: date?.toISOString() });
  };

  const handleInstructorChange = (instructorId: string) => {
    const newInstructorId = instructorId === 'all' ? undefined : instructorId;
    updateFilters({ instructorId: newInstructorId });
  };

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      'scheduled': 'Agendadas',
      'ongoing': 'Em Andamento',
      'completed': 'Concluídas',
      'cancelled': 'Canceladas',
    };
    return labels[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'scheduled': 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
      'ongoing': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
      'completed': 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400',
      'cancelled': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
    };
    return colors[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
  };

  const getInstructorInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part.charAt(0))
      .slice(0, 2)
      .join('')
      .toUpperCase();
  };

  const hasActiveFilters = filters.status || filters.dateFrom || filters.dateTo || filters.instructorId;

  if (isLoading) {
    return (
      <Card className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 shadow-sm">
        <CardContent className="p-4">
          <div className="animate-pulse">
            <div className="flex items-center space-x-4">
              <div className="h-8 bg-slate-200 dark:bg-gray-700 rounded w-32"></div>
              <div className="h-8 bg-slate-200 dark:bg-gray-700 rounded w-32"></div>
              <div className="h-8 bg-slate-200 dark:bg-gray-700 rounded w-32"></div>
              <div className="h-8 bg-slate-200 dark:bg-gray-700 rounded w-32"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 shadow-sm">
      <CardContent className="p-4">
        <div className="flex items-center space-x-2 mb-4">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Filtros</span>
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={resetFilters}
              className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
            >
              <X className="h-3 w-3 mr-1" />
              Limpar
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Filtro de Status */}
          <div className="space-y-2">
            <label className="text-xs font-medium text-muted-foreground">Status</label>
            <Select
              value={filters.status || 'all'}
              onValueChange={handleStatusChange}
            >
              <SelectTrigger className="h-9">
                <SelectValue placeholder="Todos os status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                <SelectItem value="scheduled">Agendadas</SelectItem>
                <SelectItem value="ongoing">Em Andamento</SelectItem>
                <SelectItem value="completed">Concluídas</SelectItem>
                <SelectItem value="cancelled">Canceladas</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Filtro de Data Início */}
          <div className="space-y-2">
            <label className="text-xs font-medium text-muted-foreground">Data Início</label>
            <DatePicker
              date={filters.dateFrom ? new Date(filters.dateFrom) : undefined}
              onDateChange={handleDateFromChange}
              placeholder="Data inicial"
              className="h-9"
            />
          </div>

          {/* Filtro de Data Fim */}
          <div className="space-y-2">
            <label className="text-xs font-medium text-muted-foreground">Data Fim</label>
            <DatePicker
              date={filters.dateTo ? new Date(filters.dateTo) : undefined}
              onDateChange={handleDateToChange}
              placeholder="Data final"
              className="h-9"
              minDate={filters.dateFrom ? new Date(filters.dateFrom) : undefined}
            />
          </div>

          {/* Filtro de Instrutor */}
          <div className="space-y-2">
            <label className="text-xs font-medium text-muted-foreground">Instrutor</label>
            <Select
              value={filters.instructorId || 'all'}
              onValueChange={handleInstructorChange}
              disabled={instructorsLoading}
            >
              <SelectTrigger className="h-9">
                <SelectValue placeholder="Todos os instrutores" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                {instructors.map((instructor) => (
                  <SelectItem key={instructor.id} value={instructor.id}>
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-5 w-5">
                        <AvatarImage src={instructor.avatar} alt={instructor.name} />
                        <AvatarFallback className="text-xs">
                          {getInstructorInitials(instructor.name)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="truncate">{instructor.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Filtros Ativos */}
        {hasActiveFilters && (
          <div className="flex flex-wrap items-center gap-2 mt-4 pt-4 border-t border-slate-200 dark:border-gray-700">
            <span className="text-xs font-medium text-muted-foreground">Filtros ativos:</span>
            
            {filters.status && (
              <Badge className={getStatusColor(filters.status)}>
                {getStatusLabel(filters.status)}
                <button
                  onClick={() => updateFilters({ status: null })}
                  className="ml-2 hover:opacity-70"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}

            {filters.dateFrom && (
              <Badge variant="outline">
                Início: {new Date(filters.dateFrom).toLocaleDateString('pt-BR')}
                <button
                  onClick={() => updateFilters({ dateFrom: undefined })}
                  className="ml-2 hover:opacity-70"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}

            {filters.dateTo && (
              <Badge variant="outline">
                Fim: {new Date(filters.dateTo).toLocaleDateString('pt-BR')}
                <button
                  onClick={() => updateFilters({ dateTo: undefined })}
                  className="ml-2 hover:opacity-70"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}

            {filters.instructorId && (
              <Badge variant="outline">
                {instructors.find(i => i.id === filters.instructorId)?.name || 'Instrutor'}
                <button
                  onClick={() => updateFilters({ instructorId: undefined })}
                  className="ml-2 hover:opacity-70"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
} 