"use server";

import { createClient } from "@/services/supabase/server";
import { getCurrentUser } from "@/services/auth/actions/auth-actions";
import { z } from "zod";

// Schema para filtros do relatório
const AttendanceReportFilterSchema = z.object({
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  branch_id: z.string().uuid().optional(),
  instructor_id: z.string().uuid().optional(),
  class_group_id: z.string().uuid().optional(),
});

// Tipos para o relatório
export interface AttendanceReportData {
  summary: {
    totalClasses: number;
    totalStudents: number;
    averageAttendance: number;
    attendanceRate: number;
  };
  trends: {
    thisWeek: number;
    lastWeek: number;
    growthRate: number;
  };
  topClasses: Array<{
    name: string;
    attendance: number;
    students: number;
  }>;
  recentAttendance: Array<{
    date: string;
    className: string;
    instructor: string;
    present: number;
    absent: number;
    total: number;
    rate: number;
  }>;
}

/**
 * Busca dados do relatório de presença do banco de dados
 */
export async function getAttendanceReportData(filters: unknown = {}): Promise<{
  success: boolean;
  data?: AttendanceReportData;
  errors?: any;
}> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const validationResult = AttendanceReportFilterSchema.safeParse(filters);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const validatedFilters = validationResult.data;
    const supabase = await createClient();

    // Definir período padrão (último mês se não especificado)
    const today = new Date();
    const defaultDateFrom = new Date(today);
    defaultDateFrom.setMonth(today.getMonth() - 1);
    
    const dateFrom = validatedFilters.date_from || defaultDateFrom.toISOString();
    const dateTo = validatedFilters.date_to || today.toISOString();

    // 1. Buscar todas as aulas do período com dados de presença
    let classesQuery = supabase
      .from("classes")
      .select(`
        id,
        name,
        start_time,
        end_time,
        status,
        max_capacity,
        instructor:users!classes_instructor_id_fkey (
          id,
          first_name,
          last_name,
          full_name
        ),
        attendance:attendance (
          id,
          student_id
        ),
        class_group:class_groups (
          id,
          name
        )
      `)
      .eq("tenant_id", tenantId)
      .gte("start_time", dateFrom)
      .lte("start_time", dateTo)
      .order("start_time", { ascending: false });

    // Aplicar filtros
    if (validatedFilters.branch_id) {
      classesQuery = classesQuery.eq("branch_id", validatedFilters.branch_id);
    }

    if (validatedFilters.instructor_id) {
      classesQuery = classesQuery.eq("instructor_id", validatedFilters.instructor_id);
    }

    if (validatedFilters.class_group_id) {
      classesQuery = classesQuery.eq("class_group_id", validatedFilters.class_group_id);
    }

    const { data: classesData, error: classesError } = await classesQuery;

    if (classesError) {
      console.error("Erro ao buscar aulas:", classesError);
      return { success: false, errors: { _form: "Erro ao buscar dados das aulas" } };
    }

    // 2. Calcular estatísticas gerais
    const totalClasses = classesData?.length || 0;
    const totalAttendanceRecords = classesData?.reduce((sum, classItem) => {
      return sum + (classItem.attendance?.length || 0);
    }, 0) || 0;

    // Buscar total de alunos únicos no período
    const { data: uniqueStudentsData } = await supabase
      .from("attendance")
      .select("student_id")
      .eq("tenant_id", tenantId)
      .gte("checked_in_at", dateFrom)
      .lte("checked_in_at", dateTo);

    const uniqueStudents = new Set(uniqueStudentsData?.map(record => record.student_id) || []);
    const totalStudents = uniqueStudents.size;

    const averageAttendance = totalClasses > 0 ? Math.round(totalAttendanceRecords / totalClasses) : 0;

    // Calcular taxa de frequência (presentes vs capacidade máxima)
    const classesWithCapacity = classesData?.filter(c => c.max_capacity) || [];
    const totalCapacity = classesWithCapacity.reduce((sum, c) => sum + (c.max_capacity || 0), 0);
    const actualAttendance = classesWithCapacity.reduce((sum, c) => sum + (c.attendance?.length || 0), 0);
    const attendanceRate = totalCapacity > 0 ? Math.round((actualAttendance / totalCapacity) * 100) : 0;

    // 3. Calcular tendências (semana atual vs semana anterior)
    const oneWeekAgo = new Date(today);
    oneWeekAgo.setDate(today.getDate() - 7);
    const twoWeeksAgo = new Date(today);
    twoWeeksAgo.setDate(today.getDate() - 14);

    const { data: thisWeekAttendance } = await supabase
      .from("attendance")
      .select("id")
      .eq("tenant_id", tenantId)
      .gte("checked_in_at", oneWeekAgo.toISOString())
      .lte("checked_in_at", today.toISOString());

    const { data: lastWeekAttendance } = await supabase
      .from("attendance")
      .select("id")
      .eq("tenant_id", tenantId)
      .gte("checked_in_at", twoWeeksAgo.toISOString())
      .lt("checked_in_at", oneWeekAgo.toISOString());

    const thisWeek = thisWeekAttendance?.length || 0;
    const lastWeek = lastWeekAttendance?.length || 0;
    const growthRate = lastWeek > 0 ? Math.round(((thisWeek - lastWeek) / lastWeek) * 100) : 0;

    // 4. Top 5 aulas com melhor frequência
    const classesWithAttendanceRate = classesData?.map(classItem => {
      const attendanceCount = classItem.attendance?.length || 0;
      const capacity = classItem.max_capacity || attendanceCount;
      const rate = capacity > 0 ? Math.round((attendanceCount / capacity) * 100) : 0;
      
      return {
        name: classItem.name,
        attendance: rate,
        students: attendanceCount,
        instructor: (classItem.instructor as any)?.full_name || 
                   (classItem.instructor as any)?.first_name || 'Instrutor'
      };
    }).sort((a, b) => b.attendance - a.attendance).slice(0, 5) || [];

    // 5. Presença recente (últimas 10 aulas)
    const recentClasses = classesData?.slice(0, 10).map(classItem => {
      const attendanceCount = classItem.attendance?.length || 0;
      const capacity = classItem.max_capacity || attendanceCount || 1;
      const rate = Math.round((attendanceCount / capacity) * 100);
      const absent = Math.max(0, capacity - attendanceCount);

      return {
        date: new Date(classItem.start_time).toLocaleDateString('pt-BR'),
        className: classItem.name,
        instructor: (classItem.instructor as any)?.full_name || 
                   (classItem.instructor as any)?.first_name || 'Instrutor',
        present: attendanceCount,
        absent: absent,
        total: capacity,
        rate: rate
      };
    }) || [];

    const reportData: AttendanceReportData = {
      summary: {
        totalClasses,
        totalStudents,
        averageAttendance,
        attendanceRate
      },
      trends: {
        thisWeek,
        lastWeek,
        growthRate
      },
      topClasses: classesWithAttendanceRate,
      recentAttendance: recentClasses
    };

    return {
      success: true,
      data: reportData
    };

  } catch (error) {
    console.error("Erro ao buscar dados do relatório:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Busca dados para exportação do relatório de presença
 */
export async function exportAttendanceReport(filters: unknown = {}) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const validationResult = AttendanceReportFilterSchema.safeParse(filters);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const validatedFilters = validationResult.data;
    const supabase = await createClient();

    // Definir período
    const today = new Date();
    const defaultDateFrom = new Date(today);
    defaultDateFrom.setMonth(today.getMonth() - 1);
    
    const dateFrom = validatedFilters.date_from || defaultDateFrom.toISOString();
    const dateTo = validatedFilters.date_to || today.toISOString();

    // Buscar dados detalhados para exportação
    let query = supabase
      .from("attendance")
      .select(`
        id,
        checked_in_at,
        notes,
        student:students (
          id,
          user:users!students_user_id_fkey (
            first_name,
            last_name,
            full_name,
            email
          ),
          current_belt_id
        ),
        class:classes (
          id,
          name,
          start_time,
          end_time,
          instructor:users!classes_instructor_id_fkey (
            first_name,
            last_name,
            full_name
          ),
          class_group:class_groups (
            name
          )
        )
      `)
      .eq("tenant_id", tenantId)
      .gte("checked_in_at", dateFrom)
      .lte("checked_in_at", dateTo)
      .order("checked_in_at", { ascending: false });

    const { data: attendanceData, error } = await query;

    if (error) {
      console.error("Erro ao buscar dados para exportação:", error);
      return { success: false, errors: { _form: "Erro ao buscar dados" } };
    }

    // Preparar dados para CSV
    const csvHeaders = [
      "Data",
      "Horário",
      "Aula",
      "Turma",
      "Aluno",
      "Email",
      "Faixa",
      "Grau",
      "Instrutor",
      "Observações"
    ];

    const csvRows = attendanceData ? await Promise.all(attendanceData.map(async (record) => {
      const student = record.student as any;
      const user = student?.user;
      const classData = record.class as any;
      const instructor = classData?.instructor;
      const classGroup = classData?.class_group;

      // Obter faixa via RPC
      let beltColor = '';
      let beltDegree: string | number = '';
      if (student) {
        const { data: beltDetails } = await supabase.rpc('get_student_current_belt_details', { student_id_param: student.id });
        if (beltDetails && beltDetails.length > 0) {
          const belt = beltDetails[0];
          beltColor = belt.belt_color;
          beltDegree = belt.degree;
        }
      }

      return [
        new Date(record.checked_in_at).toLocaleDateString('pt-BR'),
        new Date(record.checked_in_at).toLocaleTimeString('pt-BR'),
        classData?.name || '',
        classGroup?.name || '',
        user?.full_name || user?.first_name || '',
        user?.email || '',
        beltColor,
        beltDegree.toString(),
        instructor?.full_name || instructor?.first_name || '',
        record.notes || ''
      ];
    })) : [];

    const csvContent = [csvHeaders, ...csvRows]
      .map(row => row.map(field => `"${(field || '').toString().replace(/"/g, '""')}"`).join(','))
      .join('\n');

    const filename = `relatorio_presenca_${new Date().toISOString().split('T')[0]}.csv`;

    return {
      success: true,
      data: {
        csvContent,
        filename
      }
    };

  } catch (error) {
    console.error("Erro ao exportar relatório:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 