"use server";

import { revalidatePath } from "next/cache";
import { createClient } from "@/services/supabase/server";
import { ChangeClassGroupStatusSchema } from "../schemas/class-group";
import { validateUserAuthentication } from "./shared/validation-helpers";

/**
 * Altera o status de uma turma (ativa/inativa)
 */
export async function changeClassGroupStatus(data: unknown) {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;

    const validationResult = ChangeClassGroupStatusSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const { id, is_active } = validationResult.data;
    const supabase = await createClient();

    const { data: updatedGroup, error } = await supabase
      .from("class_groups")
      .update({ 
        is_active,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .eq("tenant_id", tenantId)
      .select()
      .single();

    if (error) {
      console.error("Erro ao alterar status da turma:", error);
      return { success: false, errors: { _form: "Erro ao alterar status da turma" } };
    }

    // Revalidar todas as páginas relacionadas à turma
    revalidatePath("/turmas");
    revalidatePath(`/turmas/${id}`);
    revalidatePath(`/turmas/${id}/alunos`);
    revalidatePath(`/turmas/${id}/aulas`);
    revalidatePath(`/turmas/${id}/editar`);
    revalidatePath("/aulas");
    
    return { 
      success: true, 
      data: updatedGroup,
      message: `Turma ${is_active ? 'ativada' : 'desativada'} com sucesso`
    };
  } catch (error) {
    console.error("Erro ao alterar status da turma:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 