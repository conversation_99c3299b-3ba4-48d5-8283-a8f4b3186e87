'use server';

import { createAdminClient } from '@/services/supabase/server';
import { z } from 'zod';

// Schema para validação dos dados de auditoria
const ClassStatusAuditSchema = z.object({
  class_id: z.string().uuid(),
  old_status: z.string(),
  new_status: z.string(),
  tenant_id: z.string().uuid(),
});

export type ClassStatusAuditData = z.infer<typeof ClassStatusAuditSchema>;

/**
 * Registra uma mudança de status de aula na tabela de auditoria
 */
export async function logClassStatusChange(data: ClassStatusAuditData) {
  try {
    const validationResult = ClassStatusAuditSchema.safeParse(data);
    if (!validationResult.success) {
      console.error('Dados de auditoria inválidos:', validationResult.error.format());
      return { success: false, error: 'Dados de auditoria inválidos' };
    }

    const { class_id, old_status, new_status, tenant_id } = validationResult.data;

    // Não registrar se o status não mudou
    if (old_status === new_status) {
      return { success: true, message: 'Nenhuma mudança de status detectada' };
    }

    const supabase = await createAdminClient();

    const { data: logEntry, error } = await supabase
      .from('class_status_logs')
      .insert({
        class_id,
        old_status,
        new_status,
        tenant_id,
        changed_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('Erro ao registrar log de auditoria:', error);
      return { success: false, error: 'Erro ao registrar log de auditoria' };
    }

    console.log(`Status da aula ${class_id} alterado de '${old_status}' para '${new_status}'`);
    return { success: true, data: logEntry };

  } catch (error) {
    console.error('Erro na função de auditoria:', error);
    return { success: false, error: 'Erro interno na auditoria' };
  }
}

/**
 * Busca o histórico de mudanças de status de uma aula
 */
export async function getClassStatusHistory(classId: string, tenantId: string) {
  try {
    const supabase = await createAdminClient();

    const { data, error } = await supabase
      .from('class_status_logs')
      .select('*')
      .eq('class_id', classId)
      .eq('tenant_id', tenantId)
      .order('changed_at', { ascending: false });

    if (error) {
      console.error('Erro ao buscar histórico de status:', error);
      return { success: false, error: 'Erro ao buscar histórico' };
    }

    return { success: true, data };

  } catch (error) {
    console.error('Erro ao buscar histórico de status:', error);
    return { success: false, error: 'Erro interno' };
  }
}

/**
 * Busca estatísticas de mudanças de status por período
 */
export async function getStatusChangeStats(tenantId: string, startDate?: string, endDate?: string) {
  try {
    const supabase = await createAdminClient();

    let query = supabase
      .from('class_status_logs')
      .select('old_status, new_status, changed_at')
      .eq('tenant_id', tenantId);

    if (startDate) {
      query = query.gte('changed_at', startDate);
    }

    if (endDate) {
      query = query.lte('changed_at', endDate);
    }

    const { data, error } = await query.order('changed_at', { ascending: false });

    if (error) {
      console.error('Erro ao buscar estatísticas de mudanças:', error);
      return { success: false, error: 'Erro ao buscar estatísticas' };
    }

    // Agrupar estatísticas por tipo de mudança
    const stats = data?.reduce((acc, log) => {
      const transition = `${log.old_status} → ${log.new_status}`;
      acc[transition] = (acc[transition] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    return { success: true, data: stats };

  } catch (error) {
    console.error('Erro ao calcular estatísticas:', error);
    return { success: false, error: 'Erro interno' };
  }
} 