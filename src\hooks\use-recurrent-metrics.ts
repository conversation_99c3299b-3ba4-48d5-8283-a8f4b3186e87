'use client';

import { useState, useEffect, useCallback } from 'react';
import { getRecurrentMetrics, getMetricsComparison, type MetricsData, type MetricsFilters } from '@/app/(dashboard)/financeiro/recorrentes/actions';

interface UseRecurrentMetricsReturn {
  metrics: MetricsData | null;
  comparison: {
    current: MetricsData;
    previous: MetricsData;
    growth: {
      alunosEmDia: number;
      receitaMensal: number;
      alunosAtrasados: number;
      taxaSucesso: number;
    };
  } | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  setFilters: (filters: MetricsFilters) => void;
}

/**
 * Hook personalizado para gerenciar métricas de pagamentos recorrentes
 * Fornece dados otimizados com cache e revalidação automática
 */
export function useRecurrentMetrics(
  initialFilters?: MetricsFilters,
  enableComparison: boolean = false
): UseRecurrentMetricsReturn {
  const [metrics, setMetrics] = useState<MetricsData | null>(null);
  const [comparison, setComparison] = useState<UseRecurrentMetricsReturn['comparison']>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<MetricsFilters>(initialFilters);

  const fetchMetrics = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (enableComparison) {
        // Buscar métricas com comparação
        const result = await getMetricsComparison();
        
        if (result.success && result.data) {
          setMetrics(result.data.current);
          setComparison(result.data);
        } else {
          setError(result.errors?._form || 'Erro ao carregar métricas');
        }
      } else {
        // Buscar apenas métricas atuais
        const result = await getRecurrentMetrics(filters);
        
        if (result.success && result.data) {
          setMetrics(result.data);
        } else {
          setError(result.errors?._form || 'Erro ao carregar métricas');
        }
      }
    } catch (err) {
      console.error('Erro no hook de métricas:', err);
      setError('Erro interno ao carregar métricas');
    } finally {
      setIsLoading(false);
    }
  }, [filters, enableComparison]);

  // Carregar métricas na inicialização e quando filtros mudarem
  useEffect(() => {
    fetchMetrics();
  }, [fetchMetrics]);

  // Função para atualizar filtros
  const updateFilters = useCallback((newFilters: MetricsFilters) => {
    setFilters(newFilters);
  }, []);

  return {
    metrics,
    comparison,
    isLoading,
    error,
    refetch: fetchMetrics,
    setFilters: updateFilters
  };
}
