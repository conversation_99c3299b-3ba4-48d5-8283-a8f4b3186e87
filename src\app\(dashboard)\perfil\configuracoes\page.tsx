"use client"

import React, { useEffect, useMemo, useState } from "react";
import dynamic from "next/dynamic";
import { usePageTitle } from "@/contexts/PageTitleContext";
import { Cog6ToothIcon } from "@heroicons/react/24/outline";
import {
  SettingsNavigation,
  NavItem,
} from "@/app/(dashboard)/academia/configuracoes/components/SettingsNavigation";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

// Lazy-load das seções
const SecuritySection = dynamic(
  () => import("./components/sections/SecuritySection"),
)
const NotificationsSection = dynamic(
  () => import("./components/sections/NotificationsSection"),
)

const sectionComponents = {
  security: SecuritySection,
  notifications: NotificationsSection,
}

type ActiveSection = keyof typeof sectionComponents

export default function PerfilConfiguracoesPage() {
  const [activeSection, setActiveSection] = useState<ActiveSection>("security")
  const { setPageTitle, setPageSubtitle, setPageIcon } = usePageTitle()

  // Itens do menu lateral
  const settingsItems: NavItem[] = useMemo(
    () => [
      {
        id: "security",
        title: "Segurança",
        description: "Senha, autenticação em duas etapas e dispositivos.",
      },
      {
        id: "notifications",
        title: "Notificações",
        description: "Preferências de e-mail e push.",
      },
    ],
    [],
  )

  // Atualizar títulos da página sempre que a seção ativa mudar
  useEffect(() => {
    const currentItem = settingsItems.find((item) => item.id === activeSection)
    const icon = <Cog6ToothIcon className="h-6 w-6 text-primary" />

    setPageTitle("Configurações do Usuário")
    setPageSubtitle(
      currentItem?.description ??
        "Gerencie as informações de segurança e notificações da sua conta.",
    )
    setPageIcon(icon)
  }, [activeSection, setPageTitle, setPageSubtitle, setPageIcon, settingsItems])

  const ActiveComponent = sectionComponents[activeSection]
  const activeItem = settingsItems.find((item) => item.id === activeSection)

  return (
    <div className="grid grid-cols-1 gap-8 md:grid-cols-4">
      {/* Navegação lateral */}
      <aside className="md:col-span-1">
        <SettingsNavigation
          items={settingsItems}
          activeId={activeSection}
          onItemClick={(id) => setActiveSection(id as ActiveSection)}
        />
      </aside>

      {/* Conteúdo principal */}
      <main className="md:col-span-3">
        <Card>
          <CardHeader>
            <CardTitle>{activeItem?.title}</CardTitle>
          </CardHeader>
          <CardContent>
            {ActiveComponent && <ActiveComponent />}
          </CardContent>
        </Card>
      </main>
    </div>
  )
} 