"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Download, Upload } from "lucide-react";

export function InstrutorActionsButtons() {
  return (
    <div className="flex flex-col sm:flex-row gap-2">
      <Button variant="outline" size="sm" className="gap-2">
        <Download className="h-4 w-4" />
        Exportar
      </Button>
      <Button variant="outline" size="sm" className="gap-2">
        <Upload className="h-4 w-4" />
        Importar
      </Button>
    </div>
  );
} 