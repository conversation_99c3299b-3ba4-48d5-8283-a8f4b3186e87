'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

interface UseAdaptivePollingProps {
  onPoll: () => Promise<void> | void;
  baseInterval?: number; // Intervalo base em ms (padrão: 30s)
  activeInterval?: number; // Intervalo quando ativo em ms (padrão: 15s)
  inactiveInterval?: number; // Intervalo quando inativo em ms (padrão: 60s)
  enabled?: boolean;
}

interface UseAdaptivePollingReturn {
  isActive: boolean;
  isPolling: boolean;
  currentInterval: number;
  startPolling: () => void;
  stopPolling: () => void;
  togglePolling: () => void;
  pollNow: () => Promise<void>;
}

export function useAdaptivePolling({
  onPoll,
  baseInterval = 30000, // 30 segundos
  activeInterval = 15000, // 15 segundos quando ativo
  inactiveInterval = 60000, // 60 segundos quando inativo
  enabled = true
}: UseAdaptivePollingProps): UseAdaptivePollingReturn {
  const [isPolling, setIsPolling] = useState(enabled);
  const [isActive, setIsActive] = useState(true);
  const [currentInterval, setCurrentInterval] = useState(baseInterval);
  
  const pollingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const activityTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isPollingRef = useRef(false);
  const lastPollTime = useRef<number>(0);

  const pollNow = useCallback(async () => {
    if (isPollingRef.current) return;
    
    isPollingRef.current = true;
    lastPollTime.current = Date.now();
    
    try {
      await onPoll();
    } catch (error) {
      console.error('Erro no polling:', error);
    } finally {
      isPollingRef.current = false;
    }
  }, [onPoll]);

  const startPolling = useCallback(() => {
    setIsPolling(true);
  }, []);

  const stopPolling = useCallback(() => {
    setIsPolling(false);
    if (pollingTimeoutRef.current) {
      clearTimeout(pollingTimeoutRef.current);
      pollingTimeoutRef.current = null;
    }
  }, []);

  const togglePolling = useCallback(() => {
    if (isPolling) {
      stopPolling();
    } else {
      startPolling();
    }
  }, [isPolling, startPolling, stopPolling]);

  // Detectar atividade do usuário
  useEffect(() => {
    const resetActivityTimer = () => {
      setIsActive(true);
      
      if (activityTimeoutRef.current) {
        clearTimeout(activityTimeoutRef.current);
      }
      
      // Considera usuário inativo após 3 minutos sem interação
      activityTimeoutRef.current = setTimeout(() => {
        setIsActive(false);
      }, 3 * 60 * 1000);
    };

    const handleActivity = () => {
      resetActivityTimer();
    };

    // Eventos de atividade
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    events.forEach(event => {
      document.addEventListener(event, handleActivity, { passive: true });
    });

    // Inicializar
    resetActivityTimer();

    return () => {
      if (activityTimeoutRef.current) {
        clearTimeout(activityTimeoutRef.current);
      }
      events.forEach(event => {
        document.removeEventListener(event, handleActivity);
      });
    };
  }, []);

  // Ajustar intervalo baseado na atividade e visibilidade
  useEffect(() => {
    let interval = baseInterval;

    if (document.visibilityState === 'hidden') {
      // Página não visível - polling mais lento
      interval = inactiveInterval;
    } else if (isActive) {
      // Usuário ativo - polling mais rápido
      interval = activeInterval;
    } else {
      // Usuário inativo mas página visível - polling normal
      interval = baseInterval;
    }

    setCurrentInterval(interval);
  }, [isActive, baseInterval, activeInterval, inactiveInterval]);

  // Gerenciar polling
  useEffect(() => {
    if (!isPolling) {
      if (pollingTimeoutRef.current) {
        clearTimeout(pollingTimeoutRef.current);
        pollingTimeoutRef.current = null;
      }
      return;
    }

    const scheduleNextPoll = () => {
      if (pollingTimeoutRef.current) {
        clearTimeout(pollingTimeoutRef.current);
      }

      pollingTimeoutRef.current = setTimeout(async () => {
        if (isPolling) {
          await pollNow();
          // Reagendar apenas se ainda estiver habilitado
          if (isPolling) {
            scheduleNextPoll();
          }
        }
      }, currentInterval);
    };

    scheduleNextPoll();

    return () => {
      if (pollingTimeoutRef.current) {
        clearTimeout(pollingTimeoutRef.current);
        pollingTimeoutRef.current = null;
      }
    };
  }, [isPolling, currentInterval, pollNow]);

  // Polling imediato quando página volta ao foco
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isPolling) {
        // Evitar polling muito frequente
        const timeSinceLastPoll = Date.now() - lastPollTime.current;
        const minInterval = Math.min(activeInterval, baseInterval) / 2; // Pelo menos metade do intervalo menor
        
        if (timeSinceLastPoll > minInterval) {
          pollNow();
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isPolling, pollNow, activeInterval, baseInterval]);

  return {
    isActive,
    isPolling,
    currentInterval,
    startPolling,
    stopPolling,
    togglePolling,
    pollNow
  };
} 