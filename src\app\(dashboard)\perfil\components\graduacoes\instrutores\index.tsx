'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { JiuJitsuBelt } from '@/components/belt'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON>alogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Award, Calendar, Clock, Star, Trophy, Plus, User } from 'lucide-react'
import { formatDistanceStrict, parseISO } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { getInstructorBeltHistory, addInstructorBelt, getAvailableBeltLevels } from '../../../actions/instructor-actions'
import { getCurrentUser } from '@/services/auth/actions/auth-actions'
import { toast } from 'sonner'

export interface BeltLevel {
  id: string
  belt_color: string
  degree: number
  label: string
  stripe_color?: string | null
  show_center_line?: boolean | null
  center_line_color?: string | null
  sort_order: number
  modality?: {
    id: string
    name: string
  }
}

export interface InstructorBelt {
  id: string
  awarded_at: string
  notes: string | null
  belt_level: BeltLevel
  awarded_by_user?: {
    first_name: string
    last_name: string
    full_name: string
  }
}

interface InstructorGraduacoesTabProps {
  userId: string
}

export function InstructorGraduacoesTab({ userId }: InstructorGraduacoesTabProps) {
  const [isAddingBelt, setIsAddingBelt] = useState(false)
  const [newBelt, setNewBelt] = useState({
    belt_level_id: '',
    notes: ''
  })

  const queryClient = useQueryClient()

  const { data: instructorData } = useQuery({
    queryKey: ['instructor-profile', userId],
    queryFn: async () => {
      const response = await fetch(`/api/instructors/${userId}`)
      if (!response.ok) throw new Error('Erro ao buscar dados do instrutor')
      return response.json()
    },
    enabled: !!userId
  })

  const { data: beltHistory, isLoading: historyLoading } = useQuery({
    queryKey: ['instructor-belt-history', instructorData?.id],
    queryFn: async () => {
      if (!instructorData?.id) return []
      const result = await getInstructorBeltHistory(instructorData.id)
      if (!result.success) throw new Error(result.error)
      return result.data || []
    },
    enabled: !!instructorData?.id
  })

  const { data: availableBeltLevels } = useQuery({
    queryKey: ['available-belt-levels'],
    queryFn: async () => {
      const result = await getAvailableBeltLevels()
      if (!result.success) throw new Error(result.error)
      return result.data || []
    }
  })

  const { data: currentUser } = useQuery({
    queryKey: ['current-user'],
    queryFn: getCurrentUser,
    staleTime: 10 * 60 * 1000
  })

  const addBeltMutation = useMutation({
    mutationFn: async (data: { belt_level_id: string; notes: string }) => {
      if (!instructorData?.id) throw new Error('ID do instrutor não encontrado')

      const result = await addInstructorBelt(instructorData.id, {
        ...data,
        awarded_at: new Date().toISOString()
      })

      if (!result.success) {
        throw new Error(result.error || 'Erro ao adicionar faixa')
      }

      return result.data
    },
    onSuccess: () => {
      toast.success('Faixa adicionada com sucesso!')
      queryClient.invalidateQueries({ queryKey: ['instructor-belt-history'] })
      queryClient.invalidateQueries({ queryKey: ['instructor-profile'] })
      setIsAddingBelt(false)
      setNewBelt({ belt_level_id: '', notes: '' })
    },
    onError: (error: Error) => {
      toast.error(`Erro ao adicionar faixa: ${error.message}`)
    }
  })

  const handleAddBelt = () => {
    if (!newBelt.belt_level_id) {
      toast.error('Selecione um nível de faixa')
      return
    }
    addBeltMutation.mutate(newBelt)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const getTimeInBelt = (awardedAt: string, nextBeltDate?: string) => {
    const awarded = new Date(awardedAt)
    const end = nextBeltDate ? new Date(nextBeltDate) : new Date()

    return formatDistanceStrict(awarded, end, {
      locale: ptBR,
      addSuffix: false
    })
  }

  const currentBelt = beltHistory?.[0]
  const isAdmin = currentUser?.role === 'admin'

  if (historyLoading) {
    return (
      <div className="space-y-6 p-6 bg-slate-50 dark:bg-slate-900 rounded-b-lg">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="p-6 bg-white dark:bg-slate-800">
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/3"></div>
                <div className="h-8 bg-slate-200 dark:bg-slate-700 rounded"></div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  const renderizarFaixaAtual = () => {
    if (!currentBelt?.belt_level) {
      return (
        <Card className="p-6 bg-white dark:bg-slate-800">
          <div className="text-center py-8">
            <Award className="w-12 h-12 mx-auto mb-4 text-slate-300 dark:text-slate-600" />
            <p className="text-slate-500 dark:text-slate-400">Nenhuma graduação registrada</p>
            {isAdmin && <p className="text-sm mt-2 text-slate-400 dark:text-slate-500">Clique em "Adicionar Faixa" para registrar a primeira graduação</p>}
          </div>
        </Card>
      )
    }

    return (
      <Card className="p-6 bg-white dark:bg-slate-800">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
          <div className="flex items-center gap-6">
            <JiuJitsuBelt beltColor={currentBelt.belt_level.belt_color} degrees={currentBelt.belt_level.degree} className="w-48 h-12" stripeColor={currentBelt.belt_level.stripe_color ?? undefined} showCenterLine={currentBelt.belt_level.show_center_line ?? undefined} centerLineColor={currentBelt.belt_level.center_line_color ?? undefined} />
            <div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">{currentBelt.belt_level.label}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Graduado em {formatDate(currentBelt.awarded_at)}</p>
              {currentBelt.belt_level.modality && <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{currentBelt.belt_level.modality.name}</p>}
            </div>
          </div>

          <div className="flex flex-col md:items-end gap-2">
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <span className="text-gray-900 dark:text-gray-100 font-medium">{getTimeInBelt(currentBelt.awarded_at)}</span>
                <span className="text-gray-500 dark:text-gray-400">na faixa</span>
              </div>
            </div>

            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <Trophy className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <span className="text-gray-900 dark:text-gray-100 font-medium">{beltHistory?.length || 0}</span>
                <span className="text-gray-500 dark:text-gray-400">graduações</span>
              </div>
            </div>
          </div>
        </div>
      </Card>
    )
  }

  const renderizarHistorico = () => {
    if (!beltHistory || beltHistory.length === 0) {
      return (
        <Card className="p-6 bg-white dark:bg-slate-800">
          <p className="text-center text-slate-500 dark:text-slate-400 py-8">Nenhum histórico de graduação encontrado.</p>
        </Card>
      )
    }

    return (
      <Card className="p-6 bg-white dark:bg-slate-800">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Histórico de Graduações</h3>

          {isAdmin && (
            <Dialog open={isAddingBelt} onOpenChange={setIsAddingBelt}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  Adicionar Faixa
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Adicionar Nova Graduação</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Nível de Faixa</label>
                    <Select value={newBelt.belt_level_id} onValueChange={(value) => setNewBelt((prev) => ({ ...prev, belt_level_id: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o nível da faixa" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableBeltLevels?.map((level: any) => {
                          const modality = Array.isArray(level.modality) ? level.modality[0] : level.modality
                          return (
                            <SelectItem key={level.id} value={level.id}>
                              <div className="flex items-center gap-2">
                                <div className="flex items-center gap-1">
                                  <div className="w-4 h-2 rounded" style={{ backgroundColor: level.belt_color }} />
                                  <span>{level.label}</span>
                                </div>
                                {modality && <span className="text-xs text-muted-foreground">- {modality.name}</span>}
                              </div>
                            </SelectItem>
                          )
                        })}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Observações</label>
                    <Textarea value={newBelt.notes} onChange={(e) => setNewBelt((prev) => ({ ...prev, notes: e.target.value }))} placeholder="Observações sobre esta graduação..." rows={3} />
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setIsAddingBelt(false)}>
                      Cancelar
                    </Button>
                    <Button onClick={handleAddBelt} disabled={addBeltMutation.isPending}>
                      {addBeltMutation.isPending ? 'Adicionando...' : 'Adicionar'}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>

        <div className="space-y-6">
          {beltHistory.map((belt: InstructorBelt, index: number) => {
            const nextBelt = beltHistory[index + 1]
            const timeInBelt = getTimeInBelt(belt.awarded_at, nextBelt?.awarded_at)

            return (
              <div key={belt.id} className="flex items-start gap-6 pb-6 border-b last:border-0 border-gray-200 dark:border-gray-700">
                <JiuJitsuBelt beltColor={belt.belt_level.belt_color} degrees={belt.belt_level.degree} className="w-32 h-8" stripeColor={belt.belt_level.stripe_color ?? undefined} showCenterLine={belt.belt_level.show_center_line ?? undefined} centerLineColor={belt.belt_level.center_line_color ?? undefined} />

                <div className="flex-grow">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="text-base font-semibold text-gray-900 dark:text-gray-100">{belt.belt_level.label}</h4>
                    {index === 0 && (
                      <Badge variant="default" className="text-xs">
                        Atual
                      </Badge>
                    )}
                  </div>

                  {belt.belt_level.modality && <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">{belt.belt_level.modality.name}</p>}

                  <div className="mt-2 space-y-1">
                    <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <Calendar className="w-4 h-4 mr-1" />
                      {formatDate(belt.awarded_at)}
                      <span className="inline-block mx-2">•</span>
                      <span>{timeInBelt}</span>
                      <span className="ml-1">{index === 0 ? 'na faixa' : 'permaneceu'}</span>
                    </div>

                    {belt.awarded_by_user && (
                      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <User className="w-4 h-4 mr-1" />
                        Professor: {belt.awarded_by_user.full_name || `${belt.awarded_by_user.first_name} ${belt.awarded_by_user.last_name}`}
                      </div>
                    )}

                    {belt.notes && (
                      <div className="flex items-start text-sm text-gray-500 dark:text-gray-400">
                        <Star className="w-4 h-4 mr-1 mt-0.5 flex-shrink-0" />
                        <span className="italic">"{belt.notes}"</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </Card>
    )
  }

  const renderizarProgressao = () => {
    if (!currentBelt?.belt_level) return null

    return (
      <Card className="p-6 bg-white dark:bg-slate-800">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">Progressão na Carreira</h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex flex-col items-center text-center">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full mb-3">
                <Award className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-1">Faixa Atual</h4>
              <div className="flex flex-col items-center gap-2">
                <JiuJitsuBelt beltColor={currentBelt.belt_level.belt_color} degrees={currentBelt.belt_level.degree} className="w-24 h-6" stripeColor={currentBelt.belt_level.stripe_color ?? undefined} showCenterLine={currentBelt.belt_level.show_center_line ?? undefined} centerLineColor={currentBelt.belt_level.center_line_color ?? undefined} />
                <span className="text-sm text-gray-600 dark:text-gray-400">{currentBelt.belt_level.label}</span>
              </div>
            </div>
          </div>

          <div className="p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex flex-col items-center text-center">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-full mb-3">
                <Clock className="w-6 h-6 text-green-600" />
              </div>
              <h4 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-1">Tempo na Faixa</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">{getTimeInBelt(currentBelt.awarded_at)}</p>
            </div>
          </div>

          <div className="p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex flex-col items-center text-center">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-full mb-3">
                <Trophy className="w-6 h-6 text-purple-600" />
              </div>
              <h4 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-1">Total de Graduações</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">{beltHistory?.length || 0}</p>
            </div>
          </div>
        </div>
      </Card>
    )
  }

  return (
    <div className="space-y-6 p-6 bg-slate-50 dark:bg-slate-900 rounded-b-lg">
      {renderizarFaixaAtual()}
      {renderizarHistorico()}
      {renderizarProgressao()}
    </div>
  )
}
