'use client';

import { 
  createContext, 
  useContext, 
  useState, 
  useEffect, 
  ReactNode 
} from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { CACHE_KEYS } from '@/constants/cache-keys';

interface UserAvatarContextType {
  avatarUrl: string | null;
  updateAvatar: (newAvatarUrl: string) => void;
}

const UserAvatarContext = createContext<UserAvatarContextType | undefined>(undefined);

export function useUserAvatar() {
  const context = useContext(UserAvatarContext);
  
  if (!context) {
    console.warn('useUserAvatar foi chamado fora de um UserAvatarProvider. Usando valores padrão.');
    return {
      avatarUrl: null,
      updateAvatar: (newAvatarUrl: string) => {
        console.warn('Tentativa de atualizar avatar fora do contexto. Operação ignorada.');
      }
    };
  }
  
  return context;
}

interface UserAvatarProviderProps {
  children: ReactNode;
  initialAvatarUrl?: string | null;
}

export function UserAvatarProvider({ 
  children, 
  initialAvatarUrl = null 
}: UserAvatarProviderProps) {
  const [avatarUrl, setAvatarUrl] = useState<string | null>(initialAvatarUrl);
  const queryClient = useQueryClient();
  
  useEffect(() => {
    // Tentar obter o avatar do cache se não fornecido inicialmente
    if (!initialAvatarUrl) {
      const cachedProfile = queryClient.getQueryData(CACHE_KEYS.USER_METADATA);
      if (cachedProfile && (cachedProfile as any).avatarUrl) {
        setAvatarUrl((cachedProfile as any).avatarUrl);
      }
    }
  }, [queryClient, initialAvatarUrl]);
  
  const updateAvatar = (newAvatarUrl: string) => {
    setAvatarUrl(newAvatarUrl);
    
    // Atualizar o avatar no cache do React Query
    const cachedProfile = queryClient.getQueryData(CACHE_KEYS.USER_METADATA);
    if (cachedProfile) {
      const updatedProfile = {
        ...(cachedProfile as any),
        avatarUrl: newAvatarUrl
      };
      
      queryClient.setQueryData(CACHE_KEYS.USER_METADATA, updatedProfile);
    }
  };
  
  return (
    <UserAvatarContext.Provider value={{ avatarUrl, updateAvatar }}>
      {children}
    </UserAvatarContext.Provider>
  );
}