'use client';

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ChevronLeft, 
  ChevronRight, 
  ChevronsLeft, 
  ChevronsRight, 
  Search, 
  BookOpen 
} from 'lucide-react';
import { ClassCard } from './ClassCard';
import { ClassesResponse } from '../types';
import { useClassFilters } from '../contexts/ClassFiltersContext';

interface ClassesListSectionProps {
  classesData: ClassesResponse;
  onEdit?: (classId: string) => void;
  onCancel?: (classId: string) => void;
  isLoading?: boolean;
}

export function ClassesListSection({ 
  classesData, 
  onEdit, 
  onCancel, 
  isLoading = false 
}: ClassesListSectionProps) {
  const { filters, updateFilters } = useClassFilters();

  const handlePageChange = (newPage: number) => {
    updateFilters({ page: newPage });
  };

  const handlePreviousPage = () => {
    if (classesData.pagination.hasPrev) {
      handlePageChange(classesData.pagination.page - 1);
    }
  };

  const handleNextPage = () => {
    if (classesData.pagination.hasNext) {
      handlePageChange(classesData.pagination.page + 1);
    }
  };

  const handleFirstPage = () => {
    handlePageChange(1);
  };

  const handleLastPage = () => {
    handlePageChange(classesData.pagination.totalPages);
  };

  const getVisiblePageNumbers = () => {
    const current = classesData.pagination.page;
    const total = classesData.pagination.totalPages;
    const delta = 2;

    const range = [];
    const rangeWithDots = [];

    for (
      let i = Math.max(2, current - delta);
      i <= Math.min(total - 1, current + delta);
      i++
    ) {
      range.push(i);
    }

    if (current - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (current + delta < total - 1) {
      rangeWithDots.push('...', total);
    } else if (total > 1) {
      rangeWithDots.push(total);
    }

    return rangeWithDots;
  };

  if (isLoading) {
    return (
      <Card className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 shadow-sm">
        <CardHeader>
          <div className="animate-pulse">
            <div className="h-6 bg-slate-200 dark:bg-gray-700 rounded w-1/4 mb-2"></div>
            <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded w-1/3"></div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-48 bg-slate-200 dark:bg-gray-700 rounded-lg"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 shadow-sm">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <BookOpen className="h-5 w-5 text-blue-600" />
            <CardTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Lista de Aulas
            </CardTitle>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-sm">
              {classesData.pagination.total} 
              {classesData.pagination.total === 1 ? ' aula' : ' aulas'}
            </Badge>
          </div>
        </div>
        
        {classesData.pagination.total > 0 && (
          <p className="text-sm text-muted-foreground">
            Exibindo {((classesData.pagination.page - 1) * classesData.pagination.limit) + 1} a{' '}
            {Math.min(
              classesData.pagination.page * classesData.pagination.limit,
              classesData.pagination.total
            )}{' '}
            de {classesData.pagination.total} aulas
          </p>
        )}
      </CardHeader>

      <CardContent>
        {classesData.data.length === 0 ? (
          <div className="text-center py-12">
            <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Nenhuma aula encontrada
            </h3>
            <p className="text-muted-foreground mb-4">
              {filters.status || filters.dateFrom || filters.dateTo || filters.instructorId
                ? 'Tente ajustar os filtros para encontrar mais aulas.'
                : 'Esta turma ainda não possui aulas agendadas.'}
            </p>
          </div>
        ) : (
          <>
            {/* Lista de Aulas */}
            <div className="space-y-4 mb-6">
              {classesData.data.map((classItem) => (
                <ClassCard
                  key={classItem.id}
                  classItem={classItem}
                  onEdit={onEdit}
                  onCancel={onCancel}
                />
              ))}
            </div>

            {/* Paginação */}
            {classesData.pagination.totalPages > 1 && (
              <div className="flex items-center justify-between pt-6 border-t border-slate-200 dark:border-gray-700">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">
                    Página {classesData.pagination.page} de {classesData.pagination.totalPages}
                  </span>
                </div>

                <div className="flex items-center space-x-1">
                  {/* Primeira página */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleFirstPage}
                    disabled={!classesData.pagination.hasPrev}
                    className="hidden sm:flex"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>

                  {/* Página anterior */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePreviousPage}
                    disabled={!classesData.pagination.hasPrev}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    <span className="hidden sm:inline ml-1">Anterior</span>
                  </Button>

                  {/* Números das páginas */}
                  <div className="hidden md:flex items-center space-x-1">
                    {getVisiblePageNumbers().map((page, index) => (
                      <div key={index}>
                        {page === '...' ? (
                          <span className="px-3 py-1 text-muted-foreground">...</span>
                        ) : (
                          <Button
                            variant={page === classesData.pagination.page ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(page as number)}
                            className="min-w-[40px]"
                          >
                            {page}
                          </Button>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Próxima página */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleNextPage}
                    disabled={!classesData.pagination.hasNext}
                  >
                    <span className="hidden sm:inline mr-1">Próxima</span>
                    <ChevronRight className="h-4 w-4" />
                  </Button>

                  {/* Última página */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleLastPage}
                    disabled={!classesData.pagination.hasNext}
                    className="hidden sm:flex"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
} 