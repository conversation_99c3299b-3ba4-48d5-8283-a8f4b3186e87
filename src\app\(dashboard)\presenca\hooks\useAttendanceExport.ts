'use client';

import { useState } from 'react';
import { exportAttendanceList } from '../actions/attendance-queries';
import { toast } from 'sonner';

export function useAttendanceExport() {
  const [isExporting, setIsExporting] = useState(false);

  const exportClassAttendance = async (classId: string) => {
    setIsExporting(true);
    try {
      const result = await exportAttendanceList(classId);
      
      if (result.success && result.data) {
        // Criar o blob CSV
        const blob = new Blob([result.data.csvContent], { type: 'text/csv;charset=utf-8;' });
        
        // Criar link temporário para download
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', result.data.filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        
        toast.success("Exportação concluída", {
          description: `Arquivo ${result.data.filename} baixado com sucesso.`,
        });
      } else {
        toast.error("Erro na exportação", {
          description: result.errors?._form || "Erro desconhecido ao exportar dados.",
        });
      }
    } catch (error) {
      console.error('Erro na exportação:', error);
      toast.error("Erro na exportação", {
        description: "Erro interno do sistema. Tente novamente.",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const exportAllAttendance = async () => {
    setIsExporting(true);
    try {
      // TODO: Implementar exportação geral quando necessário
      toast.info("Em desenvolvimento", {
        description: "Exportação geral de presenças será implementada em breve.",
      });
    } catch (error) {
      console.error('Erro na exportação geral:', error);
      toast.error("Erro na exportação", {
        description: "Erro interno do sistema. Tente novamente.",
      });
    } finally {
      setIsExporting(false);
    }
  };

  return {
    isExporting,
    exportClassAttendance,
    exportAllAttendance
  };
} 