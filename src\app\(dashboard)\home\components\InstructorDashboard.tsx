import { InstructorDashboardData } from '../types'

interface InstructorDashboardProps {
  data: InstructorDashboardData
}

export function InstructorDashboard({ data }: InstructorDashboardProps) {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      <div className="rounded-xl border bg-card text-card-foreground shadow">
        <div className="p-6 flex flex-col gap-2">
          <h3 className="font-semibold">Alunos Atribuídos</h3>
          <div className="text-3xl font-bold">{data.alunosAtribuidos}</div>
          <p className="text-muted-foreground text-sm">
            Alunos sob sua orientação
          </p>
        </div>
      </div>
      
      <div className="rounded-xl border bg-card text-card-foreground shadow">
        <div className="p-6 flex flex-col gap-2">
          <h3 className="font-semibold"><PERSON><PERSON></h3>
          <div className="text-3xl font-bold">{data.aulasHoje}</div>
          <p className="text-muted-foreground text-sm">
            Aulas programadas para hoje
          </p>
        </div>
      </div>
      
      <div className="rounded-xl border bg-card text-card-foreground shadow">
        <div className="p-6 flex flex-col gap-2">
          <h3 className="font-semibold">Graduações Pendentes</h3>
          <div className="text-3xl font-bold">{data.graduacoesPendentes}</div>
          <p className="text-muted-foreground text-sm">
            Avaliações de graduação
          </p>
        </div>
      </div>
      
      <div className="rounded-xl border bg-card text-card-foreground shadow">
        <div className="p-6 flex flex-col gap-2">
          <h3 className="font-semibold">Presenças a Registrar</h3>
          <div className="text-3xl font-bold">{data.presencasRegistrar}</div>
          <p className="text-muted-foreground text-sm">
            Chamadas em aberto
          </p>
        </div>
      </div>
    </div>
  )
} 