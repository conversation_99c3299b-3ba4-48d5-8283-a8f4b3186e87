/**
 * Exemplo de uso da funcionalidade de Taxa de Cancelamento
 * 
 * Este arquivo demonstra como implementar o cancelamento de assinatura
 * com cobrança automática da taxa configurada no plano.
 */

import { createCancellationFeePayment } from '../payment-actions'

/**
 * Exemplo 1: Cancelamento simples com taxa
 * 
 * Quando um aluno solicita o cancelamento da assinatura,
 * o sistema verifica se há taxa configurada no plano e cria a cobrança.
 */
export async function exemploCanelamentoComTaxa() {
  const membershipId = 'uuid-da-membership'
  
  try {
    // Criar cobrança da taxa de cancelamento
    const result = await createCancellationFeePayment({
      membershipId,
      motivo: 'Cancelamento solicitado pelo aluno'
    })

    if (result.success) {
      console.log('Taxa de cancelamento criada:', result.data)
      
      // Aqui você pode:
      // 1. Atualizar o status da membership para 'cancelled'
      // 2. Enviar notificação para o aluno sobre a taxa
      // 3. Registrar o cancelamento no histórico
      
    } else {
      console.error('Erro ao criar taxa:', result.errors)
      
      // Possíveis erros:
      // - Matrícula não encontrada
      // - Matrícula não está ativa
      // - Plano não possui taxa de cancelamento configurada
    }
  } catch (error) {
    console.error('Erro interno:', error)
  }
}

/**
 * Exemplo 2: Fluxo completo de cancelamento
 * 
 * Implementação completa que inclui validações e atualizações necessárias.
 */
export async function fluxoCompletoCancelamento(membershipId: string, motivo?: string) {
  try {
    // 1. Verificar se a membership existe e está ativa
    // (implementar validação conforme necessário)
    
    // 2. Criar taxa de cancelamento se configurada no plano
    const taxaResult = await createCancellationFeePayment({
      membershipId,
      motivo: motivo || 'Cancelamento solicitado'
    })

    if (taxaResult.success) {
      console.log(`Taxa de cancelamento criada: R$ ${taxaResult.data?.amount}`)
      
      // 3. Atualizar status da membership
      // await updateMembershipStatus(membershipId, 'cancelled')
      
      // 4. Cancelar pagamentos recorrentes futuros
      // await cancelRecurringPayments(membershipId)
      
      // 5. Enviar notificações
      // await sendCancellationNotification(membershipId, taxaResult.data)
      
      return {
        success: true,
        message: 'Cancelamento processado com sucesso',
        cancellationFee: taxaResult.data?.amount || 0
      }
    } else {
      // Se não há taxa configurada, ainda pode prosseguir com o cancelamento
      if (taxaResult.errors?._form?.includes('não possui taxa de cancelamento')) {
        console.log('Cancelamento sem taxa - plano não possui taxa configurada')
        
        // Prosseguir com cancelamento sem taxa
        // await updateMembershipStatus(membershipId, 'cancelled')
        
        return {
          success: true,
          message: 'Cancelamento processado sem taxa',
          cancellationFee: 0
        }
      } else {
        // Erro real - não prosseguir
        return {
          success: false,
          error: taxaResult.errors?._form || 'Erro ao processar cancelamento'
        }
      }
    }
  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    }
  }
}

/**
 * Exemplo 3: Verificar taxa antes do cancelamento
 * 
 * Útil para mostrar ao usuário o valor da taxa antes de confirmar o cancelamento.
 */
export async function verificarTaxaCancelamento(membershipId: string) {
  try {
    // Simular criação para verificar se há taxa (sem realmente criar)
    // Em uma implementação real, você criaria uma função separada para apenas verificar
    const result = await createCancellationFeePayment({
      membershipId,
      motivo: 'Verificação de taxa'
    })

    if (result.success) {
      // Se chegou aqui, há taxa configurada
      // IMPORTANTE: Cancelar este pagamento pois foi apenas para verificação
      // await cancelPayment(result.data.payment_id)
      
      return {
        hasFee: true,
        amount: result.data?.amount || 0,
        planTitle: result.data?.plan_title
      }
    } else {
      if (result.errors?._form?.includes('não possui taxa de cancelamento')) {
        return {
          hasFee: false,
          amount: 0
        }
      } else {
        throw new Error(result.errors?._form || 'Erro ao verificar taxa')
      }
    }
  } catch (error) {
    throw error
  }
}

/**
 * Como a taxa é calculada:
 * 
 * 1. O sistema busca a membership pelo ID fornecido
 * 2. Acessa o plano associado à membership
 * 3. Verifica a configuração duration_config do plano
 * 4. Extrai o valor de taxaCancelamento
 * 5. Cria um pagamento do tipo 'cancellation_fee' com esse valor
 * 
 * Configuração no plano (duration_config):
 * {
 *   "tipo": "limited",
 *   "duracao": 12,
 *   "unidadeTempo": "months",
 *   "taxaCancelamento": 50.00,  // <- Este valor é usado
 *   "opcaoRenovacao": "auto-renew"
 * }
 * 
 * Ou para planos com datas específicas:
 * {
 *   "tipo": "specific",
 *   "dataInicio": "2024-01-01",
 *   "dataFim": "2024-12-31",
 *   "taxaCancelamento": 100.00  // <- Este valor é usado
 * }
 */
