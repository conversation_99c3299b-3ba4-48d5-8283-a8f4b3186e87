'use client';

import { useUserMetadata } from './useUserMetadata';

interface UserSessionData {
  user: {
    id: string;
    email: string;
    role: string;
  };
}

interface UseSessionResult {
  session: UserSessionData | null;
  isLoading: boolean;
  error: Error | null;
}

/**
 * Hook para obter e gerenciar a sessão do usuário atual
 * Usa useUserMetadata como fonte canônica dos dados
 */
export function useSession(): UseSessionResult {
  const { metadata, isLoading, error } = useUserMetadata();
  
  // Criar objeto de sessão a partir dos metadados
  const session = metadata ? {
    user: {
      id: metadata.id,
      email: metadata.email,
      role: metadata.role
    }
  } : null;
  
  return {
    session,
    isLoading,
    error: error as Error | null,
  };
} 