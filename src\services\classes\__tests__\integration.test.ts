/**
 * Testes de integração para o sistema de atualização automática de status
 *
 * Estes testes validam:
 * 1. Compatibilidade entre o sistema antigo e novo
 * 2. Correto funcionamento dos helpers de integração
 * 3. Fallback adequado quando o cron job não funciona
 */

import { getEffectiveClassStatus, needsStatusUpdate, transformClassesWithEffectiveStatus, filterClassesByEffectiveStatus, groupClassesByEffectiveStatus, type ClassStatusData } from '../integration-helpers'

// Mock de dados de teste
const createMockClass = (id: string, status: string, minutesFromNow: { start: number; end: number }): ClassStatusData => {
  const now = new Date()
  const startTime = new Date(now.getTime() + minutesFromNow.start * 60000)
  const endTime = new Date(now.getTime() + minutesFromNow.end * 60000)

  return {
    id,
    status,
    start_time: startTime.toISOString(),
    end_time: endTime.toISOString(),
    tenant_id: 'test-tenant'
  }
}

describe('Integration Helpers', () => {
  describe('getEffectiveClassStatus', () => {
    it('deve retornar status original para aulas canceladas', async () => {
      const classData = createMockClass('1', 'cancelled', { start: -60, end: -30 })
      expect(await getEffectiveClassStatus(classData)).toBe('cancelled')
    })

    it('deve retornar status original para aulas reagendadas', async () => {
      const classData = createMockClass('2', 'rescheduled', { start: -60, end: -30 })
      expect(await getEffectiveClassStatus(classData)).toBe('rescheduled')
    })

    it('deve retornar status original para aulas concluídas', async () => {
      const classData = createMockClass('3', 'completed', { start: -60, end: -30 })
      expect(await getEffectiveClassStatus(classData)).toBe('completed')
    })

    it('deve confiar no banco para aulas agendadas futuras', async () => {
      const classData = createMockClass('4', 'scheduled', { start: 30, end: 60 })
      expect(await getEffectiveClassStatus(classData)).toBe('scheduled')
    })

    it('deve confiar no banco mesmo quando status pode estar desatualizado', async () => {
      // Aula que já terminou mas ainda está como 'scheduled'
      const classData = createMockClass('5', 'scheduled', { start: -60, end: -30 })
      expect(await getEffectiveClassStatus(classData)).toBe('scheduled')
    })
  })

  describe('needsStatusUpdate', () => {
    it('deve identificar aula agendada que deveria estar em andamento', async () => {
      const classData = createMockClass('6', 'scheduled', { start: -15, end: 15 })
      const result = await needsStatusUpdate(classData)

      expect(result.needsUpdate).toBe(true)
      expect(result.expectedStatus).toBe('ongoing')
    })

    it('deve identificar aula em andamento que deveria estar concluída', async () => {
      const classData = createMockClass('7', 'ongoing', { start: -60, end: -30 })
      const result = await needsStatusUpdate(classData)

      expect(result.needsUpdate).toBe(true)
      expect(result.expectedStatus).toBe('completed')
    })

    it('deve identificar quando status já está correto', async () => {
      const classData = createMockClass('8', 'scheduled', { start: 30, end: 60 })
      const result = await needsStatusUpdate(classData)

      expect(result.needsUpdate).toBe(false)
      expect(result.expectedStatus).toBe('scheduled')
    })

    it('não deve sugerir atualização para aulas canceladas', async () => {
      const classData = createMockClass('9', 'cancelled', { start: -60, end: -30 })
      const result = await needsStatusUpdate(classData)

      expect(result.needsUpdate).toBe(false)
      expect(result.reason).toContain('não pode ser atualizado automaticamente')
    })
  })

  describe('transformClassesWithEffectiveStatus', () => {
    it('deve adicionar campo effectiveStatus para todas as aulas', async () => {
      const classes = [createMockClass('10', 'scheduled', { start: 30, end: 60 }), createMockClass('11', 'cancelled', { start: -60, end: -30 }), createMockClass('12', 'ongoing', { start: -15, end: 15 })]

      const result = await transformClassesWithEffectiveStatus(classes)

      expect(result).toHaveLength(3)
      expect(result[0]).toHaveProperty('effectiveStatus', 'scheduled')
      expect(result[1]).toHaveProperty('effectiveStatus', 'cancelled')
      expect(result[2]).toHaveProperty('effectiveStatus', 'ongoing')
    })
  })

  describe('filterClassesByEffectiveStatus', () => {
    it('deve filtrar aulas por status efetivo', async () => {
      const classes = [createMockClass('13', 'scheduled', { start: 30, end: 60 }), createMockClass('14', 'cancelled', { start: -60, end: -30 }), createMockClass('15', 'ongoing', { start: -15, end: 15 }), createMockClass('16', 'completed', { start: -90, end: -60 })]

      const scheduledClasses = await filterClassesByEffectiveStatus(classes, 'scheduled')
      const ongoingClasses = await filterClassesByEffectiveStatus(classes, 'ongoing')
      const cancelledClasses = await filterClassesByEffectiveStatus(classes, 'cancelled')

      expect(scheduledClasses).toHaveLength(1)
      expect(ongoingClasses).toHaveLength(1)
      expect(cancelledClasses).toHaveLength(1)
    })
  })

  describe('groupClassesByEffectiveStatus', () => {
    it('deve agrupar aulas por status efetivo', async () => {
      const classes = [createMockClass('17', 'scheduled', { start: 30, end: 60 }), createMockClass('18', 'scheduled', { start: 60, end: 90 }), createMockClass('19', 'cancelled', { start: -60, end: -30 }), createMockClass('20', 'ongoing', { start: -15, end: 15 })]

      const grouped = await groupClassesByEffectiveStatus(classes)

      expect(grouped['scheduled']).toHaveLength(2)
      expect(grouped['cancelled']).toHaveLength(1)
      expect(grouped['ongoing']).toHaveLength(1)
      expect(grouped['completed']).toBeUndefined()
    })
  })
})

describe('Backward Compatibility', () => {
  it('deve manter compatibilidade com código existente que espera campos específicos', async () => {
    const classData = createMockClass('21', 'scheduled', { start: 30, end: 60 })

    // Simular transformação como era feita no código antigo
    const legacyTransform = {
      ...classData,
      calculatedStatus: await getEffectiveClassStatus(classData)
    }

    // E como é feita no código novo
    const newTransform = {
      ...classData,
      effectiveStatus: await getEffectiveClassStatus(classData)
    }

    expect(legacyTransform.calculatedStatus).toBe(newTransform.effectiveStatus)
  })

  it('deve ser resiliente a diferentes formatos de data', async () => {
    const now = new Date()
    const classData = {
      id: '22',
      status: 'scheduled',
      start_time: now.toISOString(), // ISO string
      end_time: new Date(now.getTime() + 60000).toString(), // toString format
      tenant_id: 'test-tenant'
    }

    // Não deve quebrar com formatos diferentes
    await expect(async () => await getEffectiveClassStatus(classData)).not.toThrow()
  })
})

describe('Edge Cases', () => {
  it('deve lidar com datas inválidas graciosamente', async () => {
    const classData = {
      id: '23',
      status: 'scheduled',
      start_time: 'invalid-date',
      end_time: 'invalid-date',
      tenant_id: 'test-tenant'
    }

    await expect(async () => await getEffectiveClassStatus(classData)).not.toThrow()
  })

  it('deve lidar com fuso horário corretamente', async () => {
    const now = new Date()
    const classData = {
      id: '24',
      status: 'scheduled',
      start_time: now.toISOString(),
      end_time: new Date(now.getTime() + 3600000).toISOString(), // 1 hora depois
      tenant_id: 'test-tenant'
    }

    const status = await getEffectiveClassStatus(classData)
    expect(['scheduled', 'ongoing']).toContain(status)
  })
})
