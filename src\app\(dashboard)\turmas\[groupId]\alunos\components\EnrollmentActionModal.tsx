'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { AlertTriangle, UserMinus, PauseCircle, PlayCircle, Loader2 } from 'lucide-react';

export type EnrollmentActionType = 'cancel' | 'pause' | 'reactivate';

interface EnrollmentActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (reason?: string) => Promise<{ success: boolean; error?: string }>;
  actionType: EnrollmentActionType;
  studentName: string;
  isLoading: boolean;
}

const actionConfig = {
  cancel: {
    title: 'Cancelar Matrícula',
    icon: UserMinus,
    description: 'Esta ação cancelará permanentemente a matrícula do aluno na turma.',
    reasonLabel: 'Motivo do cancelamento (opcional)',
    reasonPlaceholder: 'Ex: Solicitação do aluno, mudança de planos...',
    confirmText: 'Cancelar Matrícula',
    confirmVariant: 'destructive' as const,
    warningText: 'Atenção: Esta ação não pode ser desfeita. O aluno precisará ser matriculado novamente se desejar retornar.'
  },
  pause: {
    title: 'Pausar Matrícula',
    icon: PauseCircle,
    description: 'Esta ação pausará temporariamente a matrícula do aluno na turma.',
    reasonLabel: 'Motivo da pausa (opcional)',
    reasonPlaceholder: 'Ex: Viagem, problemas de saúde, pausa temporária...',
    confirmText: 'Pausar Matrícula',
    confirmVariant: 'default' as const,
    warningText: 'O aluno poderá ser reativado posteriormente mantendo seu histórico na turma.'
  },
  reactivate: {
    title: 'Reativar Matrícula',
    icon: PlayCircle,
    description: 'Esta ação reativará a matrícula do aluno na turma.',
    reasonLabel: 'Motivo da reativação (opcional)',
    reasonPlaceholder: 'Ex: Retorno após pausa, resolução de pendências...',
    confirmText: 'Reativar Matrícula',
    confirmVariant: 'default' as const,
    warningText: 'Verifique se há vagas disponíveis na turma antes de reativar.'
  }
};

export function EnrollmentActionModal({
  isOpen,
  onClose,
  onConfirm,
  actionType,
  studentName,
  isLoading
}: EnrollmentActionModalProps) {
  const [reason, setReason] = useState('');
  const config = actionConfig[actionType];
  const IconComponent = config.icon;

  const handleConfirm = async () => {
    const result = await onConfirm(reason.trim() || undefined);
    if (result.success) {
      setReason('');
      onClose();
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setReason('');
      onClose();
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (!open && !isLoading) {
      handleClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent 
        className="sm:max-w-md"
        onPointerDownOutside={(e) => {
          if (isLoading) e.preventDefault();
        }}
        onEscapeKeyDown={(e) => {
          if (isLoading) e.preventDefault();
        }}
      >
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className={`
              p-2 rounded-full 
              ${actionType === 'cancel' 
                ? 'bg-destructive/10 text-destructive' 
                : actionType === 'pause'
                  ? 'bg-amber-100 text-amber-600 dark:bg-amber-900 dark:text-amber-300'
                  : 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300'
              }
            `}>
              <IconComponent className="h-5 w-5" />
            </div>
            <div>
              <DialogTitle>{config.title}</DialogTitle>
              <DialogDescription className="mt-1">
                {config.description}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-muted/50 rounded-lg p-3">
            <p className="text-sm font-medium text-foreground">
              Aluno: <span className="text-muted-foreground">{studentName}</span>
            </p>
          </div>

          {config.warningText && (
            <div className="flex items-start gap-2 p-3 bg-amber-50 border border-amber-200 rounded-lg dark:bg-amber-950/20 dark:border-amber-800">
              <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-amber-800 dark:text-amber-200">
                {config.warningText}
              </p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="reason">{config.reasonLabel}</Label>
            <Textarea
              id="reason"
              placeholder={config.reasonPlaceholder}
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              disabled={isLoading}
              className="min-h-[80px] resize-none"
              maxLength={500}
            />
            {reason && (
              <p className="text-xs text-muted-foreground text-right">
                {reason.length}/500 caracteres
              </p>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button
            variant={config.confirmVariant}
            onClick={handleConfirm}
            disabled={isLoading}
          >
            {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            {config.confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 