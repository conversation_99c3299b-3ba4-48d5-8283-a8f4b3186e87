import { createAdminClient } from '@/services/supabase/server';
import { randomBytes } from 'crypto';

/**
 * Gerenciador de locks para cron jobs
 * Usa o banco de dados para garantir que apenas uma instância execute por vez
 */
export class CronLockManager {
  private jobName: string;
  private processId: string;
  private lockId: string | null = null;
  private timeoutMinutes: number;

  constructor(jobName: string, timeoutMinutes: number = 10) {
    this.jobName = jobName;
    this.timeoutMinutes = timeoutMinutes;
    // Criar identificador único para esta instância/processo
    this.processId = `${process.env.VERCEL_DEPLOYMENT_ID || 'local'}-${Date.now()}-${randomBytes(4).toString('hex')}`;
  }

  /**
   * Tenta adquirir o lock para o job
   */
  async acquireLock(executionId: string): Promise<{ success: boolean; message: string }> {
    try {
      const supabase = await createAdminClient();

      console.log(`🔒 Tentando adquirir lock para job '${this.jobName}' (processo: ${this.processId})`);

      const { data, error } = await supabase.rpc('acquire_cron_lock', {
        p_job_name: this.jobName,
        p_locked_by: this.processId,
        p_execution_id: executionId,
        p_timeout_minutes: this.timeoutMinutes
      });

      if (error) {
        console.error('❌ Erro ao adquirir lock:', error);
        return { success: false, message: `Erro no banco de dados: ${error.message}` };
      }

      if (!data || data.length === 0) {
        return { success: false, message: 'Resposta inválida do banco de dados' };
      }

      const result = data[0];
      if (result.success) {
        this.lockId = result.lock_id;
        console.log(`✅ Lock adquirido com sucesso: ${this.lockId}`);
        return { success: true, message: result.message };
      } else {
        console.warn(`⚠️ Falha ao adquirir lock: ${result.message}`);
        return { success: false, message: result.message };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      console.error('💥 Erro crítico ao adquirir lock:', error);
      return { success: false, message: `Erro crítico: ${errorMessage}` };
    }
  }

  /**
   * Libera o lock do job
   */
  async releaseLock(): Promise<{ success: boolean; message: string }> {
    if (!this.lockId) {
      return { success: true, message: 'Nenhum lock para liberar' };
    }

    try {
      const supabase = await createAdminClient();

      console.log(`🔓 Liberando lock para job '${this.jobName}' (processo: ${this.processId})`);

      const { data, error } = await supabase.rpc('release_cron_lock', {
        p_job_name: this.jobName,
        p_locked_by: this.processId
      });

      if (error) {
        console.error('❌ Erro ao liberar lock:', error);
        return { success: false, message: `Erro no banco de dados: ${error.message}` };
      }

      if (!data || data.length === 0) {
        return { success: false, message: 'Resposta inválida do banco de dados' };
      }

      const result = data[0];
      this.lockId = null;

      if (result.success) {
        console.log(`✅ Lock liberado com sucesso`);
        return { success: true, message: result.message };
      } else {
        console.warn(`⚠️ Aviso ao liberar lock: ${result.message}`);
        return { success: false, message: result.message };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      console.error('💥 Erro crítico ao liberar lock:', error);
      return { success: false, message: `Erro crítico: ${errorMessage}` };
    }
  }

  /**
   * Executa uma operação com lock automático
   */
  async executeWithLock<T>(
    executionId: string,
    operation: () => Promise<T>
  ): Promise<{ success: boolean; data?: T; error?: string; lockAcquired: boolean }> {
    let lockAcquired = false;

    try {
      // Tentar adquirir lock
      const lockResult = await this.acquireLock(executionId);
      if (!lockResult.success) {
        return {
          success: false,
          error: lockResult.message,
          lockAcquired: false
        };
      }

      lockAcquired = true;

      // Executar operação
      console.log(`🚀 Executando operação com lock para '${this.jobName}'`);
      const data = await operation();

      return {
        success: true,
        data,
        lockAcquired: true
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      console.error(`💥 Erro durante execução com lock:`, error);

      return {
        success: false,
        error: errorMessage,
        lockAcquired
      };

    } finally {
      // Sempre tentar liberar o lock
      if (lockAcquired) {
        const releaseResult = await this.releaseLock();
        if (!releaseResult.success) {
          console.error(`⚠️ Falha ao liberar lock: ${releaseResult.message}`);
        }
      }
    }
  }

  /**
   * Verifica se há um lock ativo para este job
   */
  async hasActiveLock(): Promise<{ hasLock: boolean; lockedBy?: string; expiresAt?: string }> {
    try {
      const supabase = await createAdminClient();

      const { data, error } = await supabase
        .from('cron_job_locks')
        .select('locked_by, expires_at')
        .eq('job_name', this.jobName)
        .gt('expires_at', new Date().toISOString())
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows found
        console.error('❌ Erro ao verificar lock ativo:', error);
        return { hasLock: false };
      }

      if (data) {
        return {
          hasLock: true,
          lockedBy: data.locked_by,
          expiresAt: data.expires_at
        };
      }

      return { hasLock: false };

    } catch (error) {
      console.error('💥 Erro crítico ao verificar lock ativo:', error);
      return { hasLock: false };
    }
  }

  // Getters para debugging
  get isLocked(): boolean {
    return this.lockId !== null;
  }

  get getProcessId(): string {
    return this.processId;
  }

  get getJobName(): string {
    return this.jobName;
  }
}

/**
 * Limpa locks expirados (função utilitária)
 */
export async function cleanupExpiredLocks(): Promise<{ cleaned: number }> {
  try {
    const supabase = await createAdminClient();

    const { data, error } = await supabase.rpc('cleanup_expired_cron_locks');

    if (error) {
      console.error('❌ Erro ao limpar locks expirados:', error);
      return { cleaned: 0 };
    }

    const cleaned = data || 0;
    if (cleaned > 0) {
      console.log(`🧹 Limpeza de locks: ${cleaned} locks expirados removidos`);
    }

    return { cleaned };

  } catch (error) {
    console.error('💥 Erro crítico na limpeza de locks:', error);
    return { cleaned: 0 };
  }
} 