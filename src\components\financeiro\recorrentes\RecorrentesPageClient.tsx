'use client';

import { useState, useEffect } from 'react';
import {
  PageHeader,
  MetricsGrid,
  ProximasCobrancas,
  StatusRecorrencias,
  AssinaturasRecorrentes
} from '@/components/financeiro/recorrentes';
import { getRecurrenceStatusStats, type RecurrenceStatusData } from '@/app/(dashboard)/financeiro/recorrentes/actions';
import {
  getAssinaturasRecorrentes,
  pausarAssinatura,
  retomarAssinatura,
  tentarCobrancaNovamente,
  executarAcaoEmMassa,
  type AssinaturaRecorrente
} from '@/app/(dashboard)/financeiro/recorrentes/actions/assinaturas-actions';
import { User } from '@/app/(dashboard)/alunos/components/selectusers/types';
import { toast } from 'sonner';

export function RecorrentesPageClient() {
  const [statusData, setStatusData] = useState<RecurrenceStatusData | null>(null);
  const [statusLoading, setStatusLoading] = useState(true);
  const [assinaturas, setAssinaturas] = useState<AssinaturaRecorrente[]>([]);
  const [assinaturasLoading, setAssinaturasLoading] = useState(true);
  const [isSelecting, setIsSelecting] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);

  // Buscar dados de status das recorrências
  useEffect(() => {
    const fetchStatusData = async () => {
      try {
        setStatusLoading(true);
        const result = await getRecurrenceStatusStats();

        if (result.success && result.data) {
          setStatusData(result.data);
        } else {
          console.error('Erro ao buscar status das recorrências:', result.errors);
        }
      } catch (error) {
        console.error('Erro ao buscar status das recorrências:', error);
      } finally {
        setStatusLoading(false);
      }
    };

    fetchStatusData();
  }, []);

  // Buscar assinaturas recorrentes
  useEffect(() => {
    const fetchAssinaturas = async () => {
      try {
        setAssinaturasLoading(true);
        const result = await getAssinaturasRecorrentes();

        if (result.success && result.data) {
          setAssinaturas(result.data);
        } else {
          console.error('Erro ao buscar assinaturas:', result.errors);
          toast.error('Erro ao carregar assinaturas recorrentes');
        }
      } catch (error) {
        console.error('Erro ao buscar assinaturas:', error);
        toast.error('Erro ao carregar assinaturas recorrentes');
      } finally {
        setAssinaturasLoading(false);
      }
    };

    fetchAssinaturas();
  }, []);

  const handlePausar = async (membershipId: string) => {
    try {
      const result = await pausarAssinatura(membershipId);

      if (result.success) {
        toast.success(result.message);
        // Recarregar dados
        const updatedResult = await getAssinaturasRecorrentes();
        if (updatedResult.success && updatedResult.data) {
          setAssinaturas(updatedResult.data);
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Erro ao pausar assinatura:', error);
      toast.error('Erro ao pausar assinatura');
    }
  };

  const handleRetomar = async (membershipId: string) => {
    try {
      const result = await retomarAssinatura(membershipId);

      if (result.success) {
        toast.success(result.message);
        // Recarregar dados
        const updatedResult = await getAssinaturasRecorrentes();
        if (updatedResult.success && updatedResult.data) {
          setAssinaturas(updatedResult.data);
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Erro ao retomar assinatura:', error);
      toast.error('Erro ao retomar assinatura');
    }
  };

  const handleTentarNovamente = async (membershipId: string) => {
    try {
      const result = await tentarCobrancaNovamente(membershipId);

      if (result.success) {
        toast.success(result.message);
        // Recarregar dados
        const updatedResult = await getAssinaturasRecorrentes();
        if (updatedResult.success && updatedResult.data) {
          setAssinaturas(updatedResult.data);
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Erro ao tentar cobrança novamente:', error);
      toast.error('Erro ao tentar cobrança novamente');
    }
  };

  const handleSelectUser = (user: User) => {
    setSelectedUsers(prev => {
      const isSelected = prev.some(u => u.id === user.id);
      if (isSelected) {
        return prev.filter(u => u.id !== user.id);
      } else {
        return [...prev, user];
      }
    });
  };

  const toggleSelecting = () => {
    setIsSelecting(!isSelecting);
    if (isSelecting) {
      setSelectedUsers([]);
    }
  };

  const handleRefresh = async () => {
    try {
      setAssinaturasLoading(true);
      const result = await getAssinaturasRecorrentes();

      if (result.success && result.data) {
        setAssinaturas(result.data);
        toast.success('Dados atualizados com sucesso');
      } else {
        console.error('Erro ao buscar assinaturas:', result.errors);
        toast.error('Erro ao carregar assinaturas');
      }
    } catch (error) {
      console.error('Erro ao buscar assinaturas:', error);
      toast.error('Erro ao carregar assinaturas');
    } finally {
      setAssinaturasLoading(false);
    }
  };

  const handleBulkAction = async (action: string, userIds: string[]) => {
    try {
      const result = await executarAcaoEmMassa(action, userIds);

      if (result.success) {
        toast.success(result.message);

        // Limpar seleção após ação bem-sucedida
        setSelectedUsers([]);
        setIsSelecting(false);

        // Recarregar dados
        const updatedResult = await getAssinaturasRecorrentes();
        if (updatedResult.success && updatedResult.data) {
          setAssinaturas(updatedResult.data);
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Erro ao executar ação em massa:', error);
      toast.error('Erro ao executar ação em massa');
    }
  };

  return (
    <>
      <div className="mb-6">
        <PageHeader
          title="Assinaturas e Pagamentos"
          description="Configure e gerencie assinaturas recorrentes, pagamentos únicos e cobrança automática."
        />
      </div>

      <MetricsGrid showComparison={true} />

      <div className="grid gap-6 md:grid-cols-2 mb-6">
        <ProximasCobrancas />
        <StatusRecorrencias
          data={statusData || undefined}
          isLoading={statusLoading}
        />
      </div>

      <AssinaturasRecorrentes
        assinaturas={assinaturas}
        isLoading={assinaturasLoading}
        isSelecting={isSelecting}
        selectedUsers={selectedUsers}
        onSelectUser={handleSelectUser}
        onToggleSelecting={toggleSelecting}
        onPausar={handlePausar}
        onRetomar={handleRetomar}
        onTentarNovamente={handleTentarNovamente}
        onRefresh={handleRefresh}
        onBulkAction={handleBulkAction}
      />
    </>
  );
}
