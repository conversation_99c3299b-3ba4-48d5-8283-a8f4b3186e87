'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { NavigationItem } from '@/components/layout/sidebar/types';
import { navigation } from '@/components/layout/sidebar/navigation';
import { HomeIcon, CalendarIcon } from "@heroicons/react/24/solid";
import { usePathname } from 'next/navigation';

interface FavoritesContextData {
  favorites: NavigationItem[];
  addToFavorites: (item: NavigationItem) => void;
  removeFromFavorites: (href: string) => void;
  isFavorite: (href: string) => boolean;
  toggleFavorite: (item: NavigationItem) => void;
  recentlyVisited: NavigationItem[];
  addToRecentlyVisited: (item: NavigationItem) => void;
  getRecentlyVisitedItems: (maxItems?: number) => NavigationItem[];
}

const FavoritesContext = createContext<FavoritesContextData>({} as FavoritesContextData);

interface FavoritesProviderProps {
  children: React.ReactNode;
}

const FAVORITES_STORAGE_KEY = 'sidebar-favorites';
const RECENTLY_VISITED_STORAGE_KEY = 'sidebar-recently-visited';
const MAX_RECENT_ITEMS = 10;

const principalItems: NavigationItem[] = [
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: HomeIcon,
    adminOnly: true,
    instructorOnly: false,
    metadata: {
      description: "Painel administrativo",
      category: "Principal",
      isMainFeature: true
    }
  },
  {
    name: "Home",
    href: "/home",
    icon: HomeIcon,
    adminOnly: false,
    instructorOnly: false,
    metadata: {
      description: "Página inicial",
      category: "Principal",
      isMainFeature: true
    }
  },
  {
    name: "Minha Agenda",
    href: "/agenda",
    icon: CalendarIcon,
    adminOnly: false,
    instructorOnly: false,
    metadata: {
      description: "Visualize suas aulas e compromissos",
      category: "Principal",
      isMainFeature: false
    }
  }
];

const findNavigationItem = (href: string): NavigationItem | null => {
  const allItems = [
    ...principalItems,
    ...navigation.academia,
    ...navigation.treinos,
    ...navigation.presencas,
    ...navigation.financeiro,
    ...navigation.configuracoes,
    ...navigation.loja,
  ];
  
  // Normalizar href removendo barras trailing para comparação
  const normalizeHref = (url: string) => url.replace(/\/$/, '') || '/';
  const normalizedHref = normalizeHref(href);
  
  // Tentar encontrar uma correspondência exata primeiro
  let item = allItems.find(item => item.href === href);
  
  // Se não encontrar, tentar com hrefs normalizados
  if (!item) {
    item = allItems.find(item => normalizeHref(item.href) === normalizedHref);
  }
  
  return item || null;
};

const migrateLegacyFavorites = (savedData: any): string[] => {
  if (!savedData) return [];
  
  try {
    const parsed = typeof savedData === 'string' ? JSON.parse(savedData) : savedData;
    
    if (Array.isArray(parsed) && parsed.every(item => typeof item === 'string')) {
      return parsed;
    }
    
    if (Array.isArray(parsed) && parsed.every(item => typeof item === 'object' && item.href)) {
      console.log('Migrando favoritos do formato antigo para o novo...');
      return parsed.map(item => item.href).filter(Boolean);
    }
    
    console.warn('Dados de favoritos em formato não reconhecido, limpando...');
    return [];
  } catch (error) {
    console.error('Erro ao migrar favoritos:', error);
    return [];
  }
};

const migrateRecentlyVisited = (savedData: any): string[] => {
  if (!savedData) return [];
  
  try {
    const parsed = typeof savedData === 'string' ? JSON.parse(savedData) : savedData;
    
    if (Array.isArray(parsed) && parsed.every(item => typeof item === 'string')) {
      return parsed;
    }
    
    if (Array.isArray(parsed) && parsed.every(item => typeof item === 'object' && item.href)) {
      console.log('Migrando histórico recente do formato antigo para o novo...');
      return parsed.map(item => item.href).filter(Boolean);
    }
    
    return [];
  } catch (error) {
    console.error('Erro ao migrar histórico recente:', error);
    return [];
  }
};

export function FavoritesProvider({ children }: FavoritesProviderProps) {
  const [favoriteHrefs, setFavoriteHrefs] = useState<string[]>([]);
  const [recentlyVisitedHrefs, setRecentlyVisitedHrefs] = useState<string[]>([]);
  const [isClient, setIsClient] = useState(false);
  const pathname = usePathname();

  const addToRecentlyVisited = useCallback((item: NavigationItem) => {
    setRecentlyVisitedHrefs(prev => {
      const filteredPrev = prev.filter(href => href !== item.href);
      
      const newList = [item.href, ...filteredPrev];
      
      return newList.slice(0, MAX_RECENT_ITEMS);
    });
  }, []);

  useEffect(() => {
    setIsClient(true);
    
    const savedFavorites = localStorage.getItem(FAVORITES_STORAGE_KEY);
    if (savedFavorites) {
      try {
        const migratedHrefs = migrateLegacyFavorites(savedFavorites);
        setFavoriteHrefs(migratedHrefs);
        
        if (migratedHrefs.length > 0) {
          localStorage.setItem(FAVORITES_STORAGE_KEY, JSON.stringify(migratedHrefs));
        }
      } catch (error) {
        console.error('Erro ao carregar favoritos:', error);
        setFavoriteHrefs([]);
        localStorage.removeItem(FAVORITES_STORAGE_KEY);
      }
    }

    const savedRecentlyVisited = localStorage.getItem(RECENTLY_VISITED_STORAGE_KEY);
    if (savedRecentlyVisited) {
      try {
        const migratedRecentHrefs = migrateRecentlyVisited(savedRecentlyVisited);
        setRecentlyVisitedHrefs(migratedRecentHrefs);
      } catch (error) {
        console.error('Erro ao carregar histórico recente:', error);
        setRecentlyVisitedHrefs([]);
        localStorage.removeItem(RECENTLY_VISITED_STORAGE_KEY);
      }
    }
  }, []);

  // Rastrear mudanças de pathname e adicionar ao histórico
  useEffect(() => {
    if (!isClient || !pathname) return;

    // Usar requestAnimationFrame para garantir que seja executado após a renderização
    const rafId = requestAnimationFrame(() => {
      // Segundo requestAnimationFrame para garantir que todos os componentes renderizaram
      const rafId2 = requestAnimationFrame(() => {
        const currentItem = findNavigationItem(pathname);
        if (currentItem) {
          console.log('FavoritesContext - Página renderizada, adicionando ao histórico:', currentItem.name);
          addToRecentlyVisited(currentItem);
        }
      });
      
      return () => cancelAnimationFrame(rafId2);
    });

    return () => cancelAnimationFrame(rafId);
  }, [pathname, isClient, addToRecentlyVisited]);

  useEffect(() => {
    if (isClient) {
      localStorage.setItem(FAVORITES_STORAGE_KEY, JSON.stringify(favoriteHrefs));
    }
  }, [favoriteHrefs, isClient]);

  useEffect(() => {
    if (isClient) {
      localStorage.setItem(RECENTLY_VISITED_STORAGE_KEY, JSON.stringify(recentlyVisitedHrefs));
    }
  }, [recentlyVisitedHrefs, isClient]);

  const favorites: NavigationItem[] = favoriteHrefs
    .map(href => findNavigationItem(href))
    .filter((item): item is NavigationItem => item !== null);

  const recentlyVisited: NavigationItem[] = recentlyVisitedHrefs
    .map(href => findNavigationItem(href))
    .filter((item): item is NavigationItem => item !== null);

  const addToFavorites = (item: NavigationItem) => {
    setFavoriteHrefs(prev => {
      if (prev.includes(item.href)) {
        return prev;
      }
      return [...prev, item.href];
    });
  };

  const removeFromFavorites = (href: string) => {
    setFavoriteHrefs(prev => prev.filter(favHref => favHref !== href));
  };

  const isFavorite = (href: string) => {
    return favoriteHrefs.includes(href);
  };

  const toggleFavorite = (item: NavigationItem) => {
    if (isFavorite(item.href)) {
      removeFromFavorites(item.href);
    } else {
      addToFavorites(item);
    }
  };

  const getRecentlyVisitedItems = (maxItems: number = 6): NavigationItem[] => {
    return recentlyVisited
      .filter(item => !isFavorite(item.href))
      .slice(0, maxItems);
  };

  return (
    <FavoritesContext.Provider
      value={{
        favorites,
        addToFavorites,
        removeFromFavorites,
        isFavorite,
        toggleFavorite,
        recentlyVisited,
        addToRecentlyVisited,
        getRecentlyVisitedItems,
      }}
    >
      {children}
    </FavoritesContext.Provider>
  );
}

export function useFavorites() {
  const context = useContext(FavoritesContext);
  if (!context) {
    throw new Error('useFavorites deve ser usado dentro de um FavoritesProvider');
  }
  return context;
}