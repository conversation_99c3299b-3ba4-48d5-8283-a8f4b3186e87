'use client';

import { useEffect, useState } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const toastVariants = cva(
  "fixed z-50 flex items-center gap-2 p-4 shadow-lg rounded-lg transition-all duration-300 max-w-[calc(100%-2rem)]",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground border dark:bg-zinc-800 dark:border-zinc-700",
        error: "bg-red-50 border border-red-200 text-red-800 dark:bg-red-900 dark:border-red-800 dark:text-red-100",
        success: "bg-green-50 border border-green-200 text-green-800 dark:bg-green-900 dark:border-green-800 dark:text-green-100",
        warning: "bg-yellow-50 border border-yellow-200 text-yellow-800 dark:bg-yellow-900 dark:border-yellow-800 dark:text-yellow-100",
        info: "bg-blue-50 border border-blue-200 text-blue-800 dark:bg-blue-900 dark:border-blue-800 dark:text-blue-100",
      },
      position: {
        'top-right': "top-4 right-4",
        'top-left': "top-4 left-4",
        'bottom-right': "bottom-4 right-4", 
        'bottom-left': "bottom-4 left-4",
        'bottom-center': "bottom-4 left-1/2 -translate-x-1/2",
      }
    },
    defaultVariants: {
      variant: "default",
      position: "bottom-right"
    }
  }
);

export interface ToastAlertProps extends VariantProps<typeof toastVariants> {
  message: string;
  duration?: number;
  onClose?: () => void;
  icon?: React.ReactNode;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'bottom-center';
  showCloseButton?: boolean;
}

export function ToastAlert({ 
  message, 
  variant, 
  position,
  duration = 5000, 
  onClose,
  icon,
  showCloseButton = true
}: ToastAlertProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    // Montar o componente
    setIsMounted(true);
    
    // Pequeno delay para iniciar a animação de entrada
    const entryTimer = setTimeout(() => {
      setIsVisible(true);
    }, 50);
    
    // Timer para animação de saída
    const exitTimer = setTimeout(() => {
      setIsVisible(false);
    }, duration);
    
    // Timer para desmontar o componente após a animação de saída
    const unmountTimer = setTimeout(() => {
      setIsMounted(false);
      if (onClose) onClose();
    }, duration + 300); // 300ms é a duração da animação
    
    return () => {
      clearTimeout(entryTimer);
      clearTimeout(exitTimer);
      clearTimeout(unmountTimer);
    };
  }, [duration, onClose]);

  // Função para fechar o toast manualmente
  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      setIsMounted(false);
      if (onClose) onClose();
    }, 300);
  };

  if (!isMounted) return null;

  // Renderiza o ícone baseado na variante, se nenhum ícone personalizado for fornecido
  const defaultIcon = !icon ? (
    variant === 'error' ? (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-600 dark:text-red-300 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
      </svg>
    ) : variant === 'success' ? (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600 dark:text-green-300 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
      </svg>
    ) : variant === 'warning' ? (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-600 dark:text-yellow-300 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
      </svg>
    ) : (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600 dark:text-blue-300 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
      </svg>
    )
  ) : icon;

  // Definir a direção da animação com base na posição
  const getAnimationClass = () => {
    if (!isVisible) {
      if (position === 'top-left' || position === 'bottom-left') {
        return "-translate-x-4 opacity-0";
      } else if (position === 'top-right' || position === 'bottom-right') {
        return "translate-x-4 opacity-0";
      } else {
        return "translate-y-4 opacity-0";
      }
    }
    return "translate-x-0 translate-y-0 opacity-100";
  };

  return (
    <div 
      className={cn(
        toastVariants({ variant, position }),
        getAnimationClass(),
        "flex items-center justify-between",
        // Garantir que o toast não seja cortado em telas pequenas
        "w-full sm:w-auto sm:max-w-sm md:max-w-md"
      )}
      role="alert"
    >
      <div className="flex items-center gap-2 mr-2">
        {defaultIcon}
        <span className="font-medium text-sm sm:text-base flex-1 break-words">{message}</span>
      </div>
      
      {showCloseButton && (
        <button 
          onClick={handleClose}
          className="ml-3 flex-shrink-0 text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-100 focus:ring-blue-500 rounded"
          aria-label="Fechar"
        >
          <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </div>
  );
} 