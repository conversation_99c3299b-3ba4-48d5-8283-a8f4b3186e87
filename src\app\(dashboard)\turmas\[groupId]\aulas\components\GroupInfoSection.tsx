'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Users, MapPin, Calendar, User } from 'lucide-react';
import { GroupClassInfo } from '../types';

interface GroupInfoSectionProps {
  groupInfo: GroupClassInfo;
  isLoading?: boolean;
}

export function GroupInfoSection({ groupInfo, isLoading = false }: GroupInfoSectionProps) {
  if (isLoading) {
    return (
      <Card className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 shadow-sm">
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="flex items-start space-x-4">
              <div className="h-16 w-16 bg-slate-200 dark:bg-gray-700 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-6 bg-slate-200 dark:bg-gray-700 rounded w-1/3"></div>
                <div className="h-4 bg-slate-200 dark:bg-gray-700 rounded w-1/2"></div>
                <div className="flex space-x-2">
                  <div className="h-6 bg-slate-200 dark:bg-gray-700 rounded w-16"></div>
                  <div className="h-6 bg-slate-200 dark:bg-gray-700 rounded w-20"></div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getCategoryLabel = (category: string) => {
    const categories: Record<string, string> = {
      'kids': 'Infantil',
      'teens': 'Adolescente',
      'adults': 'Adulto',
      'seniors': 'Sênior',
    };
    return categories[category] || category;
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'kids': 'bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-400',
      'teens': 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
      'adults': 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
      'seniors': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    };
    return colors[category] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
  };

  const getInstructorInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part.charAt(0))
      .slice(0, 2)
      .join('')
      .toUpperCase();
  };

  return (
    <Card className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 shadow-sm">
      <CardContent className="p-6">
        <div className="flex items-start space-x-4">
          
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 truncate">
                  {groupInfo.name}
                </h1>
                
                {groupInfo.description && (
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {groupInfo.description}
                  </p>
                )}
                
                <div className="flex flex-wrap items-center gap-2 mt-3">
                  <Badge className={getCategoryColor(groupInfo.category)}>
                    {getCategoryLabel(groupInfo.category)}
                  </Badge>
                  
                  <Badge 
                    variant={groupInfo.isActive ? "default" : "secondary"}
                    className={groupInfo.isActive ? 
                      "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400" : 
                      "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
                    }
                  >
                    {groupInfo.isActive ? 'Ativa' : 'Inativa'}
                  </Badge>
                </div>
              </div>
            </div>
            
            <div className="flex flex-wrap items-center gap-4 mt-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4" />
                <span>{groupInfo.instructor.name}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4" />
                <span>{groupInfo.branch.name}</span>
              </div>
              
              {groupInfo.maxCapacity && (
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4" />
                  <span>Capacidade: {groupInfo.maxCapacity}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 