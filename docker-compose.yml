services:
  # Serviço para desenvolvimento
  # apexsaas-dev:
  #   build:
  #     context: .
  #     target: development
  #     args:
  #       - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
  #       - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
  #   container_name: apexsaas-dev
  #   restart: unless-stopped
  #   # Configurações de memória
  #   mem_limit: 6g
  #   memswap_limit: 8g
  #   volumes:
  #     - .:/app
  #     - /app/node_modules
  #     - /app/.next
  #   # REMOVIDO: ports expostos (Traefik vai gerenciar)
  #   environment:
  #     - NODE_ENV=development
  #     - NODE_OPTIONS=--max-old-space-size=4096
  #     - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
  #     - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
  #     - NEXT_PUBLIC_BASE_DOMAIN=sondtheanime.site
  #     - COOKIE_DOMAIN=.sondtheanime.site
  #     - NEXT_PUBLIC_CHATWOOT_WEBSITE_TOKEN=${NEXT_PUBLIC_CHATWOOT_WEBSITE_TOKEN}
  #     - NEXT_PUBLIC_CHATWOOT_BASE_URL=${NEXT_PUBLIC_CHATWOOT_BASE_URL}
  #     # Variáveis para permitir fallback em desenvolvimento
  #     - NEXT_PUBLIC_TENANT_FALLBACK=true
  #     - NEXT_PUBLIC_SKIP_TENANT_CHECK=true
  #     # Habilita o polling para detecção de alterações de arquivos
  #     - WATCHPACK_POLLING=true
  #     - CHOKIDAR_USEPOLLING=true
  #   networks:
  #     - coolify  # Rede do Coolify
  #   extra_hosts:
  #     - "host.docker.internal:host-gateway"
  #   labels:
  #     # Habilitar Traefik
  #     - "traefik.enable=true"
      
  #     # Middlewares
  #     - "traefik.http.middlewares.gzip.compress=true"
  #     - "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https"
      
  #     # Roteador HTTP (redireciona para HTTPS) - Domínio raiz
  #     - "traefik.http.routers.http-apexsaas-root.entryPoints=http"
  #     - "traefik.http.routers.http-apexsaas-root.middlewares=redirect-to-https"
  #     - "traefik.http.routers.http-apexsaas-root.rule=Host(`sondtheanime.site`)"
  #     - "traefik.http.routers.http-apexsaas-root.service=apexsaas-service"
      
  #     # Roteador HTTP (redireciona para HTTPS) - Wildcard subdomínios
  #     - "traefik.http.routers.http-apexsaas-wildcard.entryPoints=http"
  #     - "traefik.http.routers.http-apexsaas-wildcard.middlewares=redirect-to-https"
  #     - "traefik.http.routers.http-apexsaas-wildcard.rule=HostRegexp(`{subdomain:[a-zA-Z0-9-]+}.sondtheanime.site`)"
  #     - "traefik.http.routers.http-apexsaas-wildcard.service=apexsaas-wildcard"
      
  #     # Roteador HTTPS - Domínio raiz
  #     - "traefik.http.routers.https-apexsaas-root.entryPoints=https"
  #     - "traefik.http.routers.https-apexsaas-root.middlewares=gzip"
  #     - "traefik.http.routers.https-apexsaas-root.rule=Host(`sondtheanime.site`)"
  #     - "traefik.http.routers.https-apexsaas-root.service=apexsaas-service"
  #     - "traefik.http.routers.https-apexsaas-root.tls.certresolver=letsencrypt"
  #     - "traefik.http.routers.https-apexsaas-root.tls=true"
      
  #     # Roteador HTTPS - Wildcard subdomínios
  #     - "traefik.http.routers.https-apexsaas-wildcard.entryPoints=https"
  #     - "traefik.http.routers.https-apexsaas-wildcard.middlewares=gzip"
  #     - "traefik.http.routers.https-apexsaas-wildcard.rule=HostRegexp(`{subdomain:[a-zA-Z0-9-]+}.sondtheanime.site`)"
  #     - "traefik.http.routers.https-apexsaas-wildcard.service=apexsaas-wildcard"
  #     - "traefik.http.routers.https-apexsaas-wildcard.tls.certresolver=letsencrypt"
  #     - "traefik.http.routers.https-apexsaas-wildcard.tls=true"
      
  #     # Serviço (apontando para porta 3000 do container)
  #     - "traefik.http.services.apexsaas-service.loadbalancer.server.port=3000"

  # Serviço para produção
  apexsaas-prod:
    build:
      context: .
      target: production
      args:
        - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
        - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
        - NEXT_PUBLIC_CHATWOOT_WEBSITE_TOKEN=${NEXT_PUBLIC_CHATWOOT_WEBSITE_TOKEN}
        - NEXT_PUBLIC_CHATWOOT_BASE_URL=${NEXT_PUBLIC_CHATWOOT_BASE_URL}
    container_name: apexsaas-prod
    restart: unless-stopped
    ports:
      - "3000:3000"  # Expondo a porta 3000 do Nextjs
    environment:
      - NODE_ENV=production
      - NODE_OPTIONS=--max-old-space-size=2048
      - TZ=America/Sao_Paulo
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - NEXT_PUBLIC_BASE_DOMAIN=sondtheanime.site
      - COOKIE_DOMAIN=.sondtheanime.site
      - NEXT_PUBLIC_CHATWOOT_WEBSITE_TOKEN=${NEXT_PUBLIC_CHATWOOT_WEBSITE_TOKEN}
      - NEXT_PUBLIC_CHATWOOT_BASE_URL=${NEXT_PUBLIC_CHATWOOT_BASE_URL}
      # Desativar configurações específicas de localhost
      - NEXT_PUBLIC_TENANT_FALLBACK=false
      - NEXT_PUBLIC_SKIP_TENANT_CHECK=false
    # networks: # Se acontecer erro 502 remover isso para testar
    #   - coolify  # Rede do Coolify 
    labels:
      - "traefik.enable=true"
      # - "traefik.docker.network=coolify" # Se acontecer erro 502 remover isso para testar

      # Middlewares
      - "traefik.http.middlewares.gzip.compress=true"
      - "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https"

      # Serviço
      - "traefik.http.services.apexsaas-prod-service.loadbalancer.server.port=3000"

      # Roteador HTTP para redirecionamento
      - "traefik.http.routers.apexsaas-prod-http.entrypoints=http"
      - "traefik.http.routers.apexsaas-prod-http.rule=HostRegexp(`^.+\\.sondtheanime\\.site$`)"
      - "traefik.http.routers.apexsaas-prod-http.middlewares=redirect-to-https"
      - "traefik.http.routers.apexsaas-prod-http.service=apexsaas-prod-service"
      - "traefik.http.routers.apexsaas-prod-http.priority=10"

      # Roteador HTTPS principal
      - "traefik.http.routers.apexsaas-prod-https.entrypoints=https"
      - "traefik.http.routers.apexsaas-prod-https.rule=HostRegexp(`^.+\\.sondtheanime\\.site$`)"
      - "traefik.http.routers.apexsaas-prod-https.middlewares=gzip"
      - "traefik.http.routers.apexsaas-prod-https.service=apexsaas-prod-service"
      - "traefik.http.routers.apexsaas-prod-https.priority=10"
      - "traefik.http.routers.apexsaas-prod-https.tls=true"
      - "traefik.http.routers.apexsaas-prod-https.tls.certresolver=letsencrypt"
      - "traefik.http.routers.apexsaas-prod-https.tls.domains[0].main=sondtheanime.site"
      - "traefik.http.routers.apexsaas-prod-https.tls.domains[0].sans=*.sondtheanime.site"

# networks: # Se acontecer erro 502 remover isso para testar
#   coolify:
#     external: true  # Usar a rede externa do Coolify