'use client';

import { useUserMetadata } from '@/hooks/user/Auth';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';

export default function UserProfileInfo() {
  const { metadata, isLoading } = useUserMetadata();

  if (isLoading) {
    return (
      <div className="flex items-center space-x-4">
        <Skeleton className="h-12 w-12 rounded-full" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-[250px]" />
          <Skeleton className="h-4 w-[200px]" />
        </div>
      </div>
    );
  }

  if (!metadata) {
    return null;
  }

  const fullName = `${metadata.first_name} ${metadata.last_name}`;
  const initials = `${metadata.first_name?.[0] || ''}${metadata.last_name?.[0] || ''}`;

  return (
    <div className="flex items-center space-x-4">
      <Avatar>
        <AvatarImage src={metadata.avatar_url} alt={fullName} />
        <AvatarFallback>{initials}</AvatarFallback>
      </Avatar>
      <div>
        <h3 className="font-medium">{fullName}</h3>
        <p className="text-sm text-muted-foreground">{metadata.email}</p>
        <span className="text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
          {metadata.role}
        </span>
      </div>
    </div>
  );
} 