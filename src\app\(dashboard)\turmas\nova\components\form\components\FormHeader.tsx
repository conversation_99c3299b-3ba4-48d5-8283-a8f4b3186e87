import { Button } from '@/components/ui/button';
import { Save, X } from 'lucide-react';
import type { FormHeaderProps } from '../types/class-group-form-types';

export const FormHeader = ({
  isEditMode,
  isSubmitting,
  primaryColor,
  onCancel,
}: FormHeaderProps) => {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-2xl font-bold text-foreground">
          {isEditMode ? 'Editar Grupo de Aulas' : 'Novo Grupo de Aulas'}
        </h1>
        <p className="text-muted-foreground">
          {isEditMode 
            ? 'Atualize as informações do grupo de aulas' 
            : 'Preencha as informações para criar um novo grupo de aulas'
          }
        </p>
      </div>
      
      <div className="flex gap-2">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          <X className="w-4 h-4 mr-2" />
          Cancelar
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
          className="bg-primary text-primary-foreground"
          style={primaryColor ? { backgroundColor: primaryColor } : undefined}
        >
          <Save className="w-4 h-4 mr-2" />
          {isSubmitting 
            ? (isEditMode ? 'Atualizando...' : 'Criando...') 
            : (isEditMode ? 'Atualizar' : 'Criar Grupo')
          }
        </Button>
      </div>
    </div>
  );
}; 