'use client';

import { useClassGroups } from '@/hooks/turmas/use-class-groups';

import { ClassGroupCard } from './class-group-card';
import { EmptyState } from '../../aulas/components/list/empty-state';
import { ListSkeleton } from '../../aulas/components/list';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useMemo, useState } from 'react';
import { toast } from 'sonner';
import type { ClassGroupFilters, ClassGroupPagination } from '@/hooks/turmas/use-class-groups';
import type { ClassGroupFilter, ClassGroupWithDetails, PaginatedResult } from '../../aulas/types';
import { ViewToggle, ViewMode } from '../../aulas/components/list/view-toggle';
import { Pagination } from '../../aulas/components/pagination/pagination';
import { ClassGroupsTable } from '../../aulas/components/list/class-groups-table';

// Componente customizado que usa ClassGroupCard
interface ClassGroupsListProps {
  classGroups: ClassGroupWithDetails[];
  pagination: PaginatedResult<ClassGroupWithDetails>['pagination'];
  currentFilters: ClassGroupFilter;
  isLoading?: boolean;
  onRefresh?: () => void;
  isFetching?: boolean;
}

function ClassGroupsList({ 
  classGroups, 
  pagination, 
  currentFilters,
  isLoading = false,
  onRefresh,
  isFetching = false
}: ClassGroupsListProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('card');

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">
            Mostrando {classGroups.length} de {pagination.total} grupos de aulas
          </span>
          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={isFetching}
              className="gap-2"
              aria-label="Atualizar lista"
            >
              <RefreshCw 
                className={`h-4 w-4 transition-transform ${isFetching ? 'animate-spin' : ''}`}
              />
              {isFetching ? 'Atualizando...' : 'Atualizar'}
            </Button>
          )}
        </div>
        
        <ViewToggle 
          mode={viewMode} 
          onModeChange={setViewMode} 
        />
      </div>

      {viewMode === 'card' ? (
        <div className={`grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 ${isLoading ? 'opacity-50 pointer-events-none' : ''}`}>
          {classGroups.map((classGroup) => (
            <ClassGroupCard 
              key={classGroup.id} 
              classGroup={classGroup}
            />
          ))}
        </div>
      ) : (
        <div className={isLoading ? 'opacity-50 pointer-events-none' : ''}>
          <ClassGroupsTable 
            groups={classGroups}
          />
        </div>
      )}

      {pagination.totalPages > 1 && (
        <Pagination
          currentPage={pagination.page}
          totalPages={pagination.totalPages}
          hasNext={pagination.hasNext}
          hasPrev={pagination.hasPrev}
        />
      )}
    </div>
  );
}

interface ClassGroupsListClientContainerProps {
  searchParams: {
    search?: string;
    instructor_id?: string;
    branch_id?: string;
    category?: string;
    is_active?: string;
    has_availability?: string;
    page?: string;
    limit?: string;
    sort_by?: string;
    sort_order?: string;
  };
}

export function ClassGroupsListClientContainer({ 
  searchParams 
}: ClassGroupsListClientContainerProps) {
  
  // Usar apenas filtros locais, ignorando URL parameters
  const initialFilters: ClassGroupFilters = {};
  const initialPagination: ClassGroupPagination = {
    page: 1,
    limit: 10,
    sort_by: 'name',
    sort_order: 'asc',
  };

  const {
    classGroups,
    pagination,
    isLoading,
    isFetching,
    error,
    hasActiveFilters,
    refetch,
    filters,
    setFilters,
    clearFilters,
    stats
  } = useClassGroups(initialFilters, initialPagination);

  // Converter filtros para o tipo esperado pelo ClassGroupsList
  const convertedFilters: ClassGroupFilter = useMemo(() => ({
    search: filters.search,
    instructor_id: filters.instructor_id,
    branch_id: filters.branch_id,
    category: filters.category,
    is_active: filters.is_active,
    has_availability: filters.has_availability,
    min_belt_level: filters.min_belt_level?.toString(),
    max_belt_level: filters.max_belt_level?.toString(),
    start_date_from: filters.start_date_from,
    start_date_to: filters.start_date_to,
  }), [filters]);

  // Loading state
  if (isLoading) {
    return <ListSkeleton />;
  }

  // Error state
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <span>{error instanceof Error ? error.message : 'Erro ao carregar turmas'}</span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isFetching}
            className="ml-4"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isFetching ? 'animate-spin' : ''}`} />
            Tentar novamente
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  // Empty state
  if (classGroups.length === 0) {
    return <EmptyState hasFilters={hasActiveFilters} />;
  }

  return (
    <div className="space-y-4">
      {/* Estatísticas rápidas */}
      {stats.total > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <p className="text-xs text-muted-foreground">Total</p>
          </div>
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-4">
            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
            <p className="text-xs text-muted-foreground">Ativas</p>
          </div>
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-4">
            <div className="text-2xl font-bold text-orange-600">{stats.withAvailability}</div>
            <p className="text-xs text-muted-foreground">Com vagas</p>
          </div>
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-4">
            <div className="text-2xl font-bold text-red-600">{stats.full}</div>
            <p className="text-xs text-muted-foreground">Lotadas</p>
          </div>
        </div>
      )}

      <ClassGroupsList
        classGroups={classGroups}
        pagination={pagination!}
        currentFilters={convertedFilters}
        isLoading={isFetching}
        onRefresh={refetch}
        isFetching={isFetching}
      />
    </div>
  );
} 