"use server";

/**
 * Server Actions para métricas de alunos
 */

import { DashboardActionResult, StudentMetrics, DateRange } from '../../types/dashboard-types';
import { getAuthenticatedClient } from '../shared/auth-utils';

/**
 * Busca métricas de alunos para um período
 */
export async function getStudentMetrics(
  currentRange: DateRange
): Promise<DashboardActionResult<StudentMetrics>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    // Buscar alunos ativos (com matrículas ativas)
    const { data: activeStudentsData } = await supabase
      .from('memberships')
      .select('student_id')
      .eq('tenant_id', tenantId)
      .eq('status', 'active');

    // Buscar total de estudantes
    const { data: totalStudentsData } = await supabase
      .from('students')
      .select('id')
      .eq('tenant_id', tenantId);

    const totalStudents = (totalStudentsData || []).length;
    const activeStudents = (activeStudentsData || []).length;

    // Para MVP, usar valores simplificados
    const newStudents = 0; // TODO: Implementar cálculo real
    const churnedStudents = 0; // TODO: Implementar cálculo real
    const retentionRate = totalStudents > 0 ? (activeStudents / totalStudents) * 100 : 0;
    const churnRate = 100 - retentionRate;
    const averageLifetimeValue = 0; // TODO: Implementar cálculo real

    const metrics: StudentMetrics = {
      totalStudents,
      activeStudents,
      newStudents,
      churnedStudents,
      retentionRate,
      churnRate,
      averageLifetimeValue
    };

    return {
      success: true,
      data: metrics,
      message: 'Métricas de alunos obtidas com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}
