import { createAuthClient } from '@/services/supabase/server/auth-client';

export interface UsageStats {
  messagesUsed: number;
  messagesLimit: number;
  remainingMessages: number;
  canSendMessage: boolean;
  resetDate: string; // Data em que o limite será resetado (próximo dia)
}

export class AIChatUsageLimitService {
  private static readonly DAILY_MESSAGE_LIMIT = 5;

  /**
   * Verifica se o usuário pode enviar uma mensagem
   */
  static async canUserSendMessage(userId: string, tenantId: string): Promise<UsageStats> {
    try {
      const { supabase } = await createAuthClient();
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD

      // Buscar ou criar registro de uso para hoje
      const { data: usage, error } = await supabase
        .from('ai_chat_usage')
        .select('message_count')
        .eq('user_id', userId)
        .eq('usage_date', today)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = não encontrado
        console.error('Erro ao verificar limite de uso da IA:', error);
        throw new Error('Erro ao verificar limite de uso');
      }

      const messagesUsed = usage?.message_count || 0;
      const canSendMessage = messagesUsed < this.DAILY_MESSAGE_LIMIT;

      // Calcular data de reset (próximo dia)
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);

      return {
        messagesUsed,
        messagesLimit: this.DAILY_MESSAGE_LIMIT,
        remainingMessages: Math.max(0, this.DAILY_MESSAGE_LIMIT - messagesUsed),
        canSendMessage,
        resetDate: tomorrow.toISOString(),
      };
    } catch (error) {
      console.error('Erro no serviço de limite de uso:', error);
      throw error;
    }
  }

  /**
   * Incrementa o contador de mensagens do usuário
   */
  static async incrementMessageCount(userId: string, tenantId: string): Promise<void> {
    try {
      const { supabase } = await createAuthClient();
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD

      // Primeiro, tentar usar a função PostgreSQL para incremento atômico
      const { error: rpcError } = await supabase.rpc(
        'increment_ai_message_count',
        {
          p_user_id: userId,
          p_tenant_id: tenantId,
          p_usage_date: today,
        }
      );

      if (!rpcError) {
        // Sucesso usando a função PostgreSQL
        return;
      }

      // Se a função RPC falhar, usar abordagem manual
      console.warn('Função increment_ai_message_count não disponível, usando método manual');

      // Buscar registro atual
      const { data: existingUsage, error: selectError } = await supabase
        .from('ai_chat_usage')
        .select('message_count')
        .eq('user_id', userId)
        .eq('usage_date', today)
        .single();

      if (selectError && selectError.code !== 'PGRST116') {
        console.error('Erro ao buscar registro existente:', selectError);
        throw new Error('Erro ao verificar registro de uso');
      }

      if (existingUsage) {
        // Atualizar registro existente incrementando o contador
        const { error: updateError } = await supabase
          .from('ai_chat_usage')
          .update({ 
            message_count: existingUsage.message_count + 1,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId)
          .eq('usage_date', today);

        if (updateError) {
          console.error('Erro ao atualizar contador de mensagens:', updateError);
          throw new Error('Erro ao incrementar contador de uso');
        }
      } else {
        // Criar novo registro
        const { error: insertError } = await supabase
          .from('ai_chat_usage')
          .insert({
            user_id: userId,
            tenant_id: tenantId,
            usage_date: today,
            message_count: 1,
          });

        if (insertError) {
          console.error('Erro ao criar registro de uso:', insertError);
          throw new Error('Erro ao criar registro de uso');
        }
      }
    } catch (error) {
      console.error('Erro ao incrementar contador de mensagens:', error);
      throw error;
    }
  }

  /**
   * Obtém estatísticas de uso para um usuário
   */
  static async getUserUsageStats(userId: string): Promise<UsageStats> {
    try {
      const { supabase } = await createAuthClient();
      const today = new Date().toISOString().split('T')[0];

      const { data: usage, error } = await supabase
        .from('ai_chat_usage')
        .select('message_count')
        .eq('user_id', userId)
        .eq('usage_date', today)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Erro ao buscar estatísticas de uso:', error);
        throw new Error('Erro ao buscar estatísticas de uso');
      }

      const messagesUsed = usage?.message_count || 0;
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);

      return {
        messagesUsed,
        messagesLimit: this.DAILY_MESSAGE_LIMIT,
        remainingMessages: Math.max(0, this.DAILY_MESSAGE_LIMIT - messagesUsed),
        canSendMessage: messagesUsed < this.DAILY_MESSAGE_LIMIT,
        resetDate: tomorrow.toISOString(),
      };
    } catch (error) {
      console.error('Erro ao obter estatísticas de uso:', error);
      throw error;
    }
  }

  /**
   * Reseta o contador de mensagens para um usuário (admin function)
   */
  static async resetUserUsage(userId: string, adminUserId: string): Promise<void> {
    try {
      const { supabase } = await createAuthClient();
      const today = new Date().toISOString().split('T')[0];

      // Verificar se o usuário admin tem permissão (opcional)
      // TODO: Implementar verificação de permissões de admin

      const { error } = await supabase
        .from('ai_chat_usage')
        .upsert({
          user_id: userId,
          usage_date: today,
          message_count: 0,
        });

      if (error) {
        console.error('Erro ao resetar uso do usuário:', error);
        throw new Error('Erro ao resetar contador de uso');
      }
    } catch (error) {
      console.error('Erro ao resetar uso do usuário:', error);
      throw error;
    }
  }
} 