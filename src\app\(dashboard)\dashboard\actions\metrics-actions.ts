'use server'

import { createClient } from '@/services/supabase/server'
import { StudentsMetrics, RevenueMetrics, StudentsChartData } from '../types'

function getBrazilDate(): Date {
  return new Date(new Date().toLocaleString("en-US", { timeZone: "America/Sao_Paulo" }))
}

function formatBrazilDate(date: Date): string {
  const brazilDate = new Date(date.toLocaleString("en-US", { timeZone: "America/Sao_Paulo" }))
  return brazilDate.toISOString().split('T')[0]
}

export async function getStudentsMetrics(tenantId: string): Promise<StudentsMetrics> {
  const supabase = await createClient()

  try {
    const { data } = await supabase.rpc('get_students_metrics_optimized', {
      p_tenant_id: tenantId
    })

    if (data && data.length > 0) {
      const metrics = data[0]
      return {
        activeStudentsNow: metrics.active_students_now || 0,
        activeStudentsLast: metrics.active_students_last || 0,
        newStudentsNow: metrics.new_students_now || 0,
        newStudentsLast: metrics.new_students_last || 0
      }
    }

    const now = getBrazilDate()
    const startOfCurrentMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const endOfCurrentMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)
    const formatDate = (date: Date) => formatBrazilDate(date)

    const { count: activeStudentsNow } = await supabase.from('students').select('users!students_user_id_fkey!inner(status)', { count: 'exact', head: true }).eq('tenant_id', tenantId).is('deleted_at', null).eq('users.status', 'active')

    const { count: activeStudentsLast } = await supabase
      .from('students')
      .select('users!students_user_id_fkey!inner(status)', { count: 'exact', head: true })
      .eq('tenant_id', tenantId)
      .lte('created_at', formatDate(endOfLastMonth))
      .or(`deleted_at.is.null,deleted_at.gt.${formatDate(endOfLastMonth)}`)
      .eq('users.status', 'active')

    const { count: newStudentsNow } = await supabase.from('students').select('users!students_user_id_fkey!inner(status)', { count: 'exact', head: true }).eq('tenant_id', tenantId).gte('created_at', formatDate(startOfCurrentMonth)).lte('created_at', formatDate(endOfCurrentMonth)).eq('users.status', 'active')

    const { count: newStudentsLast } = await supabase.from('students').select('users!students_user_id_fkey!inner(status)', { count: 'exact', head: true }).eq('tenant_id', tenantId).gte('created_at', formatDate(startOfLastMonth)).lte('created_at', formatDate(endOfLastMonth)).eq('users.status', 'active')

    return {
      activeStudentsNow,
      activeStudentsLast,
      newStudentsNow,
      newStudentsLast
    }
  } catch (error) {
    console.error('Error fetching students metrics:', error)
    return {
      activeStudentsNow: 0,
      activeStudentsLast: 0,
      newStudentsNow: 0,
      newStudentsLast: 0
    }
  }
}

export async function getStudentsChartData(tenantId: string): Promise<StudentsChartData[]> {
  const supabase = await createClient()

  try {
    const { data } = await supabase.rpc('get_students_chart_data_optimized', {
      p_tenant_id: tenantId,
      p_days: 30
    })

    if (data && data.length > 0) {
      return data.map((row: any) => ({
        date: new Date(row.day_date).toLocaleDateString('pt-BR', {
          day: '2-digit',
          month: '2-digit'
        }),
        activeStudents: row.active_count || 0,
        inactiveStudents: row.inactive_count || 0
      }))
    }

    const endDate = getBrazilDate()
    const startDate = getBrazilDate()
    startDate.setDate(endDate.getDate() - 30)

    const { data: studentsData } = await supabase
      .from('students')
      .select(
        `
        created_at,
        deleted_at,
        users!students_user_id_fkey!inner(status)
      `
      )
      .eq('tenant_id', tenantId)
      .order('created_at', { ascending: true })

    if (!studentsData) {
      return Array.from({ length: 30 }, (_, i) => {
        const currentDate = new Date(startDate)
        currentDate.setDate(startDate.getDate() + i)
        return {
          date: currentDate.toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit'
          }),
          activeStudents: 0,
          inactiveStudents: 0
        }
      })
    }

    const chartData: StudentsChartData[] = []

    for (let i = 0; i < 30; i++) {
      const currentDate = new Date(startDate)
      currentDate.setDate(startDate.getDate() + i)
      const dateStr = currentDate.toISOString().split('T')[0]

      let activeStudents = 0
      let inactiveStudents = 0

      studentsData.forEach((student) => {
        const createdAt = new Date(student.created_at).toISOString().split('T')[0]
        const deletedAt = student.deleted_at ? new Date(student.deleted_at).toISOString().split('T')[0] : null
        const userStatus = (student.users as any)?.status

        if (createdAt <= dateStr) {
          if ((!deletedAt || deletedAt > dateStr) && userStatus === 'active') {
            activeStudents++
          } else if ((deletedAt && deletedAt <= dateStr) || userStatus !== 'active') {
            inactiveStudents++
          }
        }
      })

      chartData.push({
        date: currentDate.toLocaleDateString('pt-BR', {
          day: '2-digit',
          month: '2-digit'
        }),
        activeStudents,
        inactiveStudents
      })
    }

    return chartData
  } catch (error) {
    console.error('Error fetching students chart data:', error)

    const endDate = getBrazilDate()
    const startDate = getBrazilDate()
    startDate.setDate(endDate.getDate() - 30)

    return Array.from({ length: 30 }, (_, i) => {
      const currentDate = new Date(startDate)
      currentDate.setDate(startDate.getDate() + i)

      return {
        date: currentDate.toLocaleDateString('pt-BR', {
          day: '2-digit',
          month: '2-digit'
        }),
        activeStudents: 0,
        inactiveStudents: 0
      }
    })
  }
}

export async function getRevenueMetrics(tenantId: string): Promise<RevenueMetrics> {
  const supabase = await createClient()

  const now = getBrazilDate()
  const startOfCurrentMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const endOfCurrentMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)

  const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)

  const formatDate = (date: Date) => formatBrazilDate(date)

  try {
    const [currentRevenueResult, lastRevenueResult] = await Promise.all([
      supabase
        .from('payments')
        .select('amount')
        .eq('tenant_id', tenantId)
        .in('status', ['succeeded', 'paid'])
        .gte('paid_at', formatDate(startOfCurrentMonth))
        .lte('paid_at', formatDate(endOfCurrentMonth)),

      supabase
        .from('payments')
        .select('amount')
        .eq('tenant_id', tenantId)
        .in('status', ['succeeded', 'paid'])
        .gte('paid_at', formatDate(startOfLastMonth))
        .lte('paid_at', formatDate(endOfLastMonth))
    ])

    const revenueNow = (currentRevenueResult.data || []).reduce((sum, payment) => sum + Number(payment.amount), 0)
    const revenueLast = (lastRevenueResult.data || []).reduce((sum, payment) => sum + Number(payment.amount), 0)

    return {
      revenueNow,
      revenueLast
    }
  } catch (error) {
    console.error('Error fetching revenue metrics:', error)
    return {
      revenueNow: 0,
      revenueLast: 0
    }
  }
}

export async function getRevenueGoalMetrics(tenantId: string): Promise<{
  currentRevenue: number;
  expectedRevenue: number;
}> {
  const supabase = await createClient()

  const now = getBrazilDate()
  const startOfCurrentMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const endOfCurrentMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)

  const formatDate = (date: Date) => formatBrazilDate(date)

  try {
    const [currentRevenueResult, expectedRevenueResult] = await Promise.all([
      supabase
        .from('payments')
        .select('amount')
        .eq('tenant_id', tenantId)
        .in('status', ['succeeded', 'paid'])
        .gte('due_date', formatDate(startOfCurrentMonth))
        .lte('due_date', formatDate(endOfCurrentMonth)),

      supabase
        .from('payments')
        .select('amount')
        .eq('tenant_id', tenantId)
        .gte('due_date', formatDate(startOfCurrentMonth))
        .lte('due_date', formatDate(endOfCurrentMonth))
    ])

    const currentRevenue = (currentRevenueResult.data || []).reduce((sum, payment) => sum + Number(payment.amount), 0)
    const expectedRevenue = (expectedRevenueResult.data || []).reduce((sum, payment) => sum + Number(payment.amount), 0)

    return {
      currentRevenue,
      expectedRevenue
    }
  } catch (error) {
    console.error('Error fetching revenue goal metrics:', error)
    return {
      currentRevenue: 0,
      expectedRevenue: 0
    }
  }
}

async function getCurrentRetentionRate(supabase: any, tenantId: string): Promise<number> {
  try {
    const { count: totalActiveStudents } = await supabase.from('students').select('users!students_user_id_fkey!inner(status)', { count: 'exact', head: true }).eq('tenant_id', tenantId).is('deleted_at', null).eq('users.status', 'active')

    const { count: allStudents } = await supabase.from('students').select('*', { count: 'exact', head: true }).eq('tenant_id', tenantId)

    const totalStudentsCount = allStudents || 0

    if (totalStudentsCount === 0) {
      return 0
    }

    return ((totalActiveStudents || 0) / totalStudentsCount) * 100
  } catch (error) {
    console.error('Error in getCurrentRetentionRate:', error)
    return 0
  }
}

export async function getRetentionMetrics(tenantId: string): Promise<{
  retentionRate: number;
  retentionRateLast: number
}> {
  const supabase = await createClient()

  try {
    const { data } = await supabase.rpc('get_monthly_retention_rate_optimized', {
      p_tenant_id: tenantId,
      p_months: 2
    })

    if (data && data.length >= 2) {
      const sortedData = data.sort((a: any, b: any) => {
        if (a.year !== b.year) return b.year - a.year
        return b.month - a.month
      })

      return {
        retentionRate: sortedData[0].retention_rate || 0,
        retentionRateLast: sortedData[1].retention_rate || 0 
      }
    }

    const currentRetention = await getCurrentRetentionRate(supabase, tenantId)
    return {
      retentionRate: currentRetention,
      retentionRateLast: 0
    }
  } catch (error) {
    console.error('Error in getRetentionMetrics:', error)
    return { retentionRate: 0, retentionRateLast: 0 }
  }
}

export async function getMonthlyRevenue(tenantId: string, months = 6): Promise<{ name: string; value: number }[]> {
  const supabase = await createClient()

  try {
    const { data } = await supabase.rpc('get_monthly_revenue_optimized', {
      p_tenant_id: tenantId,
      p_months: months
    })

    if (data) {
      const monthNames = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez']
      return data.map((row: any) => ({
        name: monthNames[row.month - 1] || `Mês ${row.month}`,
        value: Number(row.revenue_total) || 0
      }))
    }

    const revenueData = []
    const monthNames = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez']

    for (let i = months - 1; i >= 0; i--) {
      const date = getBrazilDate()
      date.setMonth(date.getMonth() - i)

      const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1)
      const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0)

      const { data, error } = await supabase.from('payments').select('amount').eq('tenant_id', tenantId).in('status', ['succeeded', 'paid']).gte('paid_at', formatBrazilDate(startOfMonth)).lte('paid_at', formatBrazilDate(endOfMonth))

      if (error) {
        console.error(`Error fetching revenue for month ${i}:`, error)
        revenueData.push({ name: monthNames[date.getMonth()], value: 0 })
        continue
      }

      const monthlyTotal = data?.reduce((sum, payment) => sum + Number(payment.amount), 0) || 0
      revenueData.push({ name: monthNames[date.getMonth()], value: monthlyTotal })
    }

    return revenueData
  } catch (error) {
    console.error('Error in getMonthlyRevenue:', error)
    return []
  }
}

export async function getDailyActiveStudents(tenantId: string, days = 30): Promise<{ name: string; value: number }[]> {
  const supabase = await createClient()

  try {
    const { data } = await supabase.rpc('get_daily_active_students_optimized', {
      p_tenant_id: tenantId,
      p_days: days
    })

    if (data) {
      return data.map((row: any) => ({
        name: new Date(row.date).toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' }),
        value: row.active_count || 0
      }))
    }

    const { data: fallbackData } = await supabase
      .from('students')
      .select(
        `
        created_at,
        deleted_at,
        users!students_user_id_fkey!inner(status)
      `
      )
      .eq('tenant_id', tenantId)
      .eq('users.status', 'active')
      .order('created_at', { ascending: true })

    const dailyData: { name: string; value: number }[] = []
    const now = getBrazilDate()

    for (let i = days - 1; i >= 0; i--) {
      const currentDate = getBrazilDate()
      currentDate.setDate(now.getDate() - i)
      const dateStr = formatBrazilDate(currentDate)

      const activeCount = (fallbackData || []).filter((student: any) => {
        const createdAt = new Date(student.created_at).toISOString().split('T')[0]
        const deletedAt = student.deleted_at ? new Date(student.deleted_at).toISOString().split('T')[0] : null

        return createdAt <= dateStr && (!deletedAt || deletedAt > dateStr)
      }).length

      dailyData.push({
        name: currentDate.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' }),
        value: activeCount
      })
    }

    return dailyData
  } catch (error) {
    console.error('Error in getDailyActiveStudents:', error)
    return []
  }
}

export async function getDailyNewStudents(tenantId: string, days = 30): Promise<{ name: string; value: number }[]> {
  const supabase = await createClient()

  try {
    const { data } = await supabase.rpc('get_daily_new_students_optimized', {
      p_tenant_id: tenantId,
      p_days: days
    })

    if (data) {
      return data.map((row: any) => ({
        name: new Date(row.date).toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' }),
        value: row.new_count || 0
      }))
    }

    const endDate = getBrazilDate()
    const startDate = getBrazilDate()
    startDate.setDate(endDate.getDate() - (days - 1))

    const { data: aggregatedData } = await supabase
      .from('students')
      .select(
        `
        created_at,
        users!students_user_id_fkey!inner(status)
      `
      )
      .eq('tenant_id', tenantId)
      .eq('users.status', 'active')
      .gte('created_at', startDate.toISOString().split('T')[0])
      .lte('created_at', endDate.toISOString().split('T')[0])

    const dateMap = new Map<string, number>()

    for (let i = days - 1; i >= 0; i--) {
      const currentDate = getBrazilDate()
      currentDate.setDate(currentDate.getDate() - i)
      const dateKey = formatBrazilDate(currentDate)
      dateMap.set(dateKey, 0)
    }

    ; (aggregatedData || []).forEach((student: any) => {
      const dateKey = formatBrazilDate(new Date(student.created_at))
      if (dateMap.has(dateKey)) {
        dateMap.set(dateKey, (dateMap.get(dateKey) || 0) + 1)
      }
    })

    const dailyData: { name: string; value: number }[] = []
    for (let i = days - 1; i >= 0; i--) {
      const currentDate = getBrazilDate()
      currentDate.setDate(currentDate.getDate() - i)
      const dateKey = formatBrazilDate(currentDate)

      dailyData.push({
        name: currentDate.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' }),
        value: dateMap.get(dateKey) || 0
      })
    }

    return dailyData
  } catch (error) {
    console.error('Error in getDailyNewStudents:', error)
    return []
  }
}

export async function getDailyRetentionRate(tenantId: string, days = 30): Promise<{ name: string; value: number }[]> {
  const supabase = await createClient()

  try {
    const { data } = await supabase.rpc('get_daily_retention_rate_optimized', {
      p_tenant_id: tenantId,
      p_days: days
    })

    if (data) {
      return data.map((row: any) => ({
        name: new Date(row.date).toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' }),
        value: Math.max(0, row.retention_rate || 0)
      }))
    }

    const { data: studentsData } = await supabase
      .from('students')
      .select(
        `
        created_at,
        deleted_at,
        users!students_user_id_fkey!inner(status)
      `
      )
      .eq('tenant_id', tenantId)
      .order('created_at', { ascending: true })

    const dailyData: { name: string; value: number }[] = []

    for (let i = days - 1; i >= 0; i--) {
      const currentDate = getBrazilDate()
      currentDate.setDate(currentDate.getDate() - i)
      const dateStr = formatBrazilDate(currentDate)

      let totalActive = 0
      let totalStudents = 0

        ; (studentsData || []).forEach((student: any) => {
          const createdAt = new Date(student.created_at).toISOString().split('T')[0]
          const deletedAt = student.deleted_at ? new Date(student.deleted_at).toISOString().split('T')[0] : null

          if (createdAt <= dateStr) {
            totalStudents++

            if ((!deletedAt || deletedAt > dateStr) && student.users?.status === 'active') {
              totalActive++
            }
          }
        })

      const retentionRate = totalStudents === 0 ? 0 : (totalActive / totalStudents) * 100

      dailyData.push({
        name: currentDate.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' }),
        value: Math.max(0, retentionRate)
      })
    }

    return dailyData
  } catch (error) {
    console.error('Error in getDailyRetentionRate:', error)
    return []
  }
}

export async function getMonthlyNewStudents(tenantId: string, months = 6): Promise<{ name: string; value: number }[]> {
  const supabase = await createClient()

  try {
    const { data } = await supabase.rpc('get_monthly_new_students_optimized', {
      p_tenant_id: tenantId,
      p_months: months
    })

    if (data) {
      const monthNames = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez']
      return data.map((row: any) => ({
        name: monthNames[row.month - 1] || `Mês ${row.month}`,
        value: row.new_count || 0
      }))
    }

    const newStudentsData = []
    const monthNames = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez']

    const endDate = getBrazilDate()
    const startDate = getBrazilDate()
    startDate.setMonth(endDate.getMonth() - (months - 1))
    startDate.setDate(1)

    const { data: aggregatedData } = await supabase
      .from('students')
      .select(
        `
        created_at,
        users!students_user_id_fkey!inner(status)
      `
      )
      .eq('tenant_id', tenantId)
      .eq('users.status', 'active')
      .gte('created_at', startDate.toISOString().split('T')[0])
      .lte('created_at', endDate.toISOString().split('T')[0])

    for (let i = months - 1; i >= 0; i--) {
      const date = getBrazilDate()
      date.setMonth(date.getMonth() - i)

      const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1)
      const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0)

      const monthCount = (aggregatedData || []).filter((student: any) => {
        const createdAt = new Date(student.created_at)
        return createdAt >= startOfMonth && createdAt <= endOfMonth
      }).length

      newStudentsData.push({
        name: monthNames[date.getMonth()],
        value: monthCount
      })
    }

    return newStudentsData
  } catch (error) {
    console.error('Error in getMonthlyNewStudents:', error)
    return []
  }
}

export async function getMonthlyRetentionRate(tenantId: string, months = 6): Promise<{ name: string; value: number }[]> {
  const supabase = await createClient()

  try {
    const { data } = await supabase.rpc('get_monthly_retention_rate_optimized', {
      p_tenant_id: tenantId,
      p_months: months
    })

    if (data) {
      const monthNames = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez']
      return data.map((row: any) => ({
        name: monthNames[row.month - 1] || `Mês ${row.month}`,
        value: Math.max(0, row.retention_rate || 0)
      }))
    }

    const { data: studentsData } = await supabase
      .from('students')
      .select(
        `
        created_at,
        deleted_at,
        users!students_user_id_fkey!inner(status)
      `
      )
      .eq('tenant_id', tenantId)
      .order('created_at', { ascending: true })

    const retentionData = []
    const monthNames = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez']

    for (let i = months - 1; i >= 0; i--) {
      const date = getBrazilDate()
      date.setMonth(date.getMonth() - i)

      const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0)
      const endOfMonthStr = formatBrazilDate(endOfMonth)

      let totalActive = 0
      let totalStudents = 0

        ; (studentsData || []).forEach((student: any) => {
          const createdAt = new Date(student.created_at).toISOString().split('T')[0]
          const deletedAt = student.deleted_at ? new Date(student.deleted_at).toISOString().split('T')[0] : null

          if (createdAt <= endOfMonthStr) {
            totalStudents++

            if ((!deletedAt || deletedAt > endOfMonthStr) && student.users?.status === 'active') {
              totalActive++
            }
          }
        })

      const retentionRate = totalStudents === 0 ? 0 : (totalActive / totalStudents) * 100

      retentionData.push({
        name: monthNames[date.getMonth()],
        value: Math.max(0, retentionRate)
      })
    }

    return retentionData
  } catch (error) {
    console.error('Error in getMonthlyRetentionRate:', error)
    return []
  }
}

export async function getInstructorsBeltDistribution(tenantId: string): Promise<{ name: string; value: number; color: string }[]> {
  const supabase = await createClient()

  try {
    const { data: instructors } = await supabase.from('instructors').select('id, current_belt_id').eq('tenant_id', tenantId).eq('is_active', true).is('deleted_at', null).not('current_belt_id', 'is', null)

    if (!instructors || instructors.length === 0) return []

    const beltDistribution = new Map<string, { count: number; color: string }>()

    await Promise.all(
      instructors.map(async (instructor) => {
        try {
          const { data: beltDetails } = await supabase.rpc('get_instructor_current_belt_details', {
            instructor_id_param: instructor.id
          })

          if (beltDetails && beltDetails.length > 0) {
            const belt = beltDetails[0]
            const beltLabel = belt.label || 'Faixa Não Definida'
            const beltColor = belt.belt_color || '#6b7280'

            const current = beltDistribution.get(beltLabel) || { count: 0, color: beltColor }
            beltDistribution.set(beltLabel, { count: current.count + 1, color: beltColor })
          }
        } catch (error) {
          console.error(`Error fetching belt details for instructor ${instructor.id}:`, error)
        }
      })
    )

    return Array.from(beltDistribution.entries()).map(([beltLabel, { count, color }]) => ({
      name: beltLabel,
      value: count,
      color: color || '#6b7280'
    }))
  } catch (error) {
    console.error('Error fetching instructors belt distribution:', error)
    return []
  }
}

export async function getInstructorsContractDistribution(tenantId: string): Promise<{ name: string; value: number; color: string }[]> {
  const supabase = await createClient()

  try {
    const { data: instructors } = await supabase.from('instructors').select('contract_type').eq('tenant_id', tenantId).eq('is_active', true).is('deleted_at', null)

    if (!instructors) return []

    const contractCounts: Record<string, number> = {}

    instructors.forEach((instructor) => {
      const contractType = instructor.contract_type || 'indefinido'
      contractCounts[contractType] = (contractCounts[contractType] || 0) + 1
    })

    const contractColors: Record<string, string> = {
      clt: '#10b981',
      autonomo: '#f59e0b',
      parceria: '#3b82f6',
      associacao: '#8b5cf6',
      indefinido: '#6b7280'
    }

    return Object.entries(contractCounts).map(([contract, count]) => ({
      name: contract === 'clt' ? 'CLT' : contract === 'autonomo' ? 'Autônomo' : contract === 'parceria' ? 'Parceria' : contract === 'associacao' ? 'Associação' : 'Indefinido',
      value: count,
      color: contractColors[contract] || '#6b7280'
    }))
  } catch (error) {
    console.error('Error fetching instructors contract distribution:', error)
    return []
  }
}
