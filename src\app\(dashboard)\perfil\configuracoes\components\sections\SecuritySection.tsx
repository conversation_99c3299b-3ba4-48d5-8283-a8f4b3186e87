'use client'

import { useState, useEffect, useCallback } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Card } from '@/components/ui/card'
import { Eye, EyeOff, KeyRound, LogOut } from 'lucide-react'
import { changePassword } from '@/services/auth/actions/change-password'
import { getActiveSessions, endOtherSessions, type SessionInfo, endSession } from '@/services/auth/actions/active-sessions'
import { toast } from 'sonner'

// Helper para extrair string da estrutura de erros (ZodFormattedError ou string[])
const getMsg = (err: any): string | null => {
  if (!err) return null
  if (typeof err === 'string') return err
  if (Array.isArray(err)) return err[0]
  if (typeof err === 'object' && '_errors' in err && Array.isArray(err._errors)) {
    return err._errors[0]
  }
  return null
}

export default function SecuritySection() {
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false)
  const [passwordVisibility, setPasswordVisibility] = useState({
    current: false,
    new: false,
    confirm: false
  })

  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  // Armazenar erros de forma flexível
  const [errors, setErrors] = useState<any>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // ───────────────── Sessions state ─────────────────
  const [sessions, setSessions] = useState<SessionInfo[]>([])
  const [sessionsLoading, setSessionsLoading] = useState(true)
  const [endingSessions, setEndingSessions] = useState(false)
  const [endingSessionId, setEndingSessionId] = useState<string | null>(null)

  // ───────────────── Helper para extrair nome do dispositivo ─────────────────
  const getDeviceName = (ua?: string | null) => {
    if (!ua) return 'Dispositivo'
    const uaLower = ua.toLowerCase()
    if (uaLower.includes('iphone')) return 'iPhone'
    if (uaLower.includes('ipad')) return 'iPad'
    if (uaLower.includes('android')) return 'Android'
    if (uaLower.includes('mac')) return 'macOS'
    if (uaLower.includes('windows')) return 'Windows'
    if (uaLower.includes('linux')) return 'Linux'
    return 'Outro dispositivo'
  }

  // Carrega sessões ativas
  const fetchSessions = useCallback(async () => {
    setSessionsLoading(true)
    const res = await getActiveSessions()
    if (res.success) {
      setSessions(res.sessions)
    } else {
      toast.error(res.error)
    }
    setSessionsLoading(false)
  }, [])

  useEffect(() => {
    fetchSessions()
  }, [fetchSessions])

  const handleEndOtherSessions = async () => {
    setEndingSessions(true)
    const res = await endOtherSessions()
    if (res.success) {
      toast.success('Sessões encerradas')
      await fetchSessions()
    } else {
      toast.error(res.error)
    }
    setEndingSessions(false)
  }

  const handleEndSession = async (id: string) => {
    setEndingSessionId(id)
    const res = await endSession(id)
    if (res.success) {
      toast.success('Sessão encerrada')
      await fetchSessions()
    } else {
      toast.error(res.error)
    }
    setEndingSessionId(null)
  }

  const handleInputChange = (field: 'currentPassword' | 'newPassword' | 'confirmPassword') =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormData(prev => ({ ...prev, [field]: e.target.value }))
    }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    setErrors({})
    try {
      const result = await changePassword(formData)
      if (result.success) {
        toast.success('Senha alterada com sucesso')
        setFormData({ currentPassword: '', newPassword: '', confirmPassword: '' })
      } else if (result.errors) {
        setErrors(result.errors as any)
        if ((result.errors as any)._form) {
          toast.error((result.errors as any)._form)
        }
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
    setPasswordVisibility(prev => ({
      ...prev,
      [field]: !prev[field]
    }))
  }

  return (
    <section className="space-y-8">
      {/* Password Change Card */}
      <Card className="overflow-hidden border border-border bg-muted/40">
        <div className="p-6 space-y-5">
          <h3 className="flex items-center gap-2 text-lg font-semibold">
            <KeyRound className="h-5 w-5 text-primary" /> Alterar senha
          </h3>
          
          <div className="space-y-4">
            {/* Current Password */}
            <div className="space-y-2">
              <Label htmlFor="currentPassword">Senha atual</Label>
              <div className="relative">
                <Input
                  id="currentPassword"
                  className="pe-9"
                  type={passwordVisibility.current ? "text" : "password"}
                  placeholder="Digite sua senha atual"
                  value={formData.currentPassword}
                  onChange={handleInputChange('currentPassword')}
                />
                <button
                  className="absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center text-muted-foreground/80 hover:text-foreground focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70"
                  type="button"
                  onClick={() => togglePasswordVisibility('current')}
                  aria-label={passwordVisibility.current ? "Esconder senha" : "Mostrar senha"}
                >
                  {passwordVisibility.current ? (
                    <EyeOff size={16} strokeWidth={2} aria-hidden="true" />
                  ) : (
                    <Eye size={16} strokeWidth={2} aria-hidden="true" />
                  )}
                </button>
              </div>
              {getMsg(errors.currentPassword) && (
                <p className="text-sm text-destructive mt-1">{getMsg(errors.currentPassword)}</p>
              )}
            </div>

            {/* New Password */}
            <div className="space-y-2">
              <Label htmlFor="newPassword">Nova senha</Label>
              <div className="relative">
                <Input
                  id="newPassword"
                  className="pe-9"
                  type={passwordVisibility.new ? "text" : "password"}
                  placeholder="Digite sua nova senha"
                  value={formData.newPassword}
                  onChange={handleInputChange('newPassword')}
                />
                <button
                  className="absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center text-muted-foreground/80 hover:text-foreground focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70"
                  type="button"
                  onClick={() => togglePasswordVisibility('new')}
                  aria-label={passwordVisibility.new ? "Esconder senha" : "Mostrar senha"}
                >
                  {passwordVisibility.new ? (
                    <EyeOff size={16} strokeWidth={2} aria-hidden="true" />
                  ) : (
                    <Eye size={16} strokeWidth={2} aria-hidden="true" />
                  )}
                </button>
              </div>
              {getMsg(errors.newPassword) && (
                <p className="text-sm text-destructive mt-1">{getMsg(errors.newPassword)}</p>
              )}
              <p className="text-xs text-muted-foreground">
                Use pelo menos 8 caracteres, incluindo letras, números e símbolos.
              </p>
            </div>

            {/* Confirm Password */}
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirmar nova senha</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  className="pe-9"
                  type={passwordVisibility.confirm ? "text" : "password"}
                  placeholder="Confirme sua nova senha"
                  value={formData.confirmPassword}
                  onChange={handleInputChange('confirmPassword')}
                />
                <button
                  className="absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center text-muted-foreground/80 hover:text-foreground focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70"
                  type="button"
                  onClick={() => togglePasswordVisibility('confirm')}
                  aria-label={passwordVisibility.confirm ? "Esconder senha" : "Mostrar senha"}
                >
                  {passwordVisibility.confirm ? (
                    <EyeOff size={16} strokeWidth={2} aria-hidden="true" />
                  ) : (
                    <Eye size={16} strokeWidth={2} aria-hidden="true" />
                  )}
                </button>
              </div>
              {getMsg(errors.confirmPassword) && (
                <p className="text-sm text-destructive mt-1">{getMsg(errors.confirmPassword)}</p>
              )}
            </div>
          </div>
          
          <div className="pt-2 space-y-2">
            {getMsg(errors._form) && (
              <p className="text-sm text-destructive">{getMsg(errors._form)}</p>
            )}
            <Button
              variant="secondary"
              className="w-full sm:w-auto"
              disabled={isSubmitting}
              onClick={handleSubmit}
            >
              {isSubmitting ? 'Salvando...' : 'Salvar nova senha'}
            </Button>
          </div>
        </div>
      </Card>

      {/* Two-Factor Authentication Card */}
      {/* <Card className="overflow-hidden border border-border bg-muted/40">
        <div className="p-6 space-y-4">
          <h3 className="flex items-center gap-2 text-lg font-semibold">
            <ShieldCheck className="h-5 w-5 text-primary" /> Autenticação em duas etapas (2FA)
          </h3>
          <p className="text-sm text-muted-foreground">
            Proteja sua conta exigindo um código adicional ao fazer login.
          </p>
          <div className="flex items-center justify-between rounded-md bg-card p-4">
            <div className="flex flex-col">
              <span className="font-medium">Ativar 2FA</span>
              <span className="text-xs text-muted-foreground">Recomendado para maior segurança</span>
            </div>
            <Switch checked={twoFactorEnabled} onCheckedChange={setTwoFactorEnabled} />
          </div>
        </div>
      </Card> */}

      {/* Active Sessions Card */}
      <Card className="overflow-hidden border border-border bg-muted/40">
        <div className="p-6 space-y-4">
          <h3 className="flex items-center gap-2 text-lg font-semibold">
            <LogOut className="h-5 w-5 text-primary" /> Sessões ativas
          </h3>
          <p className="text-sm text-muted-foreground">
            Encerrar sessões em dispositivos que você não reconhece.
          </p>
          
          {/* Lista de sessões */}
          {sessionsLoading ? (
            <p className="text-sm text-muted-foreground">Carregando sessões...</p>
          ) : sessions.length === 0 ? (
            <p className="text-sm text-muted-foreground">Nenhuma sessão encontrada.</p>
          ) : (
            <div className="space-y-3">
              {sessions.map(session => (
                <div key={session.id} className="rounded-md bg-card p-4 flex justify-between items-center">
                  <div>
                    <p className="font-medium">
                      {session.isCurrent
                        ? `${getDeviceName(session.userAgent)} (este dispositivo)`
                        : getDeviceName(session.userAgent)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(session.createdAt).toLocaleString('pt-BR', {
                        timeZone: 'America/Sao_Paulo',
                      })} • {session.ip ?? 'IP desconhecido'}
                    </p>
                  </div>
                  {session.isCurrent ? (
                    <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">Ativo agora</span>
                  ) : (
                    <Button
                      variant="destructive"
                      size="sm"
                      disabled={endingSessionId === session.id}
                      onClick={() => handleEndSession(session.id)}
                    >
                      {endingSessionId === session.id ? 'Encerrando...' : 'Encerrar'}
                    </Button>
                  )}
                </div>
              ))}
            </div>
          )}
          
          <Button variant="destructive" disabled={endingSessions || sessions.length <= 1} onClick={handleEndOtherSessions}>
            {endingSessions ? 'Encerrando...' : 'Encerrar todas as outras sessões'}
          </Button>
        </div>
      </Card>
    </section>
  )
} 