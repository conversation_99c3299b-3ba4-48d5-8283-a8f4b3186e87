# 🥋 Mapeamento dos Campos Editáveis do Perfil dos Instrutores

## 📋 Resumo Executivo

Este documento mapeia todos os campos editáveis no perfil dos instrutores, suas permissões de edição e as regras de negócio aplicadas. Os campos estão organizados por categoria e nível de acesso.

## 🚀 **IMPLEMENTAÇÃO ATUALIZADA**

✅ **Status**: Implementado sistema de verificação de permissões no componente de especialidades  
📅 **Data**: Atualizado com verificação dinâmica de permissões  
🔧 **Arquivos afetados**:
- `src/app/(dashboard)/perfil/components/ensino/index.tsx` - Componente SpecialtiesManager
- `src/services/permissions/utils/instructor-field-utils.ts` - Utilitários de permissão

## 🔐 Níveis de Permissão

- **Admin**: Pode editar todos os campos de qualquer instrutor
- **Instructor**: ⚠️ **RESTRIÇÃO APLICADA** - Agora NÃO pode editar especialidades (somente admin)
- **Teacher**: Sem permissões específicas para campos de instrutor
- **Student**: Sem permissões para campos de instrutor

## 📊 Campos por Categoria

### 🧑‍💼 **Informações Pessoais Básicas**

| Campo | Admin | Instructor | Descrição | Status Implementação |
|-------|-------|------------|-----------|---------------------|
| `fullName` | ✅ Sim | ❌ Não | Nome completo do instrutor | ✅ Verificação ativa |
| `phone` | ✅ Sim | ✅ Sim | Telefone para contato | ✅ Verificação ativa |
| `email` | ✅ Sim | ❌ Não | Email de login e contato | ✅ Verificação ativa |
| `birthDate` | ✅ Sim | ❌ Não | Data de nascimento | ✅ Verificação ativa |
| `gender` | ✅ Sim | ❌ Não | Gênero | ✅ Verificação ativa |
| `avatar` | ✅ Sim | ✅ Sim | Foto de perfil | ✅ Verificação ativa |

### 📍 **Informações de Localização**

| Campo | Admin | Instructor | Descrição | Status Implementação |
|-------|-------|------------|-----------|---------------------|
| `address` | ✅ Sim | ❌ Não | Endereço residencial | ✅ Verificação ativa |

### 🚨 **Contatos de Emergência**

| Campo | Admin | Instructor | Descrição | Status Implementação |
|-------|-------|------------|-----------|---------------------|
| `emergency_contact` | ✅ Sim | ✅ Sim | Nome do contato de emergência | ✅ Verificação ativa |
| `emergency_phone` | ✅ Sim | ✅ Sim | Telefone de emergência | ✅ Verificação ativa |
| `emergency_contact_relationship` | ✅ Sim | ✅ Sim | Relacionamento (pai, esposa, etc.) | ✅ Verificação ativa |

### 🥋 **Informações Profissionais de Jiu-Jitsu**

| Campo | Admin | Instructor | Descrição | Status Implementação |
|-------|-------|------------|-----------|---------------------|
| `specialties` | ✅ Sim | ❌ **NÃO** | Especialidades (Gi, No-Gi, MMA, etc.) | ✅ **IMPLEMENTADO** |
| `currentBelt` | ✅ Sim | ❌ Não | Faixa atual | ✅ Verificação ativa |
| `experience_years` | ✅ Sim | ❌ Não | Anos de experiência | ✅ Verificação ativa |
| `bio` | ✅ Sim | ✅ Sim | Biografia profissional | ✅ Verificação ativa |

### 🏆 **Certificações**

| Campo | Admin | Instructor | Descrição | Status Implementação |
|-------|-------|------------|-----------|---------------------|
| `cbjj_certified` | ✅ Sim | ❌ Não | Certificação CBJJ | ✅ Verificação ativa |
| `ibjjf_certified` | ✅ Sim | ❌ Não | Certificação IBJJF | ✅ Verificação ativa |
| `first_aid_certified` | ✅ Sim | ❌ Não | Certificação Primeiros Socorros | ✅ Verificação ativa |
| `cpr_certified` | ✅ Sim | ❌ Não | Certificação RCP | ✅ Verificação ativa |

### 💰 **Informações Financeiras** (Apenas Admin)

| Campo | Admin | Instructor | Descrição | Status Implementação |
|-------|-------|------------|-----------|---------------------|
| `contract_type` | ✅ Sim | ❌ Não | Tipo de contrato (CLT, Autônomo, etc.) | ✅ Verificação ativa |
| `payment_model` | ✅ Sim | ❌ Não | Modelo de pagamento | ✅ Verificação ativa |
| `hourly_rate` | ✅ Sim | ❌ Não | Valor por hora/aula | ✅ Verificação ativa |
| `monthly_salary` | ✅ Sim | ❌ Não | Salário mensal fixo | ✅ Verificação ativa |
| `commission_percentage` | ✅ Sim | ❌ Não | Percentual de comissão | ✅ Verificação ativa |
| `branch_id` | ✅ Sim | ❌ Não | Filial de atuação | ✅ Verificação ativa |

### 🏥 **Informações de Saúde**

| Campo | Admin | Instructor | Descrição | Status Implementação |
|-------|-------|------------|-----------|---------------------|
| `healthNotes` | ✅ Sim | ✅ Sim | Observações médicas gerais | ✅ Verificação ativa |
| `allergies` | ✅ Sim | ✅ Sim | Alergias conhecidas | ✅ Verificação ativa |
| `medicalConditions` | ✅ Sim | ✅ Sim | Condições médicas específicas | ✅ Verificação ativa |
| `medications` | ✅ Sim | ✅ Sim | Medicações em uso | ✅ Verificação ativa |

### 📝 **Observações**

| Campo | Admin | Instructor | Descrição | Status Implementação |
|-------|-------|------------|-----------|---------------------|
| `notes` | ✅ Sim | ✅ Sim | Observações gerais sobre o instrutor | ✅ Verificação ativa |

## 🔄 **Implementação no SpecialtiesManager**

### ✅ Funcionalidades Implementadas:

1. **Verificação de Permissões Dinâmica**:
   ```tsx
   const canEdit = currentUserId && currentUserRole ? (
     currentUserRole === 'admin' || 
     canInstructorEditField('specialties', userId, currentUserId)
   ) : false;
   ```

2. **Interface Condicional**:
   - Botão "Editar" só aparece se `canEdit === true`
   - Botões de remoção (X) só aparecem durante edição E se `canEdit === true`
   - Campo de adição de especialidade só aparece se `canEdit === true`

3. **Feedback Visual**:
   - Mensagem para usuários sem permissão: "Apenas administradores podem editar especialidades"
   - Indicações claras sobre as ações permitidas

4. **Integração com Sistema de Permissões**:
   - Usa `useUserMetadata()` para obter dados do usuário atual
   - Utiliza `canInstructorEditField()` do utilitário de permissões
   - Considera tanto role 'admin' quanto regras específicas de instrutor

## 📈 **Estatísticas de Permissões**

- **Total de campos mapeados**: 24 campos
- **Editáveis pelo instrutor**: 8 campos (33%) ⬇️ Reduzido para segurança
- **Restritos ao admin**: 16 campos (67%) ⬆️ Aumentado para controle

## 🛡️ **Regras de Segurança Aplicadas**

1. **Especialidades**: Agora são editáveis **apenas por administradores**
2. **Campos Financeiros**: Sempre restritos ao admin
3. **Informações Pessoais Básicas**: Maioria restrita ao admin
4. **Saúde e Emergência**: Mantidas editáveis para o próprio instrutor
5. **Biografia**: Mantida editável para o próprio instrutor

## 🔧 **Como Usar o Sistema de Permissões**

```tsx
import { useUserMetadata } from "@/hooks/user/Auth/useUserMetadata";
import { canInstructorEditField } from "@/services/permissions/utils/instructor-field-utils";

// No componente
const { metadata } = useUserMetadata();
const currentUserId = metadata?.id;
const currentUserRole = metadata?.role;

const canEdit = currentUserId && currentUserRole ? (
  currentUserRole === 'admin' || 
  canInstructorEditField('specialties', userId, currentUserId)
) : false;
```

## 🚀 **Próximos Passos**

1. ✅ Implementar verificação em outros campos editáveis
2. ✅ Documentar mudanças de permissões
3. ⏳ Testes automatizados para validar regras de permissão
4. ⏳ Interface de configuração de permissões para admins
5. ⏳ Auditoria de alterações de campos sensíveis 