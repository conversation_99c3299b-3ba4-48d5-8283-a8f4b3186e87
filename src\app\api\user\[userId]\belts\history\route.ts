import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/services/supabase/server";
import { protectProfileAccess } from "@/services/user/profile-access";

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const supabase = await createClient();
    
    // Await params before accessing properties
    const resolvedParams = await Promise.resolve(params);
    const { userId } = resolvedParams;

    if (!userId) {
      return NextResponse.json(
        { error: "ID de usuário inválido" },
        { status: 400 }
      );
    }

    const accessResult = await protectProfileAccess(userId);
    if (accessResult) {
      return NextResponse.json(
        { error: "Não autorizado a acessar este perfil" },
        { status: 403 }
      );
    }

    const { data: sessionData } = await supabase.auth.getSession();
    if (!sessionData?.session) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      );
    }

    const { data: student, error: studentError } = await supabase
      .from("students")
      .select("id")
      .eq("user_id", userId)
      .single();

    if (studentError) {
      console.error("Erro ao buscar dados do estudante:", studentError);
      return NextResponse.json(
        { error: "Erro ao buscar dados do estudante" },
        { status: 500 }
      );
    }

    const { data, error } = await supabase
      .from("student_belts")
      .select(`
        id,
        awarded_at,
        awarded_by,
        notes,
        belt_levels:belt_levels!inner(
          belt_color,
          label,
          degree,
          stripe_color,
          show_center_line,
          center_line_color,
          sort_order,
          modalities:modalities!inner(
            name
          )
        ),
        users:awarded_by (
          first_name,
          last_name
        )
      `)
      .eq("student_id", student.id)
      .order('awarded_at', { ascending: false });

    if (error) {
      console.error("Erro ao buscar histórico de graduações:", error);
      return NextResponse.json(
        { error: "Erro ao buscar histórico de graduações" },
        { status: 500 }
      );
    }

    const processedData = data.map(record => {
      const professorId = record.awarded_by;
      
      let instructorName = '';
      
      if (record.users) {
        if (Array.isArray(record.users) && record.users.length > 0) {
          const firstName = record.users[0]?.first_name || '';
          const lastName = record.users[0]?.last_name || '';
          instructorName = [firstName, lastName].filter(Boolean).join(' ');
        } 
        else if (typeof record.users === 'object' && record.users !== null) {
          const usersObj = record.users as any;
          if (usersObj.first_name || usersObj.last_name) {
            const firstName = usersObj.first_name || '';
            const lastName = usersObj.last_name || '';
            instructorName = [firstName, lastName].filter(Boolean).join(' ');
          }
        }
      }

      if (!instructorName && professorId) {
        console.log(`Tentando buscar nome do professor via subconsulta para ID: ${professorId}`);
      }

      const beltLevel = record.belt_levels as any;

      return {
        id: record.id,
        belt_color: beltLevel?.belt_color || 'white',
        belt_label: beltLevel?.label || 'Faixa Branca',
        modality_name: beltLevel?.modalities?.name || 'Modalidade',
        degree: beltLevel?.degree || 0,
        stripe_color: beltLevel?.stripe_color || null,
        show_center_line: beltLevel?.show_center_line || false,
        center_line_color: beltLevel?.center_line_color || null,
        sort_order: beltLevel?.sort_order || 0,
        awarded_at: record.awarded_at,
        instructor_name: instructorName ? `Professor ${instructorName}` : 'Professor Desconhecido',
        notes: record.notes
      };
    });

    return NextResponse.json(processedData);
  } catch (error) {
    console.error("Erro ao processar solicitação:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
} 