'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { getRecentPayments } from '@/app/(dashboard)/financeiro/mensalidades/actions/payment-actions';
import { Payment } from '@/app/(dashboard)/financeiro/mensalidades/types';
import { formatCurrency } from '@/utils/format-utils';
import { formatPaymentMethodName } from '@/utils/payment-method-formatter';
import Link from 'next/link';
import { CreditCard, DollarSign, CheckCircle, Eye } from 'lucide-react';

interface RecentPaymentsTableProps {
  limit?: number;
  showViewAllButton?: boolean;
}

export function RecentPaymentsTable({ limit = 5, showViewAllButton = true }: RecentPaymentsTableProps) {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPayments = async () => {
      setLoading(true);
      setError(null);

      const result = await getRecentPayments(limit);

      if (result.success && result.data) {
        setPayments(result.data);
      } else {
        setError(result.error || 'Erro ao carregar pagamentos recentes');
      }

      setLoading(false);
    };

    fetchPayments();
  }, [limit]);

  const getPaymentMethodIcon = (method: string | null) => {
    if (!method) return <DollarSign className="w-4 h-4" />;

    switch (method.toLowerCase()) {
      case 'credit_card':
      case 'credit card':
      case 'cartão de crédito':
        return <CreditCard className="w-4 h-4" />;
      case 'cash':
      case 'dinheiro':
        return <DollarSign className="w-4 h-4" />;
      default:
        return <DollarSign className="w-4 h-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'America/Sao_Paulo'
    });
  };

  if (loading) {
    return (
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden">
        <CardHeader className="pb-4 bg-gradient-to-r from-gray-50/50 to-transparent dark:from-gray-800/50 dark:to-transparent">
          <CardTitle className="text-gray-800 dark:text-gray-200 font-semibold">Pagamentos Recentes</CardTitle>
        </CardHeader>
        <CardContent className="pt-2">
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-center space-x-4 p-4 bg-white/50 dark:bg-gray-800/30 rounded-xl border border-gray-100 dark:border-gray-700/50">
                <div className="w-12 h-12 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                </div>
                <div className="flex-1 space-y-3">
                  <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full w-36 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                  </div>
                  <div className="h-3 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full w-28 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                  </div>
                </div>
                <div className="h-6 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full w-20 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                </div>
                <div className="h-8 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full w-24 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
        <CardHeader className="pb-4 bg-gradient-to-r from-gray-50/50 to-transparent dark:from-gray-800/50 dark:to-transparent">
          <CardTitle className="text-gray-800 dark:text-gray-200 font-semibold">Pagamentos Recentes</CardTitle>
        </CardHeader>
        <CardContent className="pt-2">
          <div className="flex flex-col items-center justify-center py-12 px-6">
            <div className="w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/30 dark:to-red-800/30 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-red-500 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <p className="text-red-600 dark:text-red-400 text-center font-medium">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (payments.length === 0) {
    return (
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
        <CardHeader className="pb-4 bg-gradient-to-r from-gray-50/50 to-transparent dark:from-gray-800/50 dark:to-transparent">
          <CardTitle className="text-gray-800 dark:text-gray-200 font-semibold">Pagamentos Recentes</CardTitle>
        </CardHeader>
        <CardContent className="pt-2">
          <div className="flex flex-col items-center justify-center py-12 px-6">
            <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800/30 dark:to-gray-700/30 rounded-full flex items-center justify-center mb-4">
              <DollarSign className="w-8 h-8 text-gray-500 dark:text-gray-400" />
            </div>
            <p className="text-gray-500 dark:text-gray-400 text-center font-medium">Nenhum pagamento encontrado</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between pb-4 bg-gradient-to-r from-gray-50/50 to-transparent dark:from-gray-800/50 dark:to-transparent">
        <CardTitle className="text-gray-800 dark:text-gray-200 font-semibold">Pagamentos Recentes</CardTitle>
        {showViewAllButton && (
          <Button variant="outline" size="sm" asChild className="border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
            <Link href="/financeiro/pagamentos">
              <Eye className="w-4 h-4 mr-2" />
              Ver todos
            </Link>
          </Button>
        )}
      </CardHeader>
      <CardContent className="pt-2">
        <div className="space-y-4">
          {payments.map((payment) => (
            <div
              key={payment.id}
              className="flex items-center space-x-4 p-4 bg-white/50 dark:bg-gray-800/30 rounded-xl border border-gray-100 dark:border-gray-700/50 hover:bg-white/80 dark:hover:bg-gray-800/50 hover:shadow-md transition-all duration-200 group"
            >
              {/* Avatar/Icon */}
              <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 rounded-full flex items-center justify-center text-blue-600 dark:text-blue-400 group-hover:scale-105 transition-transform duration-200">
                {getPaymentMethodIcon(payment.paymentMethod)}
              </div>

              {/* Payment Info */}
              <div className="flex-1 min-w-0">
                <p className="font-semibold text-gray-900 dark:text-gray-100 truncate">
                  {payment.studentName}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                  {formatPaymentMethodName(payment.paymentMethod)} • {formatDate(payment.paidAt || payment.createdAt)}
                </p>
              </div>

              {/* Amount */}
              <div className="text-right">
                <p className="font-bold text-gray-900 dark:text-gray-100 tracking-tight">
                  {formatCurrency(payment.amount)}
                </p>
              </div>

              {/* Status - Sempre "Pago" para pagamentos confirmados */}
              <div className="flex-shrink-0">
                <Badge variant="secondary" className="bg-gradient-to-r from-emerald-100 to-green-100 dark:from-emerald-900/30 dark:to-green-900/30 text-emerald-700 dark:text-emerald-400 hover:from-emerald-200 hover:to-green-200 dark:hover:from-emerald-900/40 dark:hover:to-green-900/40 border-0 shadow-sm">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Pago
                </Badge>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
