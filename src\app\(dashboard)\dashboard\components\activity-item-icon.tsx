import {
  ArrowTrendingUpIcon,
  BanknotesIcon,
  UsersIcon,
} from "@heroicons/react/24/outline";
import clsx from "clsx";
import { ActivityItem } from "../types";

interface ActivityItemIconProps {
  type: ActivityItem["type"];
}

export function ActivityItemIcon({ type }: ActivityItemIconProps) {
  const iconClasses = "h-5 w-5";
  
  const containerClasses = clsx(
    "flex h-8 w-8 flex-none items-center justify-center rounded-full",
    {
      "bg-green-100 dark:bg-green-800/30": type === "payment",
      "bg-blue-100 dark:bg-blue-800/30": type === "graduation",
      "bg-yellow-100 dark:bg-yellow-800/30": type === "attendance",
    }
  );

  const renderIcon = () => {
    switch (type) {
      case "payment":
        return (
          <BanknotesIcon
            className={clsx(iconClasses, "text-green-600 dark:text-green-500")}
            aria-hidden="true"
          />
        );
      case "graduation":
        return (
          <ArrowTrendingUpIcon
            className={clsx(iconClasses, "text-blue-600 dark:text-blue-500")}
            aria-hidden="true"
          />
        );
      case "attendance":
        return (
          <UsersIcon
            className={clsx(iconClasses, "text-yellow-600 dark:text-yellow-500")}
            aria-hidden="true"
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className={containerClasses}>
      {renderIcon()}
    </div>
  );
} 