'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle2, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { 
  ClassGroupTabNavigation,
  ClassGroupOverview,
  ClassGroupTimeline,
  ClassGroupStatsSection,
  ClassGroupCalendar,
  ClassGroupQuickActionBar,
  useClassGroup
} from './';

export function ClassGroupDetailsClient() {
  const { state } = useClassGroup();
  const router = useRouter();
  
  if (!state.classGroup) {
    return null;
  }

  const classGroup = state.classGroup;

  const handleBackToList = () => {
    router.push('/turmas');
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <Badge variant={isActive ? 'default' : 'secondary'}>
        {isActive ? 'Ativa' : 'Inativa'}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">

      {/* Header modernizado */}
      <div className="bg-white dark:bg-gray-900 border border-slate-200 dark:border-gray-700 rounded-lg p-6">
        <div className="flex flex-col space-y-4">
          {/* Botão de voltar */}
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToList}
              className="text-slate-600 hover:text-slate-900 dark:text-gray-400 dark:hover:text-gray-100 -ml-2"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar para turmas
            </Button>
          </div>
          
          {/* Conteúdo principal do header */}
          <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <div>
              <h1 className="text-2xl font-bold text-slate-900 dark:text-gray-100">
                {classGroup.name}
              </h1>
              <p className="text-slate-600 dark:text-gray-400 mt-1">
                {classGroup.description || 'Sem descrição'}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              {getStatusBadge(classGroup.is_active ?? false)}
            </div>
          </div>
        </div>
      </div>

      {/* QuickActionBar nova */}
      <ClassGroupQuickActionBar classGroup={classGroup} />

      {/* Sistema de abas novo */}
      <ClassGroupTabNavigation
        overviewContent={<ClassGroupOverview classGroup={classGroup} />}
        timelineContent={<ClassGroupTimeline classGroup={classGroup} />}
        statisticsContent={<ClassGroupStatsSection classGroup={classGroup} />}
        calendarContent={<ClassGroupCalendar classGroup={classGroup} />}
        defaultTab="overview"
      />
    </div>
  );
} 