import { QueryClient } from '@tanstack/react-query';
import { CACHE_KEYS } from '@/constants/cache-keys';
import { formatAddressFields } from '@/utils/address-utils';

const DEBUG_CACHE = true;
// Constante para desabilitar cache específico de perfil
const DISABLE_PROFILE_CACHE = true;

/**
 * Serviço de cache centralizado para gerenciar o cache do React Query entre componentes
 */
export class CacheService {
  private static instance: CacheService;
  private queryClient: QueryClient | null = null;
  private cookieListenerSetup = false;
  private localStorageListenerSetup = false;
  private lastEventTimestamp: number = 0;
  private processingProfileUpdate: boolean = false;
  private updatedProfiles: Set<string> = new Set();
  
  private constructor() {}
  
  /**
   * Obtém a instância singleton do serviço
   */
  public static getInstance(): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService();
    }
    return CacheService.instance;
  }
  
  /**
   * Inicializa o serviço com o cliente de consulta
   */
  public initialize(queryClient: QueryClient): void {
    this.queryClient = queryClient;
    this.logDebug('Serviço de cache centralizado inicializado');
    
    if (typeof window !== 'undefined' && !DISABLE_PROFILE_CACHE) {
      if (!this.cookieListenerSetup) {
        this.setupCookieListener();
      }
      
      if (!this.localStorageListenerSetup) {
        this.setupLocalStorageListener();
      }
    }
  }
  
  /**
   * Verifica se uma atualização de perfil deve ser processada (debounce)
   * para evitar loops infinitos
   */
  private shouldProcessProfileUpdate(userId: string): boolean {
    // Se cache de perfil desabilitado, sempre retorna false
    if (DISABLE_PROFILE_CACHE) return false;
    
    const now = Date.now();
    
    // Verificar se já está processando uma atualização
    if (this.processingProfileUpdate) {
      this.logDebug(`Já existe uma atualização de perfil em andamento, ignorando para ${userId}`);
      return false;
    }
    
    // Verificar se passou tempo suficiente desde a última atualização (debounce)
    if (now - this.lastEventTimestamp < 300) {
      this.logDebug(`Ignorando atualização muito próxima da anterior para ${userId}`);
      return false;
    }
    
    // Verificar se este perfil foi atualizado recentemente
    if (this.updatedProfiles.has(userId)) {
      this.logDebug(`Perfil ${userId} já foi atualizado recentemente, ignorando`);
      return false;
    }
    
    // Atualizar estado
    this.lastEventTimestamp = now;
    this.processingProfileUpdate = true;
    this.updatedProfiles.add(userId);
    
    // Limpar o estado após um tempo
    setTimeout(() => {
      this.processingProfileUpdate = false;
      
      // Remover da lista de perfis atualizados após mais tempo
      setTimeout(() => {
        this.updatedProfiles.delete(userId);
      }, 1000);
    }, 500);
    
    return true;
  }
  
  /**
   * Configura um listener para observar mudanças em cookies que indicam atualizações de perfil
   */
  private setupCookieListener(): void {
    if (typeof window === 'undefined' || DISABLE_PROFILE_CACHE) return;
    
    const checkCookiesForProfileUpdates = () => {
      const cookies = document.cookie.split(';');
      
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        
        if (name && name.startsWith('profile_updated_')) {
          const userId = name.replace('profile_updated_', '');
          
          if (userId && this.shouldProcessProfileUpdate(userId)) {
            this.logDebug(`Detectada atualização de perfil via cookie para usuário: ${userId}`);
            
            // Invalidar todos os caches relacionados ao perfil deste usuário
            this.invalidateQueries([CACHE_KEYS.USER_PROFILE[0], userId], true);
            this.invalidateQueries(CACHE_KEYS.VIEWED_USER_PROFILE(userId), true);
            
            // Remover o cookie após processá-lo
            document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
            
            // Disparar evento personalizado para notificar outros componentes
            // usando flag para evitar loops
            if (!window.__skipNextProfileUpdate) {
              window.__skipNextProfileUpdate = true;
              const event = new CustomEvent('profile:updated', { detail: { userId } });
              window.dispatchEvent(event);
              
              this.logDebug(`Evento profile:updated disparado para usuário: ${userId} via cookie`);
              
              // Resetar flag após um tempo
              setTimeout(() => {
                window.__skipNextProfileUpdate = false;
              }, 500);
            } else {
              this.logDebug(`Evento profile:updated suprimido para usuário: ${userId} para evitar loops`);
              window.__skipNextProfileUpdate = false;
            }
          }
        }
      }
    };
    
    // Verificar a cada 2 segundos
    setInterval(checkCookiesForProfileUpdates, 2000);
    
    // Também verificar quando a página recebe foco
    window.addEventListener('focus', checkCookiesForProfileUpdates);
    
    this.cookieListenerSetup = true;
    this.logDebug('Listener de cookies para atualizações de perfil configurado');
  }
  
  /**
   * Configura um listener para observar mudanças no localStorage que indicam atualizações de perfil
   */
  private setupLocalStorageListener(): void {
    if (typeof window === 'undefined' || DISABLE_PROFILE_CACHE) return;
    
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key && e.key.startsWith('profile_force_update_')) {
        const userId = e.key.replace('profile_force_update_', '');
        
        if (userId && this.shouldProcessProfileUpdate(userId)) {
          this.logDebug(`Detectada atualização de perfil via localStorage para usuário: ${userId}`);
          
          // Invalidar todos os caches relacionados ao perfil deste usuário
          this.invalidateQueries([CACHE_KEYS.USER_PROFILE[0], userId], true);
          this.invalidateQueries(CACHE_KEYS.VIEWED_USER_PROFILE(userId), true);
          
          // Forçar refetch de dados
          setTimeout(() => {
            this.refetchQueries([CACHE_KEYS.USER_PROFILE[0], userId]);
            this.refetchQueries(CACHE_KEYS.VIEWED_USER_PROFILE(userId));
          }, 100);
          
          // Disparar evento personalizado para notificar outros componentes
          // usando flag para evitar loops
          if (!window.__skipNextProfileUpdate) {
            window.__skipNextProfileUpdate = true;
            const event = new CustomEvent('profile:updated', { detail: { userId } });
            window.dispatchEvent(event);
            
            this.logDebug(`Evento profile:updated disparado para usuário: ${userId} via localStorage`);
            
            // Resetar flag após um tempo
            setTimeout(() => {
              window.__skipNextProfileUpdate = false;
            }, 500);
          } else {
            this.logDebug(`Evento profile:updated suprimido para usuário: ${userId} para evitar loops`);
            window.__skipNextProfileUpdate = false;
          }
        }
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    // Também verificar quando a página carrega
    const checkLocalStorageOnLoad = () => {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        
        if (key && key.startsWith('profile_force_update_')) {
          const userId = key.replace('profile_force_update_', '');
          const timestamp = parseInt(localStorage.getItem(key) || '0', 10);
          const now = Date.now();
          const fiveMinutesAgo = now - (5 * 60 * 1000);
          
          if (timestamp > fiveMinutesAgo && this.shouldProcessProfileUpdate(userId)) {
            this.logDebug(`Detectada atualização recente de perfil para usuário: ${userId}`);
            
            // Invalidar todos os caches relacionados ao perfil deste usuário
            this.invalidateQueries([CACHE_KEYS.USER_PROFILE[0], userId], true);
            this.invalidateQueries(CACHE_KEYS.VIEWED_USER_PROFILE(userId), true);
            
            // Forçar refetch de dados
            setTimeout(() => {
              this.refetchQueries([CACHE_KEYS.USER_PROFILE[0], userId]);
              this.refetchQueries(CACHE_KEYS.VIEWED_USER_PROFILE(userId));
            }, 100);
          } else if (timestamp <= fiveMinutesAgo) {
            // Remover itens antigos para limpar o localStorage
            localStorage.removeItem(key);
          }
        }
      }
    };
    
    // Verificar no carregamento da página
    checkLocalStorageOnLoad();
    
    // Verificar quando a página recebe foco
    window.addEventListener('focus', checkLocalStorageOnLoad);
    
    this.localStorageListenerSetup = true;
    this.logDebug('Listener de localStorage para atualizações de perfil configurado');
  }
  
  /**
   * Armazena dados no cache
   */
  public setData<T>(queryKey: unknown[], data: T): void {
    if (!this.queryClient) {
      this.logError('QueryClient não inicializado. Chame initialize() primeiro.');
      return;
    }
    
    // Verificar se é um query de perfil e se o cache de perfil está desativado
    const isProfileQuery = this.isProfileRelatedQuery(queryKey);
    if (isProfileQuery && DISABLE_PROFILE_CACHE) {
      this.logDebug(`Cache de perfil desativado, ignorando setData para: ${queryKey.join(':')}`);
      return;
    }
    
    // Processar dados de perfil antes de armazenar no cache
    let processedData = data;
    
    // Compatibilidade com o processamento de perfil existente
    if (!DISABLE_PROFILE_CACHE && queryKey.length > 0 && 
        (queryKey[0] === CACHE_KEYS.USER_PROFILE[0] || 
         queryKey[0] === 'user-profile')) {
      processedData = this.processProfileData(processedData as any) as unknown as T;
    }
    
    this.queryClient.setQueryData(queryKey, processedData);
    this.logDebug(`Dados armazenados no cache: ${queryKey.join(':')}`);
  }
  
  /**
   * Obtém dados do cache
   */
  public getData<T>(queryKey: unknown[]): T | undefined {
    if (!this.queryClient) {
      this.logError('QueryClient não inicializado. Chame initialize() primeiro.');
      return undefined;
    }
    
    // Se for query relacionada a perfil e cache de perfil estiver desabilitado, retornar undefined
    if (this.isProfileRelatedQuery(queryKey) && DISABLE_PROFILE_CACHE) {
      this.logDebug(`Cache de perfil desativado, negando acesso ao cache para: ${queryKey.join(':')}`);
      return undefined;
    }
    
    const data = this.queryClient.getQueryData<T>(queryKey);
    this.logDebug(`Verificando cache: ${queryKey.join(':')}, ${data ? 'ENCONTRADO' : 'NÃO ENCONTRADO'}`);
    return data;
  }
  
  /**
   * Verifica se uma query está relacionada ao perfil de usuário
   */
  private isProfileRelatedQuery(queryKey: unknown[]): boolean {
    if (queryKey.length === 0) return false;
    
    const firstKey = queryKey[0];
    
    // Verificar se é uma chave relacionada a perfil
    return (
      firstKey === CACHE_KEYS.USER_PROFILE[0] ||
      firstKey === 'user-profile' ||
      (typeof firstKey === 'string' && firstKey.includes('user-profile')) ||
      (queryKey.length >= 2 && 
       typeof queryKey[1] === 'string' && 
       queryKey[1].includes('profile'))
    );
  }
  
  /**
   * Processa dados de perfil antes de armazenar no cache
   * Garante que os campos de endereço estejam corretamente formatados
   */
  private processProfileData(profileData: any): any {
    if (!profileData || DISABLE_PROFILE_CACHE) return profileData;
    
    try {
      // Formatar campos de endereço
      const processedData = formatAddressFields(profileData);
      this.logDebug('Dados de perfil processados para cache');
      return processedData;
    } catch (error) {
      this.logError(`Erro ao processar dados de perfil: ${error}`);
      return profileData; // Em caso de erro, retornar dados originais
    }
  }
  
  /**
   * Pré-carrega todos os dados de um usuário para uso em múltiplas abas
   */
  public prefetchUserTabsData(userId: string, userData: any): void {
    if (!userId || !userData || DISABLE_PROFILE_CACHE) return;
    
    // Processar dados de perfil antes de armazenar
    const processedData = this.processProfileData(userData);
    this.setData(CACHE_KEYS.USER_PROFILE, processedData);
    
    // Invalidar chaves relacionadas ao usuário para forçar refetch
    this.invalidateQueries(CACHE_KEYS.ATTENDANCE.STATS(userId), true);
    this.invalidateQueries(CACHE_KEYS.ATTENDANCE.HISTORY(userId), true);
    this.invalidateQueries(CACHE_KEYS.BELTS.STATS(userId), true);
    this.invalidateQueries(CACHE_KEYS.BELTS.HISTORY(userId), true);
    this.invalidateQueries(CACHE_KEYS.BELTS.REQUIREMENTS(userId), true);
    this.invalidateQueries(CACHE_KEYS.PAYMENTS.INFO(userId), true);
    this.invalidateQueries(CACHE_KEYS.PAYMENTS.HISTORY(userId), true);
    this.invalidateQueries(CACHE_KEYS.PAYMENTS.UPCOMING(userId), true);
    
    if (userData.currentBeltId) {
      this.invalidateQueries(CACHE_KEYS.BELTS.DETAILS(userId, userData.currentBeltId), true);
    }
    
    this.logDebug(`Dados do usuário ${userId} pré-carregados para todas as abas`);
  }
  
  /**
   * Invalida uma consulta específica
   * @param queryKey Chave da consulta a ser invalidada
   * @param removeFromCache Se deve remover completamente do cache (padrão: true)
   */
  public invalidateQueries(queryKey: unknown[], removeFromCache: boolean = true): void {
    if (!this.queryClient) {
      this.logError('QueryClient não inicializado. Chame initialize() primeiro.');
      return;
    }
    
    // Para queries de perfil com cache desabilitado, sempre remover completamente
    if (this.isProfileRelatedQuery(queryKey) && DISABLE_PROFILE_CACHE) {
      removeFromCache = true;
    }
    
    // Compatível com TanStack Query v5
    this.queryClient.invalidateQueries({ queryKey });
    
    // Tentar também remover completamente do cache para garantir dados frescos
    // apenas se removeFromCache for true
    if (removeFromCache) {
      try {
        this.queryClient.removeQueries({ queryKey });
        this.logDebug(`Cache removido: ${queryKey.join(':')}`);
      } catch (e) {
        // Ignorar erros, remoção é apenas uma camada extra de segurança
      }
    }
    
    this.logDebug(`Cache invalidado: ${queryKey.join(':')}`);
  }
  
  /**
   * Força a revalidação de consultas ativas
   */
  public refetchQueries(queryKey: unknown[]): void {
    if (!this.queryClient) {
      this.logError('QueryClient não inicializado');
      return;
    }
    
    // Compatível com TanStack Query v5
    this.queryClient.refetchQueries({ queryKey });
    
    this.logDebug(`Revalidação forçada: ${queryKey.join(':')}`);
  }
  
  /**
   * Verifica se uma consulta está ativa
   */
  public isQueryActive(queryKey: unknown[]): boolean {
    if (!this.queryClient) return false;
    
    // Compatível com TanStack Query v4
    const query = this.queryClient.getQueryCache().find({ queryKey });
    return !!query && query.state.status === 'success' && query.state.fetchStatus !== 'fetching';
  }
  
  /**
   * Configura listeners para eventos de cache
   */
  public setupCacheListeners(): void {
    if (typeof window === 'undefined') return;
    
    const handleLogout = () => {
      // Limpar caches sensíveis ao fazer logout
      this.invalidateQueries(CACHE_KEYS.USER_METADATA, true);
      this.invalidateQueries(CACHE_KEYS.USER_PROFILE, true);
      this.invalidateQueries(CACHE_KEYS.USER_ADMIN_STATUS, true);
    };
    
    window.addEventListener('app:logout', handleLogout);
    
    // Sincronização entre abas
    window.addEventListener('storage', (e) => {
      if (e.key === 'supabase.auth.token' && !e.newValue) {
        handleLogout();
      }
    });
    
    // Evento de atualização de perfil - apenas se o cache de perfil estiver ativado
    if (!DISABLE_PROFILE_CACHE) {
      window.addEventListener('profile:updated', (e: Event) => {
        const customEvent = e as CustomEvent;
        const userId = customEvent.detail?.userId;
        
        if (userId && this.shouldProcessProfileUpdate(userId)) {
          this.logDebug(`Evento profile:updated recebido para usuário: ${userId}`);
          
          // Remover completamente todas as consultas relacionadas ao perfil do cache
          if (this.queryClient) {
            // Invalidar perfil do usuário
            this.invalidateQueries([CACHE_KEYS.USER_PROFILE[0], userId], true);
            
            // Invalidar visualizações de perfil
            this.invalidateQueries(CACHE_KEYS.VIEWED_USER_PROFILE(userId), true);
            
            // Invalidar dados relacionados a tabs
            this.invalidateQueries(CACHE_KEYS.ATTENDANCE.STATS(userId), true);
            this.invalidateQueries(CACHE_KEYS.ATTENDANCE.HISTORY(userId), true);
            this.invalidateQueries(CACHE_KEYS.BELTS.STATS(userId), true);
            this.invalidateQueries(CACHE_KEYS.BELTS.HISTORY(userId), true);
            this.invalidateQueries(CACHE_KEYS.BELTS.REQUIREMENTS(userId), true);
            this.invalidateQueries(CACHE_KEYS.PAYMENTS.INFO(userId), true);
            this.invalidateQueries(CACHE_KEYS.PAYMENTS.HISTORY(userId), true);
            this.invalidateQueries(CACHE_KEYS.PAYMENTS.UPCOMING(userId), true);
            
            // Forçar refetch de todas as queries invalidadas
            setTimeout(() => {
              this.refetchQueries([CACHE_KEYS.USER_PROFILE[0], userId]);
              this.refetchQueries(CACHE_KEYS.VIEWED_USER_PROFILE(userId));
              
              this.logDebug(`Refetch de queries relacionadas ao usuário ${userId} concluído`);
              
              // Disparar evento adicional para outros componentes que podem estar ouvindo
              // usando app:profile-change em vez de profile:updated para evitar loops
              window.dispatchEvent(new Event('app:profile-change'));
            }, 100);
          }
        }
      });
    } else {
      this.logDebug('Cache de perfil desativado, listeners de evento profile:updated ignorados');
    }
    
    this.logDebug('Listeners de eventos de cache configurados');
  }
  
  /**
   * Log de depuração
   */
  private logDebug(message: string): void {
    if (DEBUG_CACHE) {
      console.log(`[CACHE] ${message}`);
    }
  }
  
  /**
   * Log de erro
   */
  private logError(message: string): void {
    console.error(`[CACHE] ${message}`);
  }
}

// Estender o objeto Window para incluir nossas propriedades personalizadas
declare global {
  interface Window {
    __skipNextProfileUpdate?: boolean;
  }
}

export const cacheService = CacheService.getInstance(); 