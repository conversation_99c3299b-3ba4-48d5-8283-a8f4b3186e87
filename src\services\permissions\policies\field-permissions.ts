import { UserRole } from '../types/permission-types';
import { ProfileField, EditCondition } from '../types/field-permission-types';
import { DefaultFieldPermissionFactory } from '../factories/field-permission-factory';

// Fábrica para criar permissões de campo
const permissionFactory = new DefaultFieldPermissionFactory();

// Define as permissões de campo usando a factory
export const profileFieldPermissions = [
  // Permissões para nome completo
  permissionFactory.createPermission('fullName', {
    roles: ['admin'],
    editConditions: {
      'admin': ['self', 'others']
    }
  }),
  
  // Permissões para email
  permissionFactory.createPermission('email', {
    roles: ['admin'],
    editConditions: ['self', 'others']
  }),
  
  // Permissões para telefone
  permissionFactory.createPermission('phone', {
    roles: ['admin', 'instructor', 'teacher', 'student'],
    editConditions: {
      'admin': ['self', 'others'],
      'instructor': ['self'],
      'teacher': ['self'],
      'student': ['self']
    }
  }),
  
  // Permissões para endereço
  permissionFactory.createPermission('address', {
    roles: ['admin'],
    editConditions: {
      'admin': ['self', 'others']
    }
  }),
  
  // Permissões para contato de emergência
  permissionFactory.createPermission('emergency_contact', {
    roles: ['admin', 'instructor', 'teacher', 'student'],
    editConditions: {
      'admin': ['self', 'others'],
      'instructor': ['self'],
      'teacher': ['self'],
      'student': ['self']
    }
  }),
  
  // Permissões para telefone de emergência
  permissionFactory.createPermission('emergency_phone', {
    roles: ['admin', 'instructor', 'teacher', 'student'],
    editConditions: {
      'admin': ['self', 'others'],
      'instructor': ['self'],
      'teacher': ['self'],
      'student': ['self']
    }
  }),
  
  // Permissões para relacionamento de contato de emergência
  permissionFactory.createPermission('emergency_contact_relationship', {
    roles: ['admin', 'instructor', 'teacher', 'student'],
    editConditions: {
      'admin': ['self', 'others'],
      'instructor': ['self'],
      'teacher': ['self'],
      'student': ['self']
    }
  }),
  
  // Permissões para nome de contato de emergência (campo específico de instrutores)
  permissionFactory.createPermission('emergency_contact_name', {
    roles: ['admin', 'instructor', 'teacher'],
    editConditions: {
      'admin': ['self', 'others'],
      'instructor': ['self'],
      'teacher': ['self']
    }
  }),
  
  // Permissões para telefone de contato de emergência (campo específico de instrutores)
  permissionFactory.createPermission('emergency_contact_phone', {
    roles: ['admin', 'instructor', 'teacher'],
    editConditions: {
      'admin': ['self', 'others'],
      'instructor': ['self'],
      'teacher': ['self']
    }
  }),
  
  // Permissões para data de nascimento
  permissionFactory.createPermission('birthDate', {
    roles: ['admin'],
    editConditions: {
      'admin': ['self', 'others']
    }
  }),
  
  // Permissões para gênero
  permissionFactory.createPermission('gender', {
    roles: ['admin'],
    editConditions: {
      'admin': ['self', 'others']
    }
  }),
  
  // Permissões para notas
  permissionFactory.createPermission('notes', {
    roles: ['admin', 'instructor', 'teacher'],
    editConditions: ['self', 'others']
  }),
  
  // Permissões para avatar
  permissionFactory.createPermission('avatar', {
    roles: ['admin', 'instructor', 'teacher', 'student'],
    editConditions: {
      'admin': ['self', 'others'],
      'instructor': ['self'],
      'teacher': ['self'],
      'student': ['self']
    }
  }),
  
  // Permissões para observações médicas
  permissionFactory.createPermission('healthNotes', {
    roles: ['admin', 'instructor', 'teacher'],
    editConditions: {
      'admin': ['self', 'others'],
      'instructor': ['self', 'others'],
      'teacher': ['self', 'others']
    }
  }),
  
  // Permissões para alergias
  permissionFactory.createPermission('allergies', {
    roles: ['admin', 'instructor', 'teacher'],
    editConditions: {
      'admin': ['self', 'others'],
      'instructor': ['self', 'others'],
      'teacher': ['self', 'others']
    }
  }),
  
  // Permissões para condições médicas
  permissionFactory.createPermission('medicalConditions', {
    roles: ['admin', 'instructor', 'teacher'],
    editConditions: {
      'admin': ['self', 'others'],
      'instructor': ['self', 'others'],
      'teacher': ['self', 'others']
    }
  }),
  
  // Permissões para medicamentos
  permissionFactory.createPermission('medications', {
    roles: ['admin', 'instructor', 'teacher'],
    editConditions: {
      'admin': ['self', 'others'],
      'instructor': ['self', 'others'],
      'teacher': ['self', 'others']
    }
  }),

  // === CAMPOS ESPECÍFICOS DE INSTRUTORES ===
  
  // Permissões para faixa atual
  permissionFactory.createPermission('currentBelt', {
    roles: ['admin'],
    editConditions: ['self', 'others']
  }),
  
  // Permissões para especialidades
  permissionFactory.createPermission('specialties', {
    roles: ['admin'],
    editConditions: {
      'admin': ['self', 'others']
    }
  }),
  
  // Permissões para certificação CBJJ
  permissionFactory.createPermission('cbjj_certified', {
    roles: ['admin'],
    editConditions: {
      'admin': ['self', 'others']
    }
  }),
  
  // Permissões para certificação IBJJF
  permissionFactory.createPermission('ibjjf_certified', {
    roles: ['admin'],
    editConditions: {
      'admin': ['self', 'others']
    }
  }),
  
  // Permissões para certificação de primeiros socorros
  permissionFactory.createPermission('first_aid_certified', {
    roles: ['admin'],
    editConditions: {
      'admin': ['self', 'others']
    }
  }),
  
  // Permissões para certificação CPR
  permissionFactory.createPermission('cpr_certified', {
    roles: ['admin'],
    editConditions: {
      'admin': ['self', 'others']
    }
  }),
  
  // Permissões para tipo de contrato (apenas admin)
  permissionFactory.createPermission('contract_type', {
    roles: ['admin'],
    editConditions: ['self', 'others']
  }),
  
  // Permissões para modelo de pagamento (apenas admin)
  permissionFactory.createPermission('payment_model', {
    roles: ['admin'],
    editConditions: ['self', 'others']
  }),
  
  // Permissões para valor por hora (apenas admin)
  permissionFactory.createPermission('hourly_rate', {
    roles: ['admin'],
    editConditions: ['self', 'others']
  }),
  
  // Permissões para salário mensal (apenas admin)
  permissionFactory.createPermission('monthly_salary', {
    roles: ['admin'],
    editConditions: ['self', 'others']
  }),
  
  // Permissões para percentual de comissão (apenas admin)
  permissionFactory.createPermission('commission_percentage', {
    roles: ['admin'],
    editConditions: ['self', 'others']
  }),
  
  // Permissões para filial/branch (apenas admin)
  permissionFactory.createPermission('branch_id', {
    roles: ['admin'],
    editConditions: ['self', 'others']
  }),
  
  // Permissões para anos de experiência
  permissionFactory.createPermission('experience_years', {
    roles: ['admin'],
    editConditions: {
      'admin': ['self', 'others']
    }
  }),
  
  // Permissões para biografia/descrição profissional
  permissionFactory.createPermission('bio', {
    roles: ['admin', 'instructor'],
    editConditions: {
      'admin': ['self', 'others'],
      'instructor': ['self']
    }
  })
];

export const createFieldPermissionMap = () => {
  const map = new Map<ProfileField, any[]>();
  
  profileFieldPermissions.forEach(permission => {
    const field = permission.field;
    
    if (!map.has(field)) {
      map.set(field, []);
    }
    
    map.get(field)?.push(permission);
  });
  
  return map;
};

export const fieldPermissionMap = createFieldPermissionMap(); 