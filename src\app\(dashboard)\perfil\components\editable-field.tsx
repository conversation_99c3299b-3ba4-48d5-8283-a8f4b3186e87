'use client'

import { useState, useEffect, ReactNode, ChangeEvent, KeyboardEvent, useRef } from 'react'
import { Check, X, Edit2, Loader2 } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { useEditMode } from './edit-context'
import { cn } from '@/lib/utils'
import { useFieldPermission } from '@/services/permissions'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import usePhoneFormat from '@/hooks/form/usePhoneFormat'
import { formatContractType, formatPaymentModel, formatGender, formatCurrency, formatPercentage } from '@/utils/format-utils'

export interface EditableFieldProps {
  label: string
  value: string
  icon?: ReactNode
  fieldName: string
  placeholder?: string
  emptyText?: string
  className?: string
  type?: 'text' | 'email' | 'tel' | 'date' | 'textarea' | 'select' | 'currency' | 'percentage'
  validate?: (value: string) => string | undefined
  userId?: string
  inline?: boolean
  options?: { value: string, label: string }[]
}

export function EditableField({
  label,
  value,
  icon,
  fieldName,
  placeholder = 'Não informado',
  emptyText = 'Não informado',
  className,
  type = 'text',
  validate,
  userId,
  inline = false,
  options = []
}: EditableFieldProps) {
  const { setFieldValue, getCurrentValue } = useEditMode()
  const [isEditing, setIsEditing] = useState(false)
  const [fieldValue, setFieldValue_] = useState(value)
  const [displayValue_, setDisplayValue_] = useState(value)
  const [error, setError] = useState<string | undefined>()
  const [isSaving, setIsSaving] = useState(false)
  const [wasRecentlyEdited, setWasRecentlyEdited] = useState(false)
  const { canEditField } = useFieldPermission();
  const lastUpdateTime = useRef<number>(0);
  const { formatPhoneNumber, formatPhoneInput } = usePhoneFormat();
  
  const canEditThisField = userId ? canEditField(fieldName as any, userId) : false;
  
  useEffect(() => {
    if (wasRecentlyEdited) {
      const timer = setTimeout(() => {
        setWasRecentlyEdited(false)
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, [wasRecentlyEdited]);

  useEffect(() => {
    const handleProfileChanged = () => {
      if (isEditing) return;
      
      const now = Date.now();
      if (now - lastUpdateTime.current < 200) return;
      lastUpdateTime.current = now;
      
      const updatedValue = getCurrentValue(fieldName);
      
      if (updatedValue !== undefined && updatedValue !== displayValue_) {
        console.log(`[EditableField] Campo ${fieldName} atualizado via evento global:`, updatedValue);
        setDisplayValue_(updatedValue);
        setFieldValue_(updatedValue);
      }
    };
    
    window.addEventListener('profile:updated', handleProfileChanged);
    window.addEventListener('app:profile-change', handleProfileChanged);
    
    return () => {
      window.removeEventListener('profile:updated', handleProfileChanged);
      window.removeEventListener('app:profile-change', handleProfileChanged);
    };
  }, [fieldName, displayValue_, getCurrentValue, isEditing]);

  const currentValue = getCurrentValue(fieldName) || value

  useEffect(() => {
    if (!isEditing) {
      const valueToUse = currentValue || value
      
      if (valueToUse !== displayValue_) {
        setFieldValue_(valueToUse)
        setDisplayValue_(valueToUse)
        console.log(`[EDITABLE] Campo ${fieldName} atualizado para:`, valueToUse)
      }
      
      if (fieldName === 'gender' && options.length === 0 && !isEditing) {
        options = [
          { value: 'masculino', label: 'Masculino' },
          { value: 'feminino', label: 'Feminino' },
          { value: 'outro', label: 'Outro' },
          { value: 'prefiro_nao_informar', label: 'Prefiro não informar' }
        ];
      }
    }
  }, [value, currentValue, fieldName, isEditing, displayValue_])
  
  useEffect(() => {
    if (!isEditing) {
      const valueToUse = getCurrentValue(fieldName) || value
      setDisplayValue_(valueToUse)
    }
  }, [isEditing, fieldName, getCurrentValue, value])

  useEffect(() => {
    if (!isEditing && currentValue !== displayValue_) {
      const timer = setTimeout(() => {
        setDisplayValue_(currentValue)
        console.log(`[EDITABLE] Campo ${fieldName} atualizado sob demanda para:`, currentValue)
      }, 0)
      
      return () => clearTimeout(timer)
    }
  }, [currentValue, displayValue_, isEditing, fieldName])

  const handleEdit = () => {
    if (canEditThisField) {
      const valueToEdit = getCurrentValue(fieldName) || value
      
      if (type === 'tel') {
        // Extrai apenas os dígitos e reconstrói com DDI
        const digitsOnly = valueToEdit.replace(/\D/g, '');
        const phoneWithDDI = digitsOnly.startsWith('55') ? `+${digitsOnly}` : `+55${digitsOnly}`;
        setFieldValue_(formatPhoneInput(phoneWithDDI))
      } else if (type === 'date' && valueToEdit) {
        try {
          if (/^\d{4}-\d{2}-\d{2}/.test(valueToEdit)) {
            setFieldValue_(valueToEdit.split('T')[0]);
          } else {
            const date = new Date(valueToEdit);
            if (!isNaN(date.getTime())) {
              const localDate = new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
              setFieldValue_(localDate.toISOString().split('T')[0]);
            } else {
              setFieldValue_(valueToEdit);
            }
          }
        } catch (e) {
          console.error('Erro ao formatar data para edição:', e);
          setFieldValue_(valueToEdit);
        }
      } else {
        setFieldValue_(valueToEdit)
      }
      setIsEditing(true)
    }
  }

  const handleCancel = () => {
    const valueToRevert = getCurrentValue(fieldName) || value
    setFieldValue_(valueToRevert)
    setError(undefined)
    setIsEditing(false)
  }

  const handleConfirm = async () => {
    if (validate) {
      const validationError = validate(fieldValue)
      if (validationError) {
        setError(validationError)
        return
      }
    }

    setIsSaving(true)
    try {
      let valueToSave = fieldValue;
      
      if (type === 'tel') {
        // Extrai dígitos e mantém DDI se presente
        const digitsOnly = fieldValue.replace(/\D/g, '');
        valueToSave = `+${digitsOnly}`;
      } else if (type === 'date' && fieldValue) {
        try {
          valueToSave = fieldValue.split('T')[0];
        } catch (e) {
          console.error('Erro ao processar data para salvar:', e);
        }
      }
      
      const newDisplayValue = type === 'tel' ? formatPhoneNumber(valueToSave) : fieldValue
      
      const success = await setFieldValue(fieldName, valueToSave)
      
      if (success) {
        setDisplayValue_(newDisplayValue)
        setError(undefined)
        setIsEditing(false)
        setWasRecentlyEdited(true)
        setFieldValue_(newDisplayValue)
      } else {
        setError('Não foi possível salvar. Tente novamente ou cancele.')
      }
    } catch (error) {
      console.error('Erro ao salvar campo:', error)
      setError('Erro ao salvar. Tente novamente ou cancele.')
    } finally {
      setIsSaving(false)
    }
  }

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    let newValue = e.target.value;
    
    if (type === 'tel') {
      // Permite digitar +, DDI e número
      setFieldValue_(formatPhoneInput(newValue));
    } else if (type === 'currency') {
      // Remove everything except digits and commas/dots
      newValue = newValue.replace(/[^\d.,]/g, '');
      setFieldValue_(newValue);
    } else if (type === 'percentage') {
      // Remove everything except digits and dots, limit to 100
      newValue = newValue.replace(/[^\d.]/g, '');
      const numValue = parseFloat(newValue);
      if (!isNaN(numValue) && numValue > 100) {
        newValue = '100';
      }
      setFieldValue_(newValue);
    } else {
      setFieldValue_(e.target.value)
    }
    
    if (error) setError(undefined)
  }

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && type !== 'textarea') {
      e.preventDefault()
      handleConfirm()
    } else if (e.key === 'Escape') {
      e.preventDefault()
      handleCancel()
    }
  }

  const handleSelectChange = (newValue: string) => {
    setFieldValue_(newValue);
    if (error) setError(undefined);
  };

  const displayValue = isEditing ? fieldValue : (displayValue_ || getCurrentValue(fieldName) || emptyText)

  const formatDisplayValue = () => {
    if (isEditing) return fieldValue;
    
    if (!displayValue || displayValue === emptyText) return displayValue;
    
    if (type === 'tel' && displayValue) {
      return formatPhoneNumber(String(displayValue));
    } else if (type === 'date' && displayValue) {
      try {
        const stringValue = String(displayValue);
        const dateStr = stringValue.includes('T') ? stringValue : `${stringValue}T12:00:00`;
        
        const date = new Date(dateStr);
        
        if (!isNaN(date.getTime())) {
          const localDate = new Date(date.getTime());
          
          if (fieldName === 'birthDate') {
            const today = new Date();
            let age = today.getFullYear() - localDate.getFullYear();
            const m = today.getMonth() - localDate.getMonth();
            
            if (m < 0 || (m === 0 && today.getDate() < localDate.getDate())) {
              age--;
            }
            
            const formattedDate = localDate.toLocaleDateString('pt-BR', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
              timeZone: 'UTC'
            });
            
            return `${formattedDate} (${age} anos)`;
          }
          
          return localDate.toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            timeZone: 'UTC'
          });
        }
      } catch (e) {
        console.error('Erro ao formatar data:', e);
      }
    } else if (fieldName === 'gender' && displayValue) {
      return formatGender(String(displayValue));
    } else if (fieldName === 'contract_type' && displayValue) {
      return formatContractType(String(displayValue));
    } else if (fieldName === 'payment_model' && displayValue) {
      return formatPaymentModel(String(displayValue));
    } else if (type === 'currency' && displayValue) {
      return formatCurrency(String(displayValue));
    } else if (type === 'percentage' && displayValue) {
      return formatPercentage(String(displayValue));
    }
    
    return displayValue;
  };
  
  const formattedDisplayValue = formatDisplayValue();
  
  const renderEditField = () => {
    const baseClasses = inline 
      ? 'h-8 text-sm min-w-[120px] max-w-[200px]' 
      : '';
    
    const paddingClass = inline ? 'pr-12' : 'pr-20';
    
    if (type === 'textarea') {
      return (
        <Textarea
          value={fieldValue}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          autoFocus
          className={cn(
            paddingClass,
            inline ? 'min-h-[60px] text-sm' : 'min-h-[80px]',
            error ? 'border-rose-500 focus-visible:ring-rose-400' : ''
          )}
          disabled={isSaving}
        />
      );
    } else if (type === 'select' || fieldName === 'gender') {
      return (
        <Select 
          defaultValue={fieldValue} 
          onValueChange={handleSelectChange}
          disabled={isSaving}
        >
          <SelectTrigger className={cn(
            baseClasses,
            error ? 'border-rose-500 focus-visible:ring-rose-400' : ''
          )}>
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent>
            {fieldName === 'gender' && options.length === 0 ? (
              <>
                <SelectItem value="masculino">Masculino</SelectItem>
                <SelectItem value="feminino">Feminino</SelectItem>
                <SelectItem value="outro">Outro</SelectItem>
                <SelectItem value="prefiro_nao_informar">Prefiro não informar</SelectItem>
              </>
            ) : (
              options.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))
            )}
          </SelectContent>
        </Select>
      );
    } else {
      const inputType = type === 'currency' || type === 'percentage' ? 'text' : type;
      
      return (
        <Input
          type={inputType}
          value={fieldValue}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          placeholder={type === 'tel' ? '+55 (99) 99999-9999' : placeholder}
          autoFocus
          className={cn(
            paddingClass,
            baseClasses,
            error ? 'border-rose-500 focus-visible:ring-rose-400' : ''
          )}
          disabled={isSaving}
        />
      );
    }
  };
  
  const renderActionButtons = () => (
    <div className={cn(
      "absolute flex space-x-1",
      inline ? "right-1 top-1" : "right-1 top-1"
    )}>
      <Button 
        size="sm" 
        variant="ghost" 
        className={cn(
          "p-0 text-rose-500 hover:text-rose-600",
          inline ? "h-6 w-6" : "h-7 w-7"
        )}
        onClick={handleCancel}
        disabled={isSaving}
      >
        <X className={cn(inline ? "h-3 w-3" : "h-4 w-4")} />
      </Button>
      <Button 
        size="sm" 
        variant="ghost" 
        className={cn(
          "p-0 text-emerald-500 hover:text-emerald-600",
          inline ? "h-6 w-6" : "h-7 w-7"
        )}
        onClick={handleConfirm}
        disabled={isSaving}
      >
        {isSaving ? (
          <Loader2 className={cn("animate-spin", inline ? "h-3 w-3" : "h-4 w-4")} />
        ) : (
          <Check className={cn(inline ? "h-3 w-3" : "h-4 w-4")} />
        )}
      </Button>
    </div>
  );

  const recentlyEditedClass = wasRecentlyEdited 
    ? "bg-green-50 dark:bg-green-900/20 transition-colors duration-500" 
    : "";

  if (inline) {
    return (
      <div className={cn('group relative', className)}>
        {isEditing ? (
          <div className="relative">
            {renderEditField()}
            {renderActionButtons()}
            
            {error && (
              <p className="text-sm text-rose-500 mt-1">{error}</p>
            )}
          </div>
        ) : (
          <div className={cn(
            "flex items-center gap-1 rounded transition-colors duration-200",
            recentlyEditedClass
          )}>
            <span className={cn(
              "text-sm text-slate-900 dark:text-slate-100 min-h-[20px] flex items-center",
              !formattedDisplayValue && "text-slate-400 dark:text-slate-500 italic",
              className
            )}>
              {formattedDisplayValue || emptyText}
            </span>
            
            {canEditThisField && (
              <Button
                size="sm"
                variant="ghost"
                className="h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-slate-100 dark:hover:bg-slate-700 rounded flex-shrink-0"
                onClick={handleEdit}
              >
                <Edit2 className="h-3 w-3 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300" />
              </Button>
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={cn(
      'group relative mb-2 pb-2 rounded-md',
      wasRecentlyEdited && 'bg-yellow-50/50 dark:bg-yellow-950/30',
      !value && 'opacity-80',
      isEditing && 'bg-slate-50 dark:bg-slate-800/50 shadow-sm',
      className
    )}>
      {!inline && (
        <div className="flex justify-between items-center mb-1">
          <div className="flex items-center gap-2 text-sm text-slate-500 dark:text-slate-400">
            {icon && <span>{icon}</span>}
            <span className="font-medium">{label}</span>
          </div>
          
          {canEditThisField && !isEditing && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={handleEdit}
            >
              <Edit2 className="h-3.5 w-3.5" />
              <span className="sr-only">Editar {label}</span>
            </Button>
          )}
        </div>
      )}
      
      {isEditing ? (
        <div className="mt-1 relative">
          {renderEditField()}
          {renderActionButtons()}
          
          {error && (
            <p className="text-sm text-rose-500 mt-1">{error}</p>
          )}
        </div>
      ) : (
        <div className="mt-1">
          <p className={cn(
            "text-base text-slate-900 dark:text-slate-100 rounded p-1",
            displayValue === emptyText && "text-slate-400 dark:text-slate-500 italic",
            recentlyEditedClass
          )}>
            {formattedDisplayValue}
          </p>
        </div>
      )}
    </div>
  )
}