'use server'

import { createClient } from '@/services/supabase/server'
import { getCurrentUser } from '@/services/auth/actions/auth-actions'
import { 
  StudentDashboardData, 
  TeacherDashboardData, 
  InstructorDashboardData, 
  RecentActivity,
  UserRole 
} from './types'

export async function getStudentDashboardData(userId: string): Promise<StudentDashboardData> {
  try {
    const supabase = await createClient()
    
    // Buscar dados do aluno
    const { data: student, error: studentError } = await supabase
      .from('students')
      .select('id, user_id')
      .eq('user_id', userId)
      .single()

    if (studentError && studentError.code !== 'PGRST116') {
      console.error('Erro ao buscar dados do estudante:', studentError)
      return {
        proximasAulas: 0,
        tarefasPendentes: 0,
        mensagens: 0
      }
    }

    if (!student) {
      return {
        proximasAulas: 0,
        tarefasPendentes: 0,
        mensagens: 0
      }
    }

    // Aqui você pode adicionar queries reais quando as tabelas estiverem disponíveis
    // Por enquanto, retornando dados mock
    
    return {
      proximasAulas: 0,
      tarefasPendentes: 0,
      mensagens: 0
    }
  } catch (error) {
    console.error('Erro ao buscar dados do dashboard do estudante:', error)
    return {
      proximasAulas: 0,
      tarefasPendentes: 0,
      mensagens: 0
    }
  }
}

export async function getTeacherDashboardData(userId: string): Promise<TeacherDashboardData> {
  try {
    const supabase = await createClient()
    
    // Buscar dados do professor
    const { data: teacher, error: teacherError } = await supabase
      .from('users')
      .select('id')
      .eq('id', userId)
      .eq('role', 'teacher')
      .single()

    if (teacherError) {
      console.error('Erro ao buscar dados do professor:', teacherError)
      return {
        meusAlunos: 0,
        aulasProgramadas: 0,
        avaliacoesPendentes: 0
      }
    }

    // Aqui você pode adicionar queries reais quando as tabelas estiverem disponíveis
    // Por enquanto, retornando dados mock
    
    return {
      meusAlunos: 0,
      aulasProgramadas: 0,
      avaliacoesPendentes: 0
    }
  } catch (error) {
    console.error('Erro ao buscar dados do dashboard do professor:', error)
    return {
      meusAlunos: 0,
      aulasProgramadas: 0,
      avaliacoesPendentes: 0
    }
  }
}

export async function getInstructorDashboardData(userId: string): Promise<InstructorDashboardData> {
  try {
    const supabase = await createClient()
    
    // Buscar dados do instrutor
    const { data: instructor, error: instructorError } = await supabase
      .from('instructors')
      .select('id, user_id, is_active')
      .eq('user_id', userId)
      .single()

    if (instructorError && instructorError.code !== 'PGRST116') {
      console.error('Erro ao buscar dados do instrutor:', instructorError)
      return {
        alunosAtribuidos: 0,
        aulasHoje: 0,
        graduacoesPendentes: 0,
        presencasRegistrar: 0
      }
    }

    if (!instructor) {
      return {
        alunosAtribuidos: 0,
        aulasHoje: 0,
        graduacoesPendentes: 0,
        presencasRegistrar: 0
      }
    }

    // Buscar alunos atribuídos ao instrutor
    const { data: assignedStudents, error: studentsError } = await supabase
      .from('students')
      .select('id')
      .eq('instructor_id', instructor.id)

    const alunosCount = assignedStudents?.length || 0

    // Aqui você pode adicionar mais queries quando as tabelas estiverem disponíveis
    // Por enquanto, retornando alguns dados reais e outros mock
    
    return {
      alunosAtribuidos: alunosCount,
      aulasHoje: 0,
      graduacoesPendentes: 0,
      presencasRegistrar: 0
    }
  } catch (error) {
    console.error('Erro ao buscar dados do dashboard do instrutor:', error)
    return {
      alunosAtribuidos: 0,
      aulasHoje: 0,
      graduacoesPendentes: 0,
      presencasRegistrar: 0
    }
  }
}

export async function getRecentActivities(userId: string, userRole: UserRole): Promise<RecentActivity[]> {
  try {
    const supabase = await createClient()
    
    // Por enquanto, retornando array vazio
    // Aqui você pode implementar a busca de atividades recentes
    // baseada no tipo de usuário
    
    return []
  } catch (error) {
    console.error('Erro ao buscar atividades recentes:', error)
    return []
  }
} 