'use server'

import { createClient, createAdminClient } from '@/services/supabase/server'
import { getCurrentUser } from '@/src/services/auth/actions/auth-actions'
import { revalidatePath } from 'next/cache'
import { z } from 'zod'

// Função utilitária para excluir arquivo do storage de forma segura
async function safeRemoveFromStorage(supabase: any, avatarPath: string) {
  if (!avatarPath || avatarPath.trim() === '') {
    return { success: false, reason: 'Path inválido' }
  }

  try {
    console.log(`Tentando excluir arquivo do storage: ${avatarPath}`)
    
    // Verificar se o arquivo existe antes de tentar excluir
    const { data: fileExists, error: checkError } = await supabase
      .storage
      .from('avatars')
      .list('', { 
        search: avatarPath 
      })
    
    if (checkError) {
      console.error('Erro ao verificar arquivo no storage:', checkError)
      return { success: false, reason: 'Erro ao verificar arquivo', error: checkError }
    }
    
    const fileFound = fileExists?.some((file: any) => file.name === avatarPath)
    
    if (!fileFound) {
      console.log(`Arquivo ${avatarPath} não encontrado no storage (pode já ter sido excluído)`)
      return { success: true, reason: 'Arquivo não encontrado (já excluído)' }
    }
    
    const { error: deleteError } = await supabase
      .storage
      .from('avatars')
      .remove([avatarPath])
    
    if (deleteError) {
      console.error('Erro ao excluir avatar do storage:', deleteError)
      return { success: false, reason: 'Erro ao excluir', error: deleteError }
    }
    
    console.log(`Avatar excluído com sucesso do storage: ${avatarPath}`)
    return { success: true, reason: 'Excluído com sucesso' }
    
  } catch (storageError) {
    console.error('Exceção ao excluir avatar do storage:', storageError)
    return { success: false, reason: 'Exceção durante exclusão', error: storageError }
  }
}

// Função utilitária para atualizar o metadata do avatar no auth.users
async function updateAuthUserAvatarMetadata(
  supabase: any, 
  userId: string, 
  avatarUrl: string | null, 
  avatarStoragePath: string | null
) {
  try {
    console.log(`Atualizando avatar no metadata do auth.users para usuário ${userId}`)
    
    const { data: authUser, error: getAuthUserError } = await supabase.auth.admin.getUserById(userId)
    
    if (getAuthUserError) {
      console.error('Erro ao buscar usuário do auth:', getAuthUserError)
      return
    }
    
    if (!authUser?.user) {
      console.log('Usuário não encontrado no auth.users')
      return
    }
    
    const currentAppMetadata = authUser.user.app_metadata || {}
    let updatedAppMetadata
    
    if (avatarUrl && avatarStoragePath) {
      // Adicionar/atualizar avatar
      updatedAppMetadata = {
        ...currentAppMetadata,
        avatar_url: avatarUrl,
        avatar_storage_path: avatarStoragePath
      }
    } else {
      // Remover avatar
      const { avatar_url, avatar_storage_path, ...metadataWithoutAvatar } = currentAppMetadata
      updatedAppMetadata = metadataWithoutAvatar
    }
    
    const { error: updateAuthError } = await supabase.auth.admin.updateUserById(userId, {
      app_metadata: updatedAppMetadata
    })
    
    if (updateAuthError) {
      console.error('Erro ao atualizar metadata do auth.users:', updateAuthError)
    } else {
      console.log(`Metadata do auth.users atualizado com sucesso para usuário ${userId}`)
    }
  } catch (authError) {
    console.error('Exceção ao atualizar auth.users:', authError)
  }
}

// Função utilitária para atualizar o nome completo no auth.users
async function updateAuthUserNameMetadata(
  userId: string, 
  fullName: string,
  firstName: string,
  lastName: string
) {
  try {
    console.log(`Atualizando nome no metadata do auth.users para usuário ${userId}`)
    
    const adminSupabase = await createAdminClient()
    
    const { data: authUser, error: getAuthUserError } = await adminSupabase.auth.admin.getUserById(userId)
    
    if (getAuthUserError) {
      console.error('Erro ao buscar usuário do auth:', getAuthUserError)
      return
    }
    
    if (!authUser?.user) {
      console.log('Usuário não encontrado no auth.users')
      return
    }
    
    const currentAppMetadata = authUser.user.app_metadata || {}
    const currentUserMetadata = authUser.user.user_metadata || {}
    
    // Atualizar app_metadata
    const updatedAppMetadata = {
      ...currentAppMetadata,
      first_name: firstName,
      last_name: lastName
    }
    
    // Atualizar user_metadata
    const updatedUserMetadata = {
      ...currentUserMetadata,
      first_name: firstName,
      last_name: lastName,
      full_name: fullName
    }
    
    const { error: updateAuthError } = await adminSupabase.auth.admin.updateUserById(userId, {
      app_metadata: updatedAppMetadata,
      user_metadata: updatedUserMetadata
    })
    
    if (updateAuthError) {
      console.error('Erro ao atualizar metadata do auth.users:', updateAuthError)
    } else {
      console.log(`Metadata do auth.users atualizado com sucesso para usuário ${userId}`)
    }
  } catch (authError) {
    console.error('Exceção ao atualizar auth.users:', authError)
  }
}

const profileSchema = z.object({
  fullName: z.string().min(3, 'Nome deve ter pelo menos 3 caracteres').optional(),
  email: z.string().email('Email inválido').optional(),
  phone: z.string().optional().nullable(),
  address: z.string().optional().nullable(),
  emergency_contact: z.string().optional().nullable(),
  emergency_phone: z.string().optional().nullable(),
  emergency_contact_relationship: z.string().optional().nullable(),
  birthDate: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  gender: z.string().optional().nullable(),
  healthNotes: z.string().optional().nullable(),
  allergies: z.string().optional().nullable(),
  medicalConditions: z.string().optional().nullable(),
  medications: z.string().optional().nullable(),
})

const avatarSchema = z.object({
  file: z.instanceof(File, { message: 'Arquivo inválido' })
    .refine(file => file.size <= 50 * 1024 * 1024, 'O arquivo deve ter no máximo 50MB')
    .refine(
      file => ['image/jpeg', 'image/png', 'image/webp'].includes(file.type),
      'O arquivo deve ser uma imagem (JPEG, PNG ou WebP)'
    )
})

export type ProfileUpdateData = z.infer<typeof profileSchema>
export type AvatarUpdateData = z.infer<typeof avatarSchema>

export async function updateUserAvatar(userId: string, data: FormData) {
  try {
    const supabase = await createClient()
    const user = await getCurrentUser()
    
    if (!user) {
      return { success: false, error: 'Não autenticado' }
    }

    const currentUserId = user.id
    const { data: currentUser, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', currentUserId)
      .single()
    
    if (userError) {
      console.error('Erro ao buscar usuário:', userError)
      return { success: false, error: 'Erro ao verificar permissões' }
    }
    
    const isAdmin = currentUser?.role === 'admin'
    const isOwnProfile = currentUserId === userId
    
    console.log(`Atualizando avatar: isAdmin=${isAdmin}, isOwnProfile=${isOwnProfile}, userId=${userId}`)

    if (!isAdmin && !isOwnProfile) {
      return { success: false, error: 'Permissão negada' }
    }

    const file = data.get('file') as File
    
    if (!file) {
      return { success: false, error: 'Nenhum arquivo enviado' }
    }

    const validation = avatarSchema.safeParse({ file })
    
    if (!validation.success) {
      const formattedErrors = validation.error.format()
      return { 
        success: false, 
        error: formattedErrors.file?._errors?.[0] || 'Arquivo inválido'
      }
    }

    // Agora precisamos buscar o avatar_storage_path do usuário que está sendo modificado
    const { data: targetUser, error: targetUserError } = await supabase
      .from('users')
      .select('avatar_storage_path, avatar_url')
      .eq('id', userId)
      .single()

    if (targetUserError) {
      console.error('Erro ao buscar usuário alvo:', targetUserError)
      return { success: false, error: 'Erro ao buscar informações do usuário' }
    }

    console.log('Dados do usuário alvo:', targetUser)

    // Excluir avatar anterior se existir
    const previousAvatarPath = targetUser?.avatar_storage_path
    if (previousAvatarPath && previousAvatarPath.trim() !== '') {
      console.log(`Tentando excluir avatar anterior: ${previousAvatarPath}`)
      const removeResult = await safeRemoveFromStorage(supabase, previousAvatarPath)
      
      if (removeResult.success) {
        console.log(`Avatar anterior processado com sucesso: ${removeResult.reason}`)
      } else {
        console.error(`Falha ao excluir avatar anterior: ${removeResult.reason}`)
        // Continuamos mesmo com erro na exclusão para não impedir a atualização
      }
    } else {
      console.log(`Usuário ${userId} não possui avatar anterior para excluir ou o path é inválido`)
    }

    const fileExtension = file.name.split('.').pop()
    const fileName = `${userId}_${Date.now()}.${fileExtension}`
    
    const { error: uploadError } = await supabase
      .storage
      .from('avatars')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: true
      })
    
    if (uploadError) {
      console.error('Erro ao fazer upload do avatar:', uploadError)
      return { success: false, error: 'Erro ao fazer upload da imagem' }
    }

    const { data: publicUrlData } = supabase
      .storage
      .from('avatars')
      .getPublicUrl(fileName)
    
    const avatarUrl = publicUrlData.publicUrl
    console.log(`Novo avatar URL: ${avatarUrl}, path: ${fileName}`)

    const { error: updateError } = await supabase
      .from('users')
      .update({
        avatar_url: avatarUrl,
        avatar_storage_path: fileName,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
    
    if (updateError) {
      console.error('Erro ao atualizar perfil com o novo avatar:', updateError)
      return { success: false, error: 'Erro ao atualizar perfil' }
    }

    // Atualizar o metadata do auth.users com o novo avatar
    await updateAuthUserAvatarMetadata(supabase, userId, avatarUrl, fileName)

    console.log(`Perfil do usuário ${userId} atualizado com sucesso com novo avatar`)
    
    // Revalidar o cache da lista de alunos quando o avatar for atualizado
    // Usamos a chave de cache fornecida para garantir que a lista seja atualizada
    revalidatePath('/alunos')
    
    return { 
      success: true,
      avatarUrl
    }
  } catch (error) {
    console.error('Erro ao atualizar avatar:', error)
    
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        error: 'Dados inválidos', 
        fieldErrors: error.errors.map(e => ({
          path: e.path.join('.'),
          message: e.message
        }))
      }
    }
    
    return { success: false, error: 'Erro ao atualizar avatar' }
  }
}

export async function updateUserProfile(userId: string, data: ProfileUpdateData) {
  try {
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return { success: false, error: 'Não autenticado' }
    }

    const currentUserId = user.id
    const { data: currentUser, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', currentUserId)
      .single()
    
    if (userError) {
      console.error('Erro ao buscar usuário:', userError)
      return { success: false, error: 'Erro ao verificar permissões' }
    }
    
    const isAdmin = currentUser?.role === 'admin'
    const isOwnProfile = currentUserId === userId

    if (!isAdmin && isOwnProfile) {
      const userRole = currentUser?.role
      
      if (userRole === 'student') {
        return { success: false, error: 'Estudantes não podem editar seus próprios perfis' }
      }
    }

    if (!isAdmin && !isOwnProfile) {
      return { success: false, error: 'Permissão negada' }
    }

    const validatedData = profileSchema.parse(data)

    const { data: currentProfile, error: profileError } = await supabase
      .from('users')
      .select('metadata, first_name, last_name, role')
      .eq('id', userId)
      .single()
    
    if (profileError) {
      console.error('Erro ao buscar perfil atual:', profileError)
      return { success: false, error: 'Erro ao buscar dados do perfil' }
    }

    const addressFields = [
      'street', 'street_number', 'complement', 'neighborhood',
      'city', 'state', 'postal_code'
    ]
    
    const hasAddressFields = Object.keys(data).some(key => addressFields.includes(key))
    
    const updateData: Record<string, any> = {}
    
    if (validatedData.email) updateData.email = validatedData.email
    if (validatedData.phone !== undefined) updateData.phone = validatedData.phone
    
    if (validatedData.fullName) {
      const nameParts = validatedData.fullName.trim().split(' ')
      if (nameParts.length >= 1) {
        updateData.first_name = nameParts[0]
        updateData.full_name = validatedData.fullName
        
        if (nameParts.length > 1) {
          updateData.last_name = nameParts.slice(1).join(' ')
        }
      }
    }
    
    // Preparar metadata com dados existentes
    const metadata = { ...(currentProfile?.metadata || {}) }
    
    // Atualizar campos no metadata
    if (validatedData.address !== undefined) metadata.address = validatedData.address
    if (validatedData.emergency_contact !== undefined) metadata.emergency_contact = validatedData.emergency_contact
    if (validatedData.emergency_phone !== undefined) metadata.emergency_phone = validatedData.emergency_phone
    if (validatedData.emergency_contact_relationship !== undefined) 
      metadata.emergency_contact_relationship = validatedData.emergency_contact_relationship
    if (validatedData.birthDate !== undefined) metadata.birthDate = validatedData.birthDate
    if (validatedData.notes !== undefined) metadata.notes = validatedData.notes
    if (validatedData.gender !== undefined) metadata.gender = validatedData.gender
    if (validatedData.healthNotes !== undefined) metadata.healthNotes = validatedData.healthNotes
    if (validatedData.allergies !== undefined) metadata.allergies = validatedData.allergies
    if (validatedData.medicalConditions !== undefined) metadata.medicalConditions = validatedData.medicalConditions
    if (validatedData.medications !== undefined) metadata.medications = validatedData.medications
    
    // Adicionar metadata atualizado
    updateData.metadata = metadata

    // Atualizar perfil na tabela users
    const { error: updateError } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', userId)
    
    if (updateError) {
      console.error('Erro ao atualizar perfil:', updateError)
      return { success: false, error: 'Erro ao atualizar perfil' }
    }

    // Atualizar nome no auth.users se o nome completo foi alterado
    if (validatedData.fullName) {
      const nameParts = validatedData.fullName.trim().split(' ')
      const firstName = nameParts[0]
      const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : ''
      
      await updateAuthUserNameMetadata(
        userId, 
        validatedData.fullName,
        firstName,
        lastName
      )
    }

    if (currentProfile?.role === 'student' && 
       (validatedData.birthDate !== undefined || 
        validatedData.gender !== undefined || 
        validatedData.emergency_contact !== undefined || 
        validatedData.emergency_phone !== undefined || 
        validatedData.emergency_contact_relationship !== undefined ||
        validatedData.healthNotes !== undefined ||
        validatedData.allergies !== undefined ||
        validatedData.medicalConditions !== undefined ||
        validatedData.medications !== undefined ||
        hasAddressFields)) {
      
      // Buscar o registro do aluno pelo userId
      const { data: studentData, error: studentFindError } = await supabase
        .from('students')
        .select('id')
        .eq('user_id', userId)
        .single()
      
      if (studentFindError) {
        console.error('Erro ao buscar registro de estudante:', studentFindError)
        return { success: false, error: 'Erro ao localizar registro de estudante' }
      }

      if (studentData) {
        // Preparar dados para atualização na tabela students
        const studentUpdateData: Record<string, any> = {}
        
        if (validatedData.birthDate !== undefined) {
          studentUpdateData.birth_date = validatedData.birthDate
          console.log(`Atualizando birth_date para ${validatedData.birthDate} na tabela students`)
        }
        
        if (validatedData.gender !== undefined) {
          studentUpdateData.gender = validatedData.gender
          console.log(`Atualizando gender para ${validatedData.gender} na tabela students`)
        }

        // Dados de contato de emergência
        if (validatedData.emergency_contact !== undefined) {
          // Atualizar tanto no campo específico quanto no metadata
          studentUpdateData.emergency_contact_name = validatedData.emergency_contact
          console.log(`Atualizando emergency_contact_name para ${validatedData.emergency_contact} na tabela students`)
        }
        
        if (validatedData.emergency_phone !== undefined) {
          // Atualizar tanto no campo específico quanto no metadata
          studentUpdateData.emergency_contact_phone = validatedData.emergency_phone
          console.log(`Atualizando emergency_contact_phone para ${validatedData.emergency_phone} na tabela students`)
        }
        
        if (validatedData.emergency_contact_relationship !== undefined) {
          // Atualizar no campo específico da tabela students
          studentUpdateData.emergency_contact_relationship = validatedData.emergency_contact_relationship
          console.log(`Atualizando emergency_contact_relationship para ${validatedData.emergency_contact_relationship} na tabela students`)
        }
        
        // Dados médicos
        if (validatedData.healthNotes !== undefined) {
          studentUpdateData.health_notes = validatedData.healthNotes
          console.log(`Atualizando health_notes para ${validatedData.healthNotes} na tabela students`)
        }
        
        if (validatedData.allergies !== undefined) {
          studentUpdateData.allergies = validatedData.allergies
          console.log(`Atualizando allergies para ${validatedData.allergies} na tabela students`)
        }
        
        if (validatedData.medicalConditions !== undefined) {
          studentUpdateData.medical_conditions = validatedData.medicalConditions
          console.log(`Atualizando medical_conditions para ${validatedData.medicalConditions} na tabela students`)
        }
        
        if (validatedData.medications !== undefined) {
          studentUpdateData.medications = validatedData.medications
          console.log(`Atualizando medications para ${validatedData.medications} na tabela students`)
        }
        
        // Dados de endereço - verificar se há campos de endereço na requisição e atualizá-los
        const addressData = data as ProfileUpdateData & Record<string, any>;
        
        addressFields.forEach(field => {
          if (addressData[field] !== undefined) {
            studentUpdateData[field] = addressData[field];
            console.log(`Atualizando ${field} para ${addressData[field]} na tabela students`);
          }
        });
        
        // Atualizar tabela students
        const { error: studentUpdateError } = await supabase
          .from('students')
          .update(studentUpdateData)
          .eq('id', studentData.id)
        
        if (studentUpdateError) {
          console.error('Erro ao atualizar dados do estudante:', studentUpdateError)
          return { success: false, error: 'Erro ao atualizar dados do estudante' }
        }
      }
    }

    if (currentProfile?.role === 'instructor' &&
       (validatedData.birthDate !== undefined ||
        validatedData.gender !== undefined)) {
      // Buscar registro do instrutor pelo userId
      const { data: instructorData, error: instructorFindError } = await supabase
        .from('instructors')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (instructorFindError) {
        console.error('Erro ao buscar registro de instrutor:', instructorFindError);
        return { success: false, error: 'Erro ao localizar registro de instrutor' };
      }

      if (instructorData) {
        const instructorUpdateData: Record<string, any> = {};

        if (validatedData.birthDate !== undefined) {
          instructorUpdateData.birth_date = validatedData.birthDate;
          console.log(`Atualizando birth_date para ${validatedData.birthDate} na tabela instructors`);
        }

        if (validatedData.gender !== undefined) {
          instructorUpdateData.gender = validatedData.gender;
          console.log(`Atualizando gender para ${validatedData.gender} na tabela instructors`);
        }

        if (Object.keys(instructorUpdateData).length > 0) {
          const { error: instructorUpdateError } = await supabase
            .from('instructors')
            .update(instructorUpdateData)
            .eq('id', instructorData.id);

          if (instructorUpdateError) {
            console.error('Erro ao atualizar dados do instrutor:', instructorUpdateError);
            return { success: false, error: 'Erro ao atualizar dados do instrutor' };
          }
        }
      }
    }

    return { success: true }
  } catch (error) {
    console.error('Erro ao atualizar perfil:', error)
    
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        error: 'Dados inválidos', 
        fieldErrors: error.errors.map(e => ({
          path: e.path.join('.'),
          message: e.message
        }))
      }
    }
    
    return { success: false, error: 'Erro ao atualizar perfil' }
  }
}

export async function cleanOrphanedAvatars() {
  try {
    const supabase = await createClient()
    const user = await getCurrentUser()
    
    if (!user) {
      return { success: false, error: 'Não autenticado' }
    }

    // Verificar se é admin
    const { data: currentUser, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()
    
    if (userError || currentUser?.role !== 'admin') {
      return { success: false, error: 'Apenas administradores podem executar esta operação' }
    }

    console.log('Iniciando limpeza de arquivos órfãos no bucket de avatares...')

    // Buscar todos os arquivos no bucket
    const { data: storageFiles, error: storageError } = await supabase
      .storage
      .from('avatars')
      .list('')

    if (storageError) {
      console.error('Erro ao listar arquivos do storage:', storageError)
      return { success: false, error: 'Erro ao acessar storage' }
    }

    if (!storageFiles || storageFiles.length === 0) {
      return { success: true, message: 'Nenhum arquivo encontrado no bucket', orphansRemoved: 0 }
    }

    // Buscar todos os avatar_storage_path em uso
    const { data: usersWithAvatars, error: usersError } = await supabase
      .from('users')
      .select('avatar_storage_path')
      .not('avatar_storage_path', 'is', null)

    if (usersError) {
      console.error('Erro ao buscar usuários com avatares:', usersError)
      return { success: false, error: 'Erro ao buscar dados dos usuários' }
    }

    // Buscar também nos metadados do auth
    const { data: { users: authUsers }, error: authError } = await supabase.auth.admin.listUsers()
    
    if (authError) {
      console.error('Erro ao buscar usuários do auth:', authError)
      return { success: false, error: 'Erro ao buscar dados de autenticação' }
    }

    // Criar lista de paths em uso
    const pathsInUse = new Set<string>()
    
    // Adicionar paths da tabela users
    usersWithAvatars?.forEach(user => {
      if (user.avatar_storage_path) {
        pathsInUse.add(user.avatar_storage_path)
      }
    })

    // Adicionar paths dos metadados do auth
    authUsers?.forEach(authUser => {
      const avatarPath = authUser.app_metadata?.avatar_storage_path
      if (avatarPath) {
        pathsInUse.add(avatarPath)
      }
    })

    console.log(`Encontrados ${storageFiles.length} arquivos no storage e ${pathsInUse.size} paths em uso`)

    // Identificar arquivos órfãos
    const orphanedFiles = storageFiles.filter(file => !pathsInUse.has(file.name))

    if (orphanedFiles.length === 0) {
      return { success: true, message: 'Nenhum arquivo órfão encontrado', orphansRemoved: 0 }
    }

    console.log(`Encontrados ${orphanedFiles.length} arquivos órfãos:`, orphanedFiles.map(f => f.name))

    // Remover arquivos órfãos
    let removedCount = 0
    const errors: string[] = []

    for (const file of orphanedFiles) {
      const removeResult = await safeRemoveFromStorage(supabase, file.name)
      
      if (removeResult.success) {
        removedCount++
        console.log(`Arquivo órfão removido: ${file.name}`)
      } else {
        errors.push(`Falha ao remover ${file.name}: ${removeResult.reason}`)
        console.error(`Erro ao remover arquivo órfão ${file.name}:`, removeResult.reason)
      }
    }

    return { 
      success: true, 
      message: `Limpeza concluída. ${removedCount} de ${orphanedFiles.length} arquivos órfãos removidos.`,
      orphansRemoved: removedCount,
      totalOrphans: orphanedFiles.length,
      errors: errors.length > 0 ? errors : undefined
    }

  } catch (error) {
    console.error('Erro durante limpeza de arquivos órfãos:', error)
    return { success: false, error: 'Erro interno durante limpeza' }
  }
}

export async function deleteUserAvatar(userId: string) {
  try {
    const supabase = await createClient()
    const user = await getCurrentUser()
    
    if (!user) {
      return { success: false, error: 'Não autenticado' }
    }

    const currentUserId = user.id
    const { data: currentUser, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', currentUserId)
      .single()
    
    if (userError) {
      console.error('Erro ao buscar usuário:', userError)
      return { success: false, error: 'Erro ao verificar permissões' }
    }
    
    const isAdmin = currentUser?.role === 'admin'
    const isOwnProfile = currentUserId === userId
    
    console.log(`Excluindo avatar: isAdmin=${isAdmin}, isOwnProfile=${isOwnProfile}, userId=${userId}`)

    if (!isAdmin && !isOwnProfile) {
      return { success: false, error: 'Permissão negada' }
    }

    // Buscar o avatar_storage_path do usuário
    const { data: targetUser, error: targetUserError } = await supabase
      .from('users')
      .select('avatar_storage_path, avatar_url')
      .eq('id', userId)
      .single()

    if (targetUserError) {
      console.error('Erro ao buscar usuário alvo:', targetUserError)
      return { success: false, error: 'Erro ao buscar informações do usuário' }
    }

    console.log('Dados do usuário alvo:', targetUser)

    // Verificar se o usuário tem avatar para excluir
    const avatarPath = targetUser?.avatar_storage_path
    const avatarUrl = targetUser?.avatar_url
    
    if (!avatarUrl && !avatarPath) {
      return { success: false, error: 'Usuário não possui avatar para excluir' }
    }

    // Excluir avatar do storage apenas se existir avatar_storage_path
    // (avatars antigos ou externos podem ter apenas avatar_url)
    if (avatarPath && avatarPath.trim() !== '') {
      console.log(`Excluindo avatar do storage: ${avatarPath}`)
      const removeResult = await safeRemoveFromStorage(supabase, avatarPath)
      
      if (removeResult.success) {
        console.log(`Avatar processado com sucesso: ${removeResult.reason}`)
      } else {
        console.error(`Falha ao excluir avatar: ${removeResult.reason}`)
        // Não retornamos erro aqui para permitir que o banco seja atualizado
        // mesmo se o arquivo não existir no storage
      }
    } else {
      console.log(`Usuário ${userId} possui avatar_url mas não avatar_storage_path (avatar externo ou antigo)`)
    }

    // Atualizar o banco de dados removendo as URLs e path do avatar
    const { error: updateError } = await supabase
      .from('users')
      .update({
        avatar_url: null,
        avatar_storage_path: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
    
    if (updateError) {
      console.error('Erro ao atualizar perfil removendo avatar:', updateError)
      return { success: false, error: 'Erro ao atualizar perfil' }
    }

    // Atualizar o metadata do auth.users removendo o avatar
    await updateAuthUserAvatarMetadata(supabase, userId, null, null)

    console.log(`Avatar do usuário ${userId} excluído com sucesso`)
    
    // Revalidar o cache da lista de alunos quando o avatar for excluído
    revalidatePath('/alunos')
    
    return { 
      success: true,
      message: 'Avatar excluído com sucesso'
    }
  } catch (error) {
    console.error('Erro ao excluir avatar:', error)
    
    return { success: false, error: 'Erro ao excluir avatar' }
  }
}

const syncUserDataSchema = z.object({
  userId: z.string().uuid('ID de usuário inválido').optional(),
  syncAll: z.boolean().optional().default(false)
});

/**
 * Sincroniza dados do auth.users com a tabela users
 * Especificamente o campo full_name que pode estar incompleto na tabela users
 */
export async function syncUserDataFromAuth(formData?: { userId?: string; syncAll?: boolean }) {
  try {
    const result = syncUserDataSchema.safeParse(formData);
    
    if (!result.success) {
      return {
        success: false,
        errors: { _form: 'Dados inválidos para sincronização' }
      };
    }

    const { userId, syncAll } = result.data;
    const supabase = await createClient();

    // Se foi especificado um userId, sincronizar apenas este usuário
    if (userId) {
      return await syncSingleUser(supabase, userId);
    }

    // Se syncAll for true, sincronizar todos os usuários com dados inconsistentes
    if (syncAll) {
      return await syncAllInconsistentUsers(supabase);
    }

    return {
      success: false,
      errors: { _form: 'Parâmetros de sincronização não especificados' }
    };
  } catch (error) {
    console.error('Erro ao sincronizar dados do usuário:', error);
    return {
      success: false,
      errors: { _form: 'Erro interno do servidor' }
    };
  }
}

async function syncSingleUser(supabase: any, userId: string) {
  try {
    // Buscar dados do auth.users
    const { data: authData, error: authError } = await supabase
      .from('auth.users')
      .select('raw_user_meta_data')
      .eq('id', userId)
      .single();

    if (authError || !authData?.raw_user_meta_data) {
      return {
        success: false,
        errors: { _form: 'Dados de autenticação não encontrados' }
      };
    }

    const authMetadata = authData.raw_user_meta_data;
    const fullNameFromAuth = authMetadata.full_name;

    if (!fullNameFromAuth) {
      return {
        success: false,
        errors: { _form: 'Nome completo não encontrado nos dados de autenticação' }
      };
    }

    // Buscar dados atuais do usuário
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('full_name, first_name, last_name')
      .eq('id', userId)
      .single();

    if (userError) {
      return {
        success: false,
        errors: { _form: 'Usuário não encontrado' }
      };
    }

    // Verificar se precisa de atualização
    const needsUpdate = !userData.full_name || 
      userData.full_name !== fullNameFromAuth ||
      userData.full_name.trim().split(' ').length < 3;

    if (!needsUpdate) {
      return {
        success: true,
        data: { message: 'Dados já estão sincronizados', updated: false }
      };
    }

    // Atualizar os dados
    const { error: updateError } = await supabase
      .from('users')
      .update({
        full_name: fullNameFromAuth,
        // Também sincronizar first_name e last_name se necessário
        first_name: authMetadata.first_name || userData.first_name,
        last_name: authMetadata.last_name || userData.last_name
      })
      .eq('id', userId);

    if (updateError) {
      return {
        success: false,
        errors: { _form: 'Erro ao atualizar dados do usuário' }
      };
    }

    return {
      success: true,
      data: { 
        message: 'Dados sincronizados com sucesso',
        updated: true,
        oldFullName: userData.full_name,
        newFullName: fullNameFromAuth
      }
    };
  } catch (error) {
    console.error('Erro ao sincronizar usuário individual:', error);
    return {
      success: false,
      errors: { _form: 'Erro ao sincronizar usuário' }
    };
  }
}

async function syncAllInconsistentUsers(supabase: any) {
  try {
    // Buscar usuários com dados inconsistentes
    const { data: inconsistentUsers, error: queryError } = await supabase
      .rpc('get_users_with_inconsistent_names');

    if (queryError) {
      console.error('Erro ao buscar usuários inconsistentes:', queryError);
      // Fallback: buscar manualmente
      return await syncAllUsersFallback(supabase);
    }

    if (!inconsistentUsers || inconsistentUsers.length === 0) {
      return {
        success: true,
        data: { message: 'Nenhum usuário com dados inconsistentes encontrado', updated: 0 }
      };
    }

    let updatedCount = 0;
    const errors: string[] = [];

    for (const user of inconsistentUsers) {
      const result = await syncSingleUser(supabase, user.id);
      if (result.success && result.data?.updated) {
        updatedCount++;
      } else if (!result.success) {
        errors.push(`Erro ao sincronizar usuário ${user.id}: ${result.errors?._form}`);
      }
    }

    return {
      success: true,
      data: {
        message: `Sincronização concluída. ${updatedCount} usuários atualizados.`,
        updated: updatedCount,
        errors: errors.length > 0 ? errors : undefined
      }
    };
  } catch (error) {
    console.error('Erro ao sincronizar todos os usuários:', error);
    return {
      success: false,
      errors: { _form: 'Erro ao sincronizar usuários' }
    };
  }
}

async function syncAllUsersFallback(supabase: any) {
  try {
    // Buscar todos os usuários e seus dados de auth para comparação
    const { data: usersData, error: usersError } = await supabase
      .from('users')
      .select('id, full_name, first_name, last_name');

    if (usersError) {
      return {
        success: false,
        errors: { _form: 'Erro ao buscar usuários' }
      };
    }

    let updatedCount = 0;
    const errors: string[] = [];

    for (const user of usersData) {
      // Verificar se precisa de sincronização (nome com menos de 3 palavras)
      const needsSync = !user.full_name || 
        user.full_name.trim().split(' ').length < 3;

      if (needsSync) {
        const result = await syncSingleUser(supabase, user.id);
        if (result.success && result.data?.updated) {
          updatedCount++;
        } else if (!result.success) {
          errors.push(`Erro ao sincronizar usuário ${user.id}: ${result.errors?._form}`);
        }
      }
    }

    return {
      success: true,
      data: {
        message: `Sincronização fallback concluída. ${updatedCount} usuários atualizados.`,
        updated: updatedCount,
        errors: errors.length > 0 ? errors : undefined
      }
    };
  } catch (error) {
    console.error('Erro na sincronização fallback:', error);
    return {
      success: false,
      errors: { _form: 'Erro na sincronização fallback' }
    };
  }
} 