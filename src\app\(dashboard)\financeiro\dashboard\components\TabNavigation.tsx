"use client";

/**
 * Componente de Navegação Rápida entre Abas
 * Fornece navegação contextual e atalhos para as seções do dashboard
 */

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  DollarSign, 
  TrendingDown, 
  TrendingUp, 
  <PERSON><PERSON>hart,
  Users,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';

// ============================================================================
// TIPOS
// ============================================================================

interface TabNavigationProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
}

interface QuickNavItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  status: 'active' | 'upcoming';
  description: string;
  badge?: string;
}

// ============================================================================
// CONFIGURAÇÃO DOS ITENS DE NAVEGAÇÃO
// ============================================================================

const quickNavItems: QuickNavItem[] = [
  {
    id: 'overview',
    label: 'Visão Geral',
    icon: BarChart3,
    status: 'active',
    description: 'KPIs e resumo executivo',
    badge: 'Ativo'
  },
  {
    id: 'revenue',
    label: 'Receitas',
    icon: DollarSign,
    status: 'upcoming',
    description: 'Análise de receitas',
    badge: 'Fase 3'
  },
  {
    id: 'expenses',
    label: 'Despesas',
    icon: TrendingDown,
    status: 'upcoming',
    description: 'Controle de gastos',
    badge: 'Fase 4'
  },
  {
    id: 'cashflow',
    label: 'Fluxo de Caixa',
    icon: TrendingUp,
    status: 'upcoming',
    description: 'Entradas e saídas',
    badge: 'Fase 5'
  },
  // {
  //   id: 'analytics',
  //   label: 'Análises',
  //   icon: PieChart,
  //   status: 'upcoming',
  //   description: 'Tendências e comparações',
  //   badge: 'Fase 6'
  // },
  {
    id: 'students',
    label: 'Alunos',
    icon: Users,
    status: 'upcoming',
    description: 'Métricas de retenção',
    badge: 'Fase 6'
  }
];

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const TabNavigation: React.FC<TabNavigationProps> = ({
  activeTab,
  onTabChange,
  className
}) => {
  const getStatusColor = (status: QuickNavItem['status'], isActive: boolean) => {
    if (isActive) {
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 border-blue-200 dark:border-blue-800';
    }
    
    switch (status) {
      case 'active':
        return 'bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/30';
      case 'upcoming':
        return 'bg-gray-50 text-gray-600 dark:bg-gray-800/50 dark:text-gray-400 border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800';
    }
  };

  const getBadgeColor = (status: QuickNavItem['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'upcoming':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400';
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Navegação Rápida
        </h3>
        <Badge variant="outline" className="text-xs">
          {quickNavItems.filter(item => item.status === 'active').length} de {quickNavItems.length} seções ativas
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        {quickNavItems.map((item) => {
          const IconComponent = item.icon;
          const isActive = activeTab === item.id;
          const isClickable = item.status === 'active';

          return (
            <Button
              key={item.id}
              variant="outline"
              onClick={() => isClickable && onTabChange(item.id)}
              disabled={!isClickable}
              className={cn(
                "h-auto p-4 justify-start text-left transition-all duration-200",
                getStatusColor(item.status, isActive),
                isClickable && "cursor-pointer",
                !isClickable && "cursor-not-allowed opacity-60"
              )}
            >
              <div className="flex items-start gap-3 w-full">
                <div className="flex-shrink-0 mt-0.5">
                  <IconComponent className="h-5 w-5" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium text-sm truncate">
                      {item.label}
                    </h4>
                    {item.badge && (
                      <Badge 
                        variant="secondary" 
                        className={cn("text-xs ml-2", getBadgeColor(item.status))}
                      >
                        {item.badge}
                      </Badge>
                    )}
                  </div>
                  
                  <p className="text-xs opacity-80 line-clamp-1">
                    {item.description}
                  </p>
                </div>

                {isClickable && (
                  <div className="flex-shrink-0 mt-0.5">
                    <ChevronRight className="h-4 w-4 opacity-50" />
                  </div>
                )}
              </div>
            </Button>
          );
        })}
      </div>

      {/* Informação sobre seções futuras */}
      <div className="p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800">
        <div className="flex items-start gap-2">
          <div className="flex-shrink-0 mt-0.5">
            <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
          </div>
          <div>
            <p className="text-sm text-amber-800 dark:text-amber-200">
              <span className="font-medium">Seções em desenvolvimento:</span> As seções marcadas com "Fase X" serão implementadas nas próximas iterações do dashboard.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
