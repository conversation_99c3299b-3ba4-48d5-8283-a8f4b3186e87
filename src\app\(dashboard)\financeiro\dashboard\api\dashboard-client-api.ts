/**
 * API Client para Dashboard Financeiro
 * Funções para uso em Client Components que fazem fetch para API routes
 */

import { 
  DashboardActionResult, 
  FinancialKPIs, 
  DashboardData, 
  DateRange 
} from '../types/dashboard-types';

/**
 * Busca dados consolidados do dashboard
 */
export async function fetchDashboardData(
  currentRange: DateRange,
  previousRange: DateRange
): Promise<DashboardActionResult<DashboardData>> {
  try {
    const response = await fetch('/api/dashboard/data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        currentRange,
        previousRange
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Erro ao buscar dados do dashboard:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

/**
 * Busca KPIs do dashboard
 */
export async function fetchDashboardKPIs(
  currentRange: DateRange,
  previousRange: DateRange
): Promise<DashboardActionResult<FinancialKPIs>> {
  try {
    const response = await fetch('/api/dashboard/kpis', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        currentRange,
        previousRange
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Erro ao buscar KPIs do dashboard:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}
