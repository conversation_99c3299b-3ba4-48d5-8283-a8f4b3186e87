import { Button } from '@/components/ui/button';
import { 
  FormDescription, 
  FormLabel 
} from '@/components/ui/form';
import { cn } from '@/lib/utils';
import { DAYS_OF_WEEK } from '../constants/class-group-constants';
import type { DaysOfWeekSelectorProps } from '../types/class-group-form-types';

export const DaysOfWeekSelector = ({ form, primaryColor }: DaysOfWeekSelectorProps) => {
  const selectedDays = (form.watch('recurrence_pattern.daysOfWeek') as number[]) || [];
  
  const handleDayToggle = (dayValue: number) => {
    const isSelected = selectedDays.includes(dayValue);
    const newDays = isSelected
      ? selectedDays.filter((d: number) => d !== dayValue)
      : [...selectedDays, dayValue].sort();
    
    form.setValue('recurrence_pattern.daysOfWeek', newDays, {
      shouldValidate: true,
    });
  };

  return (
    <div className="space-y-2">
      <FormLabel><PERSON><PERSON> da <PERSON></FormLabel>
      <div className="flex flex-wrap gap-2">
        {DAYS_OF_WEEK.map((day) => {
          const isSelected = selectedDays.includes(day.value);
          return (
            <Button
              key={day.value}
              type="button"
              variant={isSelected ? "default" : "outline"}
              size="sm"
              className={cn(
                "transition-all duration-200",
                isSelected && "bg-primary text-primary-foreground"
              )}
              style={isSelected && primaryColor ? { backgroundColor: primaryColor } : undefined}
              onClick={() => handleDayToggle(day.value)}
            >
              {day.short}
            </Button>
          );
        })}
      </div>
      <FormDescription>
        Selecione os dias da semana em que as aulas acontecerão
      </FormDescription>
    </div>
  );
}; 