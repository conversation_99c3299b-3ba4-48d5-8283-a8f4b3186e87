import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { navigationConfig } from '@/config/navigation';
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { isSupabaseConfigured, getSupabaseConfig } from '@/config/supabase';
import { TenantUtils } from '@/services/tenant/tenant-utils';
import { tenantCache } from '@/services/tenant/tenant-cache';
import { TENANT_HEADERS } from '@/services/tenant/tenant-config';
import { TenantExtractionContext } from '@/services/tenant/types';

// Cache LRU para roles de usuário
interface UserRoleCacheEntry {
  role: string;
  timestamp: number;
}

class UserRoleCache {
  private cache = new Map<string, UserRoleCacheEntry>();
  private readonly maxSize = 500; // Máximo de 500 usuários em cache
  private readonly ttl = 5 * 60 * 1000; // 5 minutos de TTL

  get(userId: string): string | null {
    const entry = this.cache.get(userId);
    if (!entry) return null;

    // Verificar se expirou
    if (Date.now() - entry.timestamp > this.ttl) {
      this.cache.delete(userId);
      return null;
    }

    // Mover para o final (LRU)
    this.cache.delete(userId);
    this.cache.set(userId, entry);

    return entry.role;
  }

  set(userId: string, role: string): void {
    // Limpar entrada mais antiga se o cache estiver cheio
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) this.cache.delete(firstKey);
    }

    const entry: UserRoleCacheEntry = {
      role,
      timestamp: Date.now()
    };

    this.cache.set(userId, entry);
  }

  clear(): void {
    this.cache.clear();
  }

  remove(userId: string): boolean {
    return this.cache.delete(userId);
  }

  // Limpar entradas expiradas
  cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    this.cache.forEach((entry, key) => {
      if (now - entry.timestamp > this.ttl) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => this.cache.delete(key));
  }

  // Estatísticas do cache (útil para debug)
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      ttl: this.ttl
    };
  }
}

// Instância singleton do cache de roles
const userRoleCache = new UserRoleCache();

// Cleanup automático a cada 2 minutos
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    userRoleCache.cleanup();
  }, 2 * 60 * 1000);
}

// Rotas que requerem permissões específicas baseadas em roles
const ROLE_BASED_ROUTES = {
  // Turmas: Admins veem todas; Instrutores veem apenas suas turmas; Estudantes não têm acesso
  '/turmas': { 
    admin: 'full_access',      // Acesso completo a todas as turmas
    instructor: 'own_only',    // Apenas turmas que ministra
    student: 'no_access'       // Sem acesso
  },
  '/turmas/*': {
    admin: 'full_access',
    instructor: 'own_only',
    student: 'no_access'
  },
  // Aulas Livres: Apenas admins têm acesso
  '/aulas/livres': {
    admin: 'full_access',
    instructor: 'no_access',
    student: 'no_access'
  },
  '/aulas/livres/*': {
    admin: 'full_access',
    instructor: 'no_access',
    student: 'no_access'
  },
  // Presença: Admins veem tudo; Instrutores veem dados das aulas que ministram e das turmas que ministram
  '/presenca': {
    admin: 'full_access',
    instructor: 'teaching_only', // Apenas aulas/turmas que ministra
    student: 'no_access'
  },
  '/presenca/*': {
    admin: 'full_access',
    instructor: 'teaching_only',
    student: 'no_access'
  },
  // Financeiro: Apenas admins têm acesso
  '/financeiro': {
    admin: 'full_access',
    instructor: 'no_access',
    student: 'no_access'
  },
  '/financeiro/*': {
    admin: 'full_access',
    instructor: 'no_access',
    student: 'no_access'
  }
};

/**
 * Middleware otimizado que combina autenticação e detecção de tenant
 */
export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;

  // EARLY RETURN 1: Pular middleware para rotas desnecessárias
  if (shouldSkipMiddleware(path)) {
    return NextResponse.next();
  }

  // EARLY RETURN 2: Verificar se precisa atualizar sessão Supabase
  if (!shouldUpdateSupabaseSession(request)) {
    // Para rotas que não precisam de auth, apenas adicionar tenant slug e headers de segurança
    const response = NextResponse.next({
      request: { headers: request.headers }
    });

    const slug = extractTenantSlugOptimized(request);
    if (slug) {
      response.headers.set(TENANT_HEADERS.SLUG, slug);
    }

    addSecurityHeaders(response);
    return response;
  }

  // EARLY RETURN 3: Validar configuração do Supabase
  if (!isSupabaseConfigured()) {
    console.error('Configuração do Supabase não está disponível no middleware:', {
      path,
      nodeEnv: process.env.NODE_ENV,
    });

    if (path.startsWith('/api/')) {
      return NextResponse.json(
        { error: 'Configuração do sistema indisponível' },
        { status: 500 }
      );
    }

    return NextResponse.next();
  }

  // Extrair slug do tenant uma única vez
  const slug = extractTenantSlugOptimized(request);

  // Criar resposta base com slug
  let response = NextResponse.next({
    request: { headers: request.headers }
  });

  if (slug) {
    response.headers.set(TENANT_HEADERS.SLUG, slug);
  }

  // Criar cliente Supabase e obter usuário
  let user = null;
  let supabase = null;

  try {
    const { url: supabaseUrl, anonKey: supabaseAnonKey } = getSupabaseConfig();

    supabase = createServerClient(supabaseUrl, supabaseAnonKey, {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet: { name: string; value: string; options: CookieOptions }[]) {
          try {
            cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value));

            response = NextResponse.next({
              request: { headers: request.headers }
            });

            if (slug) {
              response.headers.set(TENANT_HEADERS.SLUG, slug);
            }

            cookiesToSet.forEach(({ name, value, options }) =>
              response.cookies.set(name, value, options)
            );
          } catch (error) {
            console.error('Erro ao definir cookies no middleware:', error);
          }
        },
      },
    });

    const { data: { user: authUser } } = await supabase.auth.getUser();
    user = authUser;
  } catch (error) {
    console.error('Erro ao criar cliente Supabase no middleware:', error);

    if (path.startsWith('/api/')) {
      return NextResponse.json(
        { error: 'Erro de configuração do sistema' },
        { status: 500 }
      );
    }

    addSecurityHeaders(response);
    return response;
  }

  // EARLY RETURN 4: Verificar modo de recepção
  const receptionModeResult = handleReceptionMode(request, path, user);
  if (receptionModeResult) {
    return receptionModeResult;
  }

  // EARLY RETURN 5: Lógica de autenticação
  const authResult = handleAuthenticationLogic(request, user, path);
  if (authResult) {
    return authResult;
  }

  // EARLY RETURN 6: Verificar permissões baseadas em roles (apenas se usuário autenticado)
  if (user && supabase) {
    const permissionResult = await handleRoleBasedPermissions(user, path, supabase, request);
    if (permissionResult) {
      return permissionResult;
    }
  }

  // Adicionar cabeçalhos de segurança e retornar
  addSecurityHeaders(response);
  return response;
}

/**
 * Verifica se o modo de recepção está ativo e redireciona para /checkin se necessário
 */
function handleReceptionMode(request: NextRequest, path: string, user: any): NextResponse | null {
  // EARLY RETURN: Obter cookie do modo de recepção
  const receptionModeCookie = request.cookies.get('apexsaas_reception_mode');

  // EARLY RETURN: Verificar rota /checkin primeiro (mais comum)
  const isCheckinRoute = path === '/checkin' || path.startsWith('/checkin/');
  if (isCheckinRoute) {
    const redirectPath = user?.app_metadata?.role === 'admin' ? '/dashboard' : '/home';

    // EARLY RETURN: Sem cookie = redirecionar
    if (!receptionModeCookie) {
      return NextResponse.redirect(new URL(redirectPath, request.url));
    }

    try {
      const receptionState = JSON.parse(receptionModeCookie.value);
      // EARLY RETURN: Modo inativo = redirecionar
      if (!receptionState?.isActive) {
        return NextResponse.redirect(new URL(redirectPath, request.url));
      }
    } catch (cookieError) {
      console.error('Erro ao analisar cookie do modo de recepção:', cookieError);
      return NextResponse.redirect(new URL(redirectPath, request.url));
    }

    // Rota /checkin com modo ativo = permitir
    return null;
  }

  // EARLY RETURN: Sem cookie = modo não ativo
  if (!receptionModeCookie) {
    return null;
  }

  let receptionState;
  try {
    receptionState = JSON.parse(receptionModeCookie.value);
  } catch (error) {
    console.error('Erro ao verificar modo de recepção:', error);
    return null;
  }

  // EARLY RETURN: Modo não ativo
  if (!receptionState.isActive) {
    return null;
  }

  // EARLY RETURN: Verificar rotas permitidas (otimizado)
  if (path.startsWith('/api') ||
      path.startsWith('/auth') ||
      path.startsWith('/_next') ||
      path === '/favicon.ico') {
    return null;
  }

  // Modo ativo e rota não permitida = redirecionar para /checkin
  console.log(`Middleware: Modo de recepção ativo - redirecionando ${path} para /checkin`);
  return NextResponse.redirect(new URL('/checkin', request.url));
}

/**
 * Verifica permissões baseadas em roles para rotas específicas
 */
async function handleRoleBasedPermissions(user: any, path: string, supabase: any, request: NextRequest): Promise<NextResponse | null> {
  // EARLY RETURN: Verificar se a rota requer verificação de role
  const routePermission = getRoutePermission(path);
  if (!routePermission) {
    return null; // Rota não requer verificação especial
  }

  // EARLY RETURN: Obter role do usuário
  const userRole = await getUserRole(user, supabase);
  if (!userRole) {
    console.error(`Middleware: Não foi possível determinar a role do usuário ${user.id}`);
    return NextResponse.redirect(new URL('/home?erro=acesso-negado', request.url));
  }

  // EARLY RETURN: Verificar permissão baseada na role
  const accessLevel = routePermission[userRole as keyof typeof routePermission];
  if (accessLevel === 'no_access') {
    console.log(`Middleware: Usuário ${user.id} (role: ${userRole}) sem acesso à rota ${path}. Redirecionando para /home.`);
    return NextResponse.redirect(new URL('/home?erro=acesso-negado', request.url));
  }

  // Para acesso restrito (own_only, teaching_only), a verificação específica será feita nas páginas
  // O middleware apenas bloqueia acessos completamente negados
  console.log(`Middleware: Usuário ${user.id} (role: ${userRole}) tem acesso à rota ${path} (nível: ${accessLevel}).`);
  return null;
}

/**
 * Obtém a configuração de permissão para uma rota (otimizado)
 */
function getRoutePermission(path: string): any {
  // EARLY RETURN: Verificar correspondência exata primeiro (mais rápido)
  const exactMatch = ROLE_BASED_ROUTES[path as keyof typeof ROLE_BASED_ROUTES];
  if (exactMatch) {
    return exactMatch;
  }

  // EARLY RETURN: Verificar padrões específicos mais comuns primeiro
  if (path.startsWith('/turmas')) {
    return ROLE_BASED_ROUTES['/turmas/*'];
  }

  if (path.startsWith('/aulas/livres')) {
    return ROLE_BASED_ROUTES['/aulas/livres/*'];
  }

  if (path.startsWith('/presenca')) {
    return ROLE_BASED_ROUTES['/presenca/*'];
  }

  if (path.startsWith('/financeiro')) {
    return ROLE_BASED_ROUTES['/financeiro/*'];
  }

  return null;
}

/**
 * Obtém a role do usuário, tentando primeiro do JWT, depois do cache e por último do banco
 */
async function getUserRole(user: any, supabase: any): Promise<string | null> {
  const userId = user.id;

  // EARLY RETURN 1: Tentar obter role do JWT primeiro (mais rápido)
  const jwtRole = user.app_metadata?.role;
  if (jwtRole) {
    // Atualizar cache com a role do JWT para futuras consultas
    userRoleCache.set(userId, jwtRole);
    return jwtRole;
  }

  // EARLY RETURN 2: Verificar cache antes de consultar o banco
  const cachedRole = userRoleCache.get(userId);
  if (cachedRole) {
    return cachedRole;
  }

  // EARLY RETURN 3: Fallback - consultar o banco de dados
  try {
    const { data, error } = await supabase
      .from('users')
      .select('role')
      .eq('user_id', userId)
      .single();

    // EARLY RETURN: Erro na consulta
    if (error || !data) {
      console.error('Erro ao buscar role do usuário no banco:', error);
      return null;
    }

    // Armazenar no cache e retornar
    const role = data.role;
    userRoleCache.set(userId, role);
    return role;
  } catch (error) {
    console.error('Erro ao obter role do usuário:', error);
    return null;
  }
}

/**
 * Extrai slug do tenant de forma otimizada
 */
function extractTenantSlugOptimized(request: NextRequest): string | null {
  const context = buildExtractionContextFromRequest(request);

  // EARLY RETURN: Verificar cache primeiro
  const cacheKey = generateCacheKey(context);
  const cachedSlug = tenantCache.get(cacheKey);
  if (cachedSlug) return cachedSlug;

  // EARLY RETURN: Usar utilitários otimizados para extração
  const slug = TenantUtils.extractTenantSlug(context);
  if (!slug || !TenantUtils.isValidSlug(slug)) {
    return null;
  }

  // Armazenar no cache e retornar
  tenantCache.set(cacheKey, slug);
  return slug;
}

/**
 * Constrói contexto de extração a partir da request (otimizado)
 */
function buildExtractionContextFromRequest(request: NextRequest): TenantExtractionContext {
  const url = request.nextUrl;
  const hostname = request.headers.get('host') || '';

  // Extrair apenas headers relevantes que existem
  const headers: Record<string, string> = {};
  const slugHeader = request.headers.get(TENANT_HEADERS.SLUG);
  if (slugHeader) headers[TENANT_HEADERS.SLUG] = slugHeader;

  // Extrair apenas cookies relevantes que existem
  const cookies: Record<string, string> = {};
  const hostCookie = request.cookies.get('host')?.value;
  const tenantCookie = request.cookies.get('x-tenant-slug')?.value;

  if (hostCookie) cookies.host = hostCookie;
  if (tenantCookie) cookies['x-tenant-slug'] = tenantCookie;

  return {
    hostname,
    pathname: url.pathname,
    headers,
    cookies
  };
}

/**
 * Gera chave de cache otimizada
 */
function generateCacheKey(context: TenantExtractionContext): string {
  // Usar apenas os componentes mais relevantes para a chave
  return [
    context.hostname || '',
    context.pathname?.split('/')[1] || '',
    context.headers?.[TENANT_HEADERS.SLUG] || '',
    context.cookies?.host || ''
  ].join('|');
}

/**
 * Determina se deve pular o middleware para esta rota (otimizado)
 */
function shouldSkipMiddleware(path: string): boolean {
  // EARLY RETURN: Rotas internas do Next.js (mais comum)
  if (path.startsWith('/_next/')) return true;

  // EARLY RETURN: Arquivos estáticos (verificação otimizada)
  if (path.startsWith('/static/')) return true;

  // EARLY RETURN: Assets com extensão (exceto APIs)
  if (path.includes('.') && !path.startsWith('/api/')) return true;

  return false;
}

/**
 * Determina se deve atualizar a sessão do Supabase (otimizado)
 */
function shouldUpdateSupabaseSession(request: NextRequest): boolean {
  const path = request.nextUrl.pathname;

  // EARLY RETURN: Assets estáticos
  if (path.includes('.') || path.startsWith('/_next/')) {
    return false;
  }

  // EARLY RETURN: Rotas que devem ser ignoradas
  if (navigationConfig.routes.shouldBypass(path)) {
    return false;
  }

  // EARLY RETURN: APIs públicas que não precisam de auth
  if (path.startsWith('/api/') && !requiresAuth(path)) {
    return false;
  }

  return true;
}

/**
 * Determina se uma rota de API requer autenticação (otimizado)
 */
function requiresAuth(apiPath: string): boolean {
  // EARLY RETURN: Verificar rotas públicas mais comuns primeiro
  if (apiPath.startsWith('/api/auth/')) return false;
  if (apiPath.startsWith('/api/tenant/')) return false;
  if (apiPath.startsWith('/api/health')) return false;
  if (apiPath.startsWith('/api/check-favicon')) return false;
  if (apiPath.startsWith('/api/favicon')) return false;

  return true;
}

/**
 * Lida com a lógica de autenticação e redirecionamento (otimizado)
 */
function handleAuthenticationLogic(request: NextRequest, user: any, path: string): NextResponse | null {
  // EARLY RETURN: Rotas que devem ser ignoradas
  if (navigationConfig.routes.shouldBypass(path)) {
    return null;
  }

  // EARLY RETURN: Usuário autenticado
  if (user) {
    // EARLY RETURN: Rota de auth (mas não API) = redirecionar para home
    if (navigationConfig.routes.isAuth(path) && !path.startsWith('/api/')) {
      return NextResponse.redirect(new URL('/home', request.url));
    }
    return null;
  }

  // Usuário NÃO autenticado
  const isPublicRoute = navigationConfig.routes.isPublic(path);
  const isAuthRoute = navigationConfig.routes.isAuth(path);

  // EARLY RETURN: Rota pública ou de auth ou API de auth = permitir
  if (isPublicRoute || isAuthRoute || path.startsWith('/api/auth/')) {
    return null;
  }

  // Rota protegida sem autenticação = redirecionar para login
  const loginUrl = new URL('/login', request.url);
  loginUrl.searchParams.set('redirectedFrom', path);
  return NextResponse.redirect(loginUrl);
}

/**
 * Adiciona cabeçalhos de segurança
 */
function addSecurityHeaders(response: NextResponse): void {
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
}

/**
 * Utilitários para gerenciar o cache de roles (útil para debug e manutenção)
 */
export const userRoleCacheUtils = {
  /**
   * Limpa todo o cache de roles
   */
  clearCache(): void {
    userRoleCache.clear();
    console.log('Middleware: Cache de roles de usuário limpo');
  },

  /**
   * Remove um usuário específico do cache
   */
  removeUser(userId: string): void {
    const removed = userRoleCache.remove(userId);
    if (removed) {
      console.log(`Middleware: Usuário ${userId} removido do cache de roles`);
    }
  },

  /**
   * Obtém estatísticas do cache
   */
  getCacheStats() {
    return userRoleCache.getStats();
  },

  /**
   * Força limpeza de entradas expiradas
   */
  cleanup(): void {
    userRoleCache.cleanup();
    console.log('Middleware: Limpeza manual do cache de roles executada');
  }
};

/**
 * Configuração do matcher otimizada
 */
export const config = {
  matcher: [
    /*
     * Matcher que inclui todas as rotas relevantes exceto assets estáticos
     * Compatível com Next.js 15 - sem grupos de captura
     */
    '/',
    '/(api|auth|dashboard|alunos|instrutores|aulas|perfil|tenant|home|turmas|presenca|agenda|financeiro)/:path*'
  ],
};