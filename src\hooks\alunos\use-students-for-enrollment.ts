'use client';

import { useQuery } from '@tanstack/react-query';
import { useState, useCallback, useMemo, useEffect, useRef } from 'react';
import { getStudentsForEnrollment, getClassGroupCapacityInfo } from '@/app/(dashboard)/turmas/[groupId]/alunos/actions/student-enrollment-actions';
import type { StudentForEnrollment, StudentsForEnrollmentResult } from '@/app/(dashboard)/turmas/[groupId]/alunos/actions/student-enrollment-actions';
import { CACHE_KEYS } from '@/constants/cache-keys';

export interface EnrollmentFilters {
  search: string;
  belt_color?: string;
  status?: 'active' | 'inactive';
  branch_id?: string;
  has_payment_pending?: boolean;
}

export interface EnrollmentCapacityInfo {
  current_enrollment_count: number;
  max_capacity?: number;
  available_spots?: number;
  is_full: boolean;
}

export function useStudentsForEnrollment(
  classGroupId: string,
  initialFilters: Partial<EnrollmentFilters> = {}
) {
  const [filters, setFilters] = useState<EnrollmentFilters>({
    search: '',
    ...initialFilters
  });
  
  const [currentPage, setCurrentPage] = useState(1);
  const [allStudents, setAllStudents] = useState<StudentForEnrollment[]>([]);
  const previousFiltersRef = useRef<string>('');
  const pageSize = 20;

  // Memoizar os filtros para evitar re-renders desnecessários
  const memoizedFilters = useMemo(() => ({
    search: filters.search?.trim() || undefined,
    belt_color: filters.belt_color,
    status: filters.status,
    branch_id: filters.branch_id,
    has_payment_pending: filters.has_payment_pending
  }), [filters.search, filters.belt_color, filters.status, filters.branch_id, filters.has_payment_pending]);

  // String dos filtros para detectar mudanças
  const filtersKey = useMemo(() => {
    return JSON.stringify(memoizedFilters);
  }, [memoizedFilters]);

  // Detectar se os filtros mudaram e resetar página/dados se necessário
  useEffect(() => {
    if (previousFiltersRef.current !== '' && previousFiltersRef.current !== filtersKey) {
      // Filtros mudaram, resetar
      setCurrentPage(1);
      setAllStudents([]);
    }
    previousFiltersRef.current = filtersKey;
  }, [filtersKey]);

  // Query para buscar estudantes disponíveis usando server action
  const {
    data: studentsData,
    isLoading: isLoadingStudents,
    error: studentsError,
    refetch: refetchStudents,
    isFetching: isFetchingStudents
  } = useQuery({
    queryKey: CACHE_KEYS.ENROLLMENT.AVAILABLE_STUDENTS(classGroupId, { ...memoizedFilters, page: currentPage }),
    queryFn: async () => {
      if (!classGroupId || typeof classGroupId !== 'string') {
        throw new Error(`ID da turma inválido: ${classGroupId}`);
      }

      const result = await getStudentsForEnrollment({
        classGroupId,
        filters: memoizedFilters,
        pagination: {
          page: currentPage,
          pageSize
        }
      });

      if (!result.success) {
        const errorMessage = result.errors && typeof result.errors === 'object' && '_form' in result.errors 
          ? result.errors._form as string
          : 'Erro ao buscar estudantes';
        throw new Error(errorMessage);
      }

      return result.data;
    },
    enabled: !!classGroupId && typeof classGroupId === 'string',
    staleTime: 5 * 60 * 1000, // 5 minutos - aumentado para reduzir refetch
    gcTime: 30 * 60 * 1000, // 30 minutos em cache
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false, // Desabilitado para evitar refetch desnecessário
    retry: 2
  });

  // Query para informações de capacidade da turma
  const {
    data: capacityData,
    isLoading: isLoadingCapacity,
    error: capacityError
  } = useQuery({
    queryKey: CACHE_KEYS.ENROLLMENT.CAPACITY_CHECK(classGroupId),
    queryFn: async () => {
      if (!classGroupId || typeof classGroupId !== 'string') {
        throw new Error(`ID da turma inválido: ${classGroupId}`);
      }

      const result = await getClassGroupCapacityInfo(classGroupId);

      if (!result.success) {
        const errorMessage = result.errors && typeof result.errors === 'object' && '_form' in result.errors 
          ? result.errors._form as string
          : 'Erro ao buscar informações de capacidade';
        throw new Error(errorMessage);
      }

      return result.data;
    },
    enabled: !!classGroupId && typeof classGroupId === 'string',
    staleTime: 10 * 60 * 1000, // 10 minutos - capacidade muda menos frequentemente
    gcTime: 60 * 60 * 1000, // 1 hora em cache
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: 2
  });

  // Efeito para acumular estudantes quando dados chegam
  useEffect(() => {
    if (studentsData?.students) {
      if (currentPage === 1) {
        // Se é a primeira página, substitua a lista
        setAllStudents(studentsData.students);
      } else {
        // Se é uma página subsequente, adicione à lista existente
        setAllStudents(prev => {
          const existingIds = new Set(prev.map(s => s.id));
          const newStudents = studentsData.students.filter(s => !existingIds.has(s.id));
          return [...prev, ...newStudents];
        });
      }
    }
  }, [studentsData?.students, currentPage]);

  // Funções de controle
  const updateFilters = useCallback((newFilters: Partial<EnrollmentFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({ search: '' });
  }, []);

  const nextPage = useCallback(() => {
    if (studentsData?.hasNextPage && !isFetchingStudents) {
      setCurrentPage(prev => prev + 1);
    }
  }, [studentsData?.hasNextPage, isFetchingStudents]);

  const prevPage = useCallback(() => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1);
    }
  }, [currentPage]);

  // Reset function para limpar dados acumulados - usar apenas quando necessário
  const resetData = useCallback(() => {
    setCurrentPage(1);
    setAllStudents([]);
    previousFiltersRef.current = '';
  }, []);

  // Estudantes disponíveis - agora usando a lista acumulada
  const availableStudents = useMemo(() => {
    return allStudents;
  }, [allStudents]);

  // Estatísticas
  const stats = useMemo(() => {
    return studentsData?.stats || {
      total: 0,
      available: 0,
      alreadyEnrolled: 0
    };
  }, [studentsData?.stats]);

  // Informações de capacidade
  const capacityInfo = useMemo((): EnrollmentCapacityInfo => {
    if (!capacityData) {
      return {
        current_enrollment_count: 0,
        max_capacity: undefined,
        available_spots: undefined,
        is_full: false
      };
    }

    return {
      current_enrollment_count: capacityData.current_enrollment_count,
      max_capacity: capacityData.max_capacity || undefined,
      available_spots: capacityData.available_spots || undefined,
      is_full: capacityData.is_full
    };
  }, [capacityData]);

  // Verificar se há filtros ativos
  const hasActiveFilters = useMemo(() => {
    return !!(filters.search?.trim() || filters.belt_color || filters.status || filters.branch_id || filters.has_payment_pending);
  }, [filters]);

  // Estados de loading combinados - incluir isFetching para mostrar loading durante busca
  const isLoading = (isLoadingStudents && allStudents.length === 0) || isLoadingCapacity;
  const isFetching = isFetchingStudents;
  const error = studentsError || capacityError;

  return {
    // Estados dos dados
    students: allStudents, // Retornar a lista acumulada
    isLoading,
    isFetching, // Novo estado para indicar quando está buscando
    error,
    
    // Estatísticas
    totalStudents: studentsData?.totalCount || 0,
    totalFiltered: studentsData?.totalCount || 0,
    currentPage,
    totalPages: Math.ceil((studentsData?.totalCount || 0) / pageSize),
    
    // Funções de controle
    filters,
    setFilters: updateFilters,
    resetFilters: clearFilters,
    refetch: refetchStudents,
    resetData, // Nova função para reset completo
    
    // Paginação
    setCurrentPage,
    nextPage,
    prevPage,
    
    // Meta informações
    hasMore: studentsData?.hasNextPage || false,
    isEmpty: availableStudents.length === 0,
    isFiltered: hasActiveFilters,
    
    // Dados
    availableStudents,
    stats,
    capacityInfo
  };
} 