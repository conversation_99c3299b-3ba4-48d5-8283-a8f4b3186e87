// Tipos para estatísticas de presença e aulas

export interface TrendData {
  value: number;
  isPositive: boolean;
  data: number[];
}

export interface AttendanceStats {
  totalClasses: number;
  ongoingClasses: number;
  upcomingClasses: number;
  completedClasses: number;
  totalAttendances: number;
  uniqueStudents: number;
}

export interface AttendanceStatsWithTrends extends AttendanceStats {
  trends: {
    total: TrendData;
    ongoing: TrendData;
    upcoming: TrendData;
    completed: TrendData;
  };
  dailyData: DailyStats[];
}

export interface DailyStats {
  date: string;
  total: number;
  ongoing: number;
  upcoming: number;
  completed: number;
}

export interface AttendanceStatsResponse {
  success: boolean;
  data?: AttendanceStatsWithTrends;
  errors?: any;
} 