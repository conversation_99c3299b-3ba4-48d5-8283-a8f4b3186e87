'use client'

import { PlanCard } from './PlanCard'
import { AddNewPlanCard } from './AddNewPlanCard'
import { usePlanAdapter } from '@/hooks/planos/use-plan-adapter'

interface PlanListProps {
  plans: any[]
}

export function PlanList({ plans: dbPlans }: PlanListProps) {
  const plans = usePlanAdapter(dbPlans)

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {plans.map(plan => (
        <PlanCard key={plan.id} plan={plan} />
      ))}
      <AddNewPlanCard />
    </div>
  )
} 