'use client';

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { UserIcon, ShieldCheckIcon, AlertTriangleIcon } from 'lucide-react';
import { MinorAccountDisplayInfo, GUARDIAN_RELATIONSHIPS, GuardianRelationship } from '@/src/types/guardian';
import { cn } from '@/lib/utils';

interface GuardianInfoCardProps {
  displayInfo: MinorAccountDisplayInfo;
  canEdit?: boolean;
  onEditGuardian?: () => void;
  className?: string;
  isAdmin?: boolean;
}

export function GuardianInfoCard({ 
  displayInfo, 
  canEdit = false, 
  onEditGuardian,
  className = '',
  isAdmin = false
}: GuardianInfoCardProps) {
  if (!displayInfo.isMinor) {
    return null;
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <ShieldCheckIcon className="h-5 w-5 text-blue-600" />
            Conta de Menor de Idade
          </CardTitle>
          
          {isAdmin && (
            displayInfo.hasGuardian ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                Responsável Configurado
              </Badge>
            ) : (
              <Badge variant="destructive">
                Responsável Necessário
              </Badge>
            )
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {displayInfo.hasGuardian ? (
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <UserIcon className="h-4 w-4" />
              <span>Esta conta é administrada por um responsável legal</span>
            </div>

            {displayInfo.guardianName && (
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="space-y-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium text-gray-900">
                        {displayInfo.guardianName}
                      </p>
                      {displayInfo.guardianRelationship && (
                        <p className="text-sm text-gray-600">
                          {GUARDIAN_RELATIONSHIPS[displayInfo.guardianRelationship as GuardianRelationship] || 
                           displayInfo.guardianRelationship}
                        </p>
                      )}
                    </div>

                    {canEdit && onEditGuardian && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={onEditGuardian}
                      >
                        Editar
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            )}

            <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded border-l-4 border-blue-400">
              <div className="flex items-start gap-2">
                <ShieldCheckIcon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-blue-800">Conta Administrada</p>
                  <p className="text-blue-700">
                    Algumas ações podem requerer aprovação do responsável legal. 
                    O responsável pode gerenciar esta conta até que o usuário complete 18 anos.
                  </p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            <Alert variant="destructive">
              <AlertTriangleIcon className="h-4 w-4" />
              <AlertDescription>
                <strong>Responsável Legal Necessário</strong>
                <br />
                Por ser menor de idade, esta conta precisa ter um responsável legal configurado.
              </AlertDescription>
            </Alert>

            {canEdit && onEditGuardian && (
              <Button 
                onClick={onEditGuardian}
                className="w-full"
                variant="default"
              >
                <UserIcon className="h-4 w-4 mr-2" />
                Configurar Responsável
              </Button>
            )}

            <div className="text-xs text-gray-500 space-y-1">
              <p><strong>Por que é necessário?</strong></p>
              <p>
                Para usuários menores de 18 anos, é obrigatório ter um responsável legal 
                cadastrado para administrar a conta e autorizar atividades.
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Componente simplificado para exibir apenas o status de menor
 */
export function MinorStatusBadge({ displayInfo }: { displayInfo: MinorAccountDisplayInfo }) {
  if (!displayInfo.isMinor) {
    return null;
  }

  return (
    <div className="flex items-center gap-2">
      <Badge 
        variant={displayInfo.hasGuardian ? "default" : "destructive"}
        className={displayInfo.hasGuardian ? "bg-blue-100 text-blue-800" : ""}
      >
        <ShieldCheckIcon className="h-3 w-3 mr-1" />
        Menor de Idade
      </Badge>
      
      {displayInfo.hasGuardian && displayInfo.guardianName && (
        <span className="text-sm text-muted-foreground">
          • Responsável: {displayInfo.guardianName}
        </span>
      )}
    </div>
  );
}

/**
 * Componente compacto para exibir informações do responsável inline
 */
export function InlineGuardianInfo({ 
  displayInfo,
  className = '',
  isAdmin = false
}: { 
  displayInfo: MinorAccountDisplayInfo;
  className?: string;
  isAdmin?: boolean;
}) {
  if (!displayInfo.isMinor) {
    return null;
  }

  const relationshipText = displayInfo.guardianRelationship 
    ? GUARDIAN_RELATIONSHIPS[displayInfo.guardianRelationship as GuardianRelationship] || displayInfo.guardianRelationship
    : '';

  return (
    <div className={cn("flex items-center gap-3 mt-2", className)}>
      <div className="flex items-center gap-2">
        <ShieldCheckIcon className="h-4 w-4 text-amber-600" />
        <span className="text-sm text-slate-600 dark:text-slate-400">
          {displayInfo.hasGuardian ? (
            <>
              Conta administrada por <strong>{displayInfo.guardianName}</strong>
              {relationshipText && (
                <span className="text-slate-500">
                  {' '}({relationshipText})
                </span>
              )}
            </>
          ) : (
            <span className="text-amber-700 dark:text-amber-400 font-medium">
              Menor de idade - responsável necessário
            </span>
          )}
        </span>
      </div>
      
      {isAdmin && (
        <>
          {displayInfo.hasGuardian && (
            <Badge 
              variant="secondary" 
              className="text-xs bg-emerald-100 text-emerald-800 dark:bg-emerald-900/40 dark:text-emerald-300"
            >
              Responsável Configurado
            </Badge>
          )}
          {!displayInfo.hasGuardian && (
            <a
              href="/academia/configuracoes/responsaveis"
              className="focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
              tabIndex={0}
              aria-label="Configurar responsável legal"
            >
              <Badge 
                variant="destructive" 
                className="text-xs cursor-pointer hover:underline"
              >
                Configuração Pendente
              </Badge>
            </a>
          )}
        </>
      )}
    </div>
  );
} 