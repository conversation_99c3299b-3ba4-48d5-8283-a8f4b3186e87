'use client'

import { useState } from 'react'
import { updateUserProfile, ProfileUpdateData } from '../actions'
import { toast } from 'sonner'
import { useQueryClient } from '@tanstack/react-query'
import { CACHE_KEYS } from '@/constants/cache-keys'

export function useProfileUpdate() {
  const [isUpdating, setIsUpdating] = useState(false)
  const queryClient = useQueryClient()

  const handleUpdateProfile = async (userId: string, data: ProfileUpdateData) => {
    try {
      setIsUpdating(true)
      console.log('[UPDATE] Enviando atualização para o servidor:', data)
      
      // Chamar o servidor para persistir as alterações
      const result = await updateUserProfile(userId, data)

      if (result.success) {
        toast.success('Perfil atualizado com sucesso')
        
        // Atualizar o cache diretamente com os novos dados em vez de invalidá-lo
        // Isso evita um refetch desnecessário que poderia causar refresh da página
        
        // Obter dados atuais do cache
        const currentUserData = queryClient.getQueryData([CACHE_KEYS.USER_PROFILE[0], userId])
        const currentViewedData = queryClient.getQueryData(CACHE_KEYS.VIEWED_USER_PROFILE(userId))

        // Se temos dados em cache, atualizá-los em vez de invalidar
        if (currentUserData) {
          queryClient.setQueryData([CACHE_KEYS.USER_PROFILE[0], userId], (oldData: any) => ({
            ...oldData,
            ...data
          }))
        } else {
          // Se não temos em cache, então refetch
          queryClient.refetchQueries({ 
            queryKey: [CACHE_KEYS.USER_PROFILE[0], userId],
            type: 'active'  // Apenas consultas ativas, não todas
          })
        }

        if (currentViewedData) {
          queryClient.setQueryData(CACHE_KEYS.VIEWED_USER_PROFILE(userId), (oldData: any) => ({
            ...oldData,
            ...data
          }))
        } else {
          queryClient.refetchQueries({
            queryKey: CACHE_KEYS.VIEWED_USER_PROFILE(userId),
            type: 'active'  // Apenas consultas ativas
          })
        }
        
        // Disparar evento específico com os dados atualizados em vez de um evento genérico
        if (typeof window !== 'undefined') {
          const now = Date.now()
          window.dispatchEvent(new CustomEvent('profile:updated', { 
            detail: { 
              userId, 
              timestamp: now,
              fields: data
            } 
          }))
        }
        
        return true
      } else {
        if (result.fieldErrors) {
          // Erros de validação por campo
          result.fieldErrors.forEach(error => {
            toast.error(`${error.path}: ${error.message}`)
          })
        } else {
          // Erro geral
          toast.error(result.error || 'Erro ao atualizar perfil')
        }
        return false
      }
    } catch (error) {
      console.error('Erro ao atualizar perfil:', error)
      toast.error('Ocorreu um erro ao atualizar o perfil')
      return false
    } finally {
      setIsUpdating(false)
    }
  }

  return {
    isUpdating,
    updateProfile: handleUpdateProfile
  }
} 