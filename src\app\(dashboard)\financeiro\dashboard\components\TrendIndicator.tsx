"use client";

/**
 * Componente de Indicador de Tendência - Fase 2
 * Exibe indicadores visuais de tendência e performance
 */

import { TrendingUp, TrendingDown, Minus, ArrowUp, ArrowDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { TrendDirection } from '../types/dashboard-types';

// ============================================================================
// TIPOS
// ============================================================================

interface TrendIndicatorProps {
  trend: TrendDirection;
  growth: number;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  showText?: boolean;
  className?: string;
}

interface PerformanceIndicatorProps {
  value: number;
  threshold?: {
    good: number;
    warning: number;
  };
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

// ============================================================================
// UTILITÁRIOS
// ============================================================================

const getTrendColor = (trend: TrendDirection, growth: number) => {
  if (trend === 'up') {
    return growth > 10 ? 'text-green-600 dark:text-green-400' : 'text-green-500 dark:text-green-400';
  }
  if (trend === 'down') {
    return growth < -10 ? 'text-red-600 dark:text-red-400' : 'text-red-500 dark:text-red-400';
  }
  return 'text-gray-500 dark:text-gray-400';
};

const getTrendBgColor = (trend: TrendDirection, growth: number) => {
  if (trend === 'up') {
    return growth > 10 ? 'bg-green-100 dark:bg-green-900/20' : 'bg-green-50 dark:bg-green-900/10';
  }
  if (trend === 'down') {
    return growth < -10 ? 'bg-red-100 dark:bg-red-900/20' : 'bg-red-50 dark:bg-red-900/10';
  }
  return 'bg-gray-100 dark:bg-gray-800';
};

const getSizeClasses = (size: 'sm' | 'md' | 'lg') => {
  switch (size) {
    case 'sm':
      return {
        container: 'h-6 w-6',
        icon: 'h-3 w-3',
        text: 'text-xs'
      };
    case 'lg':
      return {
        container: 'h-10 w-10',
        icon: 'h-5 w-5',
        text: 'text-base'
      };
    default:
      return {
        container: 'h-8 w-8',
        icon: 'h-4 w-4',
        text: 'text-sm'
      };
  }
};

// ============================================================================
// COMPONENTE PRINCIPAL DE TENDÊNCIA
// ============================================================================

export const TrendIndicator: React.FC<TrendIndicatorProps> = ({
  trend,
  growth,
  size = 'md',
  showIcon = true,
  showText = false,
  className
}) => {
  const sizeClasses = getSizeClasses(size);
  const trendColor = getTrendColor(trend, growth);
  const bgColor = getTrendBgColor(trend, growth);

  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className={sizeClasses.icon} />;
      case 'down':
        return <TrendingDown className={sizeClasses.icon} />;
      default:
        return <Minus className={sizeClasses.icon} />;
    }
  };

  const formatGrowth = (value: number) => {
    const formatted = Math.abs(value).toFixed(1);
    return `${value > 0 ? '+' : value < 0 ? '-' : ''}${formatted}%`;
  };

  if (showText) {
    return (
      <div className={cn(
        "flex items-center gap-1 font-medium",
        trendColor,
        sizeClasses.text,
        className
      )}>
        {showIcon && getTrendIcon()}
        <span>{formatGrowth(growth)}</span>
      </div>
    );
  }

  return (
    <div className={cn(
      "flex items-center justify-center rounded-full",
      sizeClasses.container,
      bgColor,
      trendColor,
      className
    )}>
      {showIcon && getTrendIcon()}
    </div>
  );
};

// ============================================================================
// INDICADOR DE PERFORMANCE
// ============================================================================

export const PerformanceIndicator: React.FC<PerformanceIndicatorProps> = ({
  value,
  threshold = { good: 70, warning: 40 },
  size = 'md',
  className
}) => {
  const sizeClasses = getSizeClasses(size);

  const getPerformanceStatus = () => {
    if (value >= threshold.good) {
      return {
        color: 'text-green-600 dark:text-green-400',
        bg: 'bg-green-100 dark:bg-green-900/20',
        icon: <ArrowUp className={sizeClasses.icon} />
      };
    }
    if (value >= threshold.warning) {
      return {
        color: 'text-yellow-600 dark:text-yellow-400',
        bg: 'bg-yellow-100 dark:bg-yellow-900/20',
        icon: <Minus className={sizeClasses.icon} />
      };
    }
    return {
      color: 'text-red-600 dark:text-red-400',
      bg: 'bg-red-100 dark:bg-red-900/20',
      icon: <ArrowDown className={sizeClasses.icon} />
    };
  };

  const status = getPerformanceStatus();

  return (
    <div className={cn(
      "flex items-center justify-center rounded-full",
      sizeClasses.container,
      status.bg,
      status.color,
      className
    )}>
      {status.icon}
    </div>
  );
};

// ============================================================================
// MINI GRÁFICO DE TENDÊNCIA (SPARKLINE)
// ============================================================================

interface SparklineProps {
  data: number[];
  trend: TrendDirection;
  width?: number;
  height?: number;
  className?: string;
}

export const Sparkline: React.FC<SparklineProps> = ({
  data,
  trend,
  width = 60,
  height = 20,
  className
}) => {
  if (data.length < 2) {
    return null;
  }

  const max = Math.max(...data);
  const min = Math.min(...data);
  const range = max - min || 1;

  const points = data.map((value, index) => {
    const x = (index / (data.length - 1)) * width;
    const y = height - ((value - min) / range) * height;
    return `${x},${y}`;
  }).join(' ');

  const strokeColor = trend === 'up' 
    ? '#10B981' 
    : trend === 'down' 
    ? '#EF4444' 
    : '#6B7280';

  return (
    <svg 
      width={width} 
      height={height} 
      className={cn("overflow-visible", className)}
    >
      <polyline
        fill="none"
        stroke={strokeColor}
        strokeWidth="1.5"
        points={points}
        className="drop-shadow-sm"
      />
    </svg>
  );
};

// ============================================================================
// BADGE DE STATUS
// ============================================================================

interface StatusBadgeProps {
  status: 'excellent' | 'good' | 'warning' | 'critical';
  text: string;
  size?: 'sm' | 'md';
  className?: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  text,
  size = 'sm',
  className
}) => {
  const getStatusStyles = () => {
    switch (status) {
      case 'excellent':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'good':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-400';
    }
  };

  const sizeClass = size === 'sm' ? 'px-2 py-1 text-xs' : 'px-3 py-1 text-sm';

  return (
    <span className={cn(
      "inline-flex items-center rounded-full font-medium",
      getStatusStyles(),
      sizeClass,
      className
    )}>
      {text}
    </span>
  );
};
