'use client';

import { useUserMetadata } from './useUserMetadata';

export interface UserBranch {
  id: string;
  name?: string;
}

export function useUserBranch() {
  const { metadata, isLoading, error, refetch } = useUserMetadata();

  const branch: UserBranch | null = metadata?.branch_id 
    ? { id: metadata.branch_id } 
    : null;

  return {
    branch,
    isLoading,
    error,
    refetch,
  };
} 