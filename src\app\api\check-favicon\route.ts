import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/services/supabase/server';

// Configuração de cache por 24 horas
const CACHE_CONTROL = 'public, max-age=3600, s-maxage=86400, stale-while-revalidate=604800';

export const dynamic = 'force-dynamic';
export const runtime = 'edge';
export const revalidate = 3600; // Revalidar a cada hora

// Cache em memória para check-favicon
const CHECK_FAVICON_CACHE = new Map();
const CACHE_TTL = 3600000; // 1 hora em milissegundos

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const slug = searchParams.get('tenant');
    
    if (!slug) {
      return NextResponse.json({ error: 'Tenant slug é obrigatório' }, { 
        status: 400,
        headers: {
          'Cache-Control': 'no-store, max-age=0'
        }
      });
    }
    
    // Verificar cache em memória
    const cacheKey = `check_favicon_${slug}`;
    const cachedData = CHECK_FAVICON_CACHE.get(cacheKey);
    
    if (cachedData && (Date.now() - cachedData.timestamp < CACHE_TTL)) {
      console.log('Usando dados de favicon do cache em memória');
      return NextResponse.json(cachedData.data, {
        headers: {
          'Cache-Control': CACHE_CONTROL,
          'ETag': cachedData.etag || `"check-favicon-${slug}"`,
          'Expires': new Date(Date.now() + 3600000).toUTCString()
        }
      });
    }
    
    const supabase = await createAdminClient();
    
    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .select('id, slug, name, logo_url, favicon_url, favicon_storage_path')
      .eq('slug', slug)
      .single();
      
    if (tenantError || !tenant) {
      return NextResponse.json({ 
        error: 'Tenant não encontrado',
        details: tenantError?.message || 'Não encontrado'
      }, { 
        status: 404,
        headers: {
          'Cache-Control': 'no-store, max-age=0'
        }
      });
    }
    
    let storageInfo = null;
    
    if (tenant.favicon_storage_path) {
      let storagePath = tenant.favicon_storage_path;
      
      if (storagePath.startsWith('http')) {
        try {
          const url = new URL(storagePath);
          const pathParts = url.pathname.split('/');
          const fileName = pathParts[pathParts.length - 1];
          
          if (fileName && !fileName.includes('object') && !fileName.includes('sign')) {
            storagePath = fileName;
          }
        } catch (e) {
          console.error('Erro ao processar URL:', e);
        }
      }
      
      const { data: objData, error: objError } = await supabase.storage
        .from('logos')
        .list('', {
          search: storagePath.replace('logos/', '')
        });
        
      if (!objError && objData && objData.length > 0) {
        const fileInfo = objData.find(f => 
          f.name === storagePath.replace('logos/', '') || 
          storagePath.endsWith(f.name)
        );
        
        if (fileInfo) {
          storageInfo = {
            bucket: 'logos',
            path: fileInfo.name,
            size: fileInfo.metadata.size,
            sizeMB: Math.round(fileInfo.metadata.size / 1024 / 1024 * 100) / 100,
            mimetype: fileInfo.metadata.mimetype,
            lastModified: fileInfo.metadata.lastModified
          };
        }
      }
    }
    
    const isTooLarge = storageInfo?.size > 100 * 1024;
    
    const urlsInfo = {
      logo_url: {
        exists: !!tenant.logo_url,
        url: tenant.logo_url
      },
      favicon_url: {
        exists: !!tenant.favicon_url,
        url: tenant.favicon_url
      }
    };
    
    const responseData = {
      tenant: {
        id: tenant.id,
        slug: tenant.slug,
        name: tenant.name
      },
      faviconInfo: {
        storage: storageInfo,
        urls: urlsInfo,
        issues: {
          isTooLarge,
          needsOptimization: isTooLarge
        },
        recommendations: isTooLarge ? 
          'Favicon é muito grande (>100KB). Considere otimizar para melhor performance.' :
          'Tamanho aceitável para uso como favicon.'
      }
    };
    
    // Armazenar no cache em memória
    CHECK_FAVICON_CACHE.set(cacheKey, {
      data: responseData,
      timestamp: Date.now(),
      etag: `"check-favicon-${slug}-${Date.now()}"`
    });
    
    return NextResponse.json(responseData, {
      headers: {
        'Cache-Control': CACHE_CONTROL,
        'ETag': `"check-favicon-${slug}-${Date.now()}"`,
        'Expires': new Date(Date.now() + 3600000).toUTCString()
      }
    });
    
  } catch (error: any) {
    console.error('Erro ao verificar favicon:', error);
    return NextResponse.json({ 
      error: 'Erro ao verificar favicon',
      details: error.message
    }, { 
      status: 500,
      headers: {
        'Cache-Control': 'no-store, max-age=0'
      }
    });
  }
} 