// Configurações para validação e formatação de números telefônicos por país

interface PhoneConfig {
  maxDigits: number;
  // Pode ser expandido com outras propriedades como formatos específicos, etc.
}

type CountryPhoneConfigs = {
  [countryCode: string]: PhoneConfig;
};

// Configurações por código de país
export const phoneConfigs: CountryPhoneConfigs = {
  // Brasil
  '+55': {
    maxDigits: 11, // DDD (2 dígitos) + número celular (9 dígitos)
  },
  // Configurações para outros países podem ser adicionadas conforme necessário
};

/**
 * Verifica se o número de telefone excede o limite máximo de dígitos para o país
 * @param countryCode Código do país (ex: '+55')
 * @param phoneNumber Número de telefone sem o código de país
 * @returns true se exceder o limite, false caso contrário
 */
export const exceedsMaxDigits = (countryCode: string, phoneNumber: string): boolean => {
  // Remover formatação para contar apenas os dígitos
  const digitsOnly = phoneNumber.replace(/\D/g, '');
  const config = phoneConfigs[countryCode];
  
  if (config && config.maxDigits) {
    return digitsOnly.length > config.maxDigits;
  }
  
  // Se não tiver configuração específica, assume um limite padrão de 15 dígitos
  // (padrão ITU-T para números internacionais)
  return digitsOnly.length > 15;
};

/**
 * Retorna o número máximo de dígitos permitidos para um código de país
 * @param countryCode Código do país (ex: '+55')
 * @returns Número máximo de dígitos ou undefined se não houver limite definido
 */
export const getMaxDigits = (countryCode: string): number | undefined => {
  const config = phoneConfigs[countryCode];
  return config?.maxDigits;
}; 