'use client';

import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ChevronLeft, UserPlus, Users, UserCheck, UserX, Pause } from 'lucide-react';
import Link from 'next/link';
import { StudentEnrollmentModal } from './StudentEnrollmentModal';
import { useQueryClient } from '@tanstack/react-query';
import { CACHE_KEYS } from '@/constants/cache-keys';

interface StudentListHeaderProps {
  classGroup: {
    id: string;
    name: string;
    max_capacity?: number;
    description?: string;
  };
  stats: {
    total: number;
    active: number;
    inactive: number;
    suspended: number;
  };
}

export function StudentListHeader({ classGroup, stats }: StudentListHeaderProps) {
  const [isEnrollmentModalOpen, setIsEnrollmentModalOpen] = useState(false);
  const queryClient = useQueryClient();

  const capacityPercentage = classGroup.max_capacity 
    ? Math.round((stats.active / classGroup.max_capacity) * 100)
    : null;

  const getCapacityColor = (percentage: number | null) => {
    if (!percentage) return 'text-muted-foreground';
    if (percentage >= 90) return 'text-red-500';
    if (percentage >= 75) return 'text-yellow-500';
    return 'text-green-500';
  };

  const isCapacityFull = classGroup.max_capacity 
    ? stats.active >= classGroup.max_capacity 
    : false;

  const availableSpots = classGroup.max_capacity 
    ? Math.max(0, classGroup.max_capacity - stats.active)
    : null;

  const handleOpenEnrollmentModal = () => {
    if (isCapacityFull) {
      return; // Não abrir modal se não há vagas
    }
    setIsEnrollmentModalOpen(true);
  };

  const handleEnrollmentSuccess = () => {
    // Invalidar cache da página de alunos para atualizar a lista
    queryClient.invalidateQueries({
      queryKey: CACHE_KEYS.ENROLLMENT.CLASS_GROUP_ENROLLMENTS(classGroup.id)
    });
  };

  return (
    <>
      <div className="space-y-4 sm:space-y-6">
        {/* Breadcrumb */}
        <nav 
          className="flex items-center space-x-2 text-sm text-muted-foreground overflow-x-auto pb-1"
          aria-label="Navegação estrutural"
        >
          <Link 
            href="/turmas" 
            className="hover:text-foreground transition-colors focus:text-foreground focus:underline focus:outline-none whitespace-nowrap"
          >
            Turmas
          </Link>
          <span aria-hidden="true">/</span>
          <Link 
            href={`/turmas/${classGroup.id}`} 
            className="hover:text-foreground transition-colors focus:text-foreground focus:underline focus:outline-none whitespace-nowrap"
          >
            {classGroup.name}
          </Link>
          <span aria-hidden="true">/</span>
          <span className="text-foreground font-medium whitespace-nowrap" aria-current="page">
            Alunos
          </span>
        </nav>

        {/* Header with integrated stats */}
        <div className="flex flex-col space-y-6 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
          {/* Title and Stats */}
          <div className="space-y-4">
            <div>
              <h1 className="text-2xl font-bold text-foreground mb-2">
                Alunos - {classGroup.name}
              </h1>
              {classGroup.description && (
                <p className="text-muted-foreground">
                  {classGroup.description}
                </p>
              )}
            </div>

            {/* Integrated Statistics */}
            <div 
              className="grid grid-cols-2 gap-3 sm:flex sm:items-center sm:gap-4 sm:flex-wrap"
              role="region"
              aria-label="Estatísticas da turma"
            >
              <div className="flex items-center gap-2">
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10" aria-hidden="true">
                  <Users className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <div className="text-base sm:text-lg font-semibold text-foreground">
                    {stats.total}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Total
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/40" aria-hidden="true">
                  <UserCheck className="h-4 w-4 text-green-600 dark:text-green-300" />
                </div>
                <div>
                  <div className="text-base sm:text-lg font-semibold text-foreground">
                    {stats.active}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Ativos
                  </div>
                </div>
              </div>

              {stats.inactive > 0 && (
                <div className="flex items-center gap-2">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-800/60" aria-hidden="true">
                    <UserX className="h-4 w-4 text-gray-500 dark:text-gray-300" />
                  </div>
                  <div>
                    <div className="text-base sm:text-lg font-semibold text-foreground">
                      {stats.inactive}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Inativos
                    </div>
                  </div>
                </div>
              )}

              {stats.suspended > 0 && (
                <div className="flex items-center gap-2">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-red-100 dark:bg-red-900/40" aria-hidden="true">
                    <Pause className="h-4 w-4 text-red-600 dark:text-red-300" />
                  </div>
                  <div>
                    <div className="text-base sm:text-lg font-semibold text-foreground">
                      {stats.suspended}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Suspensos
                    </div>
                  </div>
                </div>
              )}

              {/* Capacity indicator */}
              {classGroup.max_capacity && (
                <div className="flex items-center gap-2 col-span-2 sm:col-span-1">
                  <div 
                    className="inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold text-foreground border-border bg-background"
                    aria-label={`Capacidade: ${stats.active} de ${classGroup.max_capacity} vagas ocupadas, ${capacityPercentage}% da capacidade`}
                  >
                    <span className={getCapacityColor(capacityPercentage)}>
                      {stats.active}/{classGroup.max_capacity}
                    </span>
                    {capacityPercentage !== null && (
                      <span className="ml-1 text-muted-foreground">
                        • {capacityPercentage}%
                      </span>
                    )}
                  </div>
                  {availableSpots !== null && availableSpots > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {availableSpots} vaga{availableSpots !== 1 ? 's' : ''} disponível{availableSpots !== 1 ? 'is' : ''}
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-3">
            <Button variant="outline" asChild className="w-full sm:w-auto">
              <Link 
                href={`/turmas/${classGroup.id}`}
                aria-label={`Voltar para a turma ${classGroup.name}`}
              >
                <ChevronLeft className="h-4 w-4 mr-2" aria-hidden="true" />
                Voltar
              </Link>
            </Button>
            <Button 
              onClick={handleOpenEnrollmentModal}
              className="w-full sm:w-auto"
              variant="secondary"
              disabled={isCapacityFull}
              title={isCapacityFull ? 'Turma lotada - sem vagas disponíveis' : `Matricular aluno na turma ${classGroup.name}`}
              aria-label={isCapacityFull ? 'Turma lotada - sem vagas disponíveis' : `Matricular novo aluno na turma ${classGroup.name}`}
            >
              <UserPlus className="h-4 w-4 mr-2" aria-hidden="true" />
              {isCapacityFull ? 'Turma Lotada' : 'Matricular Aluno'}
            </Button>
          </div>
        </div>
      </div>

      {/* Modal de matrícula */}
      <StudentEnrollmentModal
        isOpen={isEnrollmentModalOpen}
        onClose={() => setIsEnrollmentModalOpen(false)}
        classGroup={{
          id: classGroup.id,
          name: classGroup.name,
          max_capacity: classGroup.max_capacity
        }}
        onEnrollmentSuccess={handleEnrollmentSuccess}
      />
    </>
  );
} 