import { NextRequest, NextResponse } from 'next/server';
import { fetchStudentsFromServer } from '@/app/(dashboard)/alunos/server/student-queries';

export async function POST(request: NextRequest) {
  try {
    // Extrair os parâmetros do corpo da requisição
    const params = await request.json();
    
    // Chamar a função de busca de alunos no servidor
    const result = await fetchStudentsFromServer(params);
    
    // Retornar os resultados
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Erro na API de consulta de alunos:', error);
    
    // Retornar um erro 500 com a mensagem
    return NextResponse.json(
      { error: error.message || 'Erro ao processar a consulta de alunos' },
      { status: 500 }
    );
  }
} 