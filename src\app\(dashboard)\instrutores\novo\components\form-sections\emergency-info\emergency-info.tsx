'use client';

import { useFormContext } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { NovoInstrutorFormValues } from "../../../actions/schemas/instrutor-schema";
import { Contact, Phone, UserPlus } from "lucide-react";
import { PhoneInput } from "@/components/shared/PhoneInput";

export default function EmergencyInfoSection() {
  const { control } = useFormContext<NovoInstrutorFormValues>();

  return (
    <div className="grid grid-cols-1 gap-6">
      {/* Contato de Emergência */}
      <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <Contact className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Contato de Emergência
          </h2>
        </div>
        
        <CardContent className="p-6 space-y-6">
          {/* Nome e Telefone (lado a lado em telas maiores) */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={control}
              name="emergency_contact_name"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <div className="flex items-center mb-1">
                    <UserPlus className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-2" />
                    <FormLabel className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Nome do Contato
                    </FormLabel>
                  </div>
                  <FormControl>
                    <Input 
                      placeholder="Nome completo do contato" 
                      {...field} 
                      className="border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="emergency_contact_phone"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <div className="flex items-center mb-1">
                    <Phone className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-2" />
                    <FormLabel className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Telefone do Contato
                    </FormLabel>
                  </div>
                  <FormControl>
                    <PhoneInput 
                      field={field}
                      className="border-slate-300 dark:border-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Relação com o Instrutor (ocupa toda a largura) */}
          <FormField
            control={control}
            name="emergency_contact_relationship"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <div className="flex items-center mb-1">
                  <UserPlus className="w-5 h-5 text-slate-400 dark:text-slate-500 mr-2" />
                  <FormLabel className="text-sm font-medium text-slate-700 dark:text-slate-300">
                    Relação com o Instrutor
                  </FormLabel>
                </div>
                <FormControl>
                  <Input 
                    placeholder="Ex: Cônjuge, Pai, Mãe, Irmão(ã), etc." 
                    {...field} 
                    className="border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 w-full"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                      }
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>
    </div>
  );
} 