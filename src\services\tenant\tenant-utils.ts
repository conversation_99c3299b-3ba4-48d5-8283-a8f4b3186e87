import { TenantExtractionContext, TenantExtractionStrategy } from './types';
import { TENANT_CONFIG, PATTERNS, EXTRACTION_PRIORITY, TENANT_HEADERS } from './tenant-config';
import { tenantCache } from './tenant-cache';

/**
 * Utilitários compartilhados para validação e extração de tenant
 */
export class TenantUtils {
  
  /**
   * Valida se um hostname é um domínio principal (não-tenant)
   */
  static isMainDomain(hostname: string): boolean {
    const normalizedHostname = hostname.split(':')[0].toLowerCase();
    
    return TENANT_CONFIG.mainDomains.some(domain => 
      normalizedHostname === domain || 
      normalizedHostname === `www.${domain}`
    ) || PATTERNS.IP_ADDRESS.test(normalizedHostname);
  }

  /**
   * Valida se um slug é válido
   */
  static isValidSlug(slug: string): boolean {
    if (!slug || slug.length < 2 || slug.length > 63) return false;
    if (TENANT_CONFIG.reservedPaths.includes(slug)) return false;
    if (this.isMainDomain(slug)) return false;
    
    return PATTERNS.VALID_SLUG.test(slug.toLowerCase());
  }

  /**
   * Normaliza um hostname removendo porta e protocolo
   */
  static normalizeHostname(hostname: string): string {
    return hostname
      .replace(/^https?:\/\//, '')
      .split(':')[0]
      .toLowerCase();
  }

  /**
   * Extrai slug do tenant usando múltiplas estratégias
   */
  static extractTenantSlug(context: TenantExtractionContext): string | null {
    // Verificar cache primeiro
    const cacheKey = this.generateCacheKey(context);
    const cachedSlug = tenantCache.get(cacheKey);
    if (cachedSlug) return cachedSlug;

    // Tentar cada estratégia na ordem de prioridade
    for (const strategy of EXTRACTION_PRIORITY) {
      const slug = this.extractByStrategy(strategy, context);
      if (slug && this.isValidSlug(slug)) {
        // Adicionar ao cache
        tenantCache.set(cacheKey, slug);
        return slug;
      }
    }

    return null;
  }

  /**
   * Extrai tenant usando uma estratégia específica
   */
  private static extractByStrategy(
    strategy: TenantExtractionStrategy, 
    context: TenantExtractionContext
  ): string | null {
    switch (strategy) {
      case 'header':
        return this.extractFromHeader(context);
      case 'subdomain':
        return this.extractFromSubdomain(context);
      case 'cookie':
        return this.extractFromCookie(context);
      case 'path':
        return this.extractFromPath(context);
      default:
        return null;
    }
  }

  /**
   * Extrai tenant do header
   */
  private static extractFromHeader(context: TenantExtractionContext): string | null {
    if (!context.headers) return null;
    
    const tenantHeader = context.headers[TENANT_HEADERS.SLUG] || 
                        context.headers[TENANT_HEADERS.SLUG.toLowerCase()];
    
    return tenantHeader && !this.isMainDomain(tenantHeader) ? tenantHeader : null;
  }

  /**
   * Extrai tenant do subdomínio
   */
  private static extractFromSubdomain(context: TenantExtractionContext): string | null {
    const hostname = context.hostname;
    if (!hostname || !hostname.includes('.')) return null;

    const normalizedHostname = this.normalizeHostname(hostname);
    if (this.isMainDomain(normalizedHostname)) return null;

    const parts = normalizedHostname.split('.');
    if (parts.length >= 2 && parts[0] !== 'www') {
      return parts[0];
    }

    return null;
  }

  /**
   * Extrai tenant dos cookies
   */
  private static extractFromCookie(context: TenantExtractionContext): string | null {
    if (!context.cookies) return null;
    
    const hostCookie = context.cookies.host || context.cookies['x-tenant-slug'];
    if (!hostCookie) return null;

    if (hostCookie.includes('.')) {
      return this.extractFromSubdomain({ hostname: hostCookie });
    }

    return this.isValidSlug(hostCookie) ? hostCookie : null;
  }

  /**
   * Extrai tenant do path
   */
  private static extractFromPath(context: TenantExtractionContext): string | null {
    const pathname = context.pathname;
    if (!pathname) return null;

    const pathParts = pathname.split('/').filter(Boolean);
    if (pathParts.length > 0 && !TENANT_CONFIG.reservedPaths.includes(pathParts[0])) {
      return pathParts[0];
    }

    return null;
  }

  /**
   * Gera chave de cache baseada no contexto
   */
  private static generateCacheKey(context: TenantExtractionContext): string {
    const parts = [
      context.hostname || '',
      context.pathname || '',
      context.headers?.[TENANT_HEADERS.SLUG] || '',
      context.cookies?.host || ''
    ];
    
    return parts.join('|');
  }

  /**
   * Limpa e sanitiza um slug
   */
  static sanitizeSlug(slug: string): string {
    return slug
      .toLowerCase()
      .replace(/[^a-z0-9-]/g, '-')
      .replace(/--+/g, '-')
      .replace(/^-|-$/g, '');
  }

  /**
   * Verifica se um contexto indica um ambiente de desenvolvimento
   */
  static isDevelopment(context: TenantExtractionContext): boolean {
    const hostname = context.hostname;
    if (!hostname) return false;
    
    return PATTERNS.LOCALHOST.test(hostname) || 
           hostname.includes('localhost') ||
           process.env.NODE_ENV === 'development';
  }
} 