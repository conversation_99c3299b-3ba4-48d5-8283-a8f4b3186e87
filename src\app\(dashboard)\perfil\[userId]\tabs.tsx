'use client'

import { useState, ReactNode, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { InformacoesTab } from '../components/informacoes'
import { GraduacoesTab } from '../components/graduacoes'
import { MensalidadesTab } from '../components/mensalidades'
import { EnsinoTab } from '../components/ensino'
import { ContratoTab } from '../components/contrato'
import { MatriculaTab } from '../components/matricula'
import { FileText, Award, CreditCard, GraduationCap, FileSignature, Users } from 'lucide-react'
import { useQueryCacheManager } from '@/hooks/user/use-user-context'

type TabType = {
  id: string
  name: string
  icon: ReactNode
  href: string
  onlyFor?: string[]
}

const tabs: TabType[] = [
  {
    id: 'informacoes',
    name: 'Informações',
    icon: <FileText className="w-4 h-4 mr-2" />,
    href: '#informacoes'
  },
  {
    id: 'ensino',
    name: 'Ensino',
    icon: <GraduationCap className="w-4 h-4 mr-2" />,
    href: '#ensino',
    onlyFor: ['instructor']
  },
  {
    id: 'matricula',
    name: 'Matrícula',
    icon: <Users className="w-4 h-4 mr-2" />,
    href: '#matricula',
    onlyFor: ['student']
  },
  {
    id: 'contrato',
    name: 'Contrato',
    icon: <FileSignature className="w-4 h-4 mr-2" />,
    href: '#contrato',
    onlyFor: ['instructor']
  },
  {
    id: 'graduacoes',
    name: 'Graduações',
    icon: <Award className="w-4 h-4 mr-2" />,
    href: '#graduacoes',
    onlyFor: ['student', 'instructor']
  },
  {
    id: 'mensalidades',
    name: 'Mensalidades',
    icon: <CreditCard className="w-4 h-4 mr-2" />,
    href: '#mensalidades',
    onlyFor: ['student']
  }
]

type TabId = 'informacoes' | 'ensino' | 'contrato' | 'graduacoes' | 'mensalidades' | 'matricula'

interface ProfileTabsProps {
  userId: string
  role: string
  userData?: any
}

export default function ProfileTabs({ userId, role, userData }: ProfileTabsProps) {
  const [currentTab, setCurrentTab] = useState<TabId>('informacoes')
  const { prefetchUserData } = useQueryCacheManager()

  useEffect(() => {
    if (userData && userId) {
      prefetchUserData(userId, userData)
      console.log('[CACHE] Dados do perfil pré-carregados para todas as abas:', userId)
    }
  }, [userData, userId, prefetchUserData])

  const filteredTabs = tabs.filter((tab) => {
    if (tab.id === 'presenca') return false
    if (tab.onlyFor && !tab.onlyFor.includes(role)) {
      return false
    }

    return true
  })

  if (filteredTabs.length > 0 && !filteredTabs.find((tab) => tab.id === currentTab)) {
    setCurrentTab(filteredTabs[0].id as TabId)
  }

  const handleTabChange = (tabId: TabId) => {
    setCurrentTab(tabId)
    console.log(`[TAB] Mudança para a aba: ${tabId}`)
  }

  const renderTabContent = () => {
    switch (currentTab) {
      case 'informacoes':
        return <InformacoesTab userId={userId} role={role} userData={userData} />
      case 'ensino':
        return <EnsinoTab userId={userId} />
      case 'matricula':
        return <MatriculaTab userId={userId} />
      case 'contrato':
        return <ContratoTab userId={userId} role={role} userData={userData} />
      case 'graduacoes':
        return <GraduacoesTab userId={userId} />
      case 'mensalidades':
        return <MensalidadesTab userId={userId} />
      default:
        return <div>Aba não implementada</div>
    }
  }

  return (
    <div className="flex flex-col h-full">
      <nav className="border-b border-slate-200 dark:border-slate-700">
        <div className="flex overflow-x-auto no-scrollbar">
          {filteredTabs.map((tab) => (
            <button key={tab.id} onClick={() => handleTabChange(tab.id as TabId)} className={cn('flex items-center py-4 px-6 text-sm font-medium whitespace-nowrap border-b-2 transition-all duration-200', currentTab === tab.id ? 'border-primary text-primary dark:border-primary dark:text-primary' : 'border-transparent text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-200')}>
              {tab.icon}
              {tab.name}
            </button>
          ))}
        </div>
      </nav>

      <div>{renderTabContent()}</div>
    </div>
  )
}
