'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  UserPlus, 
  Filter, 
  Search, 
  AlertCircle, 
  RefreshCw, 
  Users,
  BookOpen,
  Wifi,
  WifiOff
} from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface StudentEmptyStateProps {
  type: 'no-students' | 'no-results' | 'loading-error' | 'network-error' | 'permission-error';
  classGroupId?: string;
  onClearFilters?: () => void;
  onRetry?: () => void;
  onEnrollment?: () => void;
  error?: string;
  searchTerm?: string;
  activeFilters?: string[];
}

export function StudentEmptyState({ 
  type, 
  classGroupId, 
  onClearFilters,
  onRetry,
  onEnrollment,
  error,
  searchTerm,
  activeFilters = []
}: StudentEmptyStateProps) {
  const renderContent = () => {
    switch (type) {
      case 'no-students':
        return (
          <>
            <div className="inline-flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 rounded-full bg-gradient-to-br from-primary/10 to-primary/5 mb-6 sm:mb-8 shadow-lg" aria-hidden="true">
              <Users className="w-10 h-10 sm:w-12 sm:h-12 text-primary/70" />
            </div>
            <h3 className="text-xl sm:text-2xl font-bold text-foreground mb-3">
              Primeira turma? Que emocionante! 🎉
            </h3>
            <p className="text-muted-foreground mb-6 sm:mb-8 max-w-lg text-center leading-relaxed text-sm sm:text-base">
              Esta turma ainda não possui alunos matriculados. Comece sua jornada adicionando o primeiro aluno e construindo uma comunidade incrível.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 items-center w-full sm:w-auto">
              {classGroupId && onEnrollment && (
                <Button 
                  size="lg" 
                  className="shadow-md hover:shadow-lg transition-shadow w-full sm:w-auto" 
                  onClick={onEnrollment}
                  aria-label="Matricular o primeiro aluno nesta turma"
                >
                  <UserPlus className="w-4 h-4 sm:w-5 sm:h-5 mr-2" aria-hidden="true" />
                  Matricular Primeiro Aluno
                </Button>
              )}
              <Button variant="outline" size="lg" asChild className="w-full sm:w-auto">
                <Link 
                  href="/alunos"
                  aria-label="Navegar para a lista completa de alunos"
                >
                  <BookOpen className="w-4 h-4 sm:w-5 sm:h-5 mr-2" aria-hidden="true" />
                  Ver Todos os Alunos
                </Link>
              </Button>
            </div>
          </>
        );

      case 'no-results':
        return (
          <>
            <div className="inline-flex items-center justify-center w-24 h-24 rounded-full bg-gradient-to-br from-orange-100 to-orange-50 dark:from-orange-950 dark:to-orange-900 mb-8 shadow-lg">
              <Search className="w-12 h-12 text-orange-600 dark:text-orange-400" />
            </div>
            <h3 className="text-2xl font-bold text-foreground mb-3">
              Nenhum resultado encontrado
            </h3>
            <p className="text-muted-foreground mb-6 max-w-lg text-center leading-relaxed">
              {searchTerm 
                ? `Não encontramos alunos para "${searchTerm}"`
                : "Não encontramos alunos que correspondam aos filtros aplicados"
              }. Tente ajustar os critérios de busca.
            </p>
            
            {/* Active Filters Display */}
            {activeFilters.length > 0 && (
              <div className="mb-6 p-4 bg-muted/30 rounded-lg border">
                <p className="text-sm text-muted-foreground mb-2">Filtros ativos:</p>
                <div className="flex flex-wrap gap-2 justify-center">
                  {activeFilters.map((filter, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {filter}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            <div className="flex flex-col sm:flex-row gap-3 items-center w-full sm:w-auto">
              {onClearFilters && (
                <Button 
                  variant="outline" 
                  size="lg" 
                  onClick={onClearFilters} 
                  className="shadow-sm w-full sm:w-auto"
                  aria-label="Remover todos os filtros aplicados"
                >
                  <Filter className="w-4 h-4 sm:w-5 sm:h-5 mr-2" aria-hidden="true" />
                  Limpar Filtros
                </Button>
              )}
              {classGroupId && onEnrollment && (
                <Button 
                  size="lg" 
                  className="shadow-md hover:shadow-lg transition-shadow w-full sm:w-auto" 
                  onClick={onEnrollment}
                  aria-label="Adicionar novo aluno à turma"
                >
                  <UserPlus className="w-4 h-4 sm:w-5 sm:h-5 mr-2" aria-hidden="true" />
                  Adicionar Aluno
                </Button>
              )}
            </div>
          </>
        );

      case 'loading-error':
        return (
          <>
            <div className="inline-flex items-center justify-center w-24 h-24 rounded-full bg-gradient-to-br from-red-100 to-red-50 dark:from-red-950 dark:to-red-900 mb-8 shadow-lg">
              <AlertCircle className="w-12 h-12 text-red-600 dark:text-red-400" />
            </div>
            <h3 className="text-2xl font-bold text-foreground mb-3">
              Ops! Algo deu errado
            </h3>
            <p className="text-muted-foreground mb-6 max-w-lg text-center leading-relaxed">
              Ocorreu um erro ao carregar a lista de alunos. Isso pode ser temporário.
            </p>
            
            {error && (
              <div className="mb-6 p-3 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg">
                <p className="text-sm text-red-700 dark:text-red-300 font-mono">
                  {error}
                </p>
              </div>
            )}
            
            <div className="flex flex-col sm:flex-row gap-3 items-center">
              {onRetry && (
                <Button size="lg" onClick={onRetry} className="shadow-md hover:shadow-lg transition-shadow">
                  <RefreshCw className="w-5 h-5 mr-2" />
                  Tentar Novamente
                </Button>
              )}
              <Button variant="outline" size="lg" onClick={() => window.location.reload()}>
                <RefreshCw className="w-5 h-5 mr-2" />
                Recarregar Página
              </Button>
            </div>
          </>
        );

      case 'network-error':
        return (
          <>
            <div className="inline-flex items-center justify-center w-24 h-24 rounded-full bg-gradient-to-br from-gray-100 to-gray-50 dark:from-gray-800 dark:to-gray-900 mb-8 shadow-lg">
              <WifiOff className="w-12 h-12 text-gray-600 dark:text-gray-400" />
            </div>
            <h3 className="text-2xl font-bold text-foreground mb-3">
              Sem conexão com a internet
            </h3>
            <p className="text-muted-foreground mb-8 max-w-lg text-center leading-relaxed">
              Verifique sua conexão com a internet e tente novamente. Os dados serão carregados assim que a conexão for restabelecida.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 items-center">
              {onRetry && (
                <Button size="lg" onClick={onRetry} className="shadow-md hover:shadow-lg transition-shadow">
                  <Wifi className="w-5 h-5 mr-2" />
                  Verificar Conexão
                </Button>
              )}
              <Button variant="outline" size="lg" onClick={() => window.location.reload()}>
                <RefreshCw className="w-5 h-5 mr-2" />
                Recarregar
              </Button>
            </div>
          </>
        );

      case 'permission-error':
        return (
          <>
            <div className="inline-flex items-center justify-center w-24 h-24 rounded-full bg-gradient-to-br from-yellow-100 to-yellow-50 dark:from-yellow-950 dark:to-yellow-900 mb-8 shadow-lg">
              <AlertCircle className="w-12 h-12 text-yellow-600 dark:text-yellow-400" />
            </div>
            <h3 className="text-2xl font-bold text-foreground mb-3">
              Acesso restrito
            </h3>
            <p className="text-muted-foreground mb-8 max-w-lg text-center leading-relaxed">
              Você não tem permissão para visualizar os alunos desta turma. Entre em contato com o administrador se acredita que isso é um erro.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 items-center">
              <Button variant="outline" size="lg" asChild>
                <Link href="/dashboard">
                  Voltar ao Dashboard
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/suporte">
                  Contatar Suporte
                </Link>
              </Button>
            </div>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <section 
      className={cn(
        "flex flex-col items-center justify-center py-16 sm:py-20 px-4 sm:px-6 text-center",
        "min-h-[400px] bg-gradient-to-b from-background to-muted/20"
      )}
      role="status"
      aria-live="polite"
      aria-label={type === 'no-students' ? 'Nenhum aluno matriculado' : 
                  type === 'no-results' ? 'Nenhum resultado encontrado' :
                  type === 'loading-error' ? 'Erro ao carregar' :
                  type === 'network-error' ? 'Erro de conexão' :
                  'Erro de permissão'}
    >
      <div className="max-w-2xl mx-auto">
        {renderContent()}
      </div>
    </section>
  );
} 