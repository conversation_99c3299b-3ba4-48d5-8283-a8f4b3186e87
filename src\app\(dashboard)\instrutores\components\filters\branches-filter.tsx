import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Branch } from '../../types/types';

interface BranchesFilterProps {
  branches: Branch[];
  value: string;
  onChange: (value: string) => void;
}

export function BranchesFilter({ branches, value, onChange }: BranchesFilterProps) {
  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger id="branch">
        <SelectValue placeholder="Todas" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">Todas</SelectItem>
        {branches.map((branch) => (
          <SelectItem key={branch.id} value={branch.id}>
            {branch.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
} 