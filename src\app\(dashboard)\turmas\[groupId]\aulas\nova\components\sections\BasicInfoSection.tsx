'use client';

import { <PERSON><PERSON>Circle, FileText } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { FieldErrors, UseFormRegister } from 'react-hook-form';
import { CreateClassFormData } from '../schema';
import { ClassGroup } from '../types';

interface BasicInfoSectionProps {
  register: UseFormRegister<CreateClassFormData>;
  errors: FieldErrors<CreateClassFormData>;
  formValues: CreateClassFormData;
  classGroup: ClassGroup;
  getErrorMessage: (error: any) => string;
}

export function BasicInfoSection({ 
  register, 
  errors, 
  formValues, 
  classGroup, 
  getErrorMessage 
}: BasicInfoSectionProps) {
  return (
    <Card className={`transition-all duration-200 ${errors.name ? 'ring-2 ring-destructive/20' : ''}`}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-lg">
          <FileText className="h-5 w-5" />
          Informações Básicas
          {errors.name && <AlertCircle className="h-4 w-4 text-destructive" />}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">Nome da Aula *</Label>
            <Input
              id="name"
              placeholder="Ex: Aula de Yoga - Iniciantes"
              {...register('name')}
              className={errors.name ? 'border-destructive' : ''}
            />
            {errors.name && (
              <p className="text-sm text-destructive">{getErrorMessage(errors.name)}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="max_capacity">Capacidade Máxima</Label>
            <Input
              id="max_capacity"
              type="number"
              min="1"
              max="100"
              placeholder={classGroup.max_capacity?.toString() || "Ex: 15"}
              {...register('max_capacity')}
              className={errors.max_capacity ? 'border-destructive' : ''}
            />
            {errors.max_capacity && (
              <p className="text-sm text-destructive">{getErrorMessage(errors.max_capacity)}</p>
            )}
            {!errors.max_capacity && classGroup.max_capacity && (
              <p className="text-xs text-muted-foreground">
                Padrão da turma: {classGroup.max_capacity} alunos
              </p>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Descrição</Label>
          <Textarea
            id="description"
            placeholder="Descreva o conteúdo da aula, exercícios que serão praticados..."
            rows={3}
            {...register('description')}
            className={errors.description ? 'border-destructive' : ''}
          />
          {errors.description && (
            <p className="text-sm text-destructive">{getErrorMessage(errors.description)}</p>
          )}
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Máximo 500 caracteres</span>
            <span>{formValues.description?.length || 0}/500</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 