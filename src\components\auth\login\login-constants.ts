export interface ErrorMapping {
  field: string;
  message: string;
}

export interface AuthError {
  error: string;
  error_code?: string;
  tenant_info?: {
    name: string;
    slug: string;
  };
}

export interface AuthResultData {
  type: 'success' | 'error';
  title: string;
  message: string;
  tenantInfo?: { name: string; slug: string };
}

export const AUTH_ERRORS: Record<string, ErrorMapping> = {
  'Invalid login credentials': { 
    field: 'password', 
    message: 'Credenciais inválidas. Verifique seu email e senha.' 
  },
  'Email not confirmed': { 
    field: 'email', 
    message: 'Email não confirmado. Verifique sua caixa de entrada.' 
  },
  'User not found': { 
    field: 'email', 
    message: 'Usuário não encontrado.' 
  },
  'Invalid email': { 
    field: 'email', 
    message: 'Formato de email inválido.' 
  },
  'Password should be at least 6 characters': { 
    field: 'password', 
    message: 'A senha deve ter pelo menos 6 caracteres.' 
  },
  'Você não tem permissão para acessar este tenant': { 
    field: 'password', 
    message: 'Credenciais inválidas. Verifique seu email e senha.' 
  },
  'Credenciais inválidas': { 
    field: 'password', 
    message: 'Credenciais inválidas. Verifique seu email e senha.' 
  },
  'Tenant não encontrado': { 
    field: 'email', 
    message: 'Credenciais inválidas. Verifique seu email e senha.' 
  },
  'Sua conta está inativa. Entre em contato com o administrador para reativá-la.': {
    field: 'form',
    message: 'Sua conta está inativa. Entre em contato com o administrador para reativá-la.'
  },
  'Sua conta está suspensa. Entre em contato com o administrador para mais informações.': {
    field: 'form', 
    message: 'Sua conta está suspensa. Entre em contato com o administrador para mais informações.'
  },
} 