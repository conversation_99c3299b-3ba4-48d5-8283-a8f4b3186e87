import { createAdminClient } from '@/services/supabase/server';
import { TenantExtractorServer } from '@/services/tenant/tenant-extractor-server';
import { requireAuth } from '@/services/auth/actions/auth-actions';

export interface LayoutAuthResult {
  user: any;
  tenantSlug: string | null;
  tenantData: any | null;
}

/**
 * Serviço compartilhado para aplicar regras de autenticação e tenant em layouts
 * Centraliza a lógica comum entre dashboard e checkin layouts
 */
export async function validateLayoutAuth(redirectTo: string = '/login'): Promise<LayoutAuthResult> {
  // 1. Verificar autenticação
  const { user } = await requireAuth(redirectTo);
  
  // 2. Extrair tenant
  const extractor = new TenantExtractorServer();
  const tenantSlug = await extractor.extractTenantSlug();
  
  let tenantData = null;
  
  if (tenantSlug) {
    // 3. Buscar dados do tenant
    const supabase = await createAdminClient();
    
    const { data, error } = await supabase
      .from('tenants')
      .select('*')
      .eq('slug', tenantSlug)
      .single();
    
    if (error) {
      console.error('Erro ao buscar dados do tenant:', error);
    } else {
      tenantData = data;
    }
  }
  
  return {
    user,
    tenantSlug,
    tenantData,
  };
} 