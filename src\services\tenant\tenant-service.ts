'use server';

import { TenantExtractorServer } from './tenant-extractor-server';
import { cache } from 'react';
import { cookies, headers } from 'next/headers';
import { getSupabaseConfig } from '@/config/supabase';

export const getTenantSlug = cache(async (fallbackSlug = ''): Promise<string> => {
  try {
    const headersList = await headers();
    const tenantHeader = headersList.get('x-tenant-slug');
    if (tenantHeader) return tenantHeader;
  } catch (e) {
    // Falha silenciosa se não estiver em um RSC
  }
  
  const extractor = new TenantExtractorServer(fallbackSlug);
  const headersList = await headers();
  const cookieStore = await cookies();
  return extractor.extractTenantSlug(headersList, cookieStore);
});

export const getTenantData = cache(async (slug: string) => {
  if (!slug) return null;
  
  try {
    const config = getSupabaseConfig();
    const response = await fetch(
      `${config.url}/rest/v1/tenants?slug=eq.${encodeURIComponent(slug)}&select=name,primary_color,secondary_color,logo_url,description`,
      {
        headers: {
          'apikey': config.anonKey,
          'Content-Type': 'application/json'
        },
        next: { 
          revalidate: 300 // Cache por 5 minutos
        }
      }
    );

    if (!response.ok) return null;
    
    const data = await response.json();
    return data[0] || null;
  } catch (error) {
    console.error('Erro ao recuperar tenant:', error);
    return null;
  }
}); 