'use client';

import { useState, useEffect, useCallback } from 'react';
import { getGeneralSettings, type GeneralSettingsData } from '../actions';

interface UseGeneralSettingsReturn {
  settings: GeneralSettingsData;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  updateSettings: (newSettings: GeneralSettingsData) => void;
}

const defaultSettings: GeneralSettingsData = {
  currency: 'BRL',
  defaultDueDay: 'enrollment_date',
};

export function useGeneralSettings(): UseGeneralSettingsReturn {
  const [settings, setSettings] = useState<GeneralSettingsData>(defaultSettings);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSettings = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await getGeneralSettings();
      
      if (result.success && result.data) {
        setSettings(result.data as GeneralSettingsData);
      } else {
        setError(result.errors?._form || 'Erro ao carregar configurações');
        console.error('Erro ao carregar configurações:', result.errors);
      }
    } catch (err) {
      const errorMessage = 'Erro interno ao carregar configurações';
      setError(errorMessage);
      console.error('Erro ao carregar configurações:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateSettings = useCallback((newSettings: GeneralSettingsData) => {
    setSettings(newSettings);
  }, []);

  const refetch = useCallback(async () => {
    await fetchSettings();
  }, [fetchSettings]);

  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  return {
    settings,
    isLoading,
    error,
    refetch,
    updateSettings,
  };
}
