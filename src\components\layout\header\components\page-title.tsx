"use client";

import { useTenantTheme } from "@/hooks/tenant/use-tenant-theme";
import { ReactNode } from 'react';

interface PageTitleProps {
  title?: string | React.ReactNode;
  subtitle?: string;
  icon?: ReactNode;
}

export function PageTitle({ title, subtitle, icon }: PageTitleProps) {
  const { primaryColor } = useTenantTheme();

  if (!title) return null;

  return (
    <div className="flex items-center gap-3">
      {icon && (
        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
          {icon}
        </div>
      )}
      <div>
        <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
        {subtitle && (
          <p className="text-sm text-muted-foreground">{subtitle}</p>
        )}
      </div>
    </div>
  );
} 