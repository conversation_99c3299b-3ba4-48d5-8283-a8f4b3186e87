'use client';

import { useQuery } from '@tanstack/react-query';
import { getStudentEnrollmentDetails } from '@/app/(dashboard)/turmas/actions/enrollment-actions';
import { CACHE_KEYS } from '@/constants/cache-keys';
import { cacheService } from '@/services/cache';

/**
 * Hook para buscar e gerenciar dados de matrícula de um estudante
 */
export function useStudentEnrollment(userId: string, forceRefresh = false) {
  return useQuery({
    queryKey: CACHE_KEYS.ENROLLMENT.STUDENT_DETAILS(userId),
    queryFn: async () => {
      if (!userId) return null;

      // Se forceRefresh for true, pular o cache
      if (!forceRefresh) {
        const cachedData = cacheService.getData<any>(CACHE_KEYS.ENROLLMENT.STUDENT_DETAILS(userId));
        if (cachedData) {
          return cachedData;
        }
      }

      const result = await getStudentEnrollmentDetails(userId);

      if (result.error) {
        if (result.error.includes('Nenhuma matrícula encontrada')) {
          return null;
        }
        throw new Error(result.error);
      }

      if (result.data) {
        cacheService.setData(CACHE_KEYS.ENROLLMENT.STUDENT_DETAILS(userId), result.data);
      }

      return result.data;
    },
    staleTime: forceRefresh ? 0 : 5 * 60 * 1000, // Se forceRefresh, não usar staleTime
    gcTime: 15 * 60 * 1000, // 15 minutos
    refetchOnMount: forceRefresh,
    refetchOnWindowFocus: false,
    enabled: !!userId,
    retry: (failureCount, error) => {
      // Não tentar novamente se for erro de "usuário não é estudante" ou "nenhuma matrícula encontrada"
      if (error instanceof Error && (
        error.message.includes('não é um estudante') ||
        error.message.includes('Nenhuma matrícula encontrada')
      )) {
        return false;
      }
      return failureCount < 2;
    },
    // Configurar dados iniciais do cache se disponível (apenas se não for forceRefresh)
    initialData: () => {
      return userId && !forceRefresh ? cacheService.getData<any>(CACHE_KEYS.ENROLLMENT.STUDENT_DETAILS(userId)) : undefined;
    },
  });
} 