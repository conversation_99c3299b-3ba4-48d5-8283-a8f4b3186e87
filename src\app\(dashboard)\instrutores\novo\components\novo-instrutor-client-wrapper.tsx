'use client';

import { ReactNode } from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

interface NovoInstrutorClientWrapperProps {
  children: ReactNode;
}

const queryClient = new QueryClient();

export default function NovoInstrutorClientWrapper({ children }: NovoInstrutorClientWrapperProps) {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
} 