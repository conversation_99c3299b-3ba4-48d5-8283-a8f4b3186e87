/**
 * Server Actions para o Sistema de Cobranças
 * Baseado no documento: docs/planejamento-sistema-cobrancas.md
 */

'use server'

import { revalidatePath } from 'next/cache'
import { getCurrentUser } from '@/services/auth/actions/auth-actions'
import { paymentService } from './payment-service'
import {
  criarCobrancaManualSchema,
  criarTaxaGraduacaoSchema,
  criarTaxaInscricaoSchema,
  criarTaxaCancelamentoSchema,
  atualizarStatusPagamentoSchema,
  buscarPagamentosPorAlunoSchema,
  buscarPagamentosPorMembershipSchema,
  buscarTodosPagamentosSchema,
  processarPagamentosAtrasadosSchema,
  metricasPagamentosSchema,
  type CriarCobrancaManualData,
  type CriarTaxaGraduacaoData,
  type CriarTaxaInscricaoData,
  type CriarTaxaCancelamentoData,
  type AtualizarStatusPagamentoData,
  type BuscarPagamentosPorAlunoData,
  type BuscarPagamentosPorMembershipData,
  type BuscarTodosPagamentosData,
  type ProcessarPagamentosAtrasadosData,
  type MetricasPagamentosData,
  atualizarDetalhesPagamentoSchema,
  type AtualizarDetalhesPagamentoData
} from './payment-schemas'
import { ActionResult } from './payment-types'

/**
 * Criar cobrança manual
 * Implementa a action definida na FASE 3 do documento
 */
export async function createManualPayment(data: unknown): Promise<ActionResult> {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      }
    }

    const result = criarCobrancaManualSchema.safeParse(data)
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>
      }
    }

    const validatedData = result.data
    const serviceResult = await paymentService.createManualPayment(validatedData)

    if (!serviceResult.success) {
      return {
        success: false,
        errors: { _form: serviceResult.error || 'Erro ao criar cobrança manual' }
      }
    }

    revalidatePath('/financeiro')
    revalidatePath('/alunos')

    return {
      success: true,
      data: serviceResult.data
    }
  } catch (error) {
    return {
      success: false,
      errors: { _form: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}` }
    }
  }
}

/**
 * Criar taxa de graduação
 * Implementa a action definida na FASE 3 do documento
 */
export async function createGraduationFeePayment(data: unknown): Promise<ActionResult> {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      }
    }

    const result = criarTaxaGraduacaoSchema.safeParse(data)
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>
      }
    }

    const validatedData = result.data
    const serviceResult = await paymentService.createGraduationFeePayment(validatedData)

    if (!serviceResult.success) {
      return {
        success: false,
        errors: { _form: serviceResult.error || 'Erro ao criar taxa de graduação' }
      }
    }

    revalidatePath('/financeiro')
    revalidatePath('/alunos')
    revalidatePath('/graduacoes')

    return {
      success: true,
      data: serviceResult.data
    }
  } catch (error) {
    return {
      success: false,
      errors: { _form: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}` }
    }
  }
}

/**
 * Criar taxa de inscrição
 */
export async function createSignupFeePayment(data: unknown): Promise<ActionResult> {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      }
    }

    const result = criarTaxaInscricaoSchema.safeParse(data)
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>
      }
    }

    const validatedData = result.data
    const serviceResult = await paymentService.createSignupFeePayment(validatedData)

    if (!serviceResult.success) {
      return {
        success: false,
        errors: { _form: serviceResult.error || 'Erro ao criar taxa de inscrição' }
      }
    }

    revalidatePath('/financeiro')
    revalidatePath('/academia')

    return {
      success: true,
      data: serviceResult.data
    }
  } catch (error) {
    return {
      success: false,
      errors: { _form: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}` }
    }
  }
}

/**
 * Criar taxa de cancelamento
 * Cria cobrança baseada na taxa configurada no plano quando uma assinatura é cancelada
 */
export async function createCancellationFeePayment(data: unknown): Promise<ActionResult> {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      }
    }

    const result = criarTaxaCancelamentoSchema.safeParse(data)
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>
      }
    }

    const validatedData = result.data
    const serviceResult = await paymentService.createCancellationFeePayment(validatedData)

    if (!serviceResult.success) {
      return {
        success: false,
        errors: { _form: serviceResult.error || 'Erro ao criar taxa de cancelamento' }
      }
    }

    revalidatePath('/financeiro')
    revalidatePath('/academia')
    revalidatePath('/alunos')

    return {
      success: true,
      data: serviceResult.data
    }
  } catch (error) {
    return {
      success: false,
      errors: { _form: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}` }
    }
  }
}

/**
 * Processar pagamentos em atraso
 * Implementa a action definida na FASE 3 do documento
 */
export async function processOverduePayments(data?: unknown): Promise<ActionResult> {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      }
    }

    let validatedData: ProcessarPagamentosAtrasadosData | undefined
    if (data) {
      const result = processarPagamentosAtrasadosSchema.safeParse(data)
      if (!result.success) {
        return {
          success: false,
          errors: result.error.format() as unknown as Record<string, string>
        }
      }
      validatedData = result.data
    }

    const serviceResult = await paymentService.processOverduePayments(validatedData)

    if (!serviceResult.success) {
      return {
        success: false,
        errors: { _form: serviceResult.error || 'Erro ao processar pagamentos em atraso' }
      }
    }

    revalidatePath('/financeiro')

    return {
      success: true,
      data: serviceResult.data
    }
  } catch (error) {
    return {
      success: false,
      errors: { _form: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}` }
    }
  }
}

/**
 * Atualizar status de pagamento
 * Implementa a action definida na FASE 3 do documento
 */
export async function updatePaymentStatus(data: unknown): Promise<ActionResult> {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      }
    }

    const result = atualizarStatusPagamentoSchema.safeParse(data)
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>
      }
    }

    const validatedData = result.data
    const serviceResult = await paymentService.updatePaymentStatus(validatedData)

    if (!serviceResult.success) {
      return {
        success: false,
        errors: { _form: serviceResult.error || 'Erro ao atualizar status do pagamento' }
      }
    }

    revalidatePath('/financeiro')

    return {
      success: true,
      data: serviceResult.data
    }
  } catch (error) {
    return {
      success: false,
      errors: { _form: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}` }
    }
  }
}

/**
 * Buscar pagamentos por estudante
 * Implementa a action definida na FASE 3 do documento
 */
export async function getPaymentsByStudent(data: unknown): Promise<ActionResult> {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      }
    }

    const result = buscarPagamentosPorAlunoSchema.safeParse(data)
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>
      }
    }

    const validatedData = result.data
    const serviceResult = await paymentService.getPaymentsByStudent(validatedData)

    if (!serviceResult.success) {
      return {
        success: false,
        errors: { _form: serviceResult.error || 'Erro ao buscar pagamentos do estudante' }
      }
    }

    return {
      success: true,
      data: serviceResult.data
    }
  } catch (error) {
    return {
      success: false,
      errors: { _form: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}` }
    }
  }
}

/**
 * Buscar pagamentos por membership
 * Implementa a action definida na FASE 3 do documento
 */
export async function getPaymentsByMembership(data: unknown): Promise<ActionResult> {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      }
    }

    const result = buscarPagamentosPorMembershipSchema.safeParse(data)
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>
      }
    }

    const validatedData = result.data
    const serviceResult = await paymentService.getPaymentsByMembership(validatedData)

    if (!serviceResult.success) {
      return {
        success: false,
        errors: { _form: serviceResult.error || 'Erro ao buscar pagamentos da matrícula' }
      }
    }

    return {
      success: true,
      data: serviceResult.data
    }
  } catch (error) {
    return {
      success: false,
      errors: { _form: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}` }
    }
  }
}

/**
 * Buscar todos os pagamentos com filtros e paginação
 */
export async function getAllPayments(data?: unknown): Promise<ActionResult> {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      }
    }

    let validatedData: BuscarTodosPagamentosData | undefined
    if (data) {
      const result = buscarTodosPagamentosSchema.safeParse(data)
      if (!result.success) {
        return {
          success: false,
          errors: result.error.format() as unknown as Record<string, string>
        }
      }
      validatedData = result.data
    }

    // Adicionar tenant_id do usuário autenticado
    const tenantId = user.app_metadata?.tenant_id
    const searchData = {
      ...validatedData,
      tenantId
    }

    const serviceResult = await paymentService.getAllPayments(searchData)

    if (!serviceResult.success) {
      return {
        success: false,
        errors: { _form: serviceResult.error || 'Erro ao buscar pagamentos' }
      }
    }

    return {
      success: true,
      data: serviceResult.data,
      hasMore: serviceResult.hasMore
    }
  } catch (error) {
    return {
      success: false,
      errors: { _form: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}` }
    }
  }
}

/**
 * Métricas de pagamentos
 * Implementa a action definida na FASE 3 do documento
 */
export async function getPaymentMetrics(data?: unknown): Promise<ActionResult> {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      }
    }

    let validatedData: MetricasPagamentosData | undefined
    if (data) {
      const result = metricasPagamentosSchema.safeParse(data)
      if (!result.success) {
        return {
          success: false,
          errors: result.error.format() as unknown as Record<string, string>
        }
      }
      validatedData = result.data
    }

    const tenantId = user.app_metadata?.tenant_id
    const serviceResult = await paymentService.getPaymentMetrics(
      tenantId,
      validatedData?.startDate,
      validatedData?.endDate
    )

    if (!serviceResult.success) {
      return {
        success: false,
        errors: { _form: serviceResult.error || 'Erro ao buscar métricas de pagamentos' }
      }
    }

    return {
      success: true,
      data: serviceResult.data
    }
  } catch (error) {
    return {
      success: false,
      errors: { _form: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}` }
    }
  }
}

/**
 * Métricas de pagamentos do mês atual
 */
export async function getMonthlyPaymentMetrics(): Promise<ActionResult> {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      }
    }

    const tenantId = user.app_metadata?.tenant_id
    const serviceResult = await paymentService.getMonthlyPaymentMetrics(tenantId)

    if (!serviceResult.success) {
      return {
        success: false,
        errors: { _form: serviceResult.error || 'Erro ao buscar métricas mensais de pagamentos' }
      }
    }

    return {
      success: true,
      data: serviceResult.data
    }
  } catch (error) {
    return {
      success: false,
      errors: { _form: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}` }
    }
  }
}

/**
 * Confirmar pagamento
 * Atualiza status de 'awaiting_confirmation' para 'paid'
 */
export async function confirmPayment(data: unknown): Promise<ActionResult> {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      }
    }

    const result = atualizarStatusPagamentoSchema.safeParse(data)
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>
      }
    }

    const validatedData = result.data

    // Força o status para 'paid' e adiciona timestamp de pagamento
    const confirmData = {
      ...validatedData,
      novoStatus: 'paid' as const
    }

    const serviceResult = await paymentService.confirmPayment(confirmData)

    if (!serviceResult.success) {
      return {
        success: false,
        errors: { _form: serviceResult.error || 'Erro ao confirmar pagamento' }
      }
    }

    revalidatePath('/financeiro')
    revalidatePath('/perfil')
    revalidatePath('/alunos')

    return {
      success: true,
      data: serviceResult.data
    }
  } catch (error) {
    return {
      success: false,
      errors: { _form: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}` }
    }
  }
}

/**
 * Atualizar detalhes de pagamento
 */
export async function updatePaymentDetails(data: unknown): Promise<ActionResult> {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return {
        success: false,
        errors: { _form: 'Usuário não autenticado' }
      }
    }

    const result = atualizarDetalhesPagamentoSchema.safeParse(data)
    if (!result.success) {
      return {
        success: false,
        errors: result.error.format() as unknown as Record<string, string>
      }
    }

    const validatedData: AtualizarDetalhesPagamentoData = result.data
    const serviceResult = await paymentService.updatePaymentDetails(validatedData)

    if (!serviceResult.success) {
      return {
        success: false,
        errors: { _form: serviceResult.error || 'Erro ao atualizar pagamento' }
      }
    }

    revalidatePath('/financeiro')
    revalidatePath('/perfil')

    return {
      success: true,
      data: serviceResult.data
    }
  } catch (error) {
    return {
      success: false,
      errors: { _form: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}` }
    }
  }
}
