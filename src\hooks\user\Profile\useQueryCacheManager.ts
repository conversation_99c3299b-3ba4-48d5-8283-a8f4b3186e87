import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { cacheService } from '@/services/cache';

/**
 * Hook para gerenciar o cache de consultas usando o serviço de cache centralizado
 * @returns Objeto com métodos para manipular o cache
 */
export function useQueryCacheManager() {
  const queryClient = useQueryClient();
  
  useEffect(() => {
    cacheService.initialize(queryClient);
    cacheService.setupCacheListeners();
  }, [queryClient]);
  
  return {
    prefetchUserData: (userId: string, userData: any) => {
      cacheService.prefetchUserTabsData(userId, userData);
    },
    setQueryData: <T,>(queryKey: unknown[], data: T) => {
      cacheService.setData(queryKey, data);
    },
    getQueryData: <T,>(queryKey: unknown[]): T | undefined => {
      return cacheService.getData<T>(queryKey);
    },
    isQueryActive: (queryKey: unknown[]): boolean => {
      return cacheService.isQueryActive(queryKey);
    },
    invalidateQueries: (queryKey: unknown[]) => {
      cacheService.invalidateQueries(queryKey);
    },
    refetchQueries: (queryKey: unknown[]) => {
      cacheService.refetchQueries(queryKey);
    }
  };
} 