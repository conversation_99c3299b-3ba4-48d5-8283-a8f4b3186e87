'use client';

import { useUserMetadata } from '@/hooks/user/Auth';

export type UserRole = 'admin' | 'teacher' | 'instructor' | 'student';

export function useUserRole() {
  const { metadata, isLoading, error, refetch } = useUserMetadata();
  
  // Extrair o role diretamente dos metadados
  const role = metadata?.role as UserRole || 'student';
  
  return {
    role,
    isAdmin: role === 'admin',
    isTeacher: role === 'teacher',
    isInstructor: role === 'instructor',
    isStudent: role === 'student',
    isTeacherOrInstructor: role === 'instructor' || role === 'teacher',
    hasAdminPermissions: role === 'admin' || role === 'teacher' || role === 'instructor',
    isLoading,
    error,
    refetch,
  };
} 