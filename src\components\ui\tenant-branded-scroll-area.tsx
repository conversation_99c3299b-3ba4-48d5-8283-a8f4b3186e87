"use client";

import * as React from "react";
import { ScrollArea as RadixScrollArea } from "@/components/ui/scroll-area";
import { useTenantTheme } from "@/hooks/tenant/use-tenant-theme";
import { cn } from "@/lib/utils";

interface TenantBrandedScrollAreaProps extends React.ComponentProps<typeof RadixScrollArea> {
  opacity?: number;
  hoverOpacity?: number;
  className?: string;
  scrollbarClassName?: string;
  children: React.ReactNode;
  forceColor?: string;
}

/**
 * ScrollArea com a cor do tenant aplicada à scrollbar
 */
export function TenantBrandedScrollArea({
  children,
  opacity = 0.5, // 50% de opacidade
  hoverOpacity = 0.8, // 80% de opacidade
  className,
  scrollbarClassName,
  forceColor,
  ...props
}: TenantBrandedScrollAreaProps) {
  const { primaryColor } = useTenantTheme();
  // Usar o valor forçado, depois o primaryColor do tenant, ou preto como fallback
  const themeColor = forceColor || primaryColor || "#000000";
  const uniqueId = React.useId();
  const scrollbarId = `tenant-scrollbar-${uniqueId}`;
  
  // Função para debug - útil para verificar se a cor está sendo obtida corretamente
  React.useEffect(() => {
    console.log("TenantBrandedScrollArea - Color used:", themeColor);
  }, [themeColor]);
  
  // Converte qualquer formato de cor para rgba
  const hexToRgba = (hex: string, alpha: number): string => {
    // Se já for rgba, retorna
    if (hex.startsWith("rgba")) return hex;
    
    // Se for hex
    if (hex.startsWith("#")) {
      let r = 0, g = 0, b = 0;
      
      if (hex.length === 4) {
        r = parseInt(hex[1] + hex[1], 16);
        g = parseInt(hex[2] + hex[2], 16);
        b = parseInt(hex[3] + hex[3], 16);
      } else if (hex.length === 7) {
        r = parseInt(hex.substring(1, 3), 16);
        g = parseInt(hex.substring(3, 5), 16);
        b = parseInt(hex.substring(5, 7), 16);
      }
      
      return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }
    
    // Caso contrário, retorna a cor original
    return hex;
  };
  
  // Cores calculadas para uso na scrollbar
  const thumbColor = hexToRgba(themeColor, opacity);
  const thumbHoverColor = hexToRgba(themeColor, hoverOpacity);
  
  // Estilo inline para a scrollbar, alternativa ao CSS global
  const scrollBarStyle = {
    "--scrollbar-thumb-color": thumbColor,
    "--scrollbar-thumb-hover-color": thumbHoverColor,
  } as React.CSSProperties;
  
  return (
    <RadixScrollArea 
      className={cn("relative tenant-branded-scrollbar", className)} 
      {...props}
      style={scrollBarStyle}
    >
      <style jsx global>{`
        .tenant-branded-scrollbar [data-radix-scroll-area-thumb] {
          background-color: var(--scrollbar-thumb-color, ${thumbColor}) !important;
          transition: background-color 0.15s ease;
        }
        
        .tenant-branded-scrollbar [data-radix-scroll-area-thumb]:hover {
          background-color: var(--scrollbar-thumb-hover-color, ${thumbHoverColor}) !important;
        }
        
        .tenant-branded-scrollbar [data-radix-scroll-area-scrollbar] {
          width: 12px !important;
          padding: 2px !important;
        }
      `}</style>
      {children}
    </RadixScrollArea>
  );
} 