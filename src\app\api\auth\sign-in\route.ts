import { signInAction } from '@/services/auth/actions/index';
import { NextRequest, NextResponse } from 'next/server';
import { checkApiAuth } from '@/services/auth/actions/api-auth-actions';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    
    const host = request.headers.get('host');
    console.log("Host capturado na API:", host);
    
    const rememberMe = formData.get('remember_me') === 'true';
    
    formData.append('host', host || '');
    
    const result = await signInAction(formData);
    
    // Se o login foi bem-sucedido, garantir que os cookies sejam definidos na resposta
    if (result.success && !result.error) {
      // Criar uma resposta inicial
      const response = NextResponse.json(result);
      
      // Verificar se temos cookies de autenticação capturados durante o login
      if (result.authCookies && result.authCookies.length > 0) {
        console.log(`API sign-in - Aplicando ${result.authCookies.length} cookies capturados à resposta HTTP`);

        const fullDomain = host || 'localhost';
        console.log(`API sign-in - Usando domínio para cookies: ${fullDomain}`);
        
        // Aplicar cada cookie capturado à resposta
        for (const { name, value, options } of result.authCookies) {
          // Configurar opções do cookie
          const cookieOptions = { ...options };
          
          // Configurações básicas
          cookieOptions.path = '/';
          cookieOptions.sameSite = 'lax';
          cookieOptions.secure = process.env.NODE_ENV === 'production';
          
          // Configurar o domínio do cookie corretamente
          // Se for localhost, não use opção de domínio para evitar o problema com ".localhost"
          if (fullDomain !== 'localhost' && !fullDomain.includes('127.0.0.1')) {
            // Para domínios normais, use o nome exato do host
            cookieOptions.domain = fullDomain;
            console.log(`API sign-in - Definindo cookie ${name} para o domínio: ${fullDomain}`);
          } else {
            console.log(`API sign-in - Cookie ${name} usando domínio padrão (localhost)`);
          }
          
          // Para cookies de autenticação, configurações especiais
          if (name.startsWith('sb-') || name === 'supabase-auth-token') {
            cookieOptions.httpOnly = true;
            if (rememberMe) {
              // Persistir por 30 dias se lembrar-me estiver ativo
              cookieOptions.maxAge = 30 * 24 * 60 * 60; // 30 dias
            } else {
              // Remover atributos de persistência para tornar cookie de sessão
              delete cookieOptions.maxAge;
              delete cookieOptions.expires;
            }
            
            console.log(`API sign-in - Definindo cookie de autenticação: ${name} para domínio: ${cookieOptions.domain || 'atual'}`);
          }
          
          try {
            // Em ambiente de desenvolvimento, o Chrome tem problemas com domínio para localhost
            // Por isso removemos completamente a opção de domínio para ambientes locais
            if (fullDomain.includes('localhost') || fullDomain.includes('127.0.0.1')) {
              const { domain, ...cookieOptionsWithoutDomain } = cookieOptions;
              response.cookies.set(name, value, cookieOptionsWithoutDomain);
              console.log(`API sign-in - Cookie ${name} definido sem domínio específico para localhost`);
            } else {
              response.cookies.set(name, value, cookieOptions);
              console.log(`API sign-in - Cookie ${name} definido para domínio: ${cookieOptions.domain}`);
            }
          } catch (error) {
            console.error(`API sign-in - Erro ao definir cookie ${name}:`, error);
          }
        }
        
        console.log("API sign-in - Cookies aplicados à resposta com sucesso");
      } else {
        console.warn("API sign-in - Nenhum cookie de autenticação foi capturado durante o login");
        
        // Verificar se o usuário está autenticado usando a função centralizada
        const authCheck = await checkApiAuth();
        const isAuthenticated = authCheck.authenticated;
        
        console.log("API sign-in - Usuário ativo após cliente de autenticação:", isAuthenticated ? "Sim" : "Não");
        response.headers.set('X-Auth-Session-Backup', isAuthenticated ? 'true' : 'false');
      }
      
      // Usar o host diretamente como domínio para os cookies
      const fullDomain = host || 'localhost';
      
      console.log("API sign-in - Domínio configurado para cookies:", fullDomain);
      
      // Adicionar apenas os cabeçalhos essenciais para debug
      response.headers.set('X-Auth-Status', 'success');
      
      return response;
    }
    
    if (result.status) {
      return NextResponse.json(result, { status: result.status });
    }
    
    if (result.error) {
      return NextResponse.json(result, { status: 400 });
    }
    
    return NextResponse.json(result);
    
  } catch (error) {
    if (!(error instanceof Error && error.message.includes('Invalid login credentials'))) {
      console.error('Erro durante o login:', error);
    }
    
    return NextResponse.json(
      { 
        error: 'Falha ao processar o login', 
        error_code: 'server_error',
        details: error instanceof Error ? error.message : String(error)
      }, 
      { status: 500 }
    );
  }
} 