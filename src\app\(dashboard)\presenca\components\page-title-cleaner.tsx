'use client';

import { useEffect } from 'react';
import { usePageTitle } from '@/contexts/PageTitleContext';

export function PageTitleCleaner() {
  const { setPageTitle, setPageSubtitle } = usePageTitle();
  
  useEffect(() => {
    // Limpar qualquer título do contexto para permitir que o sistema dinâmico funcione
    setPageTitle(null);
    setPageSubtitle(null);
  }, [setPageTitle, setPageSubtitle]);
  
  return null;
} 