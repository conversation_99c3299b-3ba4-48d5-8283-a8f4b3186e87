import { getUserWelcomeData } from '@/services/auth/actions/index';
import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    console.log('API user-welcome-data - Iniciando requisição');
    
    // Verificar se há cookies relacionados à autenticação
    const cookieStore = await cookies();
    const cookieNames = cookieStore.getAll().map((c: any) => c.name);
    console.log('API user-welcome-data - Cookies:', cookieNames.join(', '));
    
    // Verificar se há cookies do Supabase
    const supabaseCookies = cookieNames.filter(name => 
      name.startsWith('sb-') || 
      name === 'supabase-auth-token'
    );
    
    if (supabaseCookies.length === 0) {
      console.warn('API user-welcome-data - Nenhum cookie Supabase encontrado');
    } else {
      console.log('API user-welcome-data - Cookies Supabase:', supabaseCookies.join(', '));
    }
    
    // Verificar headers da requisição
    const authorization = request.headers.get('authorization');
    console.log('API user-welcome-data - Header Authorization:', authorization ? 'Presente' : 'Ausente');
    
    // Obter dados do usuário
    const data = await getUserWelcomeData();
    console.log('API user-welcome-data - Resposta:', JSON.stringify(data));
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('API user-welcome-data - Erro ao obter dados do usuário:', error);
    return NextResponse.json(
      { 
        error: 'Falha ao obter dados do usuário',
        details: error instanceof Error ? error.message : String(error)
      }, 
      { status: 500 }
    );
  }
} 