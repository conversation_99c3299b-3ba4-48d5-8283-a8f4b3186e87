'use client';

import { createContext, useContext, ReactNode } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { CACHE_KEYS } from '@/constants/cache-keys';

interface ClassQueryContextValue {
  prefetchClasses: (groupId: string, filters: Record<string, any>) => void;
  invalidateClassQueries: (groupId: string) => void;
  invalidateAllGroupQueries: (groupId: string) => void;
}

const ClassQueryContext = createContext<ClassQueryContextValue | undefined>(undefined);

interface ClassQueryProviderProps {
  children: ReactNode;
}

export function ClassQueryProvider({ children }: ClassQueryProviderProps) {
  const queryClient = useQueryClient();

  const prefetchClasses = (groupId: string, filters: Record<string, any>) => {
    queryClient.prefetchQuery({
      queryKey: CACHE_KEYS.CLASS_GROUPS.CLASSES(groupId, filters),
      staleTime: 30 * 1000, // 30 segundos
    });
  };

  const invalidateClassQueries = (groupId: string) => {
    // Invalidar todas as queries de aulas independente dos filtros
    queryClient.invalidateQueries({
      predicate: (query) => {
        const [prefix, id] = query.queryKey;
        return prefix === 'class-group-classes' && id === groupId;
      },
    });
    
    // Invalidar estatísticas
    queryClient.invalidateQueries({
      queryKey: CACHE_KEYS.CLASS_GROUPS.CLASS_STATS(groupId),
    });
  };

  const invalidateAllGroupQueries = (groupId: string) => {
    // Invalidar todas as queries relacionadas ao grupo
    queryClient.invalidateQueries({
      predicate: (query) => {
        const [prefix, id] = query.queryKey;
        return (
          (prefix === 'class-group-classes' && id === groupId) ||
          (prefix === 'class-group-class-stats' && id === groupId) ||
          (prefix === 'class-group-info' && id === groupId) ||
          (prefix === 'class-group-instructors' && id === groupId)
        );
      },
    });
  };

  const value: ClassQueryContextValue = {
    prefetchClasses,
    invalidateClassQueries,
    invalidateAllGroupQueries,
  };

  return (
    <ClassQueryContext.Provider value={value}>
      {children}
    </ClassQueryContext.Provider>
  );
}

export function useClassQuery() {
  const context = useContext(ClassQueryContext);
  if (!context) {
    throw new Error('useClassQuery must be used within a ClassQueryProvider');
  }
  return context;
} 