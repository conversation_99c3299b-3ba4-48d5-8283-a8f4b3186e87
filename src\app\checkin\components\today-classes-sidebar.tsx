'use client';

import { useState, useEffect } from 'react';
import { Users } from 'lucide-react';;
import { getTodayClasses, type ClassItem } from '../actions/sidebar-actions';
import { DotLottieReact } from '@lottiefiles/dotlottie-react';

interface TodayClassesSidebarProps {
  onShowStudents: (classId: string, className: string) => void;
}

export function TodayClassesSidebar({ onShowStudents }: TodayClassesSidebarProps) {
  const [activeClasses, setActiveClasses] = useState<ClassItem[]>([]);
  const [upcomingClasses, setUpcomingClasses] = useState<ClassItem[]>([]);
  const [completedClasses, setCompletedClasses] = useState<ClassItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadTodayClasses = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await getTodayClasses();
      
      if (result.success && result.data) {
        setActiveClasses(result.data.activeClasses);
        setUpcomingClasses(result.data.upcomingClasses);
        setCompletedClasses(result.data.completedClasses);
      } else {
        setError(result.error || 'Erro ao carregar aulas');
      }
    } catch (err) {
      setError('Erro inesperado ao carregar aulas');
      console.error('Erro ao carregar aulas:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadTodayClasses();
    
    // Atualizar dados a cada 30 segundos
    const interval = setInterval(loadTodayClasses, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const renderClassItem = (classItem: ClassItem, isActive: boolean) => (
    <li key={classItem.id}>
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center">
            <span
              className={`w-2 h-2 rounded-full mr-2 ${
                isActive
                  ? 'bg-emerald-500'
                  : classItem.status === 'completed'
                  ? 'bg-gray-400'
                  : 'bg-slate-500 dark:bg-gray-400'
              }`}
            />
            <span className="font-bold text-slate-700 dark:text-gray-200">
              {classItem.time} - {classItem.endTime}
            </span>
          </div>
          <div className="pl-4">
            <p className="text-slate-600 dark:text-gray-300">
              {classItem.name}
            </p>
            <p className="text-sm text-slate-500 dark:text-gray-400">
              {classItem.duration}
            </p>
          </div>
        </div>
        {(isActive || classItem.status === 'completed') &&
          classItem.studentsCount > 0 && (
            <button
              onClick={() => onShowStudents(classItem.id, classItem.name)}
              className={`text-white px-2 py-1 rounded-md flex items-center text-sm cursor-pointer transition-colors ${
                isActive
                  ? 'bg-teal-500 dark:bg-teal-600 hover:bg-teal-600 dark:hover:bg-teal-700'
                  : 'bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-700'
              }`}
            >
              <Users className="w-4 h-4 mr-1" />
              <span className="font-bold">{classItem.studentsCount}</span>
            </button>
          )}
      </div>
    </li>
  );

  const renderLoadingSkeleton = () => (
    <div className="space-y-4">
      {[...Array(3)].map((_, index) => (
        <div key={index} className="animate-pulse">
          <div className="flex items-center">
            <div className="w-2 h-2 rounded-full bg-gray-300 dark:bg-gray-600 mr-2"></div>
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-16"></div>
          </div>
          <div className="pl-4 mt-1 space-y-1">
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-24"></div>
            <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-20"></div>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="bg-gradient-to-br from-slate-50 to-slate-100 dark:from-gray-800 dark:to-gray-900 border border-slate-200 dark:border-gray-700 rounded-3xl p-6 shadow-sm">
      <h2 className="text-lg font-semibold text-slate-800 dark:text-gray-100 mb-6">Aulas Hoje</h2>
      <div className="h-64">
        {isLoading ? (
          renderLoadingSkeleton()
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-full text-center py-8">
            <div className="w-24 h-24 rounded-full flex items-center justify-center bg-red-100 dark:bg-red-900/20 mb-3">
              <div className="text-red-500 text-xl">⚠️</div>
            </div>
            <p className="text-red-500 dark:text-red-400 text-sm">
              {error}
            </p>
            <button
              onClick={loadTodayClasses}
              className="mt-2 text-xs text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
            >
              Tentar novamente
            </button>
          </div>
        ) : (
          <>
            {activeClasses.length > 0 && (
              <div className="mb-6">
                <p className="text-sm font-semibold text-emerald-500 dark:text-emerald-400 mb-2">Em Progresso</p>
                <ul className="space-y-3">
                  {activeClasses.map(classItem => renderClassItem(classItem, true))}
                </ul>
              </div>
            )}
            
            {upcomingClasses.length > 0 && (
              <div className={activeClasses.length > 0 ? "mb-6" : ""}>
                <p className="text-sm font-semibold text-orange-500 dark:text-orange-400 mb-2">Próximas</p>
                <ul className="space-y-3">
                  {upcomingClasses.map(classItem => renderClassItem(classItem, false))}
                </ul>
              </div>
            )}
            
            {completedClasses.length > 0 && (
              <div>
                <p className="text-sm font-semibold text-gray-500 dark:text-gray-400 mb-2">Finalizadas</p>
                <ul className="space-y-3">
                  {completedClasses.slice(0, 2).map(classItem => renderClassItem(classItem, false))}
                </ul>
              </div>
            )}
            
            {activeClasses.length === 0 && upcomingClasses.length === 0 && completedClasses.length === 0 && (
              <div className="flex flex-col items-center justify-center h-full text-center py-8">
                <div className="w-24 h-24 rounded-full flex items-center justify-center dark:bg-white/10">
                <DotLottieReact
                  src="/calendar.lottie"
                  loop
                  autoplay
                  style={{ width: '100%', height: '100%' }}
                />
                </div>
                <p className="text-slate-500 dark:text-gray-400 text-sm mt-3">
                  Nenhuma aula agendada para hoje
                </p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
} 