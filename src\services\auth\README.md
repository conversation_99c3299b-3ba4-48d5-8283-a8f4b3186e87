# Server Actions para Autenticação

Este diretório contém as server actions para autenticação do usuário, permitindo verificar e gerenciar o estado de autenticação sem expor chaves de API do Supabase no cliente.

## Benefícios

- **Segurança**: As chaves de API do Supabase não são expostas para o cliente
- **Simplicidade**: API mais simples para verificar autenticação tanto no servidor quanto no cliente
- **Performance**: Menos código no cliente, reduzindo o tamanho do bundle
- **Manutenção**: Centralização da lógica de autenticação

## Server Actions Disponíveis

### `getCurrentUser()`

Verifica se o usuário está autenticado e retorna o objeto do usuário ou null:

```tsx
import { getCurrentUser } from '@/services/auth/actions/auth-actions';

// Em um Server Component:
const user = await getCurrentUser();

if (user) {
  // Usuário autenticado
} else {
  // Usuário não autenticado
}
```

### `requireAuth(redirectTo = '/login')`

Verifica se o usuário está autenticado e redireciona se não estiver:

```tsx
import { requireAuth } from '@/services/auth/actions/auth-actions';

// Em um layout ou page protegida
const { user } = await requireAuth('/login');

// Código só executado se o usuário estiver autenticado
```

### `logoutUser()`

Faz logout do usuário e redireciona para a página de login:

```tsx
import { logoutUser } from '@/services/auth/actions/auth-actions';

// Em um botão de logout
await logoutUser();
```

## Server Actions para APIs

### `checkApiAuth()`

Verifica se o usuário está autenticado e retorna um objeto apropriado para APIs:

```tsx
import { checkApiAuth } from '@/services/auth/actions/api-auth-actions';

export async function GET(request: NextRequest) {
  const authResult = await checkApiAuth();
  
  if (!authResult.authenticated) {
    return authResult.response; // Retorna resposta 401 já formatada
  }
  
  // Continuar com o código da API para usuários autenticados
  const user = authResult.user; // Usuário autenticado disponível aqui
}
```

### `checkApiAuthAndPermission()`

Verifica autenticação e permissão em uma só chamada, ideal para APIs:

```tsx
import { checkApiAuthAndPermission } from '@/services/auth/actions/api-auth-actions';
import { getPermissionService } from '@/services/permissions/service';

export async function GET(request: NextRequest, { params }: { params: { userId: string } }) {
  const { userId } = params;
  const permissionService = getPermissionService();
  
  const authResult = await checkApiAuthAndPermission(
    userId,        // ID do recurso sendo acessado
    'profile',     // Tipo de recurso 
    'view',        // Ação sendo realizada
    permissionService
  );
  
  if (authResult.response) {
    return authResult.response; // Retorna 401 ou 403 já formatado
  }
  
  // Continuar com o código da API para usuários autorizados
  const user = authResult.user; // Usuário autenticado disponível aqui
}
```

## Uso no Cliente (com React Hooks)

Para usar autenticação no cliente, utilize o hook `useAuth`:

```tsx
'use client';

import { useAuth } from '@/hooks/user/Auth/useAuth';

export default function MeuComponente() {
  const { user, loading, isAuthenticated, checkAuth, logout } = useAuth();
  
  if (loading) {
    return <div>Carregando...</div>;
  }
  
  if (!isAuthenticated) {
    return <div>Você precisa estar logado para acessar esta página</div>;
  }
  
  return (
    <div>
      <h1>Bem-vindo, {user.email}</h1>
      <button onClick={logout}>Logout</button>
    </div>
  );
}
```

## Uso no Middleware

Para usar autenticação no middleware, utilize as funções específicas:

```tsx
import { authMiddleware } from '@/app/middleware-auth';

export async function middleware(request: NextRequest) {
  // Verificar autenticação de forma segura
  return await authMiddleware(request);
}
```

## Recomendações

1. Em componentes de servidor, use `requireAuth()` para verificar autenticação
2. Em componentes cliente, use o hook `useAuth()` para verificar autenticação
3. Para APIs, use `checkApiAuth()` ou `checkApiAuthAndPermission()` 
4. Para rotas que exigem permissões específicas, combine com o sistema de permissões

## Fluxo de Autenticação

1. O usuário faz login através das rotas de autenticação
2. O Supabase cria uma sessão e armazena em cookies
3. As Server Actions leem esses cookies para verificar a autenticação
4. O estado de autenticação é transmitido de forma segura para o cliente

## Segurança

- As chaves de API do Supabase nunca são expostas para o cliente
- Todas as verificações de autenticação ocorrem no servidor
- Os cookies de sessão são protegidos e não manipulados diretamente 