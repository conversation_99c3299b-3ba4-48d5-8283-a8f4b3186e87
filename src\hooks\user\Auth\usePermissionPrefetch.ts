'use client';

import { useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { getPermissionClientService } from '@/services/permissions/client-service';
import { prefetchPermissionContextOnLogin } from '@/services/auth/actions/permission-prefetch';

export function usePermissionPrefetch() {
  const queryClient = useQueryClient();
  
  const prefetchPermissions = useCallback(async (userId: string) => {
    if (!userId) return false;
    
    try {
      console.log('[PermissionPrefetch] Iniciando prefetch de permissões para usuário:', userId);
      
      // 1. Primeiro carregar o contexto via server action
      const serverResult = await prefetchPermissionContextOnLogin(userId);
      console.log('[PermissionPrefetch] Resultado da server action:', serverResult ? 'Sucesso' : 'Falha');
      
      // 2. Em seguida, pré-carregar no cache do cliente
      const permissionService = getPermissionClientService();
      permissionService.setQueryClient(queryClient);
      const clientResult = await permissionService.prefetchPermissionContext(userId);
      console.log('[PermissionPrefetch] Resultado do cache do cliente:', clientResult ? 'Sucesso' : 'Falha');
      
      return true;
    } catch (error) {
      console.error('[PermissionPrefetch] Erro ao pré-carregar permissões:', error);
      return false;
    }
  }, [queryClient]);
  
  // Função para invalidar o cache de permissões
  const invalidatePermissions = useCallback(() => {
    const permissionService = getPermissionClientService();
    permissionService.invalidatePermissionCache();
    console.log('[PermissionPrefetch] Cache de permissões invalidado');
  }, []);
  
  return {
    prefetchPermissions,
    invalidatePermissions
  };
} 