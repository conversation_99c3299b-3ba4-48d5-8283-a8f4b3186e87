/**
 * Dashboard Financeiro - Exports Principais
 * Centraliza todas as exportações do módulo de dashboard
 */

// ============================================================================
// COMPONENTES
// ============================================================================

export { 
  FinancialDashboard,
  DashboardLayout,
  DashboardFilters,
  LoadingStates
} from './components';

// ============================================================================
// ACTIONS (SERVER FUNCTIONS)
// ============================================================================

export {
  getDashboardData,
  getDashboardKPIs,
  getRevenueMetrics,
  getExpenseMetrics,
  getCashFlowMetrics,
  getStudentMetrics
} from './actions/dashboard-actions';

// ============================================================================
// TIPOS
// ============================================================================

export type {
  // Principais
  DashboardData,
  FinancialKPIs,
  DashboardFilters as DashboardFiltersType,
  DateRange,
  PeriodType,

  // Métricas específicas
  FinancialRevenueMetrics,
  ExpenseMetrics,
  CashFlowMetrics,
  StudentMetrics,

  // Componentes
  FinancialDashboardProps,
  DashboardFiltersProps,
  DashboardLayoutProps,

  // Utilitários
  MetricWithGrowth,
  TrendDirection,
  LoadingStates as LoadingStatesType,
  DashboardActionResult,
  FormatOptions,
  DashboardConfig
} from './types/dashboard-types';

// ============================================================================
// UTILITÁRIOS
// ============================================================================

export {
  // Formatação
  formatCurrency,
  formatNumber,
  formatPercentage,
  
  // Cálculos
  calculateGrowth,
  getTrendDirection,
  createMetricWithGrowth,
  
  // Datas
  getDateRangeForPeriod,
  getPreviousPeriodRange,
  formatDateRange,
  
  // Validação
  isValidNumber,
  ensureNumber,
  isValidDate,
  
  // Cores e UI
  getTrendColor,
  getTrendIcon,
  
  // Performance
  debounce,
  throttle
} from './utils/dashboard-utils';

// ============================================================================
// CONSTANTES E CONFIGURAÇÕES
// ============================================================================

export const DASHBOARD_CONFIG = {
  DEFAULT_PERIOD: 'month' as const,
  DEFAULT_REFRESH_INTERVAL: 5 * 60 * 1000, // 5 minutos
  ENABLE_REAL_TIME: false,
  ENABLE_NOTIFICATIONS: true,
  
  LAYOUT: {
    SHOW_KPIS: true,
    SHOW_CHARTS: true,
    SHOW_TABLES: true
  },
  
  PERIODS: [
    { value: 'month', label: 'Mês Atual' },
    { value: 'quarter', label: 'Trimestre Atual' },
    { value: 'semester', label: 'Semestre Atual' },
    { value: 'year', label: 'Ano Atual' },
    { value: 'custom', label: 'Período Personalizado' }
  ] as const
} as const;

// ============================================================================
// VERSÃO E METADADOS
// ============================================================================

export const DASHBOARD_VERSION = {
  VERSION: '1.0.0',
  PHASE: 'Fase 1 - Estrutura Base',
  RELEASE_DATE: '2024-01-22',
  FEATURES: [
    'Componente FinancialDashboard principal',
    'Layout responsivo com seções organizadas',
    'Sistema de filtros de período',
    'Estados de loading (skeletons)',
    'KPIs principais com dados reais',
    'Integração com dados do Supabase'
  ],
  NEXT_FEATURES: [
    'Gráficos de receitas e despesas',
    'Análise de fluxo de caixa',
    'Métricas de retenção de alunos',
    'Comparações históricas',
    'Exportação de relatórios',
    'Drill-down em gráficos'
  ]
} as const;
