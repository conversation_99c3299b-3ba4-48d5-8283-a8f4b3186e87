'use client';

import { ReactNode, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { cacheService } from '@/services/cache';
import { isUserAdmin } from '@/services/user/user-client';
import { getPermissionClientService } from '@/services/permissions/client-service';
import { CACHE_KEYS } from '@/constants/cache-keys';
import { usePathname } from 'next/navigation';

interface CacheProviderProps {
  children: ReactNode;
}

/**
 * Provider para inicializar o serviço de cache centralizado
 * e configurar listeners de eventos
 */
export function CacheProvider({ children }: CacheProviderProps) {
  const queryClient = useQueryClient();
  const pathname = usePathname();
  
  // Efeito para monitorar navegação e limpar caches quando necessário
  useEffect(() => {
    // Verificar se estamos saindo de uma página de perfil
    const isLeavingProfilePage = (prevPath: string) => {
      return prevPath && prevPath.includes('/perfil/') && !pathname.includes('/perfil/');
    };
    
    let previousPath = pathname;
    
    // Função para verificar mudanças de rota
    const checkRouteChange = () => {
      if (isLeavingProfilePage(previousPath)) {
        console.log('[CACHE] Detectada navegação para fora da página de perfil, limpando caches');
        
        // Extrair userId do caminho anterior
        const match = previousPath.match(/\/perfil\/([^\/]+)/);
        if (match && match[1]) {
          const userId = match[1];
          
          // Forçar limpeza de todos os caches relacionados a este perfil (v5 format)
          queryClient.removeQueries({ queryKey: [CACHE_KEYS.USER_PROFILE[0], userId] });
          queryClient.removeQueries({ queryKey: CACHE_KEYS.VIEWED_USER_PROFILE(userId) });
          
          console.log(`[CACHE] Caches do perfil ${userId} removidos`);
        }
      }
      
      previousPath = pathname;
    };
    
    // Executar verificação quando o pathname mudar
    checkRouteChange();
    
    return () => {
      // Nada a limpar
    };
  }, [pathname, queryClient]);
  
  useEffect(() => {
    // 1. Inicializar o serviço de cache centralizado
    cacheService.initialize(queryClient);
    
    // 2. Inicializar serviço de permissões com o queryClient
    const permissionService = getPermissionClientService();
    permissionService.setQueryClient(queryClient);
    
    // 3. Configurar listeners para eventos de cache
    cacheService.setupCacheListeners();
    
    // 4. Pré-carregar dados importantes
    const prefetchImportantData = async () => {
      try {
        await queryClient.prefetchQuery({
          queryKey: CACHE_KEYS.USER_ADMIN_STATUS,
          queryFn: isUserAdmin,
          staleTime: 5 * 60 * 1000, // 5 minutos
        });
      } catch (error) {
        console.error('Erro ao pré-carregar status de admin:', error);
      }
    };
    
    prefetchImportantData();
    
    // 5. Configurar eventos para limpeza de cache
    const handleLogout = () => {
      permissionService.invalidatePermissionCache();
      queryClient.invalidateQueries({ queryKey: CACHE_KEYS.USER_METADATA });
    };
    
    // Adicionar evento para ouvir logout
    window.addEventListener('app:logout', handleLogout);
    
    // 6. Verificar mudanças de sessão em abas diferentes usando storage events
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'supabase.auth.token' && !e.newValue) {
        // Token removido, provavelmente um logout em outra aba
        handleLogout();
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    console.log('[CacheProvider] Serviço de cache centralizado inicializado');
    
    return () => {
      window.removeEventListener('app:logout', handleLogout);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [queryClient]);
  
  return <>{children}</>;
} 