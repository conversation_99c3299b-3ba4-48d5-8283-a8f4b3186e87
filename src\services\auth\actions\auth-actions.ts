'use server'

import { createClient } from '@/services/supabase/server'
import { redirect } from 'next/navigation'
import { clearAuthCookies } from '@/services/auth/utils/server-utils'
import { cache } from 'react'

/**
 * Server action para verificar se o usuário está autenticado
 * Retorna o usuário se estiver autenticado, null caso contrário
 * Utiliza React cache() para evitar chamadas redundantes
 */
export const getCurrentUser = cache(async () => {
  const supabase = await createClient()

  const {
    data: { user }
  } = await supabase.auth.getUser()

  return user
})

/**
 * Server action para verificar autenticação e redirecionar se não autenticado
 * @param redirectTo Caminho para redirecionar se não autenticado (padrão: /login)
 */
export async function requireAuth(redirectTo = '/login') {
  const user = await getCurrentUser()

  if (!user) {
    redirect(redirectTo)
  }

  return { user }
}

/**
 * Server action para fazer logout
 */
export async function logoutUser() {
  const supabase = await createClient()

  // Fazer logout no Supabase
  await supabase.auth.signOut()

  // Limpar os cookies relacionados à autenticação usando o utilitário
  const removedCookies = await clearAuthCookies()

  if (removedCookies.length > 0) {
    console.log(`Server Action - Cookies removidos: ${removedCookies.join(', ')}`)
  }

  // Também chamar a API para garantir que os cookies sejam removidos na resposta
  try {
    await fetch('/api/auth/sign-out', {
      method: 'POST',
      cache: 'no-store',
      keepalive: true
    }).catch(() => {
      /* silencia ECONNRESET */
    })
  } catch (error) {
    console.error('Erro ao chamar API de logout:', error)
  }

  redirect('/login?signout=true')
}

/**
 * Server action para obter dados de boas-vindas do usuário
 */
export async function getUserWelcomeData() {
  const user = await getCurrentUser()

  if (!user) {
    return {
      displayName: 'Usuário',
      error: 'Usuário não autenticado'
    }
  }

  try {
    const supabase = await createClient()

    // Obter metadados do usuário
    const appMetadata = user.app_metadata || {}

    // Buscar informações adicionais do usuário na tabela users
    const { data: userData, error: userError } = await supabase.from('users').select('first_name, last_name, role, branch_id, tenant_id').eq('id', user.id).single()

    if (userError) {
      console.error('Erro ao buscar dados do usuário:', userError)
      // Se o erro indicar múltiplas ou nenhuma linhas, possivelmente há cookies inválidos
      if (userError.code === 'PGRST116') {
        return {
          displayName: user.email || 'Usuário',
          error: 'Sessão inválida – múltiplos cookies de autenticação detectados',
          error_code: 'invalid_cookies' as const
        }
      }
    }

    // Buscar nome do tenant
    let tenantName = ''
    if (userData?.tenant_id || appMetadata.tenant_id) {
      const tenantId = userData?.tenant_id || appMetadata.tenant_id
      const { data: tenantData } = await supabase.from('tenants').select('name').eq('id', tenantId).single()

      tenantName = tenantData?.name || ''
    }

    // Buscar informações de faixa baseado no role do usuário
    let belt = null
    let stripes = null
    let beltLabel = null
    let beltData = null
    const userRole = userData?.role || appMetadata.role || ''

    if (userRole === 'student') {
      // Buscar detalhes da faixa atual via RPC dedicado
      const { data: studentRow } = await supabase.from('students').select('id').eq('user_id', user.id).single()

      if (studentRow) {
        const { data: beltDetails, error: beltError } = await supabase.rpc('get_student_current_belt_details', { student_id_param: studentRow.id })

        if (!beltError && beltDetails && beltDetails.length > 0) {
          const details = beltDetails[0]
          console.log('[AuthAction DEBUG] Raw belt details from RPC (Student):', details)

          const color = details.color || details.belt_color
          const degree = details.degree || 0
          const label = details.label || null
          const stripeColor = details.stripeColor || details.stripe_color || null
          const showCenterLine = details.showCenterLine ?? details.show_center_line ?? false
          const centerLineColor = details.centerLineColor || details.center_line_color || null

          belt = color
          stripes = degree
          beltLabel = label
          
          // NOVO FALLBACK: se o label veio nulo ou vazio, buscar na tabela belt_levels
          if (!beltLabel) {
            try {
              const { data: fallbackBelt } = await supabase
                .from('belt_levels')
                .select('label')
                .eq('belt_color', color)
                .eq('degree', degree)
                .single()

              if (fallbackBelt?.label) {
                beltLabel = fallbackBelt.label
              }
            } catch (fallbackError) {
              console.warn('[AuthAction DEBUG] Falha ao buscar label na tabela belt_levels como fallback:', fallbackError)
            }
          }

          beltData = {
            belt_color: color,
            stripe_color: stripeColor,
            label: beltLabel || '',
            degree: degree,
            show_center_line: showCenterLine,
            center_line_color: centerLineColor
          }
        } else if (!beltError && (!beltDetails || beltDetails.length === 0)) {
          // Se não encontrou detalhes da faixa via RPC, buscar diretamente na tabela belt_levels
          console.log('[AuthAction DEBUG] No belt details from RPC, trying to fetch from belt_levels')
          
          // Buscar a faixa atual do estudante
          const { data: currentBelt } = await supabase
            .from('student_belts')
            .select('belt_level_id')
            .eq('student_id', studentRow.id)
            .order('awarded_at', { ascending: false })
            .limit(1)
            .single()
          
          if (currentBelt) {
            // Buscar detalhes da faixa na tabela belt_levels
            const { data: beltLevel } = await supabase
              .from('belt_levels')
              .select('*')
              .eq('id', currentBelt.belt_level_id)
              .single()
            
            if (beltLevel) {
              console.log('[AuthAction DEBUG] Found belt level:', beltLevel)
              
              belt = beltLevel.belt_color
              stripes = beltLevel.degree
              beltLabel = beltLevel.label
              
              beltData = {
                belt_color: beltLevel.belt_color,
                stripe_color: beltLevel.stripe_color,
                label: beltLevel.label,
                degree: beltLevel.degree,
                show_center_line: beltLevel.show_center_line,
                center_line_color: beltLevel.center_line_color
              }
              
              console.log('[AuthAction DEBUG] Constructed beltData from belt_levels:', beltData)
            }
          }
        }
      }
    } else if (userRole === 'instructor') {
      // Buscar detalhes da faixa atual via RPC dedicado
      const { data: instructorRow } = await supabase.from('instructors').select('id').eq('user_id', user.id).single()

      if (instructorRow) {
        const { data: beltDetails, error: beltError } = await supabase.rpc('get_instructor_current_belt_details', { instructor_id_param: instructorRow.id })

        if (!beltError && beltDetails && beltDetails.length > 0) {
          const details = beltDetails[0]
          console.log('[AuthAction DEBUG] Raw belt details from RPC (Instructor):', details)

          const color = details.color || details.belt_color
          const degree = details.degree || 0
          const label = details.label || null
          const stripeColor = details.stripeColor || details.stripe_color || null
          const showCenterLine = details.showCenterLine ?? details.show_center_line ?? false
          const centerLineColor = details.centerLineColor || details.center_line_color || null

          belt = color
          stripes = degree
          beltLabel = label
          
          // NOVO FALLBACK: se o label veio nulo ou vazio, buscar na tabela belt_levels
          if (!beltLabel) {
            try {
              const { data: fallbackBelt } = await supabase
                .from('belt_levels')
                .select('label')
                .eq('belt_color', color)
                .eq('degree', degree)
                .single()

              if (fallbackBelt?.label) {
                beltLabel = fallbackBelt.label
              }
            } catch (fallbackError) {
              console.warn('[AuthAction DEBUG] Falha ao buscar label na tabela belt_levels como fallback:', fallbackError)
            }
          }

          beltData = {
            belt_color: color,
            stripe_color: stripeColor,
            label: beltLabel || '',
            degree: degree,
            show_center_line: showCenterLine,
            center_line_color: centerLineColor
          }
          console.log('[AuthAction DEBUG] Constructed beltData (Instructor):', beltData)
        } else if (!beltError && (!beltDetails || beltDetails.length === 0)) {
          // Se não encontrou detalhes da faixa via RPC, buscar diretamente na tabela belt_levels
          console.log('[AuthAction DEBUG] No belt details from RPC, trying to fetch from belt_levels')
          
          // Buscar a faixa atual do instrutor
          const { data: currentBelt } = await supabase
            .from('instructor_belts')
            .select('belt_level_id')
            .eq('instructor_id', instructorRow.id)
            .order('awarded_at', { ascending: false })
            .limit(1)
            .single()
          
          if (currentBelt) {
            // Buscar detalhes da faixa na tabela belt_levels
            const { data: beltLevel } = await supabase
              .from('belt_levels')
              .select('*')
              .eq('id', currentBelt.belt_level_id)
              .single()
            
            if (beltLevel) {
              console.log('[AuthAction DEBUG] Found belt level:', beltLevel)
              
              belt = beltLevel.belt_color
              stripes = beltLevel.degree
              beltLabel = beltLevel.label
              
              beltData = {
                belt_color: beltLevel.belt_color,
                stripe_color: beltLevel.stripe_color,
                label: beltLevel.label,
                degree: beltLevel.degree,
                show_center_line: beltLevel.show_center_line,
                center_line_color: beltLevel.center_line_color
              }
              
            }
          }
        }
      }
    }

    // Combinar dados do usuário
    const firstName = userData?.first_name || appMetadata.first_name || ''
    const lastName = userData?.last_name || appMetadata.last_name || ''
    const displayName = [firstName, lastName].filter(Boolean).join(' ') || user.email || 'Usuário'

    const beltDataJSON = beltData ? JSON.stringify(beltData) : null

    return {
      displayName,
      tenantName,
      belt,
      stripes,
      beltLabel,
      beltData: beltDataJSON,
      role: userRole
    } as const
  } catch (error) {
    console.error('Erro ao obter dados de boas-vindas:', error)
    return {
      displayName: user.email || 'Usuário',
      error: 'Erro ao obter dados completos',
      error_code: 'unknown_error' as const
    }
  }
}

/**
 * Server action para obter perfil completo do usuário
 * Esta função pode ser chamada pelo cliente
 */
export async function getUserProfile() {
  try {
    const user = await getCurrentUser()

    if (!user) {
      return { error: 'Usuário não autenticado' }
    }

    const appMetadata = user.app_metadata || {}

    let { avatar_url: metadataAvatarUrl, avatar_storage_path: metadataAvatarStoragePath, first_name: metadataFirstName, last_name: metadataLastName, role: metadataRole, branch_id: metadataBranchId, tenant_id: metadataTenantId } = appMetadata as any

    if (!metadataAvatarUrl) {
      try {
        const supabase = await createClient()
        const { data: userRow, error } = await supabase.from('users').select('avatar_url, avatar_storage_path, first_name, last_name, role, branch_id, tenant_id').eq('id', user.id).single()

        if (error) {
          console.warn('[getUserProfile] Falha ao obter dados da tabela users:', error.message)
        }

        if (userRow) {
          metadataAvatarUrl = metadataAvatarUrl || userRow.avatar_url
          metadataAvatarStoragePath = metadataAvatarStoragePath || userRow.avatar_storage_path
          metadataFirstName = metadataFirstName || userRow.first_name
          metadataLastName = metadataLastName || userRow.last_name
          metadataRole = metadataRole || userRow.role
          metadataBranchId = metadataBranchId || userRow.branch_id
          metadataTenantId = metadataTenantId || userRow.tenant_id
        }
      } catch (fetchError) {
        console.error('[getUserProfile] Erro inesperado ao buscar fallback de avatar_url:', fetchError)
      }
    }

    const fullName = `${metadataFirstName || ''}${metadataLastName ? ' ' + metadataLastName : ''}`

    return {
      data: {
        id: appMetadata.id || user.id,
        email: appMetadata.email || user.email,
        firstName: metadataFirstName || '',
        lastName: metadataLastName || '',
        fullName,
        role: metadataRole || 'student',
        avatarUrl: metadataAvatarUrl,
        avatarStoragePath: metadataAvatarStoragePath,
        branchId: metadataBranchId,
        tenantId: metadataTenantId
      }
    }
  } catch (error: any) {
    console.error('Erro inesperado ao buscar perfil do usuário:', error)
    return { error: error.message || 'Erro inesperado ao buscar perfil do usuário', data: null }
  }
}
