'use server';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';
import { createClient } from '@/services/supabase/server';
import { getCurrentUser } from '@/services/auth/actions/auth-actions';
import { UpdateClassSchema } from '../../../../aulas/actions/schemas/class';
import type { UpdateClass } from '../../../../aulas/actions/schemas/class';

/**
 * Atualiza uma aula específica de uma turma
 */
export async function updateClassInGroup(
  classId: string,
  groupId: string,
  data: unknown
) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    // Validar dados de entrada
    const validationResult = UpdateClassSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const updateData = validationResult.data;
    const supabase = await createClient();

    // Verificar se a aula existe e pertence ao tenant e à turma
    const { data: existingClass, error: existingError } = await supabase
      .from("classes")
      .select("id, status, attendance_recorded, class_group_id, instructor_id, start_time, end_time")
      .eq("id", classId)
      .eq("tenant_id", tenantId)
      .eq("class_group_id", groupId)
      .single();

    if (existingError || !existingClass) {
      return { success: false, errors: { _form: "Aula não encontrada ou não pertence a esta turma" } };
    }

    // Verificar se a aula pode ser editada
    if (existingClass.status === "completed") {
      return { 
        success: false, 
        errors: { _form: "Não é possível editar aulas concluídas" } 
      };
    }

    if (existingClass.attendance_recorded) {
      return { 
        success: false, 
        errors: { _form: "Não é possível editar aulas com presença já registrada" } 
      };
    }

    // Se há mudança de instrutor, verificar se é válido
    if (updateData.instructor_id && updateData.instructor_id !== existingClass.instructor_id) {
      const { data: instructor, error: instructorError } = await supabase
        .from("users")
        .select("id, role, status")
        .eq("id", updateData.instructor_id)
        .eq("tenant_id", tenantId)
        .single();

      if (instructorError || !instructor || instructor.role !== "instructor" || instructor.status !== "active") {
        return { success: false, errors: { instructor_id: "Instrutor não encontrado ou inativo" } };
      }

      // Verificar conflitos de horário se mudando instrutor ou horário
      const startTime = updateData.start_time ? new Date(updateData.start_time) : new Date(existingClass.start_time);
      const endTime = updateData.end_time ? new Date(updateData.end_time) : new Date(existingClass.end_time);
      
      const { data: conflictingClasses, error: conflictError } = await supabase
        .from("classes")
        .select("id, name, start_time, end_time")
        .eq("instructor_id", updateData.instructor_id)
        .eq("tenant_id", tenantId)
        .neq("id", classId) // Excluir a própria aula
        .in("status", ["scheduled", "ongoing"])
        .or(`and(start_time.lte.${endTime.toISOString()},end_time.gte.${startTime.toISOString()})`);

      if (!conflictError && conflictingClasses && conflictingClasses.length > 0) {
        return { 
          success: false, 
          errors: { 
            instructor_id: "Instrutor já possui aula agendada neste horário",
            _conflicts: conflictingClasses 
          } 
        };
      }
    }

    // Verificar conflitos de horário se apenas o horário mudou (mesmo instrutor)
    if ((updateData.start_time || updateData.end_time) && !updateData.instructor_id) {
      const startTime = updateData.start_time ? new Date(updateData.start_time) : new Date(existingClass.start_time);
      const endTime = updateData.end_time ? new Date(updateData.end_time) : new Date(existingClass.end_time);
      
      const { data: conflictingClasses, error: conflictError } = await supabase
        .from("classes")
        .select("id, name, start_time, end_time")
        .eq("instructor_id", existingClass.instructor_id)
        .eq("tenant_id", tenantId)
        .neq("id", classId)
        .in("status", ["scheduled", "ongoing"])
        .or(`and(start_time.lte.${endTime.toISOString()},end_time.gte.${startTime.toISOString()})`);

      if (!conflictError && conflictingClasses && conflictingClasses.length > 0) {
        return { 
          success: false, 
          errors: { 
            start_time: "Instrutor já possui aula agendada neste horário",
            _conflicts: conflictingClasses 
          } 
        };
      }
    }

    // Se há mudança de filial, verificar se é válida
    if (updateData.branch_id) {
      const { data: branch, error: branchError } = await supabase
        .from("branches")
        .select("id, deleted_at")
        .eq("id", updateData.branch_id)
        .eq("tenant_id", tenantId)
        .single();

      if (branchError || !branch || branch.deleted_at !== null) {
        return { success: false, errors: { branch_id: "Filial não encontrada ou inativa" } };
      }
    }

    // Preparar dados para atualização
    const updatePayload: any = {
      ...updateData,
      updated_at: new Date().toISOString(),
    };

    // Converter datas para ISO se presentes
    if (updateData.start_time) {
      updatePayload.start_time = new Date(updateData.start_time).toISOString();
    }
    if (updateData.end_time) {
      updatePayload.end_time = new Date(updateData.end_time).toISOString();
    }

    // Atualizar a aula
    const { data: updatedClass, error } = await supabase
      .from("classes")
      .update(updatePayload)
      .eq("id", classId)
      .eq("tenant_id", tenantId)
      .eq("class_group_id", groupId)
      .select(`
        *,
        instructor:users!classes_instructor_id_fkey(
          id,
          first_name,
          last_name,
          full_name
        ),
        branch:branches!classes_branch_id_fkey(
          id,
          name
        ),
        class_group:class_groups!classes_class_group_id_fkey(
          id,
          name,
          category
        )
      `)
      .single();

    if (error) {
      console.error("Erro ao atualizar aula:", error);
      return { success: false, errors: { _form: "Erro ao atualizar aula" } };
    }

    // Revalidar caches
    revalidatePath(`/turmas/${groupId}/aulas`);
    revalidatePath(`/turmas/${groupId}/aulas/editar/${classId}`);
    revalidatePath("/aulas");

    return { 
      success: true, 
      data: updatedClass,
      message: "Aula atualizada com sucesso"
    };
  } catch (error) {
    console.error("Erro ao atualizar aula:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Atualiza uma aula e redireciona para a listagem
 */
export async function updateClassAndRedirect(
  classId: string,
  groupId: string,
  data: unknown
) {
  const result = await updateClassInGroup(classId, groupId, data);
  
  if (result.success) {
    redirect(`/turmas/${groupId}/aulas`);
  }
  
  return result;
} 