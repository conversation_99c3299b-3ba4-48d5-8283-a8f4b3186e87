'use client';

import { createClient } from '@/services/supabase/client/client';

// Função helper para chamar server actions
async function callServerAction<T>(actionPromise: Promise<T>): Promise<T | null> {
  try {
    return await actionPromise;
  } catch (error) {
    console.error('Erro ao chamar server action:', error);
    return null;
  }
}

// Verificar silenciosamente se há uma sessão ativa
export async function checkAuthSession() {
  try {
    const supabase = createClient();
    const { data } = await supabase.auth.getSession();
    return data?.session !== null;
  } catch (error) {
    return false;
  }
}

interface GetUserProfileOptions {
  forceRefresh?: boolean;
}

export async function getUserProfileClient(options?: GetUserProfileOptions) {
  try {
    const forceRefresh = options?.forceRefresh || false;
    
    const lastLoginTime = typeof sessionStorage !== 'undefined' ? sessionStorage.getItem('last_login_time') : null;
    const isFreshLogin = typeof sessionStorage !== 'undefined' ? sessionStorage.getItem('is_fresh_login') === 'true' : false;
    const isRecentLogin = lastLoginTime ? (Date.now() - parseInt(lastLoginTime)) < 15000 : false;
    
    if (isFreshLogin || isRecentLogin || forceRefresh) {
      try {
        const supabase = createClient();
        const { data: refreshData } = await supabase.auth.refreshSession();
      } catch (refreshError) {
        console.warn('Erro ao tentar refresh de token:', refreshError);
      }
    }
    
    // Importar e chamar o server action para obter o perfil do usuário
    const { getUserProfile } = await import('@/services/auth/actions/auth-actions');
    const result = await callServerAction(getUserProfile());
    
    // Se ocorreu algum erro ou não tem dados, retornar o erro
    if (!result || 'error' in result) {
      return { 
        error: result?.error || 'Erro ao obter perfil do usuário',
        data: null 
      };
    }
    
    return result;
  } catch (error: any) {
    // Evitar logar erros em páginas de autenticação
    if (typeof window !== 'undefined' && 
       !(window.location.pathname.includes('/login') || 
         window.location.pathname.includes('/reset-password') || 
         window.location.pathname.includes('/auth/callback'))) {
      console.error("Erro inesperado ao buscar perfil do usuário:", error);
    }
    return { error: error.message || "Erro inesperado ao buscar perfil do usuário", data: null };
  }
}

// Hook customizado para verificar se o usuário é administrador (client-side only)
export async function isUserAdmin(): Promise<boolean> {
  try {
    // Primeiro, verifica se há uma sessão ativa sem logar erros
    const hasSession = await checkAuthSession();
    
    if (!hasSession) {
      return false;
    }
    
    const { data } = await getUserProfileClient();
    
    return data?.role === 'admin';
  } catch (error) {
    // Não logar erros em páginas de autenticação
    if (typeof window !== 'undefined' && 
       !(window.location.pathname.includes('/login') || 
         window.location.pathname.includes('/reset-password') || 
         window.location.pathname.includes('/auth/callback'))) {
      console.error("Erro ao verificar se usuário é admin:", error);
    }
    return false;
  }
} 