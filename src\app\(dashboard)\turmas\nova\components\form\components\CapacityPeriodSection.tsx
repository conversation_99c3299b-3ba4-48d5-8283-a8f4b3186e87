import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CalendarIcon, Clock, Users, Info, AlertTriangle, Infinity } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import type { FormSectionProps } from '../types/class-group-form-types';

export const CapacityPeriodSection = ({ form }: FormSectionProps) => {
  const watchedValues = form.watch();
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="w-5 h-5" />
          Capacidade e Período
        </CardTitle>
        <CardDescription>
          Configurações de capacidade e período de funcionamento
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Seção de Capacidade */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            <Label className="text-base font-semibold">Capacidade e Lista de Espera</Label>
          </div>
          
          {/* Checkbox para capacidade ilimitada */}
          <FormField
            control={form.control}
            name="unlimited_capacity"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={(checked) => {
                      field.onChange(checked);
                      // Se marcar capacidade ilimitada, limpar max_capacity
                      if (checked) {
                        form.setValue('max_capacity', null);
                      }
                    }}
                  />
                </FormControl>
                <FormLabel className="text-sm font-medium flex items-center gap-2">
                  <Infinity className="h-4 w-4" />
                  Capacidade ilimitada
                </FormLabel>
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="max_capacity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Capacidade Máxima</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder={watchedValues.unlimited_capacity ? "Ilimitado" : "Ex: 30"}
                      min={1}
                      max={1000}
                      disabled={watchedValues.unlimited_capacity}
                      value={watchedValues.unlimited_capacity ? '' : (field.value || '')}
                      onChange={(e) => {
                        if (!watchedValues.unlimited_capacity) {
                          const value = e.target.value;
                          field.onChange(value === '' ? null : Number(value) || null);
                        }
                      }}
                      className={cn(
                        watchedValues.unlimited_capacity && "bg-muted text-muted-foreground"
                      )}
                    />
                  </FormControl>
                  <FormDescription>
                    {watchedValues.unlimited_capacity 
                      ? "Capacidade ilimitada selecionada - este campo será ignorado"
                      : "Número máximo de alunos que podem ser matriculados neste grupo"
                    }
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="allow_waitlist"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Permitir Lista de Espera</FormLabel>
                  <div className="flex items-center gap-2">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <span className="text-sm text-muted-foreground">
                      {field.value 
                        ? 'Estudantes podem entrar na lista de espera' 
                        : 'Não permitir lista de espera'}
                    </span>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Alertas baseados na configuração */}
          {!watchedValues.allow_waitlist && !watchedValues.unlimited_capacity && watchedValues.max_capacity && watchedValues.max_capacity < 5 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Quando a lista de espera está desabilitada, a capacidade mínima deve ser de 5 alunos.
              </AlertDescription>
            </Alert>
          )}

          {watchedValues.unlimited_capacity && (
            <Alert>
              <Infinity className="h-4 w-4" />
              <AlertDescription>
                Com capacidade ilimitada, não há limite para o número de estudantes que podem se inscrever na turma.
              </AlertDescription>
            </Alert>
          )}

          {!watchedValues.unlimited_capacity && !watchedValues.max_capacity && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Defina uma capacidade máxima ou marque a opção de capacidade ilimitada.
              </AlertDescription>
            </Alert>
          )}

          {watchedValues.allow_waitlist && !watchedValues.unlimited_capacity && watchedValues.max_capacity && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Com a lista de espera habilitada, estudantes poderão se inscrever mesmo quando a capacidade máxima ({watchedValues.max_capacity} alunos) for atingida.
              </AlertDescription>
            </Alert>
          )}

          {watchedValues.allow_waitlist && watchedValues.unlimited_capacity && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Com capacidade ilimitada e lista de espera habilitada, todos os estudantes podem se inscrever livremente.
              </AlertDescription>
            </Alert>
          )}
        </div>

        <Separator />

        {/* Seção de Período */}
        <div className="space-y-2">
          <FormLabel>Período de Funcionamento</FormLabel>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="start_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Data de Início</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP", { locale: ptBR })
                          ) : (
                            <span>Selecione uma data</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value ? new Date(field.value) : undefined}
                        onSelect={(date) => field.onChange(date?.toISOString())}
                        disabled={(date) => date < new Date()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="end_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Data de Término</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP", { locale: ptBR })
                          ) : (
                            <span>Selecione uma data</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value ? new Date(field.value) : undefined}
                        onSelect={(date) => field.onChange(date?.toISOString())}
                        disabled={(date) => {
                          const startDate = form.getValues('start_date');
                          return startDate ? date <= new Date(startDate) : date < new Date();
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormDescription>
            Período em que o grupo estará ativo para matrículas (opcional)
          </FormDescription>
        </div>
      </CardContent>
    </Card>
  );
}; 