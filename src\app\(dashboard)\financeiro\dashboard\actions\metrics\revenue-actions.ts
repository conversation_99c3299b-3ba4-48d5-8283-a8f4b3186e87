"use server";

/**
 * Server Actions para métricas de receitas
 */

import { DashboardActionResult, FinancialRevenueMetrics, DateRange } from '../../types/dashboard-types';
import { ensureNumber } from '../../utils/dashboard-utils';
import { getAuthenticatedClient, formatDateForSQL } from '../shared/auth-utils';

/**
 * Busca métricas de receitas para um período
 */
export async function getRevenueMetrics(
  currentRange: DateRange,
  previousRange: DateRange
): Promise<DashboardActionResult<FinancialRevenueMetrics>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    // Query para período atual
    const { data: currentData, error: currentError } = await supabase
      .from('payments')
      .select('amount, status, payment_type, due_date, paid_at')
      .eq('tenant_id', tenantId)
      .gte('due_date', formatDateForSQL(currentRange.startDate))
      .lte('due_date', formatDateForSQL(currentRange.endDate));

    if (currentError) {
      return {
        success: false,
        error: `Erro ao buscar receitas: ${currentError.message}`
      };
    }

    // Calcular métricas do período atual
    const payments = currentData || [];
    const totalRevenue = payments
      .filter(p => p.status === 'paid')
      .reduce((sum, p) => sum + ensureNumber(p.amount), 0);

    const paidPayments = payments.filter(p => p.status === 'paid').length;
    const pendingPayments = payments.filter(p => p.status === 'pending').length;
    const overduePayments = payments.filter(p => p.status === 'overdue').length;
    
    const averagePayment = paidPayments > 0 ? totalRevenue / paidPayments : 0;

    // MRR (Monthly Recurring Revenue) - simplificado para MVP
    const monthlyRecurringRevenue = totalRevenue;

    // Growth será calculado quando tivermos dados do período anterior
    const revenueGrowth = 0; // TODO: Implementar comparação com período anterior

    const metrics: FinancialRevenueMetrics = {
      totalRevenue,
      paidPayments,
      pendingPayments,
      overduePayments,
      averagePayment,
      monthlyRecurringRevenue,
      revenueGrowth
    };

    return {
      success: true,
      data: metrics,
      message: 'Métricas de receitas obtidas com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}
