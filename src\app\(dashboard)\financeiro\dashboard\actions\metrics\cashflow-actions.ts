"use server";

/**
 * Server Actions para métricas de fluxo de caixa
 */

import {
  DashboardActionResult,
  CashFlowMetrics,
  DateRange,
  MonthlyCashFlowData,
  CashFlowProjectionData,
  CumulativeBalanceData,
  SeasonalityData,
  CashFlowAnalysisData
} from '../../types/dashboard-types';
import { ensureNumber, formatCurrency } from '../../utils/dashboard-utils';
import { getAuthenticatedClient, formatDateForSQL } from '../shared/auth-utils';

/**
 * Busca métricas de fluxo de caixa para um período
 */
export async function getCashFlowMetrics(
  currentRange: DateRange
): Promise<DashboardActionResult<CashFlowMetrics>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    // Buscar receitas pagas
    const { data: revenueData } = await supabase
      .from('payments')
      .select('amount')
      .eq('tenant_id', tenantId)
      .eq('status', 'paid')
      .gte('paid_at', formatDateForSQL(currentRange.startDate))
      .lte('paid_at', formatDateForSQL(currentRange.endDate));

    // Buscar despesas pagas
    const { data: expenseData } = await supabase
      .from('expenses')
      .select('amount')
      .eq('tenant_id', tenantId)
      .eq('status', 'paid')
      .gte('paid_at', formatDateForSQL(currentRange.startDate))
      .lte('paid_at', formatDateForSQL(currentRange.endDate));

    const totalInflows = (revenueData || [])
      .reduce((sum, r) => sum + ensureNumber(r.amount), 0);

    const totalOutflows = (expenseData || [])
      .reduce((sum, e) => sum + ensureNumber(e.amount), 0);

    const netCashFlow = totalInflows - totalOutflows;
    const currentBalance = netCashFlow; // Simplificado para MVP
    const projectedBalance = currentBalance; // TODO: Implementar projeção real

    const metrics: CashFlowMetrics = {
      currentBalance,
      totalInflows,
      totalOutflows,
      netCashFlow,
      projectedBalance,
      cashFlowTrend: netCashFlow > 0 ? 'up' : netCashFlow < 0 ? 'down' : 'stable'
    };

    return {
      success: true,
      data: metrics,
      message: 'Métricas de fluxo de caixa obtidas com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}

/**
 * Busca dados de fluxo de caixa mensal para gráficos
 */
export async function getMonthlyCashFlowData(
  currentRange: DateRange
): Promise<DashboardActionResult<MonthlyCashFlowData[]>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    // Buscar receitas por mês
    const { data: revenueData } = await supabase
      .from('payments')
      .select('amount, paid_at')
      .eq('tenant_id', tenantId)
      .eq('status', 'paid')
      .not('paid_at', 'is', null)
      .gte('paid_at', formatDateForSQL(currentRange.startDate))
      .lte('paid_at', formatDateForSQL(currentRange.endDate))
      .order('paid_at');

    // Buscar despesas por mês
    const { data: expenseData } = await supabase
      .from('expenses')
      .select('amount, paid_at')
      .eq('tenant_id', tenantId)
      .eq('status', 'paid')
      .not('paid_at', 'is', null)
      .gte('paid_at', formatDateForSQL(currentRange.startDate))
      .lte('paid_at', formatDateForSQL(currentRange.endDate))
      .order('paid_at');

    // Agrupar por mês
    const monthlyData = new Map<string, { inflows: number; outflows: number }>();

    // Processar receitas
    (revenueData || []).forEach(payment => {
      if (!payment.paid_at) return; // Pular se paid_at for null
      const month = new Date(payment.paid_at).toISOString().slice(0, 7); // YYYY-MM
      const current = monthlyData.get(month) || { inflows: 0, outflows: 0 };
      current.inflows += ensureNumber(payment.amount);
      monthlyData.set(month, current);
    });

    // Processar despesas
    (expenseData || []).forEach(expense => {
      if (!expense.paid_at) return; // Pular se paid_at for null
      const month = new Date(expense.paid_at).toISOString().slice(0, 7); // YYYY-MM
      const current = monthlyData.get(month) || { inflows: 0, outflows: 0 };
      current.outflows += ensureNumber(expense.amount);
      monthlyData.set(month, current);
    });

    // Converter para array e formatar
    const result: MonthlyCashFlowData[] = Array.from(monthlyData.entries())
      .map(([month, data]) => {
        const netFlow = data.inflows - data.outflows;
        return {
          month,
          inflows: data.inflows,
          outflows: data.outflows,
          netFlow,
          formattedInflows: formatCurrency(data.inflows),
          formattedOutflows: formatCurrency(data.outflows),
          formattedNetFlow: formatCurrency(netFlow)
        };
      })
      .sort((a, b) => a.month.localeCompare(b.month));

    return {
      success: true,
      data: result,
      message: 'Dados de fluxo de caixa mensal obtidos com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}

/**
 * Busca dados de saldo acumulado para gráfico
 */
export async function getCumulativeBalanceData(
  currentRange: DateRange
): Promise<DashboardActionResult<CumulativeBalanceData[]>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    // Buscar todas as transações ordenadas por data
    const { data: revenueData } = await supabase
      .from('payments')
      .select('amount, paid_at')
      .eq('tenant_id', tenantId)
      .eq('status', 'paid')
      .not('paid_at', 'is', null)
      .gte('paid_at', formatDateForSQL(currentRange.startDate))
      .lte('paid_at', formatDateForSQL(currentRange.endDate))
      .order('paid_at');

    const { data: expenseData } = await supabase
      .from('expenses')
      .select('amount, paid_at')
      .eq('tenant_id', tenantId)
      .eq('status', 'paid')
      .not('paid_at', 'is', null)
      .gte('paid_at', formatDateForSQL(currentRange.startDate))
      .lte('paid_at', formatDateForSQL(currentRange.endDate))
      .order('paid_at');

    // Combinar e ordenar todas as transações (filtrar paid_at null)
    const allTransactions: { amount: number; paid_at: string; type: 'revenue' | 'expense' }[] = [
      ...(revenueData || [])
        .filter(r => r.paid_at !== null)
        .map(r => ({ ...r, paid_at: r.paid_at!, type: 'revenue' as const })),
      ...(expenseData || [])
        .filter(e => e.paid_at !== null)
        .map(e => ({ ...e, paid_at: e.paid_at!, amount: -ensureNumber(e.amount), type: 'expense' as const }))
    ].sort((a, b) => new Date(a.paid_at).getTime() - new Date(b.paid_at).getTime());

    // Calcular saldo acumulado por mês
    const monthlyBalance = new Map<string, number>();
    let cumulativeBalance = 0;

    allTransactions.forEach(transaction => {
      const month = new Date(transaction.paid_at).toISOString().slice(0, 7);
      cumulativeBalance += ensureNumber(transaction.amount);
      monthlyBalance.set(month, cumulativeBalance);
    });

    const result: CumulativeBalanceData[] = Array.from(monthlyBalance.entries())
      .map(([month, balance]) => ({
        month,
        cumulativeBalance: balance,
        formattedBalance: formatCurrency(balance)
      }))
      .sort((a, b) => a.month.localeCompare(b.month));

    return {
      success: true,
      data: result,
      message: 'Dados de saldo acumulado obtidos com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}

/**
 * Busca análise de sazonalidade baseada em dados históricos
 */
export async function getSeasonalityAnalysis(): Promise<DashboardActionResult<SeasonalityData[]>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    // Buscar dados dos últimos 12 meses para análise de sazonalidade
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

    const { data: revenueData } = await supabase
      .from('payments')
      .select('amount, paid_at')
      .eq('tenant_id', tenantId)
      .eq('status', 'paid')
      .not('paid_at', 'is', null)
      .gte('paid_at', formatDateForSQL(oneYearAgo));

    const { data: expenseData } = await supabase
      .from('expenses')
      .select('amount, paid_at')
      .eq('tenant_id', tenantId)
      .eq('status', 'paid')
      .not('paid_at', 'is', null)
      .gte('paid_at', formatDateForSQL(oneYearAgo));

    // Agrupar por mês do ano (1-12)
    const monthlyAverages = new Map<number, { inflows: number[]; outflows: number[] }>();

    // Inicializar todos os meses
    for (let i = 1; i <= 12; i++) {
      monthlyAverages.set(i, { inflows: [], outflows: [] });
    }

    // Processar receitas
    (revenueData || []).forEach(payment => {
      if (!payment.paid_at) return; // Pular se paid_at for null
      const month = new Date(payment.paid_at).getMonth() + 1; // 1-12
      const data = monthlyAverages.get(month)!;
      data.inflows.push(ensureNumber(payment.amount));
    });

    // Processar despesas
    (expenseData || []).forEach(expense => {
      if (!expense.paid_at) return; // Pular se paid_at for null
      const month = new Date(expense.paid_at).getMonth() + 1; // 1-12
      const data = monthlyAverages.get(month)!;
      data.outflows.push(ensureNumber(expense.amount));
    });

    // Calcular médias
    const monthNames = [
      'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
      'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
    ];

    const result: SeasonalityData[] = Array.from(monthlyAverages.entries())
      .map(([monthNum, data]) => {
        const avgInflows = data.inflows.length > 0
          ? data.inflows.reduce((sum, val) => sum + val, 0) / data.inflows.length
          : 0;
        const avgOutflows = data.outflows.length > 0
          ? data.outflows.reduce((sum, val) => sum + val, 0) / data.outflows.length
          : 0;
        const avgNetFlow = avgInflows - avgOutflows;

        return {
          month: monthNum.toString().padStart(2, '0'),
          monthName: monthNames[monthNum - 1],
          averageInflows: avgInflows,
          averageOutflows: avgOutflows,
          averageNetFlow: avgNetFlow,
          formattedAverageInflows: formatCurrency(avgInflows),
          formattedAverageOutflows: formatCurrency(avgOutflows),
          formattedAverageNetFlow: formatCurrency(avgNetFlow)
        };
      })
      .sort((a, b) => parseInt(a.month) - parseInt(b.month));

    return {
      success: true,
      data: result,
      message: 'Análise de sazonalidade obtida com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}

/**
 * Gera projeções de fluxo de caixa baseadas em dados históricos
 */
export async function getCashFlowProjections(
  currentRange: DateRange,
  monthsToProject: number = 6
): Promise<DashboardActionResult<CashFlowProjectionData[]>> {
  try {
    // Buscar dados históricos para calcular médias
    const historicalData = await getMonthlyCashFlowData(currentRange);

    if (!historicalData.success || !historicalData.data) {
      throw new Error('Erro ao buscar dados históricos para projeção');
    }

    const historical = historicalData.data;

    // Calcular médias dos últimos meses
    const avgInflows = historical.length > 0
      ? historical.reduce((sum, month) => sum + month.inflows, 0) / historical.length
      : 0;
    const avgOutflows = historical.length > 0
      ? historical.reduce((sum, month) => sum + month.outflows, 0) / historical.length
      : 0;

    // Calcular tendência (crescimento/decrescimento)
    const inflowTrend = historical.length >= 2
      ? (historical[historical.length - 1].inflows - historical[0].inflows) / historical.length
      : 0;
    const outflowTrend = historical.length >= 2
      ? (historical[historical.length - 1].outflows - historical[0].outflows) / historical.length
      : 0;

    // Gerar projeções
    const projections: CashFlowProjectionData[] = [];
    const currentDate = new Date(currentRange.endDate);

    for (let i = 1; i <= monthsToProject; i++) {
      const projectionDate = new Date(currentDate);
      projectionDate.setMonth(projectionDate.getMonth() + i);

      const month = projectionDate.toISOString().slice(0, 7);

      // Aplicar tendência às projeções
      const projectedInflows = Math.max(0, avgInflows + (inflowTrend * i));
      const projectedOutflows = Math.max(0, avgOutflows + (outflowTrend * i));
      const projectedNetFlow = projectedInflows - projectedOutflows;

      // Calcular confiança baseada na quantidade de dados históricos
      const confidence = Math.min(90, Math.max(30, (historical.length / 12) * 100));

      projections.push({
        month,
        projectedInflows,
        projectedOutflows,
        projectedNetFlow,
        confidence,
        formattedProjectedInflows: formatCurrency(projectedInflows),
        formattedProjectedOutflows: formatCurrency(projectedOutflows),
        formattedProjectedNetFlow: formatCurrency(projectedNetFlow)
      });
    }

    return {
      success: true,
      data: projections,
      message: 'Projeções de fluxo de caixa geradas com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}

/**
 * Busca todos os dados de análise de fluxo de caixa
 */
export async function getCashFlowAnalysisData(
  currentRange: DateRange
): Promise<DashboardActionResult<CashFlowAnalysisData>> {
  try {
    await getAuthenticatedClient(); // Verificar autenticação

    // Buscar todos os dados em paralelo
    const [
      monthlyCashFlowResult,
      projectionsResult,
      cumulativeBalanceResult,
      seasonalityResult,
      metricsResult
    ] = await Promise.all([
      getMonthlyCashFlowData(currentRange),
      getCashFlowProjections(currentRange),
      getCumulativeBalanceData(currentRange),
      getSeasonalityAnalysis(),
      getCashFlowMetrics(currentRange)
    ]);

    // Verificar se todas as chamadas foram bem-sucedidas
    if (!monthlyCashFlowResult.success) {
      throw new Error(monthlyCashFlowResult.error || 'Erro ao buscar dados mensais');
    }
    if (!projectionsResult.success) {
      throw new Error(projectionsResult.error || 'Erro ao buscar projeções');
    }
    if (!cumulativeBalanceResult.success) {
      throw new Error(cumulativeBalanceResult.error || 'Erro ao buscar saldo acumulado');
    }
    if (!seasonalityResult.success) {
      throw new Error(seasonalityResult.error || 'Erro ao buscar análise de sazonalidade');
    }
    if (!metricsResult.success) {
      throw new Error(metricsResult.error || 'Erro ao buscar métricas');
    }

    const analysisData: CashFlowAnalysisData = {
      monthlyCashFlow: monthlyCashFlowResult.data || [],
      projections: projectionsResult.data || [],
      cumulativeBalance: cumulativeBalanceResult.data || [],
      seasonality: seasonalityResult.data || [],
      currentBalance: metricsResult.data?.currentBalance || 0,
      projectedBalance: metricsResult.data?.projectedBalance || 0,
      cashFlowTrend: metricsResult.data?.cashFlowTrend || 'stable'
    };

    return {
      success: true,
      data: analysisData,
      message: 'Dados de análise de fluxo de caixa obtidos com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}
