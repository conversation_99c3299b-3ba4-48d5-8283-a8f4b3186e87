'use client';

import { useState, useTransition, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, Key, Check, AlertCircle, Eye, EyeOff } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { savePixSettings } from '../actions/pix-config-actions';

interface PixConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentPixKey?: string;
  onPixKeyUpdated?: (newPixKey: string) => void;
}

export function PixConfigModal({ isOpen, onClose, currentPixKey, onPixKeyUpdated }: PixConfigModalProps) {
  const [pixKey, setPixKey] = useState(currentPixKey || '');
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [showPixKey, setShowPixKey] = useState(false);
  const router = useRouter();

  // Sincroniza o estado interno quando currentPixKey muda
  useEffect(() => {
    setPixKey(currentPixKey || '');
  }, [currentPixKey]);

  const handleSave = () => {
    if (!pixKey.trim()) {
      setError('Por favor, insira uma chave PIX válida');
      return;
    }

    setError(null);
    setSuccess(false);

    startTransition(async () => {
      const result = await savePixSettings({ pixKey: pixKey.trim() });

      if (!result.success) {
        setError(result.error || 'Erro ao salvar configurações PIX');
      } else {
        setSuccess(true);

        // Chama o callback para atualizar o card em tempo real
        if (onPixKeyUpdated) {
          onPixKeyUpdated(pixKey.trim());
        }

        router.refresh();

        // Fecha o modal após um breve delay para mostrar o sucesso
        setTimeout(() => {
          onClose();
          setSuccess(false);
        }, 1500);
      }
    });
  };

  const handleClose = () => {
    if (!isPending) {
      setPixKey(currentPixKey || '');
      setError(null);
      setSuccess(false);
      setShowPixKey(false);
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Key className="h-5 w-5 text-blue-600" />
            Configurar PIX
          </DialogTitle>
          <DialogDescription>
            Configure sua chave PIX para receber pagamentos. Esta chave será usada para gerar QR Codes de pagamento.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="pix-key">Chave PIX</Label>
            <div className="relative">
              <Input
                id="pix-key"
                type={showPixKey ? 'text' : 'password'}
                placeholder="Digite sua chave PIX (CPF, CNPJ, e-mail, telefone ou chave aleatória)"
                value={pixKey}
                onChange={(e) => setPixKey(e.target.value)}
                disabled={isPending}
                className="w-full pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPixKey(!showPixKey)}
                disabled={isPending}
              >
                {showPixKey ? (
                  <EyeOff className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <Eye className="h-4 w-4 text-muted-foreground" />
                )}
                <span className="sr-only">
                  {showPixKey ? 'Esconder chave PIX' : 'Mostrar chave PIX'}
                </span>
              </Button>
            </div>
            <div className="text-xs text-muted-foreground space-y-1">
              <p className="font-medium">Formatos válidos:</p>
              <div className="space-y-0.5 pl-2">
                <p><span className="font-medium">EMAIL:</span> <EMAIL></p>
                <p><span className="font-medium">CPF:</span> 12345678900</p>
                <p><span className="font-medium">CNPJ:</span> 00038166000105</p>
                <p><span className="font-medium">TELEFONE:</span> +5561912345678</p>
                <p><span className="font-medium">ALEATÓRIA:</span> 123e4567-e12b-12d1-a456-426655440000</p>
              </div>
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="border-green-200 bg-green-50 dark:bg-green-950/30 dark:border-green-800">
              <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
              <AlertDescription className="text-green-800 dark:text-green-200">
                Configurações PIX salvas com sucesso!
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isPending}
          >
            Cancelar
          </Button>
          <Button
            type="button"
            onClick={handleSave}
            disabled={isPending || !pixKey.trim()}
          >
            {isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Salvando...
              </>
            ) : (
              'Salvar'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
